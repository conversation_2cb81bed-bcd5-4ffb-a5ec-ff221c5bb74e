﻿using System.Collections.ObjectModel;

namespace PetVet.Shared.Components.Framework;

public class KtDialogService
{
    public ObservableCollection<ModalDialogConfig> Dialogs { get; set; }

    public KtDialogService()
    {
        Dialogs = new ObservableCollection<ModalDialogConfig>();
    }


    public void ShowDialogAsync(ModalDialogConfig dialogConfig)
    {
        Dialogs.Add(dialogConfig);
    }


}
public class ModalDialogConfig
{

    public ModalDialogConfig()
    {
        Id = $"Dialog{DateTime.Now.ToString("HHMMssff")}_";
        Parameters = new Dictionary<string, object>();
    }

    public void AddParameter(string key, object value)
    {
        Parameters.Add(key, value);
    }

    public string Id { get; set; }

    public string Title { get; set; }

    public string SizeClasses { get; set; }

    public string PositionClasses { get; set; }

    public string DialogContainerClasses { get; set; }

    public bool ShowCrossIcon { get; set; } = true;

    public Type Component { get; set; }

    public IDictionary<string, object> Parameters { get; set; }
}
