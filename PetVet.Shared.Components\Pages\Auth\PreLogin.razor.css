﻿.PreLogin-CONTAINER {
    background: #f2f6f9;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    padding: 0;
}

.PreLogin-ROW1{
    width: 95%;
}

.PreLogin-CLOUD-IMAGE-CONTAINER {
    height: 30vh;
}

.PreLogin-CLOUD-IMAGE {
    width: 70%;
    height: 35vh;
}

.PreLogin-BANNER {
    background: #FEA195;
    border-radius: 1.25rem;
    padding: 1.25rem;
    position: relative;
    margin: 0 auto;
    margin-top: 3.75rem;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 20vh;
    width: 100%;
}

.PreLogin-BANNER-IMG {
    height: 30vh;
    position: absolute;
    bottom: 0;
}

.PreLogin-HEADINGS {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    margin: 0.625rem;
    cursor: default;
}

.PreLogin-PRIMARY-HEADING {
    font-family: 'Acme', sans-serif;
    color: #333;
}

.PreLogin-SECONDARY-HEADING {
    font-family: 'Acme', sans-serif;
    color: #FEA195;
}

.PreLogin-PARA {
    
    color: #333;
}

.PreLogin-BUTTONS-CONTAINER {
    display: flex;
    justify-content: center;
    align-items: center;
}

.PreLogin-LOGIN-REGISTER-BUTTON {
    display: inline-block;
    background-color: #FEA195;
    border: none;
    border-radius: 0.625rem;
    padding: 0.625rem 1.25rem;
    color: #fff;

    font-weight: bold;
    cursor: pointer;
    margin: 0.625rem;
    text-align: center;
    width: 40%;
    text-decoration: none;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.PreLogin-LOGIN-REGISTER-BUTTON:hover {
    background-color: #e8755f;
    transform: scale(1.05);
}


@media (max-width: 992px) {

  
}

@media (max-width: 768px) {

    .PreLogin-CLOUD-IMAGE-CONTAINER {
        height: 30vh;
    }

    .PreLogin-CLOUD-IMAGE {
        width: 100%;
        height: 30vh;
    }
}