﻿using PetVet.Shared.DTOs;
using Refit;

namespace PetVet.Shared.Components.Apis
{
    [Headers("Authorization: Bearer ")]
    public interface IAuthApi
    {
        [Post("/api/auth/login")]
        Task<AuthResponseDto> LoginAsync(LoginDto loginDto);

        [Post("/api/auth/register/petOwner")]
        Task<PetVetApiResponse> RegisterPetOwnerAsync(PetOwnerDto petOwnerDto);

        [Post("/api/auth/register/vet")]
        Task<PetVetApiResponse> RegisterVetAsync(VetDto vetDto);

        [Post("/api/auth/changePassword")]
        Task<PetVetApiResponse> ChangePasswordAsync(ChangePasswordDto changePasswordDto);
    }
}
