# Audio Setup and Configuration Guide

## Overview

This guide explains how to set up and configure audio permissions, playback, and ringtones for the PetVet calling system.

## Prerequisites

### 1. **NuGet Package Installation**
The following package has been added to support audio playback:
```xml
<PackageReference Include="Plugin.Maui.Audio" Version="3.0.1" />
```

### 2. **Platform Permissions**

#### **Android (AndroidManifest.xml)**
```xml
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

#### **iOS (Info.plist)**
```xml
<key>NSMicrophoneUsageDescription</key>
<string>This app needs access to microphone for voice calls</string>
<key>NSCameraUsageDescription</key>
<string>This app needs access to camera for video calls</string>
<key>UIBackgroundModes</key>
<array>
    <string>audio</string>
    <string>voip</string>
</array>
```

## Service Registration

The following services are registered in `MauiProgram.cs`:

```csharp
// Register Plugin.Maui.Audio
builder.Services.AddSingleton(Plugin.Maui.Audio.AudioManager.Current);

// Register Audio Services
builder.Services.AddSingleton<IMediaCaptureService, MediaCaptureService>();
builder.Services.AddScoped<IAudioStreamingService, AudioStreamingService>();
builder.Services.AddScoped<IAudioRingtoneService, AudioRingtoneService>();
```

## Audio Components

### 1. **AudioRingtoneService**
Handles ringtone generation and playback:
- **Incoming Call Ringtone**: Pleasant ascending tones
- **Outgoing Call Ringtone**: Repeating beeps
- **Call Connected Sound**: Quick ascending beep
- **Call End Sound**: Descending tones
- **Notification Sound**: Single pleasant tone

### 2. **Enhanced MediaCaptureService**
Provides real audio capture and playback:
- Platform-native audio capture
- Real-time audio playback with WAV conversion
- Volume control and mute functionality
- Audio quality monitoring

### 3. **AudioStreamingService**
Manages real-time audio streaming:
- Audio frame processing and compression
- Buffer management for smooth playback
- Quality adaptation based on network conditions

## Testing Audio Setup

### **AudioTestPage**
A dedicated test page has been created to verify audio functionality:

#### **Features:**
1. **Permission Testing**: Check microphone and audio playback permissions
2. **Ringtone Testing**: Test all ringtone types (incoming, outgoing, connected, end)
3. **Audio Capture Testing**: Test microphone capture with real-time level monitoring
4. **Comprehensive Logging**: Detailed logs for troubleshooting

#### **How to Access:**
Navigate to `AudioTestPage` in your app to run comprehensive audio tests.

## Call Flow with Audio

### **Outgoing Call (Caller Side):**
```
1. User initiates call
2. ChatCallService.SendCallInvitationAsync()
3. Plays outgoing call ringtone (looped)
4. Waits for call acceptance
5. On acceptance: Stops ringtone, plays connected sound
6. Starts audio capture and streaming
```

### **Incoming Call (Receiver Side):**
```
1. Receives call invitation via SignalR
2. ChatCallService triggers CallInvitationReceived event
3. Plays incoming call ringtone (looped)
4. Shows CallInvitationPopup
5. On acceptance: Stops ringtone, plays connected sound
6. Starts audio capture and streaming
```

### **Call End (Both Sides):**
```
1. User ends call or call is declined
2. Stops all ringtones
3. Plays call end sound
4. Stops audio capture and streaming
```

## Troubleshooting

### **Common Issues:**

#### **1. No Ringtone Sound**
**Symptoms**: Call popup appears but no ringtone plays
**Solutions**:
- Check audio permissions using AudioTestPage
- Verify device volume is not muted
- Test ringtones individually using AudioTestPage
- Check device audio output settings

#### **2. Permission Denied**
**Symptoms**: Audio capture fails or permission errors
**Solutions**:
- Manually grant microphone permissions in device settings
- Restart the app after granting permissions
- Use AudioTestPage to verify permission status

#### **3. Audio Playback Issues**
**Symptoms**: Ringtones don't play or audio is distorted
**Solutions**:
- Test on different devices
- Check Plugin.Maui.Audio compatibility
- Verify WAV file generation in MediaCaptureService

#### **4. Platform-Specific Issues**

**Android**:
- Ensure `RECORD_AUDIO` permission is granted
- Check if device has audio focus
- Verify AudioManager settings

**iOS**:
- Ensure microphone usage description is set
- Check if app has background audio capability
- Verify AVAudioSession configuration

### **Debug Tools:**

#### **AudioTestPage Features:**
1. **Permission Checker**: Verifies microphone and audio permissions
2. **Ringtone Tester**: Tests each ringtone type individually
3. **Audio Capture Tester**: Tests microphone with real-time level display
4. **Comprehensive Logging**: Detailed logs for troubleshooting

#### **CallTestPage Enhancements:**
1. **Audio Flow Debug**: Diagnose audio streaming issues
2. **Enhanced Audio Controls**: Real-time audio quality monitoring
3. **Call Flow Logging**: Trace complete call flow with audio events

## Performance Considerations

### **Audio Quality Settings:**
- **Low Quality**: 8kHz, 8-bit (for poor network conditions)
- **Standard Quality**: 16kHz, 16-bit (default for voice calls)
- **High Quality**: 44.1kHz, 16-bit (for audio-only calls)
- **Premium Quality**: 48kHz, 24-bit (for high-quality calls)

### **Buffer Management:**
- **Minimum Buffer**: 3 frames (60ms)
- **Maximum Buffer**: 10 frames (200ms)
- **Target Latency**: < 150ms for good quality

### **Network Adaptation:**
- Automatic quality reduction when packet loss > 5%
- Compression enabled for bandwidth efficiency
- Real-time quality monitoring and adjustment

## Best Practices

### **1. Permission Handling**
- Always check permissions before starting audio operations
- Provide clear explanations for permission requests
- Handle permission denial gracefully

### **2. Audio Resource Management**
- Stop ringtones when calls are answered/declined
- Clean up audio resources when pages are disposed
- Use background tasks for audio operations

### **3. User Experience**
- Provide visual feedback for audio states (mute, volume, quality)
- Use appropriate ringtone volumes
- Implement smooth transitions between audio states

### **4. Testing**
- Test on multiple devices and platforms
- Verify audio works with different network conditions
- Test permission scenarios (granted/denied)

## Integration Examples

### **Basic Ringtone Usage:**
```csharp
// Play incoming call ringtone
await _ringtoneService.PlayIncomingCallRingtoneAsync();

// Stop all sounds when call is answered
await _ringtoneService.StopAllSoundsAsync();

// Play connected sound
await _ringtoneService.PlayCallConnectedSoundAsync();
```

### **Audio Capture with Permissions:**
```csharp
// Check permissions first
var hasPermission = await _mediaCaptureService.RequestAudioPermissionAsync();
if (hasPermission)
{
    // Start audio capture
    await _mediaCaptureService.StartAudioCaptureAsync();
}
```

### **Real-time Audio Streaming:**
```csharp
// Start streaming for a call
await _audioStreamingService.StartStreamingAsync(callId);

// Process incoming audio
await _audioStreamingService.ProcessIncomingAudioAsync(audioFrame);

// Stop streaming when call ends
await _audioStreamingService.StopStreamingAsync();
```

This comprehensive audio setup provides a professional calling experience with proper permission handling, high-quality audio playback, and robust error handling.
