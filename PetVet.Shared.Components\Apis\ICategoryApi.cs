﻿using PetVet.Shared.DTOs;
using Refit;

namespace PetVet.Shared.Components.Apis
{
    [Headers("Authorization: Bearer ")]
    public interface ICategoryApi
    {
        [Post("/api/categories")]
        Task<PetVetApiResponse> SaveCategoryAsync(CategoryDto categoryDto);

        [Get("/api/categories")]
        Task<CategoryDto[]> GetCategoriesAsync();

        [Get("/api/categories/{id}")]
        Task<CategoryDto> GetCategoryAsync(int id);

        [Delete("/api/categories/{id}")]
        Task<PetVetApiResponse> DeleteCategoryAsync(int id);
    }
}
