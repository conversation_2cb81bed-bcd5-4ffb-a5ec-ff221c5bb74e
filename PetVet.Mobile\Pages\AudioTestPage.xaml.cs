using Microsoft.Extensions.Logging;
using Microsoft.Maui.Controls.Shapes;
using PetVet.Mobile.Services;
using System.Text;
using System.Timers;

namespace PetVet.Mobile.Pages;

public partial class AudioTestPage : ContentPage
{
    private readonly IMediaCaptureService _mediaCaptureService;
    private readonly IAudioRingtoneService _ringtoneService;
    private readonly ILogger<AudioTestPage> _logger;
    
    private readonly StringBuilder _logBuilder = new();
    private System.Timers.Timer? _audioLevelTimer;

    public AudioTestPage(
        IMediaCaptureService mediaCaptureService,
        IAudioRingtoneService ringtoneService,
        ILogger<AudioTestPage> logger)
    {
        InitializeComponent();
        
        _mediaCaptureService = mediaCaptureService;
        _ringtoneService = ringtoneService;
        _logger = logger;
        
        SubscribeToEvents();
        LogMessage("Audio test page initialized");
    }

    private void SubscribeToEvents()
    {
        _mediaCaptureService.AudioDataCaptured += OnAudioDataCaptured;
        _mediaCaptureService.AudioQualityChanged += OnAudioQualityChanged;
    }

    private void OnAudioDataCaptured(object? sender, byte[] audioData)
    {
        // Update audio level meter on main thread
        MainThread.BeginInvokeOnMainThread(() =>
        {
            var metrics = _mediaCaptureService.GetAudioQualityMetrics();
            AudioLevelMeter.Progress = metrics.AudioLevel;
        });
    }

    private void OnAudioQualityChanged(object? sender, PetVet.Mobile.Models.AudioQualityMetrics metrics)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            AudioLevelMeter.Progress = metrics.AudioLevel;
        });
    }

    private async void OnCheckPermissionsClicked(object sender, EventArgs e)
    {
        try
        {
            LogMessage("Checking audio permissions...");
            
            // Check microphone permission
            var micPermission = await _mediaCaptureService.RequestAudioPermissionAsync();
            UpdatePermissionStatus(MicrophoneStatusIndicator, MicrophoneStatusLabel, 
                "Microphone", micPermission);
            
            // Test audio playback capability
            LogMessage("Testing audio playback capability...");
            var audioTest = await TestAudioPlayback();
            UpdatePermissionStatus(AudioStatusIndicator, AudioStatusLabel, 
                "Audio Playback", audioTest);
            
            LogMessage($"Permission check complete - Mic: {micPermission}, Audio: {audioTest}");
        }
        catch (Exception ex)
        {
            LogMessage($"Error checking permissions: {ex.Message}");
            _logger.LogError(ex, "Error checking audio permissions");
        }
    }

    private async Task<bool> TestAudioPlayback()
    {
        try
        {
            // Try to play a short notification sound
            return await _ringtoneService.PlayNotificationSoundAsync();
        }
        catch (Exception ex)
        {
            LogMessage($"Audio playback test failed: {ex.Message}");
            return false;
        }
    }

    private void UpdatePermissionStatus(Ellipse indicator, Label label, string permissionName, bool granted)
    {
        indicator.Fill = granted ? Colors.Green : Colors.Red;
        label.Text = $"{permissionName}: {(granted ? "Granted" : "Denied")}";
    }

    private async void OnPlayIncomingClicked(object sender, EventArgs e)
    {
        try
        {
            LogMessage("Playing incoming call ringtone...");
            var success = await _ringtoneService.PlayIncomingCallRingtoneAsync();
            LogMessage($"Incoming ringtone playback: {(success ? "Started" : "Failed")}");
        }
        catch (Exception ex)
        {
            LogMessage($"Error playing incoming ringtone: {ex.Message}");
        }
    }

    private async void OnPlayOutgoingClicked(object sender, EventArgs e)
    {
        try
        {
            LogMessage("Playing outgoing call ringtone...");
            var success = await _ringtoneService.PlayOutgoingCallRingtoneAsync();
            LogMessage($"Outgoing ringtone playback: {(success ? "Started" : "Failed")}");
        }
        catch (Exception ex)
        {
            LogMessage($"Error playing outgoing ringtone: {ex.Message}");
        }
    }

    private async void OnPlayConnectedClicked(object sender, EventArgs e)
    {
        try
        {
            LogMessage("Playing call connected sound...");
            var success = await _ringtoneService.PlayCallConnectedSoundAsync();
            LogMessage($"Connected sound playback: {(success ? "Completed" : "Failed")}");
        }
        catch (Exception ex)
        {
            LogMessage($"Error playing connected sound: {ex.Message}");
        }
    }

    private async void OnPlayEndClicked(object sender, EventArgs e)
    {
        try
        {
            LogMessage("Playing call end sound...");
            var success = await _ringtoneService.PlayCallEndSoundAsync();
            LogMessage($"End sound playback: {(success ? "Completed" : "Failed")}");
        }
        catch (Exception ex)
        {
            LogMessage($"Error playing end sound: {ex.Message}");
        }
    }

    private async void OnStopAllClicked(object sender, EventArgs e)
    {
        try
        {
            LogMessage("Stopping all sounds...");
            await _ringtoneService.StopAllSoundsAsync();
            LogMessage("All sounds stopped");
        }
        catch (Exception ex)
        {
            LogMessage($"Error stopping sounds: {ex.Message}");
        }
    }

    private async void OnStartCaptureClicked(object sender, EventArgs e)
    {
        try
        {
            LogMessage("Starting audio capture...");
            
            var success = await _mediaCaptureService.StartAudioCaptureAsync();
            if (success)
            {
                CaptureStatusLabel.Text = "Status: Capturing Audio";
                StartCaptureButton.IsEnabled = false;
                StopCaptureButton.IsEnabled = true;
                
                // Start audio level monitoring
                _audioLevelTimer = new System.Timers.Timer(100); // Update every 100ms
                _audioLevelTimer.Elapsed += OnAudioLevelTimer;
                _audioLevelTimer.Start();
                
                LogMessage("Audio capture started successfully");
            }
            else
            {
                LogMessage("Failed to start audio capture");
            }
        }
        catch (Exception ex)
        {
            LogMessage($"Error starting audio capture: {ex.Message}");
        }
    }

    private async void OnStopCaptureClicked(object sender, EventArgs e)
    {
        try
        {
            LogMessage("Stopping audio capture...");
            
            var success = await _mediaCaptureService.StopAudioCaptureAsync();
            if (success)
            {
                CaptureStatusLabel.Text = "Status: Stopped";
                StartCaptureButton.IsEnabled = true;
                StopCaptureButton.IsEnabled = false;
                
                // Stop audio level monitoring
                _audioLevelTimer?.Stop();
                _audioLevelTimer?.Dispose();
                _audioLevelTimer = null;
                
                AudioLevelMeter.Progress = 0;
                
                LogMessage("Audio capture stopped successfully");
            }
            else
            {
                LogMessage("Failed to stop audio capture");
            }
        }
        catch (Exception ex)
        {
            LogMessage($"Error stopping audio capture: {ex.Message}");
        }
    }

    private void OnAudioLevelTimer(object? sender, ElapsedEventArgs e)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            try
            {
                var metrics = _mediaCaptureService.GetAudioQualityMetrics();
                AudioLevelMeter.Progress = metrics.AudioLevel;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating audio level meter");
            }
        });
    }

    private void OnClearLogClicked(object sender, EventArgs e)
    {
        _logBuilder.Clear();
        LogLabel.Text = "Log cleared...";
    }

    private void LogMessage(string message)
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss.fff");
        var logEntry = $"[{timestamp}] {message}";
        
        _logBuilder.AppendLine(logEntry);
        
        MainThread.BeginInvokeOnMainThread(() =>
        {
            LogLabel.Text = _logBuilder.ToString();
        });
        
        _logger.LogInformation("AUDIO_TEST: {Message}", message);
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        
        // Clean up
        _audioLevelTimer?.Stop();
        _audioLevelTimer?.Dispose();
        
        // Stop any ongoing audio operations
        _ = Task.Run(async () =>
        {
            await _ringtoneService.StopAllSoundsAsync();
            await _mediaCaptureService.StopAudioCaptureAsync();
        });
        
        // Unsubscribe from events
        _mediaCaptureService.AudioDataCaptured -= OnAudioDataCaptured;
        _mediaCaptureService.AudioQualityChanged -= OnAudioQualityChanged;
    }
}
