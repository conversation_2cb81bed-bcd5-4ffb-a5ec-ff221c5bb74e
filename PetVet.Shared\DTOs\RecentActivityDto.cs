namespace PetVet.Shared.DTOs
{
    public class RecentActivityDto
    {
        public int Id { get; set; }
        public string ActivityType { get; set; } = string.Empty; // Appointment, Vaccination, PetAdded, BreedIdentified
        public string Title { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string PetName { get; set; } = string.Empty;
        public string Icon { get; set; } = string.Empty;
        public string IconColor { get; set; } = string.Empty;
        public string BackgroundColor { get; set; } = string.Empty;
        public DateTime ActivityDate { get; set; }
        public string TimeAgo { get; set; } = string.Empty;
        public int? RelatedId { get; set; } // ID of related appointment, pet, etc.
    }
} 