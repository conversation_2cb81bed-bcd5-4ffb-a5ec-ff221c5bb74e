﻿@inject IJSRuntime _JS

<!-- Modern Vet Header -->
<div class="flex items-center justify-between mb-6">
    <!-- Back Button -->
    <button @onclick="NavigateBack"
            class="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-xl flex items-center justify-center transition-all">
        <i class="fas fa-arrow-left text-gray-600"></i>
    </button>

    <!-- Title -->
    <h1 class="text-xl font-bold text-gray-800">Veterinarian Profile</h1>

    <!-- Action Buttons -->
    <div class="flex items-center gap-2">
        <!-- Notification Button -->
        <a href="notifications"
           class="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-xl flex items-center justify-center transition-all">
            <i class="fas fa-bell text-gray-600"></i>
        </a>

        <!-- Menu Button -->
        <div class="relative">
            <button @onclick="ToggleDropdown"
                    class="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-xl flex items-center justify-center transition-all">
                <i class="fas fa-ellipsis-vertical text-gray-600"></i>
            </button>

            @if (showDropdown)
            {
                <!-- Modern Dropdown Menu -->
                <div class="absolute right-0 top-12 w-48 bg-white rounded-2xl shadow-lg border border-gray-100 py-2 z-50">
                    <button class="w-full flex items-center gap-3 px-4 py-3 hover:bg-gray-50 transition-all">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-share text-green-600 text-sm"></i>
                        </div>
                        <span class="text-gray-700 font-medium">Share Profile</span>
                    </button>

                    <button class="w-full flex items-center gap-3 px-4 py-3 hover:bg-red-50 transition-all">
                        <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-flag text-red-600 text-sm"></i>
                        </div>
                        <span class="text-red-700 font-medium">Report Profile</span>
                    </button>
                </div>
            }
        </div>
    </div>
</div>

@code {
    private bool showDropdown = false;

    private async Task NavigateBack()
    {
        // Use JS interop to call window.history.back()
        await _JS.InvokeVoidAsync("eval", "window.history.back()");
    }


    private void ToggleDropdown()
    {
        showDropdown = !showDropdown;
    }
}