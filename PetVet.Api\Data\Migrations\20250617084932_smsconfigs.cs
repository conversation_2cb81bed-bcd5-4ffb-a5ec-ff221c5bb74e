﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace PetVet.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class smsconfigs : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "SmsNotificationConfigs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    NotificationType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    IsEnabled = table.Column<bool>(type: "bit", nullable: false),
                    DelayMinutes = table.Column<int>(type: "int", nullable: false),
                    MaxRetries = table.Column<int>(type: "int", nullable: false),
                    RetryIntervalMinutes = table.Column<int>(type: "int", nullable: false),
                    MessageTemplate = table.Column<string>(type: "nvarchar(160)", maxLength: 160, nullable: true),
                    EnableForVets = table.Column<bool>(type: "bit", nullable: false),
                    EnableForPetOwners = table.Column<bool>(type: "bit", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    UpdatedAt = table.Column<DateTime>(type: "datetime2", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SmsNotificationConfigs", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SmsNotificationLogs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    UserId = table.Column<int>(type: "int", nullable: false),
                    PhoneNumber = table.Column<string>(type: "nvarchar(15)", maxLength: 15, nullable: false),
                    NotificationType = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    MessageContent = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Status = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false),
                    ErrorMessage = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    AppointmentId = table.Column<int>(type: "int", nullable: true),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DeliveredAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    TwilioMessageSid = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    TwilioStatus = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    IsRetry = table.Column<bool>(type: "bit", nullable: false),
                    RetryCount = table.Column<int>(type: "int", nullable: false),
                    MaxRetries = table.Column<int>(type: "int", nullable: false),
                    NextRetryAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Cost = table.Column<decimal>(type: "decimal(10,4)", nullable: true),
                    CostUnit = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SmsNotificationLogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SmsNotificationLogs_Appointments_AppointmentId",
                        column: x => x.AppointmentId,
                        principalTable: "Appointments",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_SmsNotificationLogs_Users_UserId",
                        column: x => x.UserId,
                        principalTable: "Users",
                        principalColumn: "Id");
                });

            migrationBuilder.InsertData(
                table: "SmsNotificationConfigs",
                columns: new[] { "Id", "CreatedAt", "DelayMinutes", "EnableForPetOwners", "EnableForVets", "IsEnabled", "MaxRetries", "MessageTemplate", "NotificationType", "RetryIntervalMinutes", "UpdatedAt" },
                values: new object[,]
                {
                    { 1, new DateTime(2024, 6, 17, 8, 0, 1, 1, DateTimeKind.Utc), 0, false, true, true, 3, "New appointment: {PetOwnerName} on {Date} at {Time}", "VetNewAppointment", 5, null },
                    { 2, new DateTime(2024, 6, 17, 8, 0, 1, 1, DateTimeKind.Utc), 0, true, false, true, 3, "Appointment confirmed with Dr. {VetName} on {Date} at {Time}", "AppointmentConfirmed", 5, null },
                    { 3, new DateTime(2024, 6, 17, 8, 0, 1, 1, DateTimeKind.Utc), 0, true, true, true, 2, "Appointment cancelled: {Date} at {Time}", "AppointmentCancelled", 10, null },
                    { 4, new DateTime(2024, 6, 17, 8, 0, 1, 1, DateTimeKind.Utc), 0, true, true, true, 3, "Appointment rescheduled to {Date} at {Time}", "AppointmentRescheduled", 5, null }
                });

            migrationBuilder.CreateIndex(
                name: "IX_SmsNotificationConfigs_NotificationType",
                table: "SmsNotificationConfigs",
                column: "NotificationType",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SmsNotificationLogs_AppointmentId",
                table: "SmsNotificationLogs",
                column: "AppointmentId");

            migrationBuilder.CreateIndex(
                name: "IX_SmsNotificationLogs_CreatedAt",
                table: "SmsNotificationLogs",
                column: "CreatedAt");

            migrationBuilder.CreateIndex(
                name: "IX_SmsNotificationLogs_PhoneNumber",
                table: "SmsNotificationLogs",
                column: "PhoneNumber");

            migrationBuilder.CreateIndex(
                name: "IX_SmsNotificationLogs_Status",
                table: "SmsNotificationLogs",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_SmsNotificationLogs_TwilioMessageSid",
                table: "SmsNotificationLogs",
                column: "TwilioMessageSid");

            migrationBuilder.CreateIndex(
                name: "IX_SmsNotificationLogs_UserId",
                table: "SmsNotificationLogs",
                column: "UserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "SmsNotificationConfigs");

            migrationBuilder.DropTable(
                name: "SmsNotificationLogs");
        }
    }
}
