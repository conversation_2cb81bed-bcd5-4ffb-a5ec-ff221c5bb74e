﻿@inject PetVetAuthStateProvider _PetVetAuthStateProvider
@inject NavigationManager _NavigationManager
@inject IAppState _AppState

<div class="bg-gray-900 text-white p-4">
    <div class="flex items-center justify-between">
        <a class="text-xl font-bold text-white" href="">Admin Dashboard</a>
        <button title="Navigation menu" class="md:hidden text-white hover:text-gray-300" @onclick="ToggleNavMenu">
            <i class="fas fa-bars text-xl"></i>
        </button>
    </div>
</div>

<div class="@NavMenuCssClass bg-gray-800 text-white h-screen overflow-y-auto" @onclick="ToggleNavMenu">
    <nav class="flex flex-col p-4 space-y-2">

        <AuthorizeView>

            <Authorized>

                <AuthorizeView Roles="@(nameof(UserRole.Admin))" Context="adminContext">

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="admin/home" Match="NavLinkMatch.All">
                            <i class="fas fa-home text-lg"></i>
                            <span>Home</span>
                        </NavLink>
                    </div>

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="admin/account">
                            <i class="fas fa-user text-lg"></i>
                            <span>My Account</span>
                        </NavLink>
                    </div>

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="/admin/manage/pets/profile">
                            <i class="fas fa-paw text-lg"></i>
                            <span>Manage Pets</span>
                        </NavLink>
                    </div>

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="admin/manage/categories">
                            <i class="fas fa-th-large text-lg"></i>
                            <span>Manage Categories</span>
                        </NavLink>
                    </div>

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="admin/manage/education">
                            <i class="fas fa-graduation-cap text-lg"></i>
                            <span>Manage Education</span>
                        </NavLink>
                    </div>

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="admin/manage/users/profile">
                            <i class="fas fa-users text-lg"></i>
                            <span>Manage Users</span>
                        </NavLink>
                    </div>

                </AuthorizeView>

                <AuthorizeView Roles="@(nameof(UserRole.PetOwner))" Context="petOwnerContext">

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="petOwner/home" Match="NavLinkMatch.All">
                            <i class="fas fa-home text-lg"></i>
                            <span>Home</span>
                        </NavLink>
                    </div>

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="petOwner/account">
                            <i class="fas fa-user text-lg"></i>
                            <span>My Account</span>
                        </NavLink>
                    </div>

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="petOwner/myPets">
                            <i class="fas fa-cat text-lg"></i>
                            <span>My Pets</span>
                        </NavLink>
                    </div>

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="petOwner/vetList">
                            <i class="fas fa-user-md text-lg"></i>
                            <span>Vets</span>
                        </NavLink>
                    </div>

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="petOwner/appointments">
                            <i class="fas fa-calendar-alt text-lg"></i>
                            <span>My Appointments</span>
                        </NavLink>
                    </div>

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="petOwner/calendar">
                            <i class="fas fa-calendar text-lg"></i>
                            <span>My Calendar</span>
                        </NavLink>
                    </div>

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="petOwner/chat">
                            <i class="fas fa-comments text-lg"></i>
                            <span>My Chats</span>
                        </NavLink>
                    </div>

                </AuthorizeView>

                <AuthorizeView Roles="@(nameof(UserRole.Vet))" Context="vetContext">

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="vet/home" Match="NavLinkMatch.All">
                            <i class="fas fa-home text-lg"></i>
                            <span>Home</span>
                        </NavLink>
                    </div>

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="vet/account">
                            <i class="fas fa-user text-lg"></i>
                            <span>My Account</span>
                        </NavLink>
                    </div>

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="vet/schedule">
                            <i class="fas fa-clock text-lg"></i>
                            <span>My Schedule</span>
                        </NavLink>
                    </div>

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="vet/appointments">
                            <i class="fas fa-calendar-alt text-lg"></i>
                            <span>My Appointments</span>
                        </NavLink>
                    </div>

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="vet/calendar">
                            <i class="fas fa-calendar text-lg"></i>
                            <span>My Calendar</span>
                        </NavLink>
                    </div>

                    <div class="nav-item">
                        <NavLink class="nav-link flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-primary-600 transition-all duration-200" 
                                 href="vet/chat">
                            <i class="fas fa-comments text-lg"></i>
                            <span>My Chats</span>
                        </NavLink>
                    </div>

                </AuthorizeView>

            </Authorized>

        </AuthorizeView>

        <div class="nav-item border-t border-gray-700 pt-4 mt-4">
            <button class="flex items-center space-x-3 px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-red-600 transition-all duration-200 w-full" 
                    @onclick="LogoutAsync">
                <i class="fas fa-sign-out-alt text-lg"></i>
                <span>Logout</span>
            </button>
        </div>

    </nav>
</div>

@code {

    private UserDto _user = new();
    private bool collapseNavMenu = true;

    private string? NavMenuCssClass => collapseNavMenu ? "hidden md:block" : "block";

    private void ToggleNavMenu()
    {
        collapseNavMenu = !collapseNavMenu;
    }

    private async Task LogoutAsync()
    {
        _AppState.ShowLoader("Logging out");
        await _PetVetAuthStateProvider.SetLogoutAsync();
        _NavigationManager.NavigateTo("auth/pre/login", replace: true);
        _AppState.HideLoader();
    }
}
