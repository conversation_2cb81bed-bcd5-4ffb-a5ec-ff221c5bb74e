﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PetVet.Api.Data.Entities
{
    public class Pet
    {
        [Key]
        public int Id { get; set; }
        [MaxLength(35)]
        public required string Name { get; set; }
        public string? ImageUrl { get; set; }
        [Range(1, 100)]
        public int Age { get; set; }
        [Range(1, 100)]
        public double Weight { get; set; }
        [MaxLength(35)]
        public required string Gender { get; set; }
        [MaxLength(35)]
        public required string Breed { get; set; }
        public DateTime? DateOfBirth { get; set; }
        [MaxLength(35)]
        public required string VaccinationStatus { get; set; }
        public int CategoryId { get; set; }
        [ForeignKey(nameof(CategoryId))]
        public virtual Category? Category { get; set; }

        // User who owns this pet
        public int UserId { get; set; }
        [ForeignKey(nameof(UserId))]
        public virtual User? User { get; set; }

        // Number of animals in this group (for group categories)
        public int Count { get; set; } = 1;
    }
}
