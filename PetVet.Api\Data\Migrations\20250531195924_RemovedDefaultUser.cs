﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PetVet.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class RemovedDefaultUser : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            //migrationBuilder.DeleteData(
            //    table: "Users",
            //    keyColumn: "Id",
            //    keyValue: 1);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "Users",
                columns: new[] { "Id", "Address", "CertificationsUrl", "ClinicName", "EducationId", "Email", "ImageUrl", "IsApproved", "IsOnline", "LicenceDocumentUrl", "Name", "PasswordHash", "Phone", "Rating", "Role", "Specialization", "YearsOfExperience" },
                values: new object[] { 1, "Abbottabad", null, null, null, "<EMAIL>", null, true, null, null, "Bilal", "AQAAAAIAAYagAAAAEIGveQWY67M4o0NkcNH5M7y6ZnDrWQxrbG40qHTxTGA5noyaxW/UkzsksE7u3rY3MQ==", "03457265250", null, "Admin", null, 0 });
        }
    }
}
