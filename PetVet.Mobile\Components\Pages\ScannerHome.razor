﻿@page "/scanner"
@using PetVet.Shared.DTOs
@using PetVet.Shared.Components.Apis
@using Refit
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject IBreedApi BreedApi

<div class="scanner-container page-transition">
    <!-- Header -->
    <div class="header">
        <div class="header-title"><PERSON> Breed Scanner</div>
       
    </div>

    <!-- Upload Card -->
    <div class="modern-card">
        @if (!isProcessing && selectedImageUrl == null)
        {
            <!-- Upload Area -->
            <div class="upload-area" @onclick="TriggerFileInput" 
                 ondragover="event.preventDefault();" 
                 ondrop="handleDrop(event)">
                <svg width="64" height="64" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="margin: 0 auto 16px auto; color: var(--primary-green);">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <div style="font-size: 18px; font-weight: 600; color: var(--text-dark); margin-bottom: 8px;">
                    Upload Pet Photo
                </div>
                <div style="font-size: 14px; color: var(--text-light); margin-bottom: 24px;">
                    Drag and drop or click to select an image
                </div>
                <button class="btn btn-primary">
                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    Choose Image
                </button>
            </div>
        }
        else if (isProcessing)
        {
            <!-- Processing State -->
            <div class="scanning-card" style="background: linear-gradient(135deg, var(--primary-green) 0%, var(--primary-green-dark) 100%); color: white; text-align: center; padding: 40px 24px;">
                <div class="scan-title">
                    Analyzing Pet Breed
                    <span class="loading-dots">
                        <span class="loading-dot"></span>
                        <span class="loading-dot"></span>
                        <span class="loading-dot"></span>
                    </span>
                </div>
                
                <div class="scan-image-container">
                    <img src="@selectedImageUrl" alt="Pet being analyzed" class="scan-image" />
                    <div class="scan-overlay"></div>
                </div>
                
                <div style="font-size: 14px; opacity: 0.8; margin-top: 16px;">
                    Using AI to identify breed characteristics...
                </div>
            </div>
        }
        else if (breedResult != null && breedResult.IsPet)
        {
            <!-- Results Display -->
            <div style="text-align: center; margin-bottom: 24px;">
                <img src="@selectedImageUrl" alt="Analyzed pet"
                     style="width: 200px; height: 200px; border-radius: 20px; object-fit: cover; box-shadow: var(--shadow-lg);" />
            </div>

            <div style="text-align: center; margin-bottom: 24px;">
                <div class="badge">@GetAnimalType(breedResult.TopBreed)</div>
                <div class="breed-name">@breedResult.TopBreed</div>
                <div class="match-percentage">@((breedResult.ConfidenceScore * 100).ToString("F1"))% Match</div>
                @if (!string.IsNullOrEmpty(breedResult.ModelUsed))
                {
                    <div style="font-size: 12px; color: var(--text-light); margin-top: 4px;">
                        Powered by @breedResult.ModelUsed
                    </div>
                }
            </div>

            <!-- Top Predictions -->
            <div class="result-list">
                <div class="result-item">
                    <div style="width: 48px; height: 48px; border-radius: 12px; background: linear-gradient(135deg, var(--primary-green) 0%, var(--secondary-blue) 100%); display: flex; align-items: center; justify-content: center; margin-right: 16px;">
                        <span style="color: white; font-weight: 600; font-size: 14px;">1</span>
                    </div>
                    <div class="result-info">
                        <div class="result-name">@breedResult.TopBreed</div>
                        <div class="result-percentage">@((breedResult.ConfidenceScore * 100).ToString("F1"))% Match</div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="btn-group">
                <button class="btn btn-secondary" @onclick="ResetScanner">
                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Scan Another
                </button>
                @* <button class="btn btn-primary" @onclick="() => NavigateToDetails(breedResult.TopBreed)">
                    <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    View Details
                </button> *@
            </div>
        }
        else if (breedResult != null && !breedResult.IsPet)
        {
            <!-- Not a Pet State -->
            <div style="text-align: center; padding: 40px;">
                <svg width="48" height="48" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="margin: 0 auto 16px auto; color: #f59e0b;">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
                <div style="color: #f59e0b; font-size: 16px; font-weight: 600; margin-bottom: 8px;">Not a Pet Detected</div>
                <div style="color: var(--text-light); font-size: 14px; margin-bottom: 24px;">@breedResult.Message</div>
                <button class="btn btn-primary" @onclick="ResetScanner">Try Another Image</button>
            </div>
        }
        else if (errorMessage != null)
        {
            <!-- Error State -->
            <div style="text-align: center; padding: 40px;">
                <svg width="48" height="48" fill="none" stroke="currentColor" viewBox="0 0 24 24" style="margin: 0 auto 16px auto; color: #ef4444;">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <div style="color: #ef4444; font-size: 16px; font-weight: 600; margin-bottom: 8px;">Analysis Failed</div>
                <div style="color: var(--text-light); font-size: 14px; margin-bottom: 24px;">@errorMessage</div>
                <button class="btn btn-primary" @onclick="ResetScanner">Try Again</button>
            </div>
        }
    </div>
     
</div>

<!-- Hidden file input -->
<InputFile @ref="fileInput" OnChange="HandleFileSelected" accept="image/*" style="display: none;" />

@code {
    private InputFile? fileInput;
    private bool isProcessing = false;
    private string? selectedImageUrl;
    private BreedDto? breedResult;
    private string? errorMessage;

    private async Task TriggerFileInput()
    {
        await JSRuntime.InvokeVoidAsync("triggerFileInput", fileInput?.Element);
    }

    private async Task HandleFileSelected(InputFileChangeEventArgs e)
    {
        var file = e.File;
        if (file != null)
        {
            try
            {
                isProcessing = true;
                errorMessage = null;
                StateHasChanged();

                // Create image URL for preview
                var buffer = new byte[file.Size];
                await file.OpenReadStream().ReadAsync(buffer);
                selectedImageUrl = $"data:{file.ContentType};base64,{Convert.ToBase64String(buffer)}";
                StateHasChanged();

                // Classify the image using server API
                using var stream = file.OpenReadStream();
                var streamPart = new StreamPart(stream, file.Name, file.ContentType);
                breedResult = await BreedApi.PredictBreedAsync(streamPart);
            }
            catch (Exception ex)
            {
                errorMessage = $"Failed to analyze image: {ex.Message}";
                breedResult = null;
            }
            finally
            {
                isProcessing = false;
                StateHasChanged();
            }
        }
    }

    private void ResetScanner()
    {
        selectedImageUrl = null;
        breedResult = null;
        errorMessage = null;
        isProcessing = false;
        StateHasChanged();
    }

    // private void NavigateToDetails(string breedName)
    // {
    //     Navigation.NavigateTo($"/details/{breedName.Replace(" ", "-").ToLower()}");
    // }
     
    private string GetAnimalType(string breedName)
    {
        var lowerBreed = breedName.ToLower();
        if (lowerBreed.Contains("cat") || lowerBreed.Contains("persian") || lowerBreed.Contains("siamese") || 
            lowerBreed.Contains("maine") || lowerBreed.Contains("ragdoll") || lowerBreed.Contains("sphynx") ||
            lowerBreed.Contains("abyssinian") || lowerBreed.Contains("mumbai"))
        {
            return "Cat Breed";
        }
        else if (lowerBreed.Contains("dog") || lowerBreed.Contains("chihuahua") || lowerBreed.Contains("pug") ||
                 lowerBreed.Contains("beagle") || lowerBreed.Contains("boxer") || lowerBreed.Contains("bulldog") ||
                 lowerBreed.Contains("corgi") || lowerBreed.Contains("shepherd") || lowerBreed.Contains("husky") ||
                 lowerBreed.Contains("labrador") || lowerBreed.Contains("pomeranian") || lowerBreed.Contains("rottweiler") ||
                 lowerBreed.Contains("shiba") || lowerBreed.Contains("yorkshire"))
        {
            return "Dog Breed";
        }
        else
        {
            return "Animal Type";
        }
    }
}

<link href="/css/modern-scanner.css" rel="stylesheet" />

<script>
    window.triggerFileInput = (element) => {
        if (element) {
            element.click();
        }
    };

    window.handleDrop = (event) => {
        event.preventDefault();
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            const fileInput = document.querySelector('input[type="file"]');
            if (fileInput) {
                fileInput.files = files;
                fileInput.dispatchEvent(new Event('change', { bubbles: true }));
            }
        }
    };
</script>
