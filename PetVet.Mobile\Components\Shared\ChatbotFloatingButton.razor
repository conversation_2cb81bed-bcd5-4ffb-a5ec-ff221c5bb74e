@using Microsoft.AspNetCore.Components.Web
@inject IJSRuntime JSRuntime

<!-- Floating Chatbot Button -->
<div class="chatbot-fab-container">
    <button @onclick="ToggleChatbot" 
            class="chatbot-fab @(IsOpen ? "chatbot-fab--open" : "")" >
        
        <!-- Chat Icon (when closed) -->
        <div class="chatbot-fab__icon chatbot-fab__icon--chat @(IsOpen ? "chatbot-fab__icon--hidden" : "")">
            <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"/>
            </svg>
        </div>

        <!-- Close Icon (when open) -->
        <div class="chatbot-fab__icon chatbot-fab__icon--close @(IsOpen ? "" : "chatbot-fab__icon--hidden")">
            <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                      d="M6 18L18 6M6 6l12 12"/>
            </svg>
        </div>

        <!-- Notification Badge -->
        @if (HasUnreadMessages && !IsOpen)
        {
            <div class="chatbot-fab__badge">
                @UnreadCount
            </div>
        }

        <!-- Pulse Animation Ring -->
        <div class="chatbot-fab__pulse @(IsOpen ? "chatbot-fab__pulse--hidden" : "")"></div>
    </button>

    
</div>

@code {
    [Parameter] public bool IsOpen { get; set; } = false;
    [Parameter] public EventCallback<bool> IsOpenChanged { get; set; }
    [Parameter] public bool HasUnreadMessages { get; set; } = false;
    [Parameter] public int UnreadCount { get; set; } = 0;

    private async Task ToggleChatbot()
    {
        IsOpen = !IsOpen;
        await IsOpenChanged.InvokeAsync(IsOpen);
        
        // Add haptic feedback on mobile
        try
        {
            await JSRuntime.InvokeVoidAsync("navigator.vibrate", 50);
        }
        catch
        {
            // Vibration not supported, ignore
        }
    }
}


