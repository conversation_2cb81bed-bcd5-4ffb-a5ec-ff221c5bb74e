﻿<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Apis\**" />
    <Content Remove="Apis\**" />
    <EmbeddedResource Remove="Apis\**" />
    <None Remove="Apis\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="9.0.5" PrivateAssets="all" />
    <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.12.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PetVet.Shared.Components\PetVet.Shared.Components.csproj" />
    <ProjectReference Include="..\PetVet.Shared\PetVet.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="Pages\Areas\Admin\PetList.razor">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
    </Content>
  </ItemGroup>

</Project>
