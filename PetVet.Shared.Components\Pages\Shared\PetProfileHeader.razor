﻿@inject IJSRuntime _JS
@inject IPetApi _PetApi
@inject IAppState _AppState
@inject NavigationManager _NavigationManager
@inject PetVetAuthStateProvider _PetVetAuthStateProvider

<BsModal OnModalConfirmation="ConfirmDelete_Click"
         ButtonText="Delete"
         Title="Are you sure you want to delete this pet profile?">
</BsModal>

<!-- Modern Header with Theme Styling -->
<div class="flex items-center justify-between mb-6">
    <!-- Back Button -->
    <button @onclick="NavigateBack"
            class="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-xl flex items-center justify-center transition-all">
        <i class="fas fa-arrow-left text-gray-600"></i>
    </button>

    <!-- Title -->
    <h1 class="text-xl font-bold text-gray-800">Pet Profile</h1>

    <!-- Action Buttons -->
    <div class="flex items-center gap-2">
        @if (!IsVetUser)
        {
            <!-- Notification Button -->
            <a href="notifications"
               class="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-xl flex items-center justify-center transition-all">
                <i class="fas fa-bell text-gray-600"></i>
            </a>

            <!-- Menu Button -->
            <div class="relative">
                <button @onclick="ToggleDropdown"
                        class="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-xl flex items-center justify-center transition-all">
                    <i class="fas fa-ellipsis-vertical text-gray-600"></i>
                </button>

                @if (showDropdown)
                {
                    <!-- Modern Dropdown Menu -->
                    <div class="absolute right-0 top-12 w-48 bg-white rounded-2xl shadow-lg border border-gray-100 py-2 z-50">
                        <a href="/petOwner/myPets/profile/update/@PetId"
                           class="flex items-center gap-3 px-4 py-3 hover:bg-gray-50 transition-all">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-edit text-blue-600 text-sm"></i>
                            </div>
                            <span class="text-gray-700 font-medium">Edit Profile</span>
                        </a>

                        <button @onclick="HandleShareProfile"
                                class="w-full flex items-center gap-3 px-4 py-3 hover:bg-gray-50 transition-all">
                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-share text-green-600 text-sm"></i>
                            </div>
                            <span class="text-gray-700 font-medium">Share Profile</span>
                        </button>

                        <button @onclick="HandleDelete"
                                class="w-full flex items-center gap-3 px-4 py-3 hover:bg-red-50 transition-all">
                            <div class="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-trash text-red-600 text-sm"></i>
                            </div>
                            <span class="text-red-700 font-medium">Delete Profile</span>
                        </button>
                    </div>
                }
            </div>
        }
    </div>
</div>

@code {
    [Parameter] public int PetId { get; set; }
    [Parameter] public EventCallback OnPetDeleted { get; set; }
    [Parameter] public EventCallback OnShareProfile { get; set; }
    private bool showDropdown = false;
    private bool IsVetUser => _PetVetAuthStateProvider?.User?.Role?.ToLower().Contains("vet") ?? false;

    private async Task NavigateBack()
    {
        // Get the current URL
        var currentUrl = _NavigationManager.Uri;

        // Check if we came from the chat page
        if (currentUrl.Contains("/vet/shared/petprofile/") || currentUrl.Contains("/shared/petprofile/"))
        {
            // Extract the chatUserId from the current URL if it exists
            var uri = new Uri(currentUrl);
            var queryString = uri.Query;
            if (!string.IsNullOrEmpty(queryString))
            {
                // Remove the '?' from the start of the query string
                queryString = queryString.TrimStart('?');

                // Split the query string into key-value pairs
                var queryParams = queryString.Split('&');
                foreach (var param in queryParams)
                {
                    var parts = param.Split('=');
                    if (parts.Length == 2 && parts[0] == "chatUserId")
                    {
                        if (int.TryParse(parts[1], out var chatUserId))
                        {
                            // Determine the correct chat route based on user role
                            string chatRoute = _PetVetAuthStateProvider.User.Role.ToLower().Contains("vet")
                                ? "/vet/chat"
                                : "/petOwner/chat";

                            // Navigate back to chat with the chatUserId parameter
                            _NavigationManager.NavigateTo($"{chatRoute}?userId={chatUserId}");
                            return;
                        }
                    }
                }
            }
        }

        // If we didn't come from chat or couldn't get the chatUserId, use default back navigation
        await _JS.InvokeVoidAsync("eval", "window.history.back()");
    }

    private void ToggleDropdown()
    {
        showDropdown = !showDropdown;
    }

    private async Task HandleShareProfile()
    {
        showDropdown = false;
        await OnShareProfile.InvokeAsync();
    }

    private async Task HandleDelete()
    {
        showDropdown = false;
        await _JS.InvokeVoidAsync("ShowConfirmationModal");
    }

    [JSInvokable]
    public async Task ConfirmDelete_Click(bool isConfirmed)
    {
        await _JS.InvokeVoidAsync("HideConfirmationModal");

        if (isConfirmed && PetId != 0)
        {
            _AppState.ShowLoader("Deleting Pet Profile");

            var result = await _PetApi.DeletePetAsync(PetId);

            if (result.IsSuccess)
            {
                await _JS.ToastrSuccess("Pet profile deleted successfully.");

                _NavigationManager.NavigateTo("/petOwner/myPets");

                await OnPetDeleted.InvokeAsync();
            }
            else
            {
                await _JS.ToastrError("Failed to delete pet profile.");
            }

            _AppState.HideLoader();
        }
    }
}