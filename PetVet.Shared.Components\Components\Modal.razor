﻿<!-- Modern Modal Overlay -->
@if (IsVisible)
{
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
        <!-- Modern Modal Container -->
        <div class="bg-white rounded-3xl shadow-2xl border border-gray-100 w-full max-w-md @GetSizeClass() max-h-[90vh] overflow-hidden">

            <!-- Modern Header -->
            <div class="bg-gradient-to-r from-teal-500 to-blue-600 px-6 py-4 text-white">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-3">
                        <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center">
                            <i class="fas fa-check text-white text-sm"></i>
                        </div>
                        <h3 class="text-lg font-bold">@Title</h3>
                    </div>
                    <button @onclick="CloseModal"
                            class="w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-all">
                        <i class="fas fa-times text-white text-sm"></i>
                    </button>
                </div>
            </div>

            <!-- Modern Body -->
            <div class="p-6 overflow-y-auto max-h-[60vh]">
                @ChildContent
            </div>

            <!-- Modern Footer -->
            <div class="px-6 py-4 bg-gray-50 border-t border-gray-100 flex gap-3 justify-end">
                <button @onclick="CloseModal"
                        class="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all flex items-center gap-2">
                    <i class="fas fa-times text-sm"></i>
                    <span>Close</span>
                </button>
                <button @onclick="OnActionButtonClick"
                        class="px-4 py-2 bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white rounded-xl font-medium transition-all flex items-center gap-2 shadow-lg">
                    <i class="fas fa-check text-sm"></i>
                    <span>@ActionButtonText</span>
                </button>
            </div>
        </div>
    </div>
}

@code {
    [Parameter, EditorRequired]
    public string Title { get; set; }

    [Parameter, EditorRequired]
    public RenderFragment ChildContent { get; set; }

    [Parameter]
    public string ActionButtonText { get; set; } = "Ok";

    [Parameter]
    public EventCallback OnActionButtonClick { get; set; }

    [Parameter]
    public EventCallback OnCancelButtonClick { get; set; }

    [Parameter]
    public ModalSize Size { get; set; } = ModalSize.Default;

    private string GetSizeClass() => Size switch
    {
        ModalSize.Small => "max-w-sm",
        ModalSize.Default => "max-w-md",
        ModalSize.Large => "max-w-2xl",
        ModalSize.ExtraLarge => "max-w-4xl",
        _ => "max-w-md"
    };

    [Parameter]
    public bool IsVisible { get; set; } = false;

    private void CloseModal()
    {
        IsVisible = false;
        OnCancelButtonClick.InvokeAsync();
        StateHasChanged();
    }
}
