﻿using System.Collections;
using System.Web;

namespace PetVet.MauiApp.Helpers
{
    public static class ObjectExtensions
    {
        public static string ToQueryString(this object obj)
        {
            var properties = obj.GetType().GetProperties();
            var collection = HttpUtility.ParseQueryString(string.Empty);

            foreach (var property in properties)
            {
                if (property.PropertyType != typeof(string) && property.PropertyType.GetInterfaces().Contains(typeof(IEnumerable)))
                {
                    var list = (IEnumerable)property.GetValue(obj, null);
                    if (list != null)
                    {
                        foreach (var arrayElement in list)
                        {
                            if (!string.IsNullOrEmpty(arrayElement?.ToString()))
                            {
                                collection.Add(property.Name, arrayElement.ToString());
                            }
                        }
                    }
                }
                else
                {
                    var value = property.GetValue(obj, null)?.ToString();
                    if (!string.IsNullOrEmpty(value))
                    {
                        collection[property.Name] = value;
                    }
                }
            }

            return string.Concat("?", collection.ToString());
        }
    }
}
