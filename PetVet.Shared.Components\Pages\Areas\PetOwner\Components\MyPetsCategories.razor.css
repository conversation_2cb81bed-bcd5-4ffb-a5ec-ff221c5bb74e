﻿.MyPetsCategories-CONTAINER {
    margin: 3.45rem 0;
    position: relative;
    overflow: hidden;
}

.MyPetsCategories-Heading {
    font-size: 1.125rem;
    font-family: 'Acme', sans-serif;
    font-weight: bold;
    cursor: default;
}

.MyPetsCategories-VIEWALL-BTN {
    font-size: 0.875rem;
    color: #FFC107;
    
    text-decoration: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: color 0.3s ease;
}

    .MyPetsCategories-VIEWALL-BTN:hover {
        color: #e0a406;
    }

.MyPetsCategories-VIEWALL-BTN-ICON {
    background-color: #f8c73d;
    border-radius: 0.3125rem;
    padding: 0.125rem;
    margin-left: 0.3125rem;
    font-size: 0.625rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 0.875rem;
    height: 0.875rem;
    color: white;
    transition: background-color 0.3s ease;
}

    .MyPetsCategories-VIEWALL-BTN-ICON:hover {
        background-color: #e0a406;
    }

.MyPetsCategories-CATEGORY-LIST {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    gap: 0.625rem;
    margin: 0.625rem 0;
    padding: 0.625rem;
    white-space: nowrap;
    overflow-x: auto;
    scrollbar-width: thin;
    -ms-overflow-style: none;
}

    .MyPetsCategories-CATEGORY-LIST::-webkit-scrollbar {
        display: none;
    }

.MyPetsCategories-CATEGORY {
    background-color: white;
    border-radius: 0.625rem;
    padding: 0.625rem;
    
    text-align: center;
    flex: 0 0 auto;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

    .MyPetsCategories-CATEGORY.ACTIVE {
        background-color: #157BAB;
        color: white;
    }

.MyPetsCategories-CATEGORY-ICON {
    font-size: 1.25rem;
}

.MyPetsCategories-CATEGORY:hover {
    background-color: #FFC107;
}

.MyPetsFilters-SLIDING-SCREEN {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    width: 60%;
    height: 100%;
    background-color: #f8f9fa;
    z-index: 1000;
    transition: transform 0.4s ease-in-out;
    display: flex;
    flex-direction: column;
    padding: 1.5rem;
}

.MyPetsFilters-SLIDING-SCREEN {
    transform: translateX(-100%);
}

.MyPetsFilters-CLOSE-BUTTON {
    align-self: flex-end;
    margin-top: auto;
    margin-bottom: 1rem;
    padding: 0.5rem 1rem;
    background-color: #FFC107;
    color: #333;
    
    border: none;
    border-radius: 0.5rem;
    font-weight: bold;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
}

.MyPetsFilters-CLOSE-BUTTON:hover {
    background-color: #e0a406;
    transform: scale(1.05);
}


@media (max-width: 768px) {
    .MyPetsCategories-CATEGORY-LIST {
        justify-content: flex-start;
    }
}