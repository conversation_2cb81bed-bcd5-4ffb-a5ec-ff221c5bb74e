using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PetVet.Api.Data;
using PetVet.Api.Data.Entities;
using System.Text;

namespace PetVet.Api.Controllers
{
    /// <summary>
    /// Controller for handling Twilio webhook callbacks for SMS delivery reports
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class TwilioWebhookController : ControllerBase
    {
        private readonly IDbContextFactory<PetVetContext> _contextFactory;
        private readonly IConfiguration _configuration;
        private readonly ILogger<TwilioWebhookController> _logger;

        public TwilioWebhookController(
            IDbContextFactory<PetVetContext> contextFactory,
            IConfiguration configuration,
            ILogger<TwilioWebhookController> logger)
        {
            _contextFactory = contextFactory;
            _configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// Handle Twilio SMS status callbacks
        /// </summary>
        [HttpPost("sms-status")]
        public async Task<IActionResult> HandleSmsStatusCallback()
        {
            try
            {
                // Read the request body
                using var reader = new StreamReader(Request.Body, Encoding.UTF8);
                var body = await reader.ReadToEndAsync();

                _logger.LogInformation("Received Twilio SMS status callback: {Body}", body);

                // Parse form data
                var formData = ParseFormData(body);

                if (!formData.ContainsKey("MessageSid") || !formData.ContainsKey("MessageStatus"))
                {
                    _logger.LogWarning("Invalid Twilio callback: missing MessageSid or MessageStatus");
                    return BadRequest("Invalid callback data");
                }

                var messageSid = formData["MessageSid"];
                var messageStatus = formData["MessageStatus"];
                var errorCode = formData.GetValueOrDefault("ErrorCode");
                var errorMessage = formData.GetValueOrDefault("ErrorMessage");

                // Update the SMS notification log
                await UpdateSmsNotificationStatus(messageSid, messageStatus, errorCode, errorMessage);

                _logger.LogInformation("Updated SMS status for MessageSid {MessageSid} to {Status}", 
                    messageSid, messageStatus);

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing Twilio SMS status callback");
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Webhook verification endpoint for Twilio
        /// </summary>
        [HttpGet("verify")]
        public IActionResult VerifyWebhook([FromQuery] string token)
        {
            var expectedToken = _configuration["Twilio:WebhookVerifyToken"];
            
            if (string.IsNullOrEmpty(expectedToken))
            {
                _logger.LogWarning("Twilio webhook verify token not configured");
                return BadRequest("Webhook verification not configured");
            }

            if (token == expectedToken)
            {
                _logger.LogInformation("Twilio webhook verification successful");
                return Ok("Webhook verified");
            }

            _logger.LogWarning("Twilio webhook verification failed: invalid token");
            return Unauthorized("Invalid verification token");
        }

        /// <summary>
        /// Health check endpoint for Twilio webhook
        /// </summary>
        [HttpGet("health")]
        public IActionResult HealthCheck()
        {
            return Ok(new { 
                status = "healthy", 
                timestamp = DateTime.UtcNow,
                service = "Twilio Webhook Handler"
            });
        }

        private async Task UpdateSmsNotificationStatus(string messageSid, string status, string? errorCode, string? errorMessage)
        {
            using var context = _contextFactory.CreateDbContext();

            try
            {
                var notification = await context.SmsNotificationLogs
                    .FirstOrDefaultAsync(s => s.TwilioMessageSid == messageSid);

                if (notification == null)
                {
                    _logger.LogWarning("SMS notification not found for MessageSid {MessageSid}", messageSid);
                    return;
                }

                // Update status based on Twilio status
                notification.TwilioStatus = status;
                notification.Status = MapTwilioStatusToInternalStatus(status);

                // Update delivery timestamp for delivered messages
                if (status.Equals("delivered", StringComparison.OrdinalIgnoreCase))
                {
                    notification.DeliveredAt = DateTime.UtcNow;
                }

                // Update error information if present
                if (!string.IsNullOrEmpty(errorCode) || !string.IsNullOrEmpty(errorMessage))
                {
                    notification.ErrorMessage = $"Error {errorCode}: {errorMessage}".Trim();
                    notification.Status = "Failed";
                }

                await context.SaveChangesAsync();

                _logger.LogDebug("Updated SMS notification {Id} status to {Status}", 
                    notification.Id, notification.Status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating SMS notification status for MessageSid {MessageSid}", messageSid);
            }
        }

        private string MapTwilioStatusToInternalStatus(string twilioStatus)
        {
            return twilioStatus.ToLower() switch
            {
                "queued" => "Pending",
                "sending" => "Pending",
                "sent" => "Sent",
                "delivered" => "Delivered",
                "undelivered" => "Failed",
                "failed" => "Failed",
                "received" => "Delivered", // For incoming messages
                _ => "Unknown"
            };
        }

        private Dictionary<string, string> ParseFormData(string formData)
        {
            var result = new Dictionary<string, string>();

            if (string.IsNullOrEmpty(formData))
                return result;

            var pairs = formData.Split('&');
            foreach (var pair in pairs)
            {
                var keyValue = pair.Split('=', 2);
                if (keyValue.Length == 2)
                {
                    var key = Uri.UnescapeDataString(keyValue[0]);
                    var value = Uri.UnescapeDataString(keyValue[1]);
                    result[key] = value;
                }
            }

            return result;
        }
    }
}
