/* Modern Mobile-First Layout Styles */

/* Custom animations */
@keyframes slideUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* Header styles */
header {
    animation: fadeIn 0.5s ease-out;
}

/* Profile dropdown animation */
.profile-dropdown {
    animation: slideUp 0.3s ease-out;
}

/* Quick actions modal animation */
.quick-actions-modal {
    animation: slideUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Floating action button */
.fab {
    animation: bounce 2s infinite;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fab:hover {
    animation: none;
    transform: scale(1.1);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Content area */
main {
    scroll-behavior: smooth;
}

/* Custom scrollbar for webkit browsers */
main::-webkit-scrollbar {
    width: 4px;
}

main::-webkit-scrollbar-track {
    background: transparent;
}

main::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.3);
    border-radius: 2px;
}

main::-webkit-scrollbar-thumb:hover {
    background: rgba(156, 163, 175, 0.5);
}

/* Backdrop blur support */
@supports (backdrop-filter: blur(10px)) {
    .backdrop-blur-md {
        backdrop-filter: blur(12px);
    }
}

/* Mobile optimizations */
@media (max-width: 640px) {
    /* Ensure proper spacing on small screens */
    .max-w-md {
        max-width: 100%;
        padding: 0 1rem;
    }

    /* Adjust header for mobile */
    header {
        padding: 0.75rem 1rem;
    }

    /* Optimize floating action button position */
    .fab {
        bottom: 6rem;
        right: 1rem;
        width: 3.5rem;
        height: 3.5rem;
    }
}

/* Tablet and larger screens */
@media (min-width: 768px) {
    .max-w-md {
        max-width: 28rem;
    }

    /* Center content better on larger screens */
    main {
        padding: 0 2rem;
    }
}

/* Desktop optimizations */
@media (min-width: 1024px) {
    /* Limit maximum width for better readability */
    .max-w-md {
        max-width: 32rem;
    }

    /* Add subtle hover effects for desktop */
    .hover\:shadow-md:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .hover\:shadow-xl:hover {
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
    /* Dark mode styles can be added here */
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
