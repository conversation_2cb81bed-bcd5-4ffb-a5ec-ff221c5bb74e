﻿using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.Extensions.Logging;
using PetVet.Mobile.Services.Storage;
using PetVet.Shared.AppState;
using PetVet.Shared.Components.Apis;
using PetVet.Shared.Components.Services.Auth;
using PetVet.Shared.Models.AzureCommunication;
using PetVet.Shared.Services;
using PetVet.Mobile.Services;
using PetVet.Shared.IStorage;
using Refit;
using PetVet.Mobile.Platform;
using PetVet.Shared.IPlatform;
using PetVet.ServiceContracts.Features.Conversation;
using Platform.Client.Common.Features.Conversation;
using Platform.Client.Common.Features.Conversation.ChatThreads.Form;
using PetVet.Client.Common.Data;
using Microsoft.EntityFrameworkCore;
using PetVet.MauiApp.Services;

using PetVet.Shared.Components.Framework;
using PetVet.Platform.Razor;
using Microsoft.Maui.LifecycleEvents;

using Plugin.Firebase.CloudMessaging;
using DeepMessage.ServiceContracts.Features.Configurations;
using Platform.Client.Common.Features.Configurations;
using Plugin.Maui.Audio;


#if ANDROID
using Plugin.Firebase.Core.Platforms.Android;
using Xamarin.Android.Net;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
#elif IOS
using Security;
#endif

namespace PetVet.Mobile
{
    public static class MauiProgram
    {
        //public static string ApiBaseUrl = "https://***************:7071";
        public static string ApiBaseUrl = "https://petvetapi.azurewebsites.net";

        public static Microsoft.Maui.Hosting.MauiApp CreateMauiApp()
        {

            var builder = Microsoft.Maui.Hosting.MauiApp.CreateBuilder();
            builder
                .UseMauiApp<App>()
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "OpenSansRegular");
                });

            builder.Services.AddMauiBlazorWebView();

#if DEBUG
            builder.Services.AddBlazorWebViewDeveloperTools();
            builder.Logging.AddDebug();
#endif
#if ANDROID
            builder.Services.AddSingleton<IAudioCaptureService, AudioCaptureService>();
#endif

            var dbPath = Path.Combine(FileSystem.AppDataDirectory, "petvetv7.db");
            builder.Services.AddDbContext<AppDbContext>(options =>
                options.UseSqlite($"Filename={dbPath}"));

            builder.Services.AddCascadingAuthenticationState();
            builder.Services.AddSingleton<PetVetAuthStateProvider>();
            builder.Services.AddSingleton<AuthenticationStateProvider>(sp => sp.GetRequiredService<PetVetAuthStateProvider>());
            builder.Services.AddAuthorizationCore();

            builder.Services.AddSingleton<IStorageService, StorageService>()
                .AddSingleton<IAppState, AppState>()
                .AddSingleton<IPlatform, MobilePlatform>();

            ConfigureRefit(builder.Services);
            builder.Services.AddSingleton<ChatSyncUpService>();
            builder.Services.AddSingleton<SignalRClientService>();
            builder.Services.AddScoped<IStartChatFormDataService, StartChatClientSideFormDataService>();
            builder.Services.AddScoped<IChatThreadsListingDataService, ChatThreadsOfflineListingDataService>();
            builder.Services.AddScoped<ChatThreadsClientSideListingDataService>();
            builder.Services.AddScoped<IChatMessagesListingDataService, ChatMessagesClientSideListingDataService>();
            builder.Services.AddScoped<IChatMessageFormDataService, ChatMessageClientSideFormDataService>();
            builder.Services.AddScoped<IChatThreadSyncFormDataService, ChatThreadSyncClientSideFormDataService>();
            builder.Services.AddScoped<IChatMessagesSyncFormDataService, ChatMessagesSyncClientSideFormDataService>();
             builder.Services.AddScoped<IDeviceTokenFormDataService, DeviceTokenClientSideFormDataService>();
            builder.Services.AddHttpClient<BaseHttpClient, HttpTokenClient>("ServerAPI", client =>
            {
                client.BaseAddress = new Uri(ApiBaseUrl);

            }).ConfigurePrimaryHttpMessageHandler(GetInsecureHandler);
            builder.Services.AddScoped<ILocalStorageService, LocalStorageService>();
             RegisterFirebaseServices(builder);
            builder.Services.AddSingleton(_ => CrossFirebaseCloudMessaging.Current);
            // Configure Azure Communication Services from appsettings.json
            builder.Services.Configure<AzureCommunicationConfig>(builder.Configuration.GetSection("AzureCommunication"));

            // Register HttpClient for Azure Communication Service
            builder.Services.AddHttpClient<AzureCommunicationService>();
 
            // Register Plugin.Maui.Audio
            builder.Services.AddSingleton(Plugin.Maui.Audio.AudioManager.Current);

            // Register platform-specific media capture service
            builder.Services.AddSingleton<IMediaCaptureService, MediaCaptureService>();

            // Register Azure Communication Service
            builder.Services.AddScoped<IAzureCommunicationService, AzureCommunicationService>();

            // Register Chat Call Service (uses existing SignalR ChatHub)
            builder.Services.AddScoped<IChatCallService, ChatCallService>();

            // Register Audio Streaming Service for real-time audio
            builder.Services.AddScoped<IAudioStreamingService, AudioStreamingService>();

            // Register Audio Ringtone Service for call sounds
            builder.Services.AddScoped<IAudioRingtoneService, AudioRingtoneService>();

            // Register Call Debug Service for troubleshooting
            builder.Services.AddScoped<ICallDebugService, CallDebugService>();

            // Register Audio Flow Debug Service for audio troubleshooting
            builder.Services.AddScoped<IAudioFlowDebugService, AudioFlowDebugService>();

            var app = builder.Build();
            var scope = app.Services.CreateScope();
            var services = scope.ServiceProvider;
            var dbContext = services.GetRequiredService<AppDbContext>();
            dbContext.Database.Migrate();
            services.GetRequiredService<ChatSyncUpService>().Start();
            services.GetRequiredService<SignalRClientService>().Start($"{ApiBaseUrl}/hubs/chathub");

            return app;
        }

        private static MauiAppBuilder RegisterFirebaseServices(this MauiAppBuilder builder)
        {
            builder.ConfigureLifecycleEvents(events =>
            {
#if IOS
            events.AddiOS(iOS => iOS.WillFinishLaunching((_,__) => {
                CrossFirebase.Initialize();
                return false;
            }));
#elif ANDROID

                events.AddAndroid(android => android.OnCreate((activity, _) =>
                    CrossFirebase.Initialize(activity))); 

#endif
            });
            CrossFirebaseCloudMessaging.Current.CheckIfValidAsync();
            //builder.Services.AddSingleton(_ => CrossFirebaseAuth.Current);
            CrossFirebaseCloudMessaging.Current.NotificationReceived += Current_NotificationReceived;
            return builder;
        }

        private static void Current_NotificationReceived(object? sender, Plugin.Firebase.CloudMessaging.EventArgs.FCMNotificationReceivedEventArgs e)
        {

        }
        public static HttpClientHandler GetInsecureHandler()
        {
            HttpClientHandler handler = new HttpClientHandler();
            handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) =>
            {
                if (cert.Issuer.Equals("CN=localhost"))
                    return true;
                return errors == System.Net.Security.SslPolicyErrors.None;
            };
            return handler;
        }
        static void ConfigureRefit(IServiceCollection services)
        { 
            services.AddRefitClient<IAuthApi>(GetRefitSettings)
                .ConfigureHttpClient(SetHttpClient);

            services.AddRefitClient<ICategoryApi>(GetRefitSettings)
                .ConfigureHttpClient(SetHttpClient);

            services.AddRefitClient<IEducationApi>(GetRefitSettings)
                .ConfigureHttpClient(SetHttpClient);

            services.AddRefitClient<IPetApi>(GetRefitSettings)
                .ConfigureHttpClient(SetHttpClient);

            services.AddRefitClient<IUserApi>(GetRefitSettings)
                .ConfigureHttpClient(SetHttpClient);

            services.AddRefitClient<IMessageApi>(GetRefitSettings)
                .ConfigureHttpClient(SetHttpClient);

            services.AddRefitClient<IAppointmentApi>(GetRefitSettings)
                .ConfigureHttpClient(SetHttpClient);

            services.AddRefitClient<IBreedApi>(GetRefitSettings)
                .ConfigureHttpClient(SetHttpClient);

            services.AddRefitClient<IChatApi>(GetRefitSettings)
                .ConfigureHttpClient(SetHttpClient);

            services.AddRefitClient<IRecentActivityApi>(GetRefitSettings)
                .ConfigureHttpClient(SetHttpClient);

         



            void SetHttpClient(HttpClient httpClient) => httpClient.BaseAddress = new Uri(ApiBaseUrl);

            static RefitSettings GetRefitSettings(IServiceProvider sp)
            {
                var authStateProvider = sp.GetRequiredService<PetVetAuthStateProvider>();
                return new RefitSettings
                {
                    AuthorizationHeaderValueGetter = async (_, __) => await Task.FromResult(authStateProvider.User?.Token ?? ""),
                    HttpMessageHandlerFactory = () =>
                    {
#if ANDROID
                        var androidMessageHandler = new AndroidMessageHandler
                        {
                            ServerCertificateCustomValidationCallback =
                            (HttpRequestMessage requestMessage, X509Certificate2? certificate2, X509Chain? chain, SslPolicyErrors sslPolicyErrors) =>
                            certificate2?.Issuer == "CN=localhost" || sslPolicyErrors == SslPolicyErrors.None
                        };
                        return androidMessageHandler;
#endif
                    }
                };
            }
        }
    }
}
