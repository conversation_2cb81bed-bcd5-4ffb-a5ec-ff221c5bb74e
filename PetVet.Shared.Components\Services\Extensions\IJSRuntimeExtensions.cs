﻿using Microsoft.JSInterop;

namespace PetVet.Shared.Components.Services.Extensions
{
    public static class IJSRuntimeExtensions
    {
        public static async Task ToastrSuccess(this IJSRuntime js, string message)
        {
            //await js.InvokeVoidAsync("showToastr", "info", message);
        }

        public static async Task ToastrError(this IJSRuntime js, string message)
        {
            //await js.InvokeVoidAsync("showToastr", "error", message);
        }
    }
}
