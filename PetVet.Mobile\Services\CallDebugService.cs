using Microsoft.Extensions.Logging;
using PetVet.MauiApp.Services;
using PetVet.Mobile.Models;
using PetVet.Shared.Components.Framework;
using System.Text.Json;

namespace PetVet.Mobile.Services;

/// <summary>
/// Debug service to help diagnose call connectivity issues
/// </summary>
public interface ICallDebugService
{
    Task<CallDebugReport> DiagnoseCallConnectivityAsync(string conversationId, string friendUserId);
    Task LogCallFlow(string step, object data);
}

public class CallDebugService : ICallDebugService
{
    private readonly IChatCallService _chatCallService;
    private readonly SignalRClientService _signalRService;
    private readonly ILocalStorageService _localStorageService;
    private readonly ILogger<CallDebugService> _logger;

    public CallDebugService(
        IChatCallService chatCallService,
        SignalRClientService signalRService,
        ILocalStorageService localStorageService,
        ILogger<CallDebugService> logger)
    {
        _chatCallService = chatCallService;
        _signalRService = signalRService;
        _localStorageService = localStorageService;
        _logger = logger;
    }

    public async Task<CallDebugReport> DiagnoseCallConnectivityAsync(string conversationId, string friendUserId)
    {
        var report = new CallDebugReport
        {
            ConversationId = conversationId,
            FriendUserId = friendUserId,
            Timestamp = DateTime.UtcNow
        };

        try
        {
            // Check 1: SignalR Connection
            report.IsSignalRConnected = _signalRService._hubConnection.ConnectionId != null;
            await LogCallFlow("SignalR Connection Check", new { IsConnected = report.IsSignalRConnected });

            // Check 2: User Authentication
            var userId = await _localStorageService.GetValue(System.Security.Claims.ClaimTypes.NameIdentifier);
            var userName = await _localStorageService.GetValue(System.Security.Claims.ClaimTypes.Name);
            
            report.CurrentUserId = userId ?? "NOT_FOUND";
            report.CurrentUserName = userName ?? "NOT_FOUND";
            report.IsUserAuthenticated = !string.IsNullOrEmpty(userId) && !string.IsNullOrEmpty(userName);
            
            await LogCallFlow("User Authentication Check", new 
            { 
                UserId = report.CurrentUserId, 
                UserName = report.CurrentUserName,
                IsAuthenticated = report.IsUserAuthenticated 
            });

            // Check 3: Call Service Initialization
            try
            {
                var initialized = await _chatCallService.InitializeAsync();
                report.IsCallServiceInitialized = initialized;
                await LogCallFlow("Call Service Initialization", new { IsInitialized = initialized });
            }
            catch (Exception ex)
            {
                report.IsCallServiceInitialized = false;
                report.Errors.Add($"Call service initialization failed: {ex.Message}");
                await LogCallFlow("Call Service Initialization Error", new { Error = ex.Message });
            }

            // Check 4: Active Calls
            var activeCalls = _chatCallService.GetActiveCalls();
            report.ActiveCallsCount = activeCalls.Count;
            report.HasActiveCallInConversation = _chatCallService.IsInCall(conversationId);
            
            await LogCallFlow("Active Calls Check", new 
            { 
                ActiveCallsCount = report.ActiveCallsCount,
                HasActiveCallInConversation = report.HasActiveCallInConversation 
            });

            // Check 5: Test Call Invitation (dry run)
            if (report.IsSignalRConnected && report.IsUserAuthenticated && report.IsCallServiceInitialized)
            {
                try
                {
                    // Don't actually send, just validate parameters
                    if (string.IsNullOrEmpty(conversationId))
                    {
                        report.Errors.Add("ConversationId is null or empty");
                    }
                    if (string.IsNullOrEmpty(friendUserId))
                    {
                        report.Errors.Add("FriendUserId is null or empty");
                    }
                    if (!int.TryParse(friendUserId, out _))
                    {
                        report.Errors.Add($"FriendUserId '{friendUserId}' is not a valid integer");
                    }
                    
                    report.CanSendCallInvitation = report.Errors.Count == 0;
                    
                    await LogCallFlow("Call Invitation Validation", new 
                    { 
                        CanSend = report.CanSendCallInvitation,
                        Errors = report.Errors 
                    });
                }
                catch (Exception ex)
                {
                    report.CanSendCallInvitation = false;
                    report.Errors.Add($"Call invitation validation failed: {ex.Message}");
                }
            }
            else
            {
                report.CanSendCallInvitation = false;
                if (!report.IsSignalRConnected) report.Errors.Add("SignalR not connected");
                if (!report.IsUserAuthenticated) report.Errors.Add("User not authenticated");
                if (!report.IsCallServiceInitialized) report.Errors.Add("Call service not initialized");
            }

            // Overall status
            report.OverallStatus = report.CanSendCallInvitation ? "READY" : "NOT_READY";
            
            await LogCallFlow("Diagnosis Complete", new 
            { 
                Status = report.OverallStatus,
                ErrorCount = report.Errors.Count 
            });

        }
        catch (Exception ex)
        {
            report.OverallStatus = "ERROR";
            report.Errors.Add($"Diagnosis failed: {ex.Message}");
            _logger.LogError(ex, "Error during call connectivity diagnosis");
        }

        return report;
    }

    public async Task LogCallFlow(string step, object data)
    {
        try
        {
            var logEntry = new
            {
                Timestamp = DateTime.UtcNow,
                Step = step,
                Data = data
            };

            var json = JsonSerializer.Serialize(logEntry, new JsonSerializerOptions { WriteIndented = true });
            _logger.LogInformation("CALL_FLOW: {Step} - {Data}", step, json);
            
            // Also write to debug output for easier viewing during development
            System.Diagnostics.Debug.WriteLine($"[CALL_FLOW] {step}: {json}");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging call flow step: {Step}", step);
        }
    }
}

public class CallDebugReport
{
    public string ConversationId { get; set; } = "";
    public string FriendUserId { get; set; } = "";
    public DateTime Timestamp { get; set; }
    
    // Connection status
    public bool IsSignalRConnected { get; set; }
    
    // Authentication status
    public bool IsUserAuthenticated { get; set; }
    public string CurrentUserId { get; set; } = "";
    public string CurrentUserName { get; set; } = "";
    
    // Call service status
    public bool IsCallServiceInitialized { get; set; }
    public int ActiveCallsCount { get; set; }
    public bool HasActiveCallInConversation { get; set; }
    
    // Call capability
    public bool CanSendCallInvitation { get; set; }
    
    // Overall status
    public string OverallStatus { get; set; } = "UNKNOWN";
    public List<string> Errors { get; set; } = new();
    
    public string GetSummary()
    {
        var summary = $"Call Debug Report for Conversation {ConversationId}\n";
        summary += $"Status: {OverallStatus}\n";
        summary += $"SignalR Connected: {IsSignalRConnected}\n";
        summary += $"User Authenticated: {IsUserAuthenticated} (ID: {CurrentUserId})\n";
        summary += $"Call Service Ready: {IsCallServiceInitialized}\n";
        summary += $"Can Send Calls: {CanSendCallInvitation}\n";
        
        if (Errors.Any())
        {
            summary += $"\nErrors ({Errors.Count}):\n";
            foreach (var error in Errors)
            {
                summary += $"- {error}\n";
            }
        }
        
        return summary;
    }
}
