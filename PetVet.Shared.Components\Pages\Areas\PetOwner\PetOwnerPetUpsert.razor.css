﻿.PetOwnerPetUpsert-Loading {
    height: 85vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.PetOwnerPetUpsert-CONTAINER {
    background: #f2f6f9;
    padding: 2rem;
    min-height: 100vh;
    width: 100%;
    max-width: 72rem;
    margin: 0 auto;
    font-family: 'Inter', sans-serif;
    overflow-x: hidden;
}

.header-section {
    background: #2968ed;
    border-radius: 1.25rem;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    text-align: center;
}

.header-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    font-family: 'Acme', sans-serif;
}

.header-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    
}

.content-section {
    background: white;
    border-radius: 1.25rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.profile-type-section {
    padding: 2rem;
    background: white;
    border-radius: 1.25rem;
}

.Profile-Type-Label {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 1.5rem;
    font-family: 'Acme', sans-serif;
}

.profile-type-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.profile-type-option {
    background: white;
    border-radius: 1.25rem;
    padding: 1rem;
    margin: 1rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 2px solid #e5e7eb;
}

    .profile-type-option.selected {
        border-color: #2968ed;
        background-color: rgba(41, 104, 237, 0.1);
        transform: translateY(-4px);
        box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.15);
    }

    .profile-type-option:hover:not(.selected) {
        transform: translateY(-2px);
        box-shadow: 0 6px 8px -2px rgba(0, 0, 0, 0.12);
    }

.profile-type-icon {
    width: 2.5rem;
    height: 2.5rem;
    margin-bottom: 1rem;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* Stats Section */
.stats-section {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    background-color: white;
    border-radius: 1.25rem;
    padding: 1.5rem;
    margin: 2rem 0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.stats-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #3B82F6;
    font-size: 0.875rem;
    font-weight: 600;
    text-align: center;
    transition: transform 0.3s ease;
    font-family: 'Acme', sans-serif;
}

    .stats-item:hover {
        transform: scale(1.05);
    }

.stats-icon {
    width: 2.5rem;
    height: 2.5rem;
    margin-bottom: 0.75rem;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1F2937;
    font-family: 'Acme', sans-serif;
}

.stat-label {
    font-size: 1rem;
    color: #6B7280;
    
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 0.75rem;
    font-family: 'Acme', sans-serif;
}

.card-description {
    color: #6B7280;
    font-size: 0.875rem;
    line-height: 1.5;
    
}

.form-section {
    padding: 2rem;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

    .label {
        font-weight: 600;
        color: #374151;
        
    }

.form-control,
.form-select,
.form-control {
    padding: 0.875rem;
    border: 1px solid #E5E7EB;
    border-radius: 1.25rem;
    
    transition: all 0.3s ease;
    font-size: 1rem;
}

    .form-control:focus,
    .form-select:focus,
    .form-control:focus {
        outline: none;
        border-color: #3B82F6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

.form-text {
    color: #6B7280;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    
}

.image-section {
    margin-bottom: 2rem;
}

.image-preview-container {
    text-align: center;
    margin-bottom: 1.5rem;
}

.img-thumbnail {
    width: 200px;
    height: 200px;
    object-fit: cover;
    border-radius: 1.25rem;
    margin-bottom: 1rem;
    border: 3px solid rgba(41, 104, 237, 0.5);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

    .img-thumbnail:hover {
        transform: scale(1.05);
    }

.upload-container {
    text-align: center;
}

.action-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    border-radius: 0.75rem;
    
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
    min-width: 150px;
}

.PetOwnerPetUpsert-BUTTON-Div {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.PetOwnerPetUpsert-Create-BTN {
    background: #2968ed;
    color: white;
    border: none;
    padding: 0.875rem 1.5rem;
    border-radius: 1.25rem;
    
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
}

    .PetOwnerPetUpsert-Create-BTN:hover {
        background: #1d4ed8;
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(41, 104, 237, 0.5);
    }

.PetOwnerPetUpsert-Back-BTN {
    background: #f3f4f6;
    color: #1F2937;
    border: none;
    padding: 0.875rem 1.5rem;
    border-radius: 1.25rem;
    
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
    text-decoration: none;
}

    .PetOwnerPetUpsert-Back-BTN:hover {
        background: #e5e7eb;
        transform: translateY(-2px);
        text-decoration: none;
    }

.PetOwnerPetUpsert-Create-BTN-ELSE {
    opacity: 0.7;
    cursor: not-allowed;
}

.error-alert {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: #FEE2E2;
    color: #991B1B;
    border-radius: 1.25rem;
    margin-bottom: 1.5rem;
    
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .PetOwnerPetUpsert-CONTAINER {
        padding: 1rem;
    }

    .header-section {
        padding: 1.5rem;
    }

    .header-title {
        font-size: 1.5rem;
    }

    .profile-type-container {
        grid-template-columns: 2fr;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .PetOwnerPetUpsert-BUTTON-Div {
        flex-direction: column;
    }

    .img-thumbnail {
        width: 150px;
        height: 150px;
    }
}

@media (max-width: 992px) {
    .PetOwnerPetUpsert-Loading {
        height: 100vh;
    }

    .PetOwnerPetUpsert-CARD-BODY {
        padding: 2rem;
    }

    .PetOwnerPetUpsert-HEADING {
        font-size: 1.75rem;
    }

    .PetOwnerPetUpsert-BUTTON-Div {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .PetOwnerPetUpsert-Create-BTN,
    .PetOwnerPetUpsert-Back-BTN {
        width: 100%;
    }
}

@media (max-width: 768px) {
    .PetOwnerPetUpsert-Loading {
        height: 85vh;
    }

    .PetOwnerPetUpsert-CARD-BODY {
        padding: 1rem;
    }

    .PetOwnerPetUpsert-HEADING {
        font-size: 1.25rem;
    }

    .PetOwnerPetUpsert-BUTTON-Div {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .PetOwnerPetUpsert-Create-BTN,
    .PetOwnerPetUpsert-Back-BTN {
        width: 100%;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.PetOwnerPetUpsert-CONTAINER {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}