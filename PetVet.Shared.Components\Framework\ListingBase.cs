﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Rendering;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.JSInterop;
using PetVet.Shared.Interfaces;
using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace PetVet.Framework.Core
{
    public abstract class ListingBase<TListViewModel, TListBusinessObject, TFilterViewModel, TFilterBusinessObject, TService> : FrameworkBaseComponent, INotifyPropertyChanged
    where TListViewModel : class
    where TListBusinessObject : class
    where TService : IListingDataService<TListBusinessObject, TFilterBusinessObject>
    where TFilterViewModel : BaseFilterViewModel, new()
    where TFilterBusinessObject : BaseFilterBusinessObject, new()

    {

        #region Index Properties

        public int TotalPages { get; set; }

        private string _sortColumn;

        public string SortColumn
        {
            get { return _sortColumn; }
            set
            {
                _sortColumn = value;
                NotifyPropertyChanged();
            }
        }

        [Inject]
        protected ILogger<TListViewModel>? Logger { get; set; }

        //public string? ErrorMessage { get; set; }


        private bool _isTableBusy = true;
        public bool IsTableBusy
        {
            get
            {
                return _isTableBusy;
            }
            set
            {
                _isTableBusy = value;
                //if (Layout != null)
                //{
                //    Layout.IsTableBusy = value;
                //}
            }
        }
        private int _totalRows;
        public int TotalRows
        {
            get
            {
                return _totalRows;
            }
            set
            {
                if (_totalRows != value)
                    _totalRows = value;
            }
        }

        #endregion


        public TFilterViewModel FilterViewModel { get; set; } = new TFilterViewModel();
        public TService ServiceList { get; set; }

        private List<TListViewModel> _items;
        protected List<TListViewModel> Items
        {
            get
            {
                return _items;
            }
            set
            {

                _items = value;
                NotifyPropertyChanged();

            }
        }

        protected virtual PaginationStripModel PaginationStrip { get; set; }

        //[Inject]
        PersistentComponentState ApplicationState { get; set; }

        private PersistingComponentStateSubscription persistingSubscription;


        private int _rowsPerPage = 10;

        public virtual int RowsPerPage
        {
            get { return _rowsPerPage; }
            set
            {
                if (_rowsPerPage != value)
                {
                    _rowsPerPage = value;
                    NotifyPropertyChanged();
                }
            }
        }


        public virtual bool UsePagination => true;

        public string _componentId = Guid.NewGuid().ToString().Replace("-", "");

        public class ItemsLoadEventArgs
        {
            public ItemsLoadEventArgs(IEnumerable<TListViewModel> items, bool isFirstTime)
            {
                Items = items;
                IsFirstTime = isFirstTime;
            }

            public IEnumerable<TListViewModel> Items { get; set; }
            public bool IsFirstTime { get; set; }
        }

        [Parameter]
        public EventCallback<ItemsLoadEventArgs> OnItemsRendered { get; set; }

        public ListingBase()
        {
            PaginationStrip = new PaginationStripModel()
            {
                CurrentIndex = 1,
                RowsPerPage = RowsPerPage
            };
            this.PropertyChanged += ListingComponent_PropertyChanged;
        }
         
        protected virtual Task OnCallback(int n)
        {
            return Task.CompletedTask;
        }
        protected override async Task OnInitializedAsync()
        {

            await OnFrameworkParametersSet();

            FilterViewModel.PropertyChanged += FilterViewModel_PropertyChanged;
            PaginationStrip.PropertyChanged += async (p, q) =>
            {
                if (q.PropertyName == nameof(PaginationStrip.RowsPerPage)
                || q.PropertyName == nameof(PaginationStrip.CurrentIndex)
                || q.PropertyName == nameof(SortColumn))
                {
                    if (q.PropertyName == nameof(PaginationStrip.RowsPerPage))
                    {
                        await OnCallback(-1);
                    }
                    if (PaginationStrip.CurrentIndex < 0)
                    {
                        PaginationStrip.CurrentIndex = 1;
                    }
                    else if (TotalPages > 0 && PaginationStrip.CurrentIndex > TotalPages)
                    {
                        PaginationStrip.CurrentIndex = TotalPages;
                    }

                    FilterViewModel.RowsPerPage = PaginationStrip.RowsPerPage;
                    FilterViewModel.CurrentIndex = (TotalRows <= PaginationStrip.RowsPerPage) ? 1 : PaginationStrip.CurrentIndex;
                    await LoadItems();
                    StateHasChanged();

                }
            };

            if (LoadItemsOnInit)
            {
                await LoadItems();

            }

            using var scope = ScopeFactory?.CreateScope();
            await LoadSelectLists(scope);
            IsTableBusy = false;
            await InvokeAsync(() => StateHasChanged());
            await base.OnInitializedAsync();
        }

        protected override void BuildRenderTree(RenderTreeBuilder builder)
        {
            var componentName = GetType().Name;

            // Start comment
            builder.OpenElement(0, "!--");
            builder.AddContent(1, $" Start: {componentName} ");
            builder.CloseElement();

            // Render the component's actual content
            base.BuildRenderTree(builder);

            // End comment
            builder.OpenElement(2, "!--");
            builder.AddContent(3, $" End: {componentName} ");
            builder.CloseElement();
        }
        public virtual void ListingComponent_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {

        }

        protected virtual void FilterViewModel_PropertyChanged(object? sender, PropertyChangedEventArgs e)
        {
            _ = LoadItems();
        }

        protected async Task BlockTableUI()
        {
            IsTableBusy = true;
            await InvokeAsync(() => StateHasChanged());
            await Task.Delay(5);
        }
        protected async Task UnBlockTableUI()
        {
            IsTableBusy = false;
            await InvokeAsync(() => StateHasChanged());
        }

        protected virtual Task LoadSelectLists(IServiceScope scope)
        {
            return Task.CompletedTask;
        }

        protected virtual async Task ItemsLoaded(TService service)
        {
            await Task.CompletedTask;
        }

        protected virtual int GetCountQuery()
        {
            return -1;
        }

        protected async Task LoadItems()
        {
            await LoadItems(true);
        }

        protected virtual List<TListViewModel> ConvertToListViewModel(List<TListBusinessObject> list)
        {
            return list.Clone<List<TListViewModel>>();
        }
        bool firstTimeItemsLoadedFlag = true;
        virtual public bool LoadItemsOnInit { get; } = true;
        protected async Task LoadItems(bool showLoader)
        {

            if (showLoader)
            {
                IsTableBusy = true;
                await InvokeAsync(() => StateHasChanged());
                await Task.Delay(10);
            }

            using (var scope = ScopeFactory!.CreateScope())
            {
                try
                {
                    var crudService = scope.ServiceProvider.GetRequiredService<TService>();
                    var FilterBusinessObject = FilterViewModel.Clone<TFilterBusinessObject>();
                    //var pagedItems = await crudService.GetPaginatedItems(FilterBusinessObject);
                    var pagedItems = await crudService.GetPaginatedItems(FilterBusinessObject);
                    if (pagedItems == null)
                        throw new Exception($"Paginated items null from {typeof(TService)}");

                    Items = ConvertToListViewModel(pagedItems.Items);
                    TotalPages = pagedItems.TotalPages;
                    TotalRows = pagedItems.TotalRows;
                    await ItemsLoaded(crudService);
                }
                catch (UnauthorizedAccessException)
                {
                    NavigationManager?.NavigateTo($"/identity/account/login");
                }
                catch (Exception ex)
                {
                    //NotificationService.ShowError(ex);
                    Logger.LogError($"Failed LoadItems {ex}");
                }

                IsTableBusy = false;
                await InvokeAsync(() => StateHasChanged());
            }

            await OnItemsRendered.InvokeAsync(new ItemsLoadEventArgs(Items, firstTimeItemsLoadedFlag));
            firstTimeItemsLoadedFlag = false;
        }

        protected virtual Task OnFrameworkParametersSet()
        {


            return Task.CompletedTask;
        }

        public override void DialogService_OnClose(dynamic obj)
        {
            _ = LoadItems();
        }


        public event PropertyChangedEventHandler PropertyChanged;

        public void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        public TService GetService()
        {
            var scope = ScopeFactory.CreateScope();
            return scope.ServiceProvider.GetRequiredService<TService>();
        }

        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender && JsRuntime is not null)
            {
                //  TimeZoneOffset = 5;
                var timezoneOffset = await JsRuntime.InvokeAsync<string>("getCookie", "timezoneoffset");
                if (!string.IsNullOrEmpty(timezoneOffset) && double.TryParse(timezoneOffset, out double offset))
                {
                    TimeZoneOffset = (int)offset;
                }
            }
        }

    }
}