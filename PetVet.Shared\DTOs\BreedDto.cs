﻿namespace PetVet.Shared.DTOs
{
    public class BreedDto
    {
        public bool IsPet { get; set; }
        public string Message { get; set; } = string.Empty;
        public List<(string Tag, double Probability)> Predictions { get; set; } = new List<(string, double)>();
        public string TopBreed { get; set; } = string.Empty;
        public double ConfidenceScore { get; set; }
        public string ModelUsed { get; set; } = string.Empty; // "CustomVision", "ONNX", or "Fallback"
        public List<BreedPredictionDto> DetailedPredictions { get; set; } = new List<BreedPredictionDto>();
    }

    public class BreedPredictionDto
    {
        public string BreedName { get; set; } = string.Empty;
        public double Confidence { get; set; }
        public double Percentage => Confidence * 100.0;
    }
}