using Microsoft.Extensions.Logging;
using PetVet.Mobile.Models;
using System.Collections.Concurrent;
using System.Diagnostics;
using Plugin.Maui.Audio;
using Microsoft.Maui.Controls;

namespace PetVet.Mobile.Services;

/// <summary>
/// Cross-platform media capture service for audio and video with real-time streaming support
/// </summary>
public interface IMediaCaptureService
{
    // Events
    event EventHandler<byte[]>? AudioDataCaptured;
    event EventHandler<byte[]>? VideoDataCaptured;
    event EventHandler<AudioQualityMetrics>? AudioQualityChanged;

    // Audio capture
    Task<bool> StartAudioCaptureAsync();
    Task<bool> StartAudioCaptureAsync(AudioStreamingConfig config);
    Task<bool> StopAudioCaptureAsync();
    bool PlayAudioAsync(byte[] audioData);
    bool PlayAudioFrameAsync(AudioFrame frame);
    bool IsAudioCapturing { get; }

    // Audio controls
    void SetMuted(bool muted);
    void SetVolume(double volume);
    bool IsMuted { get; }
    double Volume { get; }

    // Audio quality
    AudioQualityMetrics GetAudioQualityMetrics();
    void SetAudioQuality(AudioQuality quality);

    // Video capture
    Task<bool> StartVideoCaptureAsync();
    Task<bool> StopVideoCaptureAsync();
    bool DisplayVideoAsync(byte[] videoData);
    bool IsVideoCapturing { get; }

    // Permissions
    Task<bool> RequestAudioPermissionAsync();
    Task<bool> RequestVideoPermissionAsync();
    Task<bool> RequestCameraPermissionAsync();
}

public class MediaCaptureService : IMediaCaptureService, IDisposable
{
    private readonly ILogger<MediaCaptureService> _logger;
    private readonly IAudioManager _audioManager;
    private readonly IAudioCaptureService audioCaptureService;
    private bool _isAudioCapturing = false;
    private bool _isVideoCapturing = false;
    private bool _isMuted = false;
    private double _volume = 1.0;
    private CancellationTokenSource? _audioCaptureToken;
    private CancellationTokenSource? _videoCaptureToken;

    // Audio configuration
    private AudioStreamingConfig _audioConfig;
    private AudioQualityMetrics _audioMetrics;
    private readonly Stopwatch _captureStopwatch = new();
    private long _frameSequence = 0;

    // Audio buffering for playback
    private readonly AudioBuffer _playbackBuffer;
    private Task? _playbackTask;
    private CancellationTokenSource? _playbackToken;

    // Video properties
    private const int VideoWidth = 320;
    private const int VideoHeight = 240;
    private const int VideoFps = 15;

    // Events
    public event EventHandler<byte[]>? AudioDataCaptured;
    public event EventHandler<byte[]>? VideoDataCaptured;
    public event EventHandler<AudioQualityMetrics>? AudioQualityChanged;

    public bool IsAudioCapturing => _isAudioCapturing;
    public bool IsVideoCapturing => _isVideoCapturing;
    public bool IsMuted => _isMuted;
    public double Volume => _volume;

    public MediaCaptureService(ILogger<MediaCaptureService> logger, IAudioManager audioManager, IAudioCaptureService audioCaptureService)
    {
        _logger = logger;
        _audioManager = audioManager;
        this.audioCaptureService = audioCaptureService;
        _audioConfig = AudioStreamingConfig.GetQualityConfig(AudioQuality.Standard);
        _audioMetrics = new AudioQualityMetrics();
        _playbackBuffer = new AudioBuffer();
    }

    #region Audio Capture

    public async Task<bool> StartAudioCaptureAsync()
    {
        return await StartAudioCaptureAsync(_audioConfig);
    }

    public async Task<bool> StartAudioCaptureAsync(AudioStreamingConfig config)
    {
        try
        {
            if (_isAudioCapturing)
            {
                _logger.LogWarning("Audio capture is already running");
                return true;
            }

            // Request audio permission
            var hasPermission = await RequestAudioPermissionAsync();
            if (!hasPermission)
            {
                _logger.LogError("Audio permission denied");
                return false;
            }

            _audioConfig = config;
            _audioCaptureToken = new CancellationTokenSource();
            _isAudioCapturing = true;
            _frameSequence = 0;
            _captureStopwatch.Restart();

            // Start audio capture on background thread
            _ = Task.Run(() => CaptureAudioLoop(_audioCaptureToken.Token));

            // Start playback processing
            _playbackToken = new CancellationTokenSource();
            _playbackTask = Task.Run(() => PlaybackLoop(_playbackToken.Token));

            _logger.LogInformation("Audio capture started with config: {SampleRate}Hz, {Quality}",
                config.SampleRate, config.Quality);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting audio capture");
            return false;
        }
    }

    public async Task<bool> StopAudioCaptureAsync()
    {
        try
        {
            if (!_isAudioCapturing)
                return true;

            _audioCaptureToken?.Cancel();
            _playbackToken?.Cancel();
            _isAudioCapturing = false;

            // Wait for tasks to complete
            if (_playbackTask != null)
            {
                await _playbackTask;
                _playbackTask = null;
            }

            _playbackBuffer.Clear();
            _captureStopwatch.Stop();

            _logger.LogInformation("Audio capture stopped");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping audio capture");
            return false;
        }
    }

    private async Task CaptureAudioLoop(CancellationToken cancellationToken)
    {
        try
        {
            var frameInterval = _audioConfig.GetFrameDuration();
            var nextCaptureTime = DateTime.UtcNow;

            while (!cancellationToken.IsCancellationRequested && _isAudioCapturing)
            {
                var captureStart = DateTime.UtcNow;

                // Capture audio frame
                var audioData = await CaptureAudioFrameAsync();

                if (audioData != null && audioData.Length > 0 && !_isMuted)
                {
                    // Update quality metrics
                    UpdateCaptureMetrics(audioData, captureStart);

                    // Trigger event for transmission
                    AudioDataCaptured?.Invoke(this, audioData);
                }

                // Precise timing control
                nextCaptureTime = nextCaptureTime.Add(frameInterval);
                var delay = nextCaptureTime - DateTime.UtcNow;

                if (delay > TimeSpan.Zero)
                {
                    await Task.Delay(delay, cancellationToken);
                }
                else
                {
                    // We're running behind, adjust next capture time
                    nextCaptureTime = DateTime.UtcNow;
                }
            }
        }
        catch (OperationCanceledException)
        {
            // Expected when cancellation is requested
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in audio capture loop");
        }
    }

    private async Task<byte[]?> CaptureAudioFrameAsync()
    {
        try
        {
            // Calculate frame size based on configuration
            var frameSize = _audioConfig.GetFrameSizeInBytes();


            return audioCaptureService.CaptureFrameAsync(frameSize);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error capturing audio frame");
            return null;
        }
    }


    public bool PlayAudioAsync(byte[] audioData)
    {
        try
        {
            if (audioData == null || audioData.Length == 0)
                return false;

            // Create audio frame for buffered playback
            var frame = new AudioFrame
            {
                AudioData = audioData,
                SampleRate = _audioConfig.SampleRate,
                Channels = _audioConfig.Channels,
                BitsPerSample = _audioConfig.BitsPerSample,
                Timestamp = DateTime.UtcNow,
                Duration = TimeSpan.FromMilliseconds(audioData.Length * 1000.0 / (_audioConfig.SampleRate * _audioConfig.Channels * _audioConfig.BitsPerSample / 8))
            };

            return PlayAudioFrameAsync(frame);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error playing audio");
            return false;
        }
    }

    public bool PlayAudioFrameAsync(AudioFrame frame)
    {
        try
        {
            if (frame?.AudioData == null || frame.AudioData.Length == 0)
                return false;

            // Add to playback buffer for real-time processing
            _playbackBuffer.AddFrame(frame);

            _logger.LogDebug("Queued audio frame for playback: {Length} bytes, buffer count: {Count}",
                frame.AudioData.Length, _playbackBuffer.Count);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error queuing audio frame for playback");
            return false;
        }
    }

    private async Task PlaybackLoop(CancellationToken cancellationToken)
    {
        try
        {
            var frameInterval = _audioConfig.GetFrameDuration();

            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    // Check if we have frames to play and buffer is ready
                    if (!_playbackBuffer.IsBuffering && !_playbackBuffer.IsEmpty)
                    {
                        var frame = _playbackBuffer.GetNextFrame();
                        if (frame != null)
                        {
                            PlayAudioFrameDirectAsync(frame);
                        }
                    }

                    // Wait for next playback cycle
                    await Task.Delay(frameInterval, cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in playback loop iteration");
                    await Task.Delay(100, cancellationToken); // Brief pause before retry
                }
            }
        }
        catch (OperationCanceledException)
        {
            // Expected when cancellation is requested
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in audio playback loop");
        }
    }

    private void PlayAudioFrameDirectAsync(AudioFrame frame)
    {
        try
        {
            // Apply volume adjustment

            // Convert to WAV format for playback
            var wavData = CreateWavFile(frame.AudioData, 16000, 1, 16);

            // Play audio using Plugin.Maui.Audio
            using var stream = new MemoryStream(wavData);
            using var player = _audioManager.CreatePlayer(stream);

            player.Play();


            // Don't wait for completion to maintain real-time playback
            // The audio will play in the background
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error playing audio frame directly");
        }
    }

    private byte[] CreateWavFile(byte[] audioData, int sampleRate, int channels, int bitsPerSample)
    {
        try
        {
            using var stream = new MemoryStream();
            using var writer = new BinaryWriter(stream);

            var dataSize = audioData.Length;
            var fileSize = 36 + dataSize;

            // WAV header
            writer.Write("RIFF".ToCharArray());
            writer.Write(fileSize);
            writer.Write("WAVE".ToCharArray());

            // Format chunk
            writer.Write("fmt ".ToCharArray());
            writer.Write(16); // Chunk size
            writer.Write((short)1); // Audio format (PCM)
            writer.Write((short)channels);
            writer.Write(sampleRate);
            writer.Write(sampleRate * channels * bitsPerSample / 8); // Byte rate
            writer.Write((short)(channels * bitsPerSample / 8)); // Block align
            writer.Write((short)bitsPerSample);

            // Data chunk
            writer.Write("data".ToCharArray());
            writer.Write(dataSize);
            writer.Write(audioData);

            return stream.ToArray();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating WAV file");
            return audioData; // Return original data as fallback
        }
    }


    private void UpdateCaptureMetrics(byte[] audioData, DateTime captureStart)
    {
        try
        {
            _audioMetrics.PacketsReceived++;
            _audioMetrics.Latency = (DateTime.UtcNow - captureStart).TotalMilliseconds;
            _audioMetrics.CurrentQuality = _audioConfig.Quality;
            _audioMetrics.LastUpdate = DateTime.UtcNow;
            _audioMetrics.BufferHealth = _playbackBuffer.BufferedDuration;

            // Calculate audio level (RMS)
            _audioMetrics.AudioLevel = CalculateAudioLevel(audioData);
            _audioMetrics.IsMuted = _isMuted;

            // Trigger quality update event periodically
            if (DateTime.UtcNow - _audioMetrics.LastUpdate > TimeSpan.FromSeconds(1))
            {
                AudioQualityChanged?.Invoke(this, _audioMetrics);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating capture metrics");
        }
    }

    private double CalculateAudioLevel(byte[] audioData)
    {
        try
        {
            double sum = 0;
            int sampleCount = 0;

            for (int i = 0; i < audioData.Length - 1; i += 2)
            {
                var sample = BitConverter.ToInt16(audioData, i);
                sum += sample * sample;
                sampleCount++;
            }

            if (sampleCount == 0) return 0;

            var rms = Math.Sqrt(sum / sampleCount);
            return Math.Min(1.0, rms / 32768.0); // Normalize to 0-1
        }
        catch
        {
            return 0;
        }
    }

    public void SetMuted(bool muted)
    {
        _isMuted = muted;
        _audioMetrics.IsMuted = muted;
        _logger.LogInformation("Audio {Status}", muted ? "muted" : "unmuted");
    }

    public void SetVolume(double volume)
    {
        _volume = Math.Max(0.0, Math.Min(2.0, volume));
        _logger.LogDebug("Volume set to {Volume}", _volume);
    }

    public AudioQualityMetrics GetAudioQualityMetrics()
    {
        return _audioMetrics;
    }

    public void SetAudioQuality(AudioQuality quality)
    {
        _audioConfig = AudioStreamingConfig.GetQualityConfig(quality);
        _audioMetrics.CurrentQuality = quality;
        _logger.LogInformation("Audio quality changed to {Quality}", quality);
    }

    #endregion

    #region Video Capture

    public async Task<bool> StartVideoCaptureAsync()
    {
        try
        {
            if (_isVideoCapturing)
            {
                _logger.LogWarning("Video capture is already running");
                return true;
            }

            // Request camera permission
            var hasPermission = await RequestCameraPermissionAsync();
            if (!hasPermission)
            {
                _logger.LogError("Camera permission denied");
                return false;
            }

            _videoCaptureToken = new CancellationTokenSource();
            _isVideoCapturing = true;

            // Start video capture on background thread
            _ = Task.Run(() => CaptureVideoLoop(_videoCaptureToken.Token));

            _logger.LogInformation("Video capture started");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting video capture");
            return false;
        }
    }

    public async Task<bool> StopVideoCaptureAsync()
    {
        try
        {
            if (!_isVideoCapturing)
                return true;

            _videoCaptureToken?.Cancel();
            _isVideoCapturing = false;

            _logger.LogInformation("Video capture stopped");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping video capture");
            return false;
        }
    }

    private async Task CaptureVideoLoop(CancellationToken cancellationToken)
    {
        try
        {
            while (!cancellationToken.IsCancellationRequested && _isVideoCapturing)
            {
                // Simulate video capture
                var videoData = await CaptureVideoFrameAsync();

                if (videoData != null && videoData.Length > 0)
                {
                    VideoDataCaptured?.Invoke(this, videoData);
                }

                // Control capture rate (15 FPS = ~67ms intervals)
                await Task.Delay(67, cancellationToken);
            }
        }
        catch (OperationCanceledException)
        {
            // Expected when cancellation is requested
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in video capture loop");
        }
    }

    private async Task<byte[]?> CaptureVideoFrameAsync()
    {
        try
        {
            // Platform-specific video capture implementation would go here
            // For now, return simulated video data (RGB frame)
            var frameSize = VideoWidth * VideoHeight * 3; // RGB = 3 bytes per pixel
            var videoBuffer = new byte[frameSize];

            // Generate some dummy video data (gradient pattern)
            var random = new Random();
            for (int y = 0; y < VideoHeight; y++)
            {
                for (int x = 0; x < VideoWidth; x++)
                {
                    var index = (y * VideoWidth + x) * 3;
                    videoBuffer[index] = (byte)(x * 255 / VideoWidth);     // Red gradient
                    videoBuffer[index + 1] = (byte)(y * 255 / VideoHeight); // Green gradient
                    videoBuffer[index + 2] = (byte)(random.Next(0, 100));   // Random blue
                }
            }

            return videoBuffer;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error capturing video frame");
            return null;
        }
    }

    public bool DisplayVideoAsync(byte[] videoData)
    {
        try
        {
            if (videoData == null || videoData.Length == 0)
                return false;

            // Platform-specific video display implementation would go here
            // For now, just log that we received video data
            _logger.LogDebug("Displaying video data: {Length} bytes", videoData.Length);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error displaying video");
            return false;
        }
    }

    #endregion

    #region Permissions

    public async Task<bool> RequestAudioPermissionAsync()
    {
        try
        {
            var status = await Permissions.RequestAsync<Permissions.Microphone>();
            return status == PermissionStatus.Granted;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error requesting audio permission");
            return false;
        }
    }

    public async Task<bool> RequestVideoPermissionAsync()
    {
        return await RequestCameraPermissionAsync();
    }

    public async Task<bool> RequestCameraPermissionAsync()
    {
        try
        {
            var status = await Permissions.RequestAsync<Permissions.Camera>();
            return status == PermissionStatus.Granted;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error requesting camera permission");
            return false;
        }
    }

    #endregion

    public void Dispose()
    {
        _audioCaptureToken?.Cancel();
        _videoCaptureToken?.Cancel();
        _playbackToken?.Cancel();

        // Wait for tasks to complete
        _playbackTask?.Wait(1000);

        _audioCaptureToken?.Dispose();
        _videoCaptureToken?.Dispose();
        _playbackToken?.Dispose();

        _playbackBuffer.Clear();

        _isAudioCapturing = false;
        _isVideoCapturing = false;

        _logger.LogInformation("MediaCaptureService disposed");
    }
}
