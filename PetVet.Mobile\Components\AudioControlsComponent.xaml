<?xml version="1.0" encoding="utf-8" ?>
<ContentView x:Class="PetVet.Mobile.Components.AudioControlsComponent"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">
    
    <Frame BackgroundColor="White"
           CornerRadius="15"
           HasShadow="True"
           Padding="15"
           Margin="10">
        
        <StackLayout Spacing="15">
            
            <!-- Audio Quality Indicator -->
            <Grid ColumnDefinitions="Auto,*,Auto" ColumnSpacing="10">
                <Label Grid.Column="0"
                       Text="Audio Quality"
                       FontSize="14"
                       FontAttributes="Bold"
                       TextColor="#374151"
                       VerticalOptions="Center" />
                
                <StackLayout Grid.Column="1"
                             Orientation="Horizontal"
                             Spacing="5"
                             VerticalOptions="Center">
                    <Ellipse x:Name="QualityIndicator1"
                             Fill="#10B981"
                             HeightRequest="8"
                             WidthRequest="8" />
                    <Ellipse x:Name="QualityIndicator2"
                             Fill="#10B981"
                             HeightRequest="8"
                             WidthRequest="8" />
                    <Ellipse x:Name="QualityIndicator3"
                             Fill="#10B981"
                             HeightRequest="8"
                             WidthRequest="8" />
                    <Ellipse x:Name="QualityIndicator4"
                             Fill="#E5E7EB"
                             HeightRequest="8"
                             WidthRequest="8" />
                    <Ellipse x:Name="QualityIndicator5"
                             Fill="#E5E7EB"
                             HeightRequest="8"
                             WidthRequest="8" />
                </StackLayout>
                
                <Label x:Name="LatencyLabel"
                       Grid.Column="2"
                       Text="45ms"
                       FontSize="12"
                       TextColor="#6B7280"
                       VerticalOptions="Center" />
            </Grid>
            
            <!-- Audio Level Meter -->
            <Grid ColumnDefinitions="Auto,*" ColumnSpacing="10">
                <Label Grid.Column="0"
                       Text="🎤"
                       FontSize="16"
                       VerticalOptions="Center" />
                
                <ProgressBar x:Name="AudioLevelMeter"
                             Grid.Column="1"
                             Progress="0.3"
                             ProgressColor="#FEA195"
                             BackgroundColor="#E5E7EB"
                             HeightRequest="8"
                             VerticalOptions="Center" />
            </Grid>
            
            <!-- Volume Control -->
            <Grid ColumnDefinitions="Auto,*,Auto" ColumnSpacing="10">
                <Label Grid.Column="0"
                       Text="🔊"
                       FontSize="16"
                       VerticalOptions="Center" />
                
                <Slider x:Name="VolumeSlider"
                        Grid.Column="1"
                        Minimum="0"
                        Maximum="2"
                        Value="1"
                        ThumbColor="#FEA195"
                        MinimumTrackColor="#FEA195"
                        MaximumTrackColor="#E5E7EB"
                        ValueChanged="OnVolumeChanged" />
                
                <Label x:Name="VolumeLabel"
                       Grid.Column="2"
                       Text="100%"
                       FontSize="12"
                       TextColor="#6B7280"
                       VerticalOptions="Center" />
            </Grid>
            
            <!-- Audio Controls -->
            <Grid ColumnDefinitions="*,*,*" ColumnSpacing="15">
                
                <!-- Mute Button -->
                <Button x:Name="MuteButton"
                        Grid.Column="0"
                        Text="🎤"
                        BackgroundColor="#8FBFA8"
                        TextColor="White"
                        CornerRadius="25"
                        HeightRequest="50"
                        FontSize="20"
                        Clicked="OnMuteClicked" />
                
                <!-- Quality Button -->
                <Button x:Name="QualityButton"
                        Grid.Column="1"
                        Text="HD"
                        BackgroundColor="#6B7280"
                        TextColor="White"
                        CornerRadius="25"
                        HeightRequest="50"
                        FontSize="14"
                        FontAttributes="Bold"
                        Clicked="OnQualityClicked" />
                
                <!-- Settings Button -->
                <Button x:Name="SettingsButton"
                        Grid.Column="2"
                        Text="⚙️"
                        BackgroundColor="#6B7280"
                        TextColor="White"
                        CornerRadius="25"
                        HeightRequest="50"
                        FontSize="18"
                        Clicked="OnSettingsClicked" />
            </Grid>
            
            <!-- Network Status -->
            <Grid ColumnDefinitions="Auto,*,Auto" ColumnSpacing="10">
                <Ellipse x:Name="NetworkStatusIndicator"
                         Grid.Column="0"
                         Fill="#10B981"
                         HeightRequest="12"
                         WidthRequest="12"
                         VerticalOptions="Center" />
                
                <Label x:Name="NetworkStatusLabel"
                       Grid.Column="1"
                       Text="Excellent connection"
                       FontSize="12"
                       TextColor="#6B7280"
                       VerticalOptions="Center" />
                
                <Label x:Name="PacketLossLabel"
                       Grid.Column="2"
                       Text="0% loss"
                       FontSize="10"
                       TextColor="#6B7280"
                       VerticalOptions="Center" />
            </Grid>
            
            <!-- Buffer Health -->
            <Grid ColumnDefinitions="Auto,*" ColumnSpacing="10"
                  x:Name="BufferHealthGrid"
                  IsVisible="False">
                <Label Grid.Column="0"
                       Text="Buffer:"
                       FontSize="10"
                       TextColor="#6B7280"
                       VerticalOptions="Center" />
                
                <ProgressBar x:Name="BufferHealthMeter"
                             Grid.Column="1"
                             Progress="0.7"
                             ProgressColor="#8FBFA8"
                             BackgroundColor="#E5E7EB"
                             HeightRequest="4"
                             VerticalOptions="Center" />
            </Grid>
            
        </StackLayout>
    </Frame>
</ContentView>
