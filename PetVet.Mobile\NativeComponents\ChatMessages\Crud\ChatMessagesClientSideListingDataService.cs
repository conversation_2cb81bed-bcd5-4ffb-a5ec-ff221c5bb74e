﻿using PetVet.ServiceContracts.Features.Conversation;
using PetVet.Cient.Common.Data;
using System.Security.Claims;
using PetVet.Client.Common.Data;
using PetVet.Shared.Components.Framework;

namespace Platform.Client.Common.Features.Conversation;
public class ChatMessagesClientSideListingDataService : ClientSideListingDataService<ChatMessagesListingBusinessObject, ChatMessagesFilterBusinessObject>, IChatMessagesListingDataService
{
    private readonly AppDbContext context;
    private readonly ILocalStorageService localStorageService;

    public ChatMessagesClientSideListingDataService(AppDbContext context, ILocalStorageService localStorageService)
    {
        this.context = context;
        this.localStorageService = localStorageService;
    }
    public override IQueryable<ChatMessagesListingBusinessObject> GetQuery(ChatMessagesFilterBusinessObject filterBusinessObject)
    {
        var userId = Convert.ToInt32(Task.Run(() => (localStorageService.GetValue(ClaimTypes.NameIdentifier))).Result);
        var messages = context.Messages.ToList();
        var query = (from m in context.Messages
                     where m.ConversationId == filterBusinessObject.ConversationId
                     orderby m.CreatedAt descending
                     select new ChatMessagesListingBusinessObject
                     {
                         Id = m.Id,
                         Content = m.PlainContent,
                         IsIncoming = m.SenderId != userId,
                         Timestamp = m.CreatedAt,
                         DeliveryStatus = m.DeliveryStatus,
                     });

        return query;
    }
}
