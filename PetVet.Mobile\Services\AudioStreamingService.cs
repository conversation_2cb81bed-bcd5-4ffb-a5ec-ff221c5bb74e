using Microsoft.Extensions.Logging;
using PetVet.Mobile.Models;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace PetVet.Mobile.Services;

/// <summary>
/// Enhanced audio streaming service for real-time voice calls
/// </summary>
public interface IAudioStreamingService
{
    // Events
    event EventHandler<AudioFrame>? AudioFrameReady;
    event EventHandler<AudioQualityMetrics>? QualityMetricsUpdated;

    // Configuration
    AudioStreamingConfig Config { get; set; }

    // Streaming control
    Task<bool> StartStreamingAsync(string callId);
    Task<bool> StopStreamingAsync();
    bool IsStreaming { get; }

    // Audio processing
    bool ProcessIncomingAudioAsync(AudioFrame frame);
    bool SendAudioFrameAsync(string callId, byte[] audioData);

    // Quality control
    void SetAudioQuality(AudioQuality quality);
    AudioQualityMetrics GetQualityMetrics(string callId);

    // Volume and mute control
    void SetVolume(double volume);
    void SetMuted(bool muted);
    bool IsMuted { get; }

    // Buffer management
    void ClearBuffers();
    TimeSpan GetBufferHealth();
}

public class AudioStreamingService : IAudioStreamingService, IDisposable
{
    private readonly ILogger<AudioStreamingService> _logger;
    private readonly AudioMixer _audioMixer;
    private readonly ConcurrentDictionary<string, AudioQualityMetrics> _qualityMetrics = new();

    private AudioStreamingConfig _config;
    private bool _isStreaming = false;
    private bool _isMuted = false;
    private double _volume = 1.0;
    private string _currentCallId = "";
    private long _sequenceNumber = 0;

    private CancellationTokenSource? _streamingToken;
    private Task? _playbackTask;
    private readonly object _lockObject = new();

    // Performance tracking
    private readonly Stopwatch _latencyStopwatch = new();
    private readonly Queue<double> _latencyHistory = new();
    private DateTime _lastQualityUpdate = DateTime.UtcNow;

    // Events
    public event EventHandler<AudioFrame>? AudioFrameReady;
    public event EventHandler<AudioQualityMetrics>? QualityMetricsUpdated;

    public AudioStreamingConfig Config
    {
        get => _config;
        set
        {
            _config = value;
            _logger.LogInformation("Audio config updated: {SampleRate}Hz, {Quality}",
                value.SampleRate, value.Quality);
        }
    }

    public bool IsStreaming => _isStreaming;
    public bool IsMuted => _isMuted;

    public AudioStreamingService(ILogger<AudioStreamingService> logger)
    {
        _logger = logger;
        _config = AudioStreamingConfig.GetQualityConfig(AudioQuality.Standard);
        _audioMixer = new AudioMixer(_config);
    }

    public async Task<bool> StartStreamingAsync(string callId)
    {
        try
        {
            if (_isStreaming)
            {
                _logger.LogWarning("Audio streaming already active for call {CallId}", _currentCallId);
                return true;
            }

            _currentCallId = callId;
            _streamingToken = new CancellationTokenSource();
            _isStreaming = true;
            _sequenceNumber = 0;

            // Initialize quality metrics
            _qualityMetrics[callId] = new AudioQualityMetrics
            {
                CallId = callId,
                CurrentQuality = _config.Quality
            };

            // Start playback loop
            _playbackTask = Task.Run(() => PlaybackLoop(_streamingToken.Token));

            _logger.LogInformation("Audio streaming started for call {CallId}", callId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting audio streaming");
            return false;
        }
    }

    public async Task<bool> StopStreamingAsync()
    {
        try
        {
            if (!_isStreaming)
                return true;

            _streamingToken?.Cancel();
            _isStreaming = false;

            if (_playbackTask != null)
            {
                await _playbackTask;
                _playbackTask = null;
            }

            _audioMixer.ClearAll();
            _qualityMetrics.Clear();
            _currentCallId = "";

            _logger.LogInformation("Audio streaming stopped");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping audio streaming");
            return false;
        }
    }

    public bool ProcessIncomingAudioAsync(AudioFrame frame)
    {
        try
        {
            if (!_isStreaming || frame.CallId != _currentCallId)
                return false;

            // Update quality metrics
            UpdateQualityMetrics(frame);

            // Add to mixer for playback
            _audioMixer.AddParticipant(frame.FromUserId);
            _audioMixer.AddAudioFrame(frame.FromUserId, frame);

            _logger.LogDebug("Processed incoming audio frame from {UserId}, size: {Size} bytes",
                frame.FromUserId, frame.AudioData.Length);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing incoming audio frame");
            return false;
        }
    }

    public bool SendAudioFrameAsync(string callId, byte[] audioData)
    {
        try
        {
            if (!_isStreaming || _isMuted || callId != _currentCallId)
                return false;

            // Create audio frame with metadata
            var frame = new AudioFrame
            {
                CallId = callId,
                FromUserId = 0, // Will be set by ChatCallService
                AudioData = audioData,
                SampleRate = _config.SampleRate,
                Channels = _config.Channels,
                BitsPerSample = _config.BitsPerSample,
                SequenceNumber = Interlocked.Increment(ref _sequenceNumber),
                Timestamp = DateTime.UtcNow,
                Duration = _config.GetFrameDuration(),
                Quality = _config.Quality
            };

            // Trigger event for transmission
            AudioFrameReady?.Invoke(this, frame);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending audio frame");
            return false;
        }
    }



    private async Task PlaybackLoop(CancellationToken cancellationToken)
    {
        try
        {
            var frameInterval = _config.GetFrameDuration();

            while (!cancellationToken.IsCancellationRequested && _isStreaming)
            {
                try
                {
                    // Get mixed audio from all participants
                    var mixedAudio = _audioMixer.GetMixedAudio();

                    if (mixedAudio != null && mixedAudio.Length > 0)
                    {
                        // Apply volume and play audio
                        var volumeAdjustedAudio = ApplyVolumeAdjustment(mixedAudio, _volume);
                        await PlayAudioDirectAsync(volumeAdjustedAudio);
                    }

                    // Update quality metrics periodically
                    if (DateTime.UtcNow - _lastQualityUpdate > TimeSpan.FromSeconds(1))
                    {
                        UpdateAndBroadcastQualityMetrics();
                        _lastQualityUpdate = DateTime.UtcNow;
                    }

                    // Wait for next frame
                    await Task.Delay(frameInterval, cancellationToken);
                }
                catch (OperationCanceledException)
                {
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in playback loop");
                    await Task.Delay(100, cancellationToken); // Brief pause before retry
                }
            }
        }
        catch (OperationCanceledException)
        {
            // Expected when cancellation is requested
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Fatal error in playback loop");
        }
    }

    private byte[] CreateWavFile(byte[] samples, int sampleRate, int channels, int bitsPerSample)
    {
        using var stream = new MemoryStream();
        using var writer = new BinaryWriter(stream);

        var dataSize = samples.Length * sizeof(short);
        var fileSize = 36 + dataSize;

        // WAV header
        writer.Write("RIFF".ToCharArray());
        writer.Write(fileSize);
        writer.Write("WAVE".ToCharArray());

        // Format chunk
        writer.Write("fmt ".ToCharArray());
        writer.Write(16); // Chunk size
        writer.Write((short)1); // Audio format (PCM)
        writer.Write((short)channels);
        writer.Write(sampleRate);
        writer.Write(sampleRate * channels * bitsPerSample / 8); // Byte rate
        writer.Write((short)(channels * bitsPerSample / 8)); // Block align
        writer.Write((short)bitsPerSample);

        // Data chunk
        writer.Write("data".ToCharArray());
        writer.Write(dataSize);

        // Audio data
        foreach (var sample in samples)
        {
            writer.Write(sample);
        }

        return stream.ToArray();
    }


    private async Task PlayAudioDirectAsync(byte[] audioData)
    {
        try
        {
            _logger.LogDebug("Playing audio: {Length} bytes", audioData.Length);

            // Save the byte array to a temp .wav file
            var tempFile = Path.Combine(FileSystem.CacheDirectory, $"temp_audio_{Guid.NewGuid()}.wav");
            File.WriteAllBytes(tempFile, CreateWavFile(audioData, 16000, 1, 16));

#if ANDROID
            var mediaPlayer = new Android.Media.MediaPlayer();
            mediaPlayer.SetDataSource(tempFile);
            mediaPlayer.Prepare();
            mediaPlayer.Start();

            // Optional: wait until done
            mediaPlayer.Completion += (s, e) =>
            {
                mediaPlayer.Release();
                File.Delete(tempFile);
            };

#elif IOS
        var url = Foundation.NSUrl.FromFilename(tempFile);
        var player = new AVFoundation.AVAudioPlayer(url);
        player.FinishedPlaying += (s, e) =>
        {
            player.Dispose();
            File.Delete(tempFile);
        };
        player.PrepareToPlay();
        player.Play();

#elif WINDOWS
        var player = new Windows.Media.Playback.MediaPlayer();
        var file = await Windows.Storage.StorageFile.GetFileFromPathAsync(tempFile);
        var source = Windows.Media.Core.MediaSource.CreateFromStorageFile(file);
        player.Source = source;
        player.Play();
#endif
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error playing audio directly");
        }
    }


    private byte[] ApplyVolumeAdjustment(byte[] audioData, double volume)
    {
        if (Math.Abs(volume - 1.0) < 0.01)
            return audioData;

        var adjustedData = new byte[audioData.Length];

        for (int i = 0; i < audioData.Length - 1; i += 2)
        {
            // Convert to 16-bit sample
            var sample = BitConverter.ToInt16(audioData, i);

            // Apply volume
            var adjustedSample = (short)Math.Max(-32768, Math.Min(32767, sample * volume));

            // Convert back to bytes
            var bytes = BitConverter.GetBytes(adjustedSample);
            adjustedData[i] = bytes[0];
            adjustedData[i + 1] = bytes[1];
        }

        return adjustedData;
    }

    private void UpdateQualityMetrics(AudioFrame frame)
    {
        if (!_qualityMetrics.TryGetValue(frame.CallId, out var metrics))
            return;

        // Update latency
        var latency = (DateTime.UtcNow - frame.Timestamp).TotalMilliseconds;
        _latencyHistory.Enqueue(latency);

        if (_latencyHistory.Count > 10)
            _latencyHistory.Dequeue();

        metrics.Latency = _latencyHistory.Average();
        metrics.PacketsReceived++;
        metrics.LastUpdate = DateTime.UtcNow;
        metrics.BufferHealth = GetBufferHealth();
    }

    private void UpdateAndBroadcastQualityMetrics()
    {
        foreach (var metrics in _qualityMetrics.Values)
        {
            QualityMetricsUpdated?.Invoke(this, metrics);
        }
    }

    public void SetAudioQuality(AudioQuality quality)
    {
        _config = AudioStreamingConfig.GetQualityConfig(quality);
        _logger.LogInformation("Audio quality changed to {Quality}", quality);
    }

    public AudioQualityMetrics GetQualityMetrics(string callId)
    {
        return _qualityMetrics.TryGetValue(callId, out var metrics)
            ? metrics
            : new AudioQualityMetrics { CallId = callId };
    }

    public void SetVolume(double volume)
    {
        _volume = Math.Max(0.0, Math.Min(2.0, volume));
        _logger.LogDebug("Volume set to {Volume}", _volume);
    }

    public void SetMuted(bool muted)
    {
        _isMuted = muted;
        _logger.LogInformation("Audio {Status}", muted ? "muted" : "unmuted");
    }

    public void ClearBuffers()
    {
        _audioMixer.ClearAll();
        _logger.LogDebug("Audio buffers cleared");
    }

    public TimeSpan GetBufferHealth()
    {
        // Return estimated buffer health
        return TimeSpan.FromMilliseconds(50); // Placeholder
    }

    public void Dispose()
    {
        _streamingToken?.Cancel();
        _playbackTask?.Wait(1000);
        _streamingToken?.Dispose();
        _audioMixer.ClearAll();
        _qualityMetrics.Clear();

        _logger.LogInformation("AudioStreamingService disposed");
    }
}
