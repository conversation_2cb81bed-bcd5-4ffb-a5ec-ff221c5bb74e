namespace PetVet.Api.Models
{
    public class PetBreedPrediction
    {
        public string BreedName { get; set; } = string.Empty;
        public float Confidence { get; set; }
        public float Percentage => Confidence * 100f;
    }

    public class PetBreedModelInfo
    {
        public string ModelName { get; set; } = string.Empty;
        public int[] InputSize { get; set; } = Array.Empty<int>();
        public int NumClasses { get; set; }
        public string[] ClassNames { get; set; } = Array.Empty<string>();
        public PreprocessingInfo Preprocessing { get; set; } = new();
    }

    public class PreprocessingInfo
    {
        public float[] Mean { get; set; } = Array.Empty<float>();
        public float[] Std { get; set; } = Array.Empty<float>();
    }
}
