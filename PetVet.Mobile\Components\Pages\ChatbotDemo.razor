@page "/chatbot-demo"
@inject IJSRuntime JSRuntime

<PageTitle>AI Chatbot Demo</PageTitle>

<!-- Demo Page for Chatbot -->
<div class="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 p-4">
    <div class="max-w-md mx-auto">
        
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="w-16 h-16 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="text-white">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                </svg>
            </div>
            <h1 class="text-2xl font-bold text-gray-800 mb-2">AI Chatbot Demo</h1>
            <p class="text-gray-600">Test the PetVet AI Assistant functionality</p>
        </div>

        <!-- Demo Cards -->
        <div class="space-y-4 mb-8">
            
            <!-- Quick Test Card -->
            <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="text-primary-500">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    </svg>
                    Quick Test Messages
                </h3>
                <div class="grid grid-cols-1 gap-3">
                    <button @onclick='() => SendTestMessage("Hello! Can you help me with pet care?")'
                            class="p-3 bg-primary-50 rounded-xl text-left hover:bg-primary-100 transition-colors">
                        <span class="text-sm font-medium text-primary-700">Say Hello</span>
                        <p class="text-xs text-primary-600 mt-1">Test basic greeting</p>
                    </button>

                    <button @onclick='() => SendTestMessage("What should I feed my dog?")'
                            class="p-3 bg-secondary-50 rounded-xl text-left hover:bg-secondary-100 transition-colors">
                        <span class="text-sm font-medium text-secondary-700">Pet Nutrition</span>
                        <p class="text-xs text-secondary-600 mt-1">Ask about pet food</p>
                    </button>

                    <button @onclick='() => SendTestMessage("My cat seems lethargic, what should I do?")'
                            class="p-3 bg-orange-50 rounded-xl text-left hover:bg-orange-100 transition-colors">
                        <span class="text-sm font-medium text-orange-700">Health Concern</span>
                        <p class="text-xs text-orange-600 mt-1">Test health advice</p>
                    </button>

                    <button @onclick='() => SendTestMessage("How often should I groom my pet?")'
                            class="p-3 bg-blue-50 rounded-xl text-left hover:bg-blue-100 transition-colors">
                        <span class="text-sm font-medium text-blue-700">Grooming Tips</span>
                        <p class="text-xs text-blue-600 mt-1">Ask about grooming</p>
                    </button>
                </div>
            </div>

            <!-- Chatbot Controls Card -->
            <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="text-secondary-500">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"/>
                    </svg>
                    Chatbot Controls
                </h3>
                <div class="grid grid-cols-2 gap-3">
                    <button @onclick="OpenChatbot"
                            class="p-3 bg-green-50 rounded-xl text-center hover:bg-green-100 transition-colors">
                        <div class="text-green-500 text-xl mb-1">💬</div>
                        <span class="text-sm font-medium text-green-700">Open Chat</span>
                    </button>

                    <button @onclick="CloseChatbot"
                            class="p-3 bg-red-50 rounded-xl text-center hover:bg-red-100 transition-colors">
                        <div class="text-red-500 text-xl mb-1">✕</div>
                        <span class="text-sm font-medium text-red-700">Close Chat</span>
                    </button>
                </div>
            </div>

            <!-- Custom Message Card -->
            <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="text-purple-500">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                    Custom Message
                </h3>
                <div class="space-y-3">
                    <textarea @bind="customMessage" 
                             placeholder="Type your custom message here..."
                             class="w-full p-3 border border-gray-200 rounded-xl resize-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                             rows="3"></textarea>
                    <button @onclick="SendCustomMessage" 
                            class="w-full p-3 bg-gradient-to-r from-primary-500 to-secondary-500 text-white rounded-xl font-medium hover:shadow-lg transition-all"
                            disabled="@string.IsNullOrWhiteSpace(customMessage)">
                        Send Custom Message
                    </button>
                </div>
            </div>

            <!-- Info Card -->
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
                <h3 class="text-lg font-semibold text-blue-800 mb-2 flex items-center gap-2">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="text-blue-500">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Integration Ready
                </h3>
                <p class="text-blue-700 text-sm leading-relaxed">
                    The chatbot UI is ready for Azure OpenAI integration. Look for the floating chat button in the bottom-right corner.
                    The current responses are simulated - replace the SimulateAIResponse method
                    in Chatbot.razor with your Azure OpenAI API calls.
                </p>
            </div>
        </div>

        <!-- Stats Card -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Chatbot Status</h3>
            <div class="grid grid-cols-2 gap-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-primary-500">@(isChatbotOpen ? "Open" : "Closed")</div>
                    <div class="text-sm text-gray-600">Status</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-secondary-500">@messageCount</div>
                    <div class="text-sm text-gray-600">Messages</div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string customMessage = "";
    private bool isChatbotOpen = false;
    private int messageCount = 0;

    private async Task SendTestMessage(string message)
    {
        await SendMessageToChatbot(message);
    }

    private async Task SendCustomMessage()
    {
        if (!string.IsNullOrWhiteSpace(customMessage))
        {
            await SendMessageToChatbot(customMessage);
            customMessage = "";
        }
    }

    private async Task SendMessageToChatbot(string message)
    {
        // Get reference to MainLayout and send message to chatbot
        try
        {
            await JSRuntime.InvokeVoidAsync("eval", $"window.parent.blazorCulture.invokeMethodAsync('SendChatbotMessage', '{message}')");
            messageCount++;
            StateHasChanged();
        }
        catch
        {
            // Fallback: just open the chatbot
            await OpenChatbot();
        }
    }

    private async Task OpenChatbot()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("eval", "window.parent.blazorCulture.invokeMethodAsync('OpenChatbot')");
            isChatbotOpen = true;
            StateHasChanged();
        }
        catch
        {
            // Handle error
            Console.WriteLine("Could not open chatbot");
        }
    }

    private async Task CloseChatbot()
    {
        // Note: This is just for demo - the actual close is handled by the chatbot component
        isChatbotOpen = false;
        StateHasChanged();
    }
}
