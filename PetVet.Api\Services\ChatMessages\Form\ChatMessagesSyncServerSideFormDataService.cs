﻿using PetVet.Server.DataServices.Data;
using PetVet.Server.DataServices.Helpers;
using PetVet.ServiceContracts.Enums;
using PetVet.ServiceContracts.Features.Conversation;
using Microsoft.EntityFrameworkCore;
using PetVet.Api.Data;
namespace PetVet.Api.Services.ChatMessages.Form;
public class ChatMessagesSyncServerSideFormDataService : IChatMessagesSyncFormDataService
{

    private readonly PetVetContext _context;
    private readonly MessageDispatcher messageDispatcher;

    public ChatMessagesSyncServerSideFormDataService(PetVetContext context,
        MessageDispatcher messageDispatcher)
    {
        _context = context;
        this.messageDispatcher = messageDispatcher;
    }

    public async Task<string> SaveAsync(ChatMessagesSyncFormBusinessObject formBusinessObject)
    {

        var message = _context.ConversationMessages.FirstOrDefault(x => x.Id == formBusinessObject.Id);
        if (message == null)
        {
            message = new ConversationMessage()
            {
                Id = formBusinessObject.Id,
                ConversationId = formBusinessObject.ConversationId,
                CreatedAt = formBusinessObject.CreatedAt,
                DeletedAt = formBusinessObject.DeletedAt,

                IsDeleted = formBusinessObject.IsDeleted,
                IsEdited = formBusinessObject.IsEdited,
                EditedAt = formBusinessObject.EditedAt,
                PlainContent = formBusinessObject.PlainContent,
                SenderId = formBusinessObject.SenderId,
                //todo: sender device id for multi device support
                DeliveryStatus = DeliveryStatus.SentToMessageServer,
                DeliveryStatusTime = DateTime.UtcNow,
            };
            _context.ConversationMessages.Add(message);
            await _context.SaveChangesAsync();

            var conversationParticipants = await _context.ConversationParticipants.Where(x => x.ConversationId == message.ConversationId
                && x.UserId != formBusinessObject.SenderId).ToListAsync();

            foreach (var participant in conversationParticipants)
            {
                var messageRecipient = await _context.MessageRecipients.FirstOrDefaultAsync(x => x.MessageId == message.Id && x.RecipientId == participant.UserId);
                if (messageRecipient == null)
                {
                    messageRecipient = new MessageRecipient()
                    {
                        Id = Guid.NewGuid().ToString().ToLower(),
                        MessageId = message.Id,
                        UserDeviceId = participant.UserId,
                        RecipientId = participant.UserId,
                        DeliveryStatus = DeliveryStatus.SentToMessageServer,
                        DeliveryStatusTime = DateTime.UtcNow,
                    };
                }
                _context.MessageRecipients.Add(messageRecipient);
                await _context.SaveChangesAsync();

            }
        }

        var pendingMessages = (from m in _context.ConversationMessages
                               from u in _context.Users.Where(x => x.Id == m.SenderId)
                               where m.Id == message.Id
                               select new ChatMessagesSyncFormBusinessObject
                               {
                                   Id = m.Id,
                                   ConversationId = m.ConversationId,
                                   SenderId = m.SenderId,
                                   CreatedAt = m.CreatedAt,
                                   DeletedAt = m.DeletedAt,
                                   EditedAt = m.EditedAt,
                                   IsDeleted = m.IsDeleted,
                                   IsEdited = m.IsEdited,
                                   PlainContent = m.PlainContent,
                                   EnableFallBackChannel = true,
                                   SenderUserName = u.Name,
                                   SenderDisplayPictureUrl = u.ImageUrl,
                               }).ToList();
        foreach (var item in pendingMessages)
        {
            messageDispatcher.DispatchMessage(item);
        }
        return message.Id;
    }

    public async Task<ChatMessagesSyncFormBusinessObject> GetItemByIdAsync(string id)
    {
        var query = from m in _context.ConversationMessages
                    from u in _context.Users.Where(x => x.Id == m.SenderId)
                    where m.Id == id
                    select new ChatMessagesSyncFormBusinessObject()
                    {
                        Id = m.Id,
                        ConversationId = m.ConversationId,
                        SenderId = m.SenderId,
                        CreatedAt = m.CreatedAt,
                        DeletedAt = m.DeletedAt,
                        EditedAt = m.EditedAt,
                        IsDeleted = m.IsDeleted,
                        IsEdited = m.IsEdited,
                        PlainContent = m.PlainContent,
                        SenderUserName = u.Name,
                        SenderDisplayPictureUrl = u.ImageUrl,
                    };
        var chatMessage = await query.FirstAsync();
        return chatMessage;
    }
}
