using PetVet.Mobile.Models;
using PetVet.Mobile.Services;
using PetVet.Mobile.Components;
using Microsoft.Extensions.Logging;
using System.Collections.ObjectModel;
using System.Text;
using PetVet.MauiApp.Services;
using PetVet.Shared.Components.Framework;
using System.Security.Claims;
using System.Threading.Tasks;

namespace PetVet.Mobile.Pages;

public partial class CallTestPage : ContentPage
{
    private readonly IChatCallService _chatCallService;
    private readonly IMediaCaptureService _mediaCaptureService;
    private readonly SignalRClientService _signalRService;
    private readonly ICallDebugService _callDebugService;
    private readonly ILocalStorageService localStorageService;
    private readonly string conversationId;
    private readonly int friendId;
    private readonly string friendName;
    private readonly ILogger<CallTestPage> _logger;

    private readonly StringBuilder _logBuilder = new();
    private string? _currentCallId;
    private AudioControlsComponent? _audioControls;

    public ObservableCollection<CallSession> ActiveCalls { get; set; } = new();

    public CallTestPage(
        IChatCallService chatCallService,
        IMediaCaptureService mediaCaptureService,
        SignalRClientService signalRService,
        ICallDebugService callDebugService,
        ILocalStorageService localStorageService,
        string conversationId,
        int friendId,
        string friendName,
        ILogger<CallTestPage> logger)
    {
        InitializeComponent();

        _chatCallService = chatCallService;
        _mediaCaptureService = mediaCaptureService;
        _signalRService = signalRService;
        _callDebugService = callDebugService;
        this.localStorageService = localStorageService;
        this.conversationId = conversationId;
        this.friendId = friendId;
        this.friendName = friendName;
        _logger = logger;

        BindingContext = this;

        SubscribeToEvents();
        UpdateConnectionStatus();
        InitializeAudioControls();

        LogMessage("Call test page initialized");


    }
    protected override async void OnAppearing()
    {
        UserIdEntry.Text = await localStorageService.GetValue(ClaimTypes.NameIdentifier);
        UserNameEntry.Text = await localStorageService.GetValue(ClaimTypes.Name) ?? UserIdEntry.Text;

        ThreadIdEntry.Text = conversationId.ToString();
        FriendIdEntry.Text = friendId.ToString();

        OnInitializeClicked(null, new EventArgs());
        base.OnAppearing();
    }
    private void SubscribeToEvents()
    {
        // SignalR events
        //_signalRService.Connected += OnSignalRConnected;
        //_signalRService.Disconnected += OnSignalRDisconnected;

        // Call events
        //_chatCallService.CallInvitationReceived += OnCallInvitationReceived;
        _chatCallService.CallResponseReceived += OnCallResponseReceived;
        _chatCallService.CallStatusChanged += OnCallStatusChanged;

        // Media events
        _mediaCaptureService.AudioDataCaptured += OnAudioDataCaptured;
        _mediaCaptureService.VideoDataCaptured += OnVideoDataCaptured;
    }

    private void OnSignalRConnected(object? sender, EventArgs e)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            UpdateConnectionStatus();
            LogMessage("SignalR connected");
        });
    }

    private void OnSignalRDisconnected(object? sender, EventArgs e)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            UpdateConnectionStatus();
            LogMessage("SignalR disconnected");
        });
    }

    private void UpdateConnectionStatus()
    {
        var isConnected = _signalRService._hubConnection.ConnectionId != null;
        ConnectionStatusIndicator.Fill = isConnected ? Colors.Green : Colors.Red;
        ConnectionStatusLabel.Text = isConnected ? "Connected" : "Disconnected";
    }

    private void InitializeAudioControls()
    {
        try
        {
            _audioControls = new AudioControlsComponent();
            _audioControls.MuteToggled += OnAudioControlsMuteToggled;
            _audioControls.QualityChanged += OnAudioControlsQualityChanged;
            _audioControls.VolumeChanged += OnAudioControlsVolumeChanged;

            AudioControlsContainer.Content = _audioControls;

            LogMessage("Enhanced audio controls initialized");
        }
        catch (Exception ex)
        {
            LogMessage($"Error initializing audio controls: {ex.Message}");
        }
    }

    private void OnAudioControlsMuteToggled(object? sender, bool isMuted)
    {
        LogMessage($"Audio controls: Mute {(isMuted ? "enabled" : "disabled")}");
    }

    private void OnAudioControlsQualityChanged(object? sender, AudioQuality quality)
    {
        LogMessage($"Audio controls: Quality changed to {quality}");
    }

    private void OnAudioControlsVolumeChanged(object? sender, double volume)
    {
        LogMessage($"Audio controls: Volume changed to {volume * 100:F0}%");
    }

    private async void OnInitializeClicked(object sender, EventArgs e)
    {
        try
        {
            // Try automatic initialization first
            var initialized = await _chatCallService.InitializeAsync();

            if (initialized)
            {
                LogMessage("Call service initialized automatically from stored credentials");
            }
            else
            {
                // Fall back to manual initialization
                var userId = Convert.ToInt32(UserIdEntry.Text?.Trim());
                var userName = UserNameEntry.Text?.Trim();

                if (userId == 0 || string.IsNullOrEmpty(userName))
                {
                    await DisplayAlert("Error", "Please enter both User ID and User Name, or ensure you're logged in", "OK");
                    return;
                }

                _chatCallService.Initialize(userId, userName);
                LogMessage($"Call service initialized manually for {userName} ({userId})");
            }
        }
        catch (Exception ex)
        {
            LogMessage($"Error initializing: {ex.Message}");
            await DisplayAlert("Error", $"Failed to initialize: {ex.Message}", "OK");
        }
    }

    private async void OnAudioCallClicked(object sender, EventArgs e)
    {
        try
        {
            var threadId = ThreadIdEntry.Text?.Trim();
            var friendId = Convert.ToInt32(FriendIdEntry.Text?.Trim());

            if (string.IsNullOrEmpty(threadId) || friendId == 0)
            {
                await DisplayAlert("Error", "Please enter Thread ID and Friend ID", "OK");
                return;
            }

            LogMessage($"Starting audio call to {friendId} in thread {threadId}");
            var success = await _chatCallService.SendCallInvitationAsync(threadId, friendId, CallType.Audio);

            if (success)
            {
                LogMessage("Audio call invitation sent successfully");
                UpdateActiveCallsList();

                // Set call ID for audio controls
                var activeCall = _chatCallService.GetActiveCall(threadId);
                if (activeCall != null)
                {
                    _audioControls?.SetCallId(activeCall.CallId);
                }
            }
            else
            {
                LogMessage("Failed to send audio call invitation");
            }
        }
        catch (Exception ex)
        {
            LogMessage($"Error starting audio call: {ex.Message}");
            await DisplayAlert("Error", $"Failed to start audio call: {ex.Message}", "OK");
        }
    }

    private async void OnVideoCallClicked(object sender, EventArgs e)
    {
        try
        {
            var threadId = ThreadIdEntry.Text?.Trim();
            var friendId = Convert.ToInt32(FriendIdEntry.Text?.Trim());

            if (string.IsNullOrEmpty(threadId) || friendId == 0)
            {
                await DisplayAlert("Error", "Please enter Thread ID and Friend ID", "OK");
                return;
            }

            LogMessage($"Starting video call to {friendId} in thread {threadId}");
            var success = await _chatCallService.SendCallInvitationAsync(threadId, friendId, CallType.AudioVideo);

            if (success)
            {
                LogMessage("Video call invitation sent successfully");
                UpdateActiveCallsList();

                // Set call ID for audio controls
                var activeCall = _chatCallService.GetActiveCall(threadId);
                if (activeCall != null)
                {
                    _audioControls?.SetCallId(activeCall.CallId);
                }
            }
            else
            {
                LogMessage("Failed to send video call invitation");
            }
        }
        catch (Exception ex)
        {
            LogMessage($"Error starting video call: {ex.Message}");
            await DisplayAlert("Error", $"Failed to start video call: {ex.Message}", "OK");
        }
    }

    private async void OnEndCallClicked(object sender, EventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(_currentCallId))
            {
                await DisplayAlert("Info", "No active call to end", "OK");
                return;
            }

            LogMessage($"Ending call {_currentCallId}");
            var success = await _chatCallService.EndCallAsync(_currentCallId);

            if (success)
            {
                LogMessage("Call ended successfully");
                _currentCallId = null;
                EndCallButton.IsEnabled = false;
                UpdateActiveCallsList();
            }
            else
            {
                LogMessage("Failed to end call");
            }
        }
        catch (Exception ex)
        {
            LogMessage($"Error ending call: {ex.Message}");
            await DisplayAlert("Error", $"Failed to end call: {ex.Message}", "OK");
        }
    }

    private async void OnToggleAudioClicked(object sender, EventArgs e)
    {
        try
        {
            if (_mediaCaptureService.IsAudioCapturing)
            {
                await _mediaCaptureService.StopAudioCaptureAsync();
                ToggleAudioButton.Text = "Start Audio";
                AudioStatusLabel.Text = "Stopped";
                LogMessage("Audio capture stopped");
            }
            else
            {
                var success = await _mediaCaptureService.StartAudioCaptureAsync();
                if (success)
                {
                    ToggleAudioButton.Text = "Stop Audio";
                    AudioStatusLabel.Text = "Capturing";
                    LogMessage("Audio capture started");
                }
                else
                {
                    LogMessage("Failed to start audio capture");
                }
            }
        }
        catch (Exception ex)
        {
            LogMessage($"Error toggling audio: {ex.Message}");
        }
    }

    private async void OnToggleVideoClicked(object sender, EventArgs e)
    {
        try
        {
            if (_mediaCaptureService.IsVideoCapturing)
            {
                await _mediaCaptureService.StopVideoCaptureAsync();
                ToggleVideoButton.Text = "Start Video";
                VideoStatusLabel.Text = "Stopped";
                LogMessage("Video capture stopped");
            }
            else
            {
                var success = await _mediaCaptureService.StartVideoCaptureAsync();
                if (success)
                {
                    ToggleVideoButton.Text = "Stop Video";
                    VideoStatusLabel.Text = "Capturing";
                    LogMessage("Video capture started");
                }
                else
                {
                    LogMessage("Failed to start video capture");
                }
            }
        }
        catch (Exception ex)
        {
            LogMessage($"Error toggling video: {ex.Message}");
        }
    }

    private async void OnDiagnoseClicked(object sender, EventArgs e)
    {
        try
        {
            LogMessage("Starting call connectivity diagnosis...");

            var threadId = ThreadIdEntry.Text?.Trim();
            var friendId = FriendIdEntry.Text?.Trim();

            var report = await _callDebugService.DiagnoseCallConnectivityAsync(threadId, friendId);

            LogMessage("=== CALL DIAGNOSIS REPORT ===");
            LogMessage(report.GetSummary());
            LogMessage("=== END REPORT ===");

            // Show summary in alert
            var alertMessage = $"Status: {report.OverallStatus}\n";
            if (report.Errors.Any())
            {
                alertMessage += $"Issues found: {report.Errors.Count}\n";
                alertMessage += string.Join("\n", report.Errors.Take(3));
                if (report.Errors.Count > 3)
                {
                    alertMessage += $"\n... and {report.Errors.Count - 3} more (see log)";
                }
            }
            else
            {
                alertMessage += "All systems ready for calling!";
            }

            await DisplayAlert("Call Diagnosis", alertMessage, "OK");
        }
        catch (Exception ex)
        {
            LogMessage($"Error during diagnosis: {ex.Message}");
            await DisplayAlert("Error", $"Diagnosis failed: {ex.Message}", "OK");
        }
    }

    private async void OnAudioFlowClicked(object sender, EventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(_currentCallId))
            {
                await DisplayAlert("Info", "No active call to diagnose. Start a call first.", "OK");
                return;
            }

            LogMessage("Starting audio flow diagnosis...");

            // Create a temporary audio flow debug service
            var audioFlowDebugService = new AudioFlowDebugService(
                _chatCallService,
                _mediaCaptureService,
                Application.Current?.Handler?.MauiContext?.Services?.GetRequiredService<IAudioStreamingService>(),
                _signalRService,
                _logger);

            var report = await audioFlowDebugService.DiagnoseAudioFlowAsync(_currentCallId);

            LogMessage("=== AUDIO FLOW DIAGNOSIS REPORT ===");
            LogMessage(report.GetSummary());
            LogMessage("=== END REPORT ===");

            // Show detailed report in alert
            var alertMessage = report.GetSummary();
            if (alertMessage.Length > 500)
            {
                alertMessage = alertMessage.Substring(0, 500) + "...\n\nSee log for full details.";
            }

            await DisplayAlert("Audio Flow Diagnosis", alertMessage, "OK");
        }
        catch (Exception ex)
        {
            LogMessage($"Error during audio flow diagnosis: {ex.Message}");
            await DisplayAlert("Error", $"Audio flow diagnosis failed: {ex.Message}", "OK");
        }
    }

    private void OnClearLogClicked(object sender, EventArgs e)
    {
        _logBuilder.Clear();
        LogLabel.Text = "Log cleared...";
    }

  

    private void OnCallResponseReceived(object? sender, CallResponseMessage e)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            LogMessage($"Call response received: {(e.Accepted ? "Accepted" : "Declined")} by {e.UserName}");

            if (e.Accepted)
            {
                _currentCallId = e.CallId;
                EndCallButton.IsEnabled = true;

                // Set call ID for audio controls
                _audioControls?.SetCallId(e.CallId);
                LogMessage($"Audio controls configured for call {e.CallId} after acceptance");
            }

            UpdateActiveCallsList();
        });
    }

    private void OnCallStatusChanged(object? sender, CallStatusMessage e)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            LogMessage($"Call status changed: {e.Status} for call {e.CallId}");

            if (e.Status == CallStatus.Ended && e.CallId == _currentCallId)
            {
                _currentCallId = null;
                EndCallButton.IsEnabled = false;
            }

            UpdateActiveCallsList();
        });
    }

    private void OnAudioDataCaptured(object? sender, byte[] audioData)
    {
        // Don't log every audio frame to avoid spam
        // LogMessage($"Audio data captured: {audioData.Length} bytes");
    }

    private void OnVideoDataCaptured(object? sender, byte[] videoData)
    {
        // Don't log every video frame to avoid spam
        // LogMessage($"Video data captured: {videoData.Length} bytes");
    }

    private void UpdateActiveCallsList()
    {
        try
        {
            var activeCalls = _chatCallService.GetActiveCalls();
            ActiveCalls.Clear();

            foreach (var call in activeCalls)
            {
                ActiveCalls.Add(call);
            }
        }
        catch (Exception ex)
        {
            LogMessage($"Error updating active calls list: {ex.Message}");
        }
    }

    private void LogMessage(string message)
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss");
        _logBuilder.AppendLine($"[{timestamp}] {message}");

        MainThread.BeginInvokeOnMainThread(() =>
        {
            LogLabel.Text = _logBuilder.ToString();
        });
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();

        // Unsubscribe from events
        //_signalRService.Connected -= OnSignalRConnected;
        //_signalRService.Disconnected -= OnSignalRDisconnected;

        //_chatCallService.CallInvitationReceived -= OnCallInvitationReceived;
        _chatCallService.CallResponseReceived -= OnCallResponseReceived;
        _chatCallService.CallStatusChanged -= OnCallStatusChanged;

        _mediaCaptureService.AudioDataCaptured -= OnAudioDataCaptured;
        _mediaCaptureService.VideoDataCaptured -= OnVideoDataCaptured;
    }
}
