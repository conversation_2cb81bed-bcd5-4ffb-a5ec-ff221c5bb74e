.VetTips-CONTAINER {
    background: #f2f6f9;
    padding: 2rem;
    min-height: 100vh;
    width: 100%;
    max-width: 72rem;
    margin: 0 auto;
    font-family: 'Inter', sans-serif;
}

/* Search section */
.search-container {
    position: sticky;
    top: 0;
    z-index: 10;
    background: inherit;
    padding: 1rem 0;
    margin-bottom: 2rem;
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

.search-box {
    position: relative;
    width: 100%;
}

.search-box input {
    width: 100%;
    padding: 0.875rem 2.5rem;
    background-color: white;
    border: 1px solid #E5E7EB;
    border-radius: 1rem;
    font-size: 1rem;
    color: #374151;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #3B82F6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6B7280;
    font-size: 1rem;
}

/* Featured Articles */
.featured-articles {
    margin-bottom: 3rem;
}

.featured-articles h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 1.5rem;
    font-family: 'Acme', sans-serif;
}

.featured-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(24rem, 1fr));
    gap: 1.5rem;
}

.featured-card {
    background: #157BAB;
    border-radius: 1.25rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    color: white;
}

.featured-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.15);
}

/* All Articles */
.all-articles h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 1.5rem;
    font-family: 'Acme', sans-serif;
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
    gap: 1.5rem;
}

.article-card {
    background: white;
    border-radius: 1.25rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
}

.article-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.15);
}

.article-image-container {
    width: 100%;
    height: 12rem;
    overflow: hidden;
}

.article-image-container img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.article-card:hover .article-image-container img {
    transform: scale(1.05);
}

.article-content {
    padding: 1.5rem;
}

.featured-card .article-content {
    padding: 2rem;
}

.article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.featured-card .article-meta {
    color: #E5E7EB;
}

.article-card .article-meta {
    color: #6B7280;
}

.article-content h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    font-family: 'Acme', sans-serif;
    color: #1F2937;
}

.featured-card .article-content h3 {
    color: white;
}

.article-content p {
    font-size: 0.875rem;
    line-height: 1.5;
    margin-bottom: 1rem;
    color: #6B7280;
}

.featured-card .article-content p {
    color: #E5E7EB;
}

.article-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    background: #F3F4F6;
    color: #3B82F6;
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
}

.featured-card .tag {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

/* Mobile Layout */
@media (max-width: 639px) {
    .VetTips-CONTAINER {
        padding: 1rem;
    }

    .featured-grid,
    .articles-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .article-image-container {
        height: 10rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.VetTips-CONTAINER {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}