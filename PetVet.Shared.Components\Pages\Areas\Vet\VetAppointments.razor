@page "/vet/appointments"
@using CommunityToolkit.Mvvm.Messaging
@inject IAppointmentApi _AppointmentApi
@inject IAppState _AppState
@inject IJSRuntime _JS
@inject NavigationManager _NavigationManager
@inject PetVetAuthStateProvider _PetVetAuthStateProvider

<PageTitle>My Appointments</PageTitle>

<!-- Modern Vet Appointments Page -->
<div class="px-4 py-6 space-y-6">

    <!-- Header Section -->
    <div class="text-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-2">My Appointments</h1>
        <p class="text-gray-600">View and manage your patient appointments</p>
    </div>

    <!-- Quick Stats Cards -->
    <div class="grid grid-cols-3 gap-4 mb-6">
        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-4 text-white text-center">
            <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2">
                <i class="fas fa-clock text-sm"></i>
            </div>
            <div class="text-xl font-bold">@appointments.Count(a => a.Status == nameof(AppointmentStatus.Confirmed))</div>
            <div class="text-xs text-blue-100">Upcoming</div>
        </div>
        <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-2xl p-4 text-white text-center">
            <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2">
                <i class="fas fa-check text-sm"></i>
            </div>
            <div class="text-xl font-bold">@appointments.Count(a => a.Status == nameof(AppointmentStatus.Completed))</div>
            <div class="text-xs text-green-100">Completed</div>
        </div>
        <div class="bg-gradient-to-br from-red-500 to-red-600 rounded-2xl p-4 text-white text-center">
            <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2">
                <i class="fas fa-times text-sm"></i>
            </div>
            <div class="text-xl font-bold">@appointments.Count(a => a.Status == nameof(AppointmentStatus.Cancelled))</div>
            <div class="text-xs text-red-100">Cancelled</div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 mb-6">
        <!-- Status Filters -->
        <div class="flex gap-2 mb-4 overflow-x-auto">
            <button class="px-4 py-2 rounded-xl text-sm font-medium transition-all whitespace-nowrap @(selectedStatus == "All" ? "bg-blue-500 text-white" : "bg-gray-100 text-gray-600 hover:bg-gray-200")"
                    @onclick="@(() => FilterByStatus("All"))">
                All
            </button>
            <button class="px-4 py-2 rounded-xl text-sm font-medium transition-all whitespace-nowrap @(selectedStatus == "Confirmed" ? "bg-blue-500 text-white" : "bg-gray-100 text-gray-600 hover:bg-gray-200")"
                    @onclick="@(() => FilterByStatus("Confirmed"))">
                Upcoming
            </button>
            <button class="px-4 py-2 rounded-xl text-sm font-medium transition-all whitespace-nowrap @(selectedStatus == "Completed" ? "bg-green-500 text-white" : "bg-gray-100 text-gray-600 hover:bg-gray-200")"
                    @onclick="@(() => FilterByStatus("Completed"))">
                Completed
            </button>
            <button class="px-4 py-2 rounded-xl text-sm font-medium transition-all whitespace-nowrap @(selectedStatus == "Cancelled" ? "bg-red-500 text-white" : "bg-gray-100 text-gray-600 hover:bg-gray-200")"
                    @onclick="@(() => FilterByStatus("Cancelled"))">
                Cancelled
            </button>
        </div>

        <!-- Date Filter -->
        <div class="space-y-3">
            <div class="grid grid-cols-2 gap-3">
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">From</label>
                    <input type="date"
                           class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           @bind="startDate" />
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">To</label>
                    <input type="date"
                           class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           @bind="endDate" />
                </div>
            </div>
            <div class="flex gap-2">
                <button class="flex-1 bg-blue-500 text-white py-2 px-4 rounded-xl text-sm font-medium hover:bg-blue-600 transition-all"
                        @onclick="async () => await ApplyDateFilter()">
                    <i class="fas fa-filter mr-1"></i>Apply
                </button>
                <button class="flex-1 bg-gray-100 text-gray-600 py-2 px-4 rounded-xl text-sm font-medium hover:bg-gray-200 transition-all"
                        @onclick="async () => await ResetDateFilter()">
                    <i class="fas fa-undo mr-1"></i>Reset
                </button>
            </div>
        </div>
    </div>

    <!-- Appointments List -->
    <div class="space-y-4">
        @if (!filteredAppointments.Any())
        {
            <div class="bg-white rounded-2xl p-8 text-center shadow-sm border border-gray-100">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-calendar-times text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-bold text-gray-800 mb-2">No Appointments Found</h3>
                <p class="text-gray-500">No appointments match your current filters.</p>
            </div>
        }
        else
        {
            foreach (var appointment in filteredAppointments)
            {
                <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 @GetStatusBorderClass(appointment.Status)">
                    <div class="flex items-start gap-4">
                        <!-- Date Badge -->
                        <div class="flex-shrink-0">
                            <div class="w-16 h-16 @GetStatusBgClass(appointment.Status) rounded-2xl flex flex-col items-center justify-center text-white">
                                <span class="text-xs font-medium">@GetMonthFromDate(appointment.AppointmentDate)</span>
                                <span class="text-lg font-bold">@GetDayFromDate(appointment.AppointmentDate)</span>
                            </div>
                        </div>

                        <!-- Appointment Info -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-start justify-between mb-2">
                                <div>
                                    <h3 class="font-bold text-gray-800">@appointment.PetOwnerName</h3>
                                    <div class="flex items-center gap-2 text-sm text-gray-600">
                                        <i class="fas fa-clock"></i>
                                        <span>@appointment.Time</span>
                                    </div>
                                </div>
                                <span class="px-3 py-1 rounded-full text-xs font-medium @GetStatusClass(appointment.Status)">
                                    @appointment.Status
                                </span>
                            </div>

                            @if (!string.IsNullOrEmpty(appointment.Notes))
                            {
                                <div class="bg-gray-50 rounded-xl p-3 mb-3">
                                    <div class="flex items-start gap-2 text-sm text-gray-600">
                                        <i class="fas fa-clipboard-list mt-0.5"></i>
                                        <span>@appointment.Notes</span>
                                    </div>
                                </div>
                            }

                            <!-- Action Buttons -->
                            <div class="flex gap-2 flex-wrap">
                                @if (appointment.Status == nameof(AppointmentStatus.Confirmed))
                                {
                                    <button class="px-3 py-2 bg-green-100 text-green-700 rounded-xl text-xs font-medium hover:bg-green-200 transition-all"
                                            @onclick="() => MarkComplete(appointment.Id)">
                                        <i class="fas fa-check-circle mr-1"></i>Mark Complete
                                    </button>
                                    <button class="px-3 py-2 bg-red-100 text-red-700 rounded-xl text-xs font-medium hover:bg-red-200 transition-all"
                                            @onclick="() => CancelAppointment(appointment.Id)">
                                        <i class="fas fa-times-circle mr-1"></i>Cancel
                                    </button>
                                }
                                <button class="px-3 py-2 bg-blue-100 text-blue-700 rounded-xl text-xs font-medium hover:bg-blue-200 transition-all"
                                        @onclick="() => Contact(appointment.PetOwnerId, appointment.PetOwnerName, appointment.PetOwnerNameDisplayPicture)">
                                    <i class="fas fa-comments mr-1"></i>Message Patient
                                </button>
                                <button class="px-3 py-2 bg-purple-100 text-purple-700 rounded-xl text-xs font-medium hover:bg-purple-200 transition-all"
                                        @onclick="() => ViewDetails(appointment)">
                                    <i class="fas fa-eye mr-1"></i>View Details
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
    </div>

    <!-- Calendar Link -->
    <div class="mt-6">
        <a href="/vet/calendar"
           class="w-full bg-gradient-to-r from-purple-500 to-indigo-600 text-white py-4 px-6 rounded-2xl font-medium hover:from-purple-600 hover:to-indigo-700 transition-all transform hover:scale-105 shadow-lg flex items-center justify-center gap-2">
            <i class="fas fa-calendar"></i>
            <span>View Calendar</span>
        </a>
    </div>

</div>

<!-- Modern Appointment Details Modal -->
@if (isDetailsModalOpen && selectedAppointment != null)
{
    <Modal Title="Appointment Details"
           ActionButtonText="Close"
           OnActionButtonClick="CloseDetailsModal"
           OnCancelButtonClick="CloseDetailsModal"
           IsVisible="isDetailsModalOpen"
           Size="ModalSize.Large">

        <div class="space-y-4">
            <!-- Patient Info -->
            <div class="bg-gray-50 rounded-xl p-4">
                <div class="flex items-center gap-3 mb-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-blue-600"></i>
                    </div>
                    <div>
                        <h3 class="font-bold text-gray-800">Patient Information</h3>
                        <p class="text-sm text-gray-600">@selectedAppointment.PetOwnerName</p>
                    </div>
                </div>
            </div>

            <!-- Appointment Details -->
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-gray-50 rounded-xl p-4">
                    <div class="flex items-center gap-2 mb-2">
                        <i class="fas fa-calendar text-purple-600"></i>
                        <span class="text-sm font-medium text-gray-700">Date</span>
                    </div>
                    <p class="font-bold text-gray-800">@FormatAppointmentDate(selectedAppointment.AppointmentDate)</p>
                </div>

                <div class="bg-gray-50 rounded-xl p-4">
                    <div class="flex items-center gap-2 mb-2">
                        <i class="fas fa-clock text-blue-600"></i>
                        <span class="text-sm font-medium text-gray-700">Time</span>
                    </div>
                    <p class="font-bold text-gray-800">@selectedAppointment.Time</p>
                </div>
            </div>

            <!-- Status -->
            <div class="bg-gray-50 rounded-xl p-4">
                <div class="flex items-center gap-2 mb-2">
                    <i class="fas fa-info-circle text-green-600"></i>
                    <span class="text-sm font-medium text-gray-700">Status</span>
                </div>
                <span class="px-3 py-1 rounded-full text-sm font-medium @GetStatusClass(selectedAppointment.Status)">
                    @selectedAppointment.Status
                </span>
            </div>

            <!-- Notes -->
            @if (!string.IsNullOrEmpty(selectedAppointment.Notes))
            {
                <div class="bg-gray-50 rounded-xl p-4">
                    <div class="flex items-center gap-2 mb-2">
                        <i class="fas fa-clipboard-list text-orange-600"></i>
                        <span class="text-sm font-medium text-gray-700">Notes</span>
                    </div>
                    <p class="text-gray-800">@selectedAppointment.Notes</p>
                </div>
            }

            <!-- Actions -->
            <div class="pt-4 border-t border-gray-200">
                <button class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-6 rounded-xl font-medium hover:from-blue-600 hover:to-purple-700 transition-all"
                        @onclick="() => Contact(selectedAppointment.PetOwnerId, selectedAppointment.PetOwnerName, selectedAppointment.PetOwnerNameDisplayPicture)">
                    <i class="fas fa-comments mr-2"></i>Message Patient
                </button>
            </div>
        </div>
    </Modal>
}

<!-- Confirmation Modal -->
<BsModal OnModalConfirmation="ConfirmCancel_Click"
         ButtonText="Cancel Appointment"
         Title="Are you sure you want to cancel this appointment?">
</BsModal>

@code {
    private int _currentUserId;
    private List<AppointmentDto> appointments = new();
    private List<AppointmentDto> filteredAppointments = new();
    private string selectedStatus = "All";
    private DateTime startDate = DateTime.Today;
    private DateTime endDate = DateTime.Today.AddDays(30);
    private bool isDateFilterActive = true;

    // Modal state
    private bool isDetailsModalOpen = false;
    private AppointmentDto selectedAppointment;

    protected override async Task OnInitializedAsync()
    {
        _currentUserId = _PetVetAuthStateProvider.User.Id;
        await LoadAppointments();
    }

    private async Task LoadAppointments()
    {
        try
        {
            _AppState.ShowLoader("Loading Appointments");
            appointments = await _AppointmentApi.GetVetAppointmentsAsync(_currentUserId);
            ApplyFilters();
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error loading appointments: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private void FilterByStatus(string status)
    {
        selectedStatus = status;
        ApplyFilters();
    }

    private async Task ApplyDateFilter()
    {
        if (endDate < startDate)
        {
            await _JS.ToastrError("End date cannot be earlier than start date");
            return;
        }

        isDateFilterActive = true;
        ApplyFilters();
        await _JS.ToastrSuccess("Date filter applied");
    }

    private async Task ResetDateFilter()
    {
        startDate = DateTime.Today;
        endDate = DateTime.Today.AddDays(30);
        isDateFilterActive = true;
        ApplyFilters();
        await _JS.ToastrSuccess("Date filter reset to default range");
    }

    private void ApplyFilters()
    {
        filteredAppointments = appointments.Where(a =>
        {
            bool matchesStatus = selectedStatus == "All" || a.Status == selectedStatus;

            if (!DateTime.TryParse(a.AppointmentDate, out DateTime appointmentDate))
            {
                return matchesStatus;
            }

            bool matchesDateRange = !isDateFilterActive || (appointmentDate.Date >= startDate.Date && appointmentDate.Date <= endDate.Date);
            return matchesStatus && matchesDateRange;

        }).ToList();

        // Sort by date and time
        filteredAppointments = filteredAppointments
            .OrderByDescending(a => DateTime.TryParse(a.AppointmentDate, out DateTime dt) ? dt : DateTime.MinValue)
            .ThenBy(a => a.Time)
            .ToList();
    }

    private string GetStatusClass(string status)
    {
        return status switch
        {
            nameof(AppointmentStatus.Confirmed) => "bg-blue-100 text-blue-700",
            nameof(AppointmentStatus.Completed) => "bg-green-100 text-green-700",
            nameof(AppointmentStatus.Cancelled) => "bg-red-100 text-red-700",
            nameof(AppointmentStatus.Expired) => "bg-gray-100 text-gray-700",
            _ => "bg-gray-100 text-gray-700"
        };
    }

    private string GetStatusBgClass(string status)
    {
        return status switch
        {
            nameof(AppointmentStatus.Confirmed) => "bg-gradient-to-br from-blue-500 to-blue-600",
            nameof(AppointmentStatus.Completed) => "bg-gradient-to-br from-green-500 to-green-600",
            nameof(AppointmentStatus.Cancelled) => "bg-gradient-to-br from-red-500 to-red-600",
            nameof(AppointmentStatus.Expired) => "bg-gradient-to-br from-gray-500 to-gray-600",
            _ => "bg-gradient-to-br from-gray-500 to-gray-600"
        };
    }

    private string GetStatusBorderClass(string status)
    {
        return status switch
        {
            nameof(AppointmentStatus.Confirmed) => "border-l-4 border-l-blue-500",
            nameof(AppointmentStatus.Completed) => "border-l-4 border-l-green-500",
            nameof(AppointmentStatus.Cancelled) => "border-l-4 border-l-red-500",
            nameof(AppointmentStatus.Expired) => "border-l-4 border-l-gray-500",
            _ => ""
        };
    }

    private string GetMonthFromDate(string dateString)
    {
        if (DateTime.TryParse(dateString, out DateTime date))
        {
            return date.ToString("MMM");
        }
        return "";
    }

    private string GetDayFromDate(string dateString)
    {
        if (DateTime.TryParse(dateString, out DateTime date))
        {
            return date.Day.ToString();
        }
        return "";
    }

    private string FormatAppointmentDate(string dateString)
    {
        if (DateTime.TryParse(dateString, out DateTime date))
        {
            return date.ToString("MMMM d, yyyy");
        }
        return dateString;
    }

    private void ViewDetails(AppointmentDto appointment)
    {
        selectedAppointment = appointment;
        isDetailsModalOpen = true;
    }

    private void CloseDetailsModal()
    {
        isDetailsModalOpen = false;
        selectedAppointment = null;
    }

    private void Contact(int petOwnerId, string petOwnerName, string petOwnerDisplayPicture)
    {
        // Navigate to chat with the patient
        //_NavigationManager.NavigateTo($"/vet/chat?userId={petOwnerId}");
        WeakReferenceMessenger.Default.Send<Tuple<string, int, string, string>>(new Tuple<string, int, string, string>("StartChat", petOwnerId, petOwnerName, petOwnerDisplayPicture));
    }

    private async Task MarkComplete(int appointmentId)
    {
        try
        {
            _AppState.ShowLoader("Updating appointment");
            var response = await _AppointmentApi.UpdateAppointmentStatusAsync(appointmentId, nameof(AppointmentStatus.Completed));
            if (response.IsSuccess)
            {
                await _JS.ToastrSuccess("Appointment marked as completed");
                await LoadAppointments();
            }
            else
            {
                await _JS.ToastrError($"Failed to update appointment: {response.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error updating appointment: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private async Task CancelAppointment(int appointmentId)
    {
        try
        {
            _AppState.ShowLoader("Cancelling appointment");
            var response = await _AppointmentApi.UpdateAppointmentStatusAsync(appointmentId, nameof(AppointmentStatus.Cancelled));
            if (response.IsSuccess)
            {
                await _JS.ToastrSuccess("Appointment cancelled successfully");
                await LoadAppointments();
            }
            else
            {
                await _JS.ToastrError($"Failed to cancel appointment: {response.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error cancelling appointment: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private async Task ConfirmCancel_Click(bool isConfirmed)
    {
        if (isConfirmed && selectedAppointment != null)
        {
            await CancelAppointment(selectedAppointment.Id);
        }
    }
}