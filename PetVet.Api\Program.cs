using DeepMessage.Server.DataServices.Features.Configurations;
using DeepMessage.ServiceContracts.Features.Configurations;
using FirebaseAdmin;
using Google.Apis.Auth.OAuth2;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.IdentityModel.Tokens;
using PetVet.Api.Data;
using PetVet.Api.Data.Entities;
using PetVet.Api.Endpoints;
using PetVet.Api.Hubs;
using PetVet.Api.Services;
using PetVet.Api.Services.ChatMessages.Form;
using PetVet.Api.Services.ChatMessages.Listing;
using PetVet.Api.Services.ChatThreads.Form;
using PetVet.Api.Services.ChatThreads.Listing;
using PetVet.Server.DataServices.Helpers;
using PetVet.ServiceContracts.Features.Conversation;
using PetVet.Shared.Models.AzureOpenAI;
using PetVet.Shared.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

builder.Services.AddTransient<IPasswordHasher<User>, PasswordHasher<User>>();

builder.Services.AddHttpContextAccessor();

var connectionString = builder.Configuration.GetConnectionString("PetVet");

builder.Services.AddDbContext<PetVetContext>(options =>
{
    options.UseSqlServer(connectionString);
}, optionsLifetime: ServiceLifetime.Singleton);

builder.Services.AddDbContextFactory<PetVetContext>(options =>
{
    options.UseSqlServer(connectionString);
});

builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    var secretKey = builder.Configuration.GetValue<string>("Jwt:Secret");
    var symmetricKey = new SymmetricSecurityKey(System.Text.Encoding.UTF8.GetBytes(secretKey));
    options.TokenValidationParameters = new TokenValidationParameters
    {
        IssuerSigningKey = symmetricKey,
        ValidIssuer = builder.Configuration.GetValue<string>("Jwt:Issuer"),
        ValidAudience = builder.Configuration.GetValue<string>("Jwt:Audience"),
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateIssuerSigningKey = true,
    };

    options.Events = new JwtBearerEvents
    {
        OnMessageReceived = (context) =>
        {
            if (context.Request.Path.StartsWithSegments("/hubs/petvet-chat"))
            {
                var jwt = context.Request.Query["access-token"];

                if (!string.IsNullOrEmpty(jwt))
                {
                    context.Token = jwt;
                }
            }
            return Task.CompletedTask;
        }
    };
});

builder.Services.AddAuthorization();

builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(p =>
    {
             p.AllowAnyOrigin()
            .AllowAnyHeader()
            .AllowAnyMethod();
    });
});

builder.Services.AddSingleton<IMemoryCache, MemoryCache>();
builder.Services.AddHttpClient();

// Configure Azure OpenAI
builder.Services.Configure<AzureOpenAIConfig>(
    builder.Configuration.GetSection("AzureOpenAI"));

// Register Azure OpenAI service
builder.Services.AddHttpClient<IAzureOpenAIService, AzureOpenAIService>();
builder.Services.AddScoped<IAzureOpenAIService, AzureOpenAIService>();

builder.Services.AddScoped<PetService>();
builder.Services.AddScoped<CategoryService>();
builder.Services.AddScoped<AuthService>();
builder.Services.AddScoped<UserService>();
builder.Services.AddScoped<EducationService>();
builder.Services.AddScoped<AppointmentService>();
builder.Services.AddScoped<MessageService>();
builder.Services.AddScoped<SmsService>();
// Twilio SMS notification services
builder.Services.AddScoped<TwilioSmsService>();
builder.Services.AddScoped<SmsNotificationService>();
// builder.Services.AddScoped<FileUploadService>(); // Comment out or remove if this service doesn't exist
builder.Services.AddSingleton<MLNetBreedClassificationService>(); // Singleton for performance
builder.Services.AddScoped<BreedService>();
builder.Services.AddScoped<RecentActivityService>();


builder.Services.AddTransient<IChatThreadsListingDataService, ChatThreadsServerSideListingDataService>();
builder.Services.AddTransient<IStartChatFormDataService, StartChatServerSideFormDataService>();
builder.Services.AddTransient<IChatMessagesListingDataService, ChatMessagesServerSideListingDataService>();
builder.Services.AddTransient<IChatMessageFormDataService, ChatMessageServerSideFormDataService>(); 

builder.Services.AddScoped<IChatThreadSyncFormDataService, ChatThreadSyncServerSideFormDataService>();
builder.Services.AddScoped<IChatMessagesSyncFormDataService, ChatMessagesSyncServerSideFormDataService>();
builder.Services.AddScoped<IDeviceTokenFormDataService, DeviceTokenServerSideFormDataService>();
builder.Services.AddSingleton<ChatHub>();
builder.Services.AddSingleton<MessageDispatcher>();

builder.Services.AddControllers();
builder.Services.AddSignalR(x =>
{
    x.EnableDetailedErrors = true;
    
});

var app = builder.Build();

#if DEBUG
ApplyDbMigrations(app.Services);
#endif

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseCors();
app.MapHub<ChatHub>("/hubs/chathub");
app.UseHttpsRedirection();
app.UseAuthentication()
    .UseAuthorization();

app.MapAuthEndpoints()
    .MapCategoryEndpoints()
    .MapEducationEndpoints()
    .MapPetEndpoints()
    .MapUserEndpoints()
    .MapAppointmentEndpoints()
    .MapMessageEndpoints()
    //.MapFileUploadEndpoints() // Comment out or remove if these endpoints don't exist
    .MapBreedEndpoints()
    .MapRecentActivityEndpoints();
app.MapControllers();
//app.MapHub<PetVetChatHub>("/hubs/petvet-chat");

FirebaseApp app_ = FirebaseApp.Create(new AppOptions()
{
    Credential = GoogleCredential.FromFile("service-account.json"),
});
Console.WriteLine("PetVet API is running...");
app.Run();
static void ApplyDbMigrations(IServiceProvider sp)
{
    var scope = sp.CreateScope();
    var context = scope.ServiceProvider.GetRequiredService<PetVetContext>();
    if (context.Database.GetPendingMigrations().Any())
    {
        context.Database.Migrate();
    }
}

