﻿using PetVet.Api.Services;
using PetVet.Shared.DTOs;
using PetVet.Shared;

namespace PetVet.Api.Endpoints
{
    public static class EducationEndpoints
    {
        public static IEndpointRouteBuilder MapEducationEndpoints(this IEndpointRouteBuilder app)
        {
            var categoryGroup = app.MapGroup("/api/education");

            categoryGroup.MapGet("/{id:int}", async (int id, EducationService educationService) =>
            Results.Ok(await educationService.GetEducationAsync(id)));

            categoryGroup.MapGet("", async (EducationService educationService) =>
                Results.Ok(await educationService.GetEducationListAsync()));

            categoryGroup.MapPost("", async (EducationDto dto, EducationService educationService) =>
                Results.Ok(await educationService.SaveEducationAsync(dto)))
                .RequireAuthorization(p => p.RequireRole(nameof(UserRole.Admin)));

            categoryGroup.MapDelete("/{id:int}", async (int id, EducationService educationService) =>
                Results.Ok(await educationService.DeleteEducationAsync(id)))
                .RequireAuthorization(p => p.RequireRole(nameof(UserRole.Admin)));

            return app;
        }
    }
}
