﻿namespace PetVet.Shared.DTOs
{
    public record PetVetApiResponse(bool IsSuccess, string? ErrorMessage)
    {
        public static PetVetApiResponse Success() => new(true, null);
        public static PetVetApiResponse Fail(string errorMessage) => new(false, errorMessage);
    }


    public record PetVetApiResponse<TData>(TData Data, bool IsSuccess, string? ErrorMessage)
    {
        public static PetVetApiResponse<TData> Success(TData data) => new(data, true, null);
        public static PetVetApiResponse<TData> Fail(string errorMessage) => new(default!, false, errorMessage);
    }
}
