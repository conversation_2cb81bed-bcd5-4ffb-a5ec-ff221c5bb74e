﻿<?xml version="1.0" encoding="utf-8" ?>
<local:ChatMessagesListingViewBase
    x:Class="Platform.Client.Common.Features.Conversation.ChatMessagesListingView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Conversation"
    x:DataType="local:ChatMessagesListingView"
    BackgroundColor="#3E97DA"
    NavigationPage.HasBackButton="False"
    NavigationPage.HasNavigationBar="True">
    <NavigationPage.TitleView>
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="36" />
                <ColumnDefinition Width="44" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="40" />
                <ColumnDefinition Width="40" />
                <ColumnDefinition Width="40" />
            </Grid.ColumnDefinitions>
            <Grid.GestureRecognizers>
                <TapGestureRecognizer Command="{Binding BackCommand}" />
            </Grid.GestureRecognizers>

            <!--  Back Button  -->
            <Image
                HorizontalOptions="Start"
                MaximumHeightRequest="24"
                MaximumWidthRequest="24"
                Source="left.svg" />

            <!--  Profile Image  -->
            <Border
                Grid.Column="1"
                HeightRequest="36"
                HorizontalOptions="Start"
                WidthRequest="36">
                <Border.StrokeShape>
                    <RoundRectangle CornerRadius="45" />
                </Border.StrokeShape>
                <Image Source="{Binding ProfileImage}" />
            </Border>

            <!--  Friend Name and Status  -->
            <StackLayout Grid.Column="2" VerticalOptions="Center">
                <Label
                    FontAttributes="Bold"
                    FontSize="18"
                    HorizontalOptions="Start"
                    Text="{Binding FriendName}"
                    TextColor="White"
                    VerticalOptions="Center" />
                <Label
                    x:Name="CallStatusLabel"
                    FontSize="12"
                    HorizontalOptions="Start"
                    IsVisible="True"
                    Text="Online"
                    TextColor="#E0E0E0" />
            </StackLayout>

            <!--  Audio Call Button  -->
            <ImageButton
                x:Name="AudioCallButton"
                Grid.Column="3"
                BackgroundColor="Transparent"
                Clicked="OnAudioCallClicked"
                CornerRadius="18"
              Source="phone_solid.png"
                HeightRequest="36"
                 
                ToolTipProperties.Text="Start Audio Call"
                WidthRequest="36" />

            <!--  Video Call Button  -->
            <ImageButton
                x:Name="VideoCallButton"
                Grid.Column="4"
                BackgroundColor="Transparent"
                Clicked="OnVideoCallClicked"
                CornerRadius="18" 
                HeightRequest="36"
               Source="video_solid.png"
                ToolTipProperties.Text="Start Video Call"
                WidthRequest="36" />

            <!--  Emergency Call Button 
            <Button
                x:Name="EmergencyCallButton"
                Grid.Column="5"
                BackgroundColor="#EF4444"
                Clicked="OnEmergencyCallClicked"
                CornerRadius="18"
                FontSize="16"
                HeightRequest="36"
                Text="🚨"
                TextColor="White"
                ToolTipProperties.Text="Emergency Call"
                WidthRequest="36" /> -->
        </Grid>
    </NavigationPage.TitleView>
    <Shell.BackButtonBehavior>
        <BackButtonBehavior IsVisible="False" />
    </Shell.BackButtonBehavior>
    <!--  Page content goes here  -->
    <ContentPage.Resources>
        <DataTemplate x:Key="DateSeparatorTemplate" x:DataType="local:DateSeparatorItem">
            <Border
                Margin="8"
                BackgroundColor="#E5F3FF"
                HorizontalOptions="Center"
                StrokeShape="RoundRectangle 8"
                StrokeThickness="0"
                VerticalOptions="Start"
                WidthRequest="110">
                <Label
                    Padding="8"
                    FontAttributes="Bold"
                    FontSize="Small"
                    HorizontalOptions="Fill"
                    HorizontalTextAlignment="Center"
                    Text="{Binding Date, StringFormat='{0:dd MMM yyyy}'}"
                    TextColor="#2875C7" />
            </Border>
        </DataTemplate>
        <DataTemplate x:Key="MessageTemplate" x:DataType="local:ChatMessagesListingViewModel">
            <VerticalStackLayout>
                <Border
                    Margin="8,4,8,4"
                    Padding="10"
                    BackgroundColor="{Binding BackgroundColor}"
                    HorizontalOptions="{Binding HorizontalOptions}"
                    MaximumWidthRequest="250"
                    MinimumWidthRequest="150"
                    Stroke="#EAEAEA"
                    StrokeShape="{Binding BubbleShape}"
                    StrokeThickness="1">
                    <Label
                        FontSize="16"
                        Text="{Binding Content}"
                        TextColor="{Binding MessageColor}" />
                </Border>
                <HorizontalStackLayout Margin="12,0,8,8" HorizontalOptions="{Binding HorizontalOptions}">
                    <Label
                        FontAttributes="Bold"
                        FontSize="11"
                        HorizontalOptions="End"
                        Text="{Binding TimeOfTheDay}"
                        TextColor="{Binding TimestampColor}" />
                    <Image
                        Margin="4,0,0,0"
                        HeightRequest="12"
                        Source="{Binding DeliverySymbol}" />
                </HorizontalStackLayout>
            </VerticalStackLayout>
        </DataTemplate>
        <local:ChatTemplateSelector
            x:Key="ChatTemplateSelector"
            DateSeparatorTemplate="{StaticResource DateSeparatorTemplate}"
            MessageTemplate="{StaticResource MessageTemplate}" />
    </ContentPage.Resources>
    <ContentPage.Content>
        <Border BackgroundColor="#F4F4F5" StrokeThickness="0">
            <Border.StrokeShape>
                <RoundRectangle CornerRadius="16,16,0,0" />
            </Border.StrokeShape>
            <Grid RowDefinitions="Auto, *, 64">

                <!--  Active Call Overlay  -->
                <Frame
                    x:Name="ActiveCallFrame"
                    Grid.Row="0"
                    Padding="15,10"
                    BackgroundColor="#2875C7"
                    CornerRadius="0"
                    HasShadow="True"
                    IsVisible="False">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <StackLayout
                            Grid.Column="0"
                            Orientation="Horizontal"
                            VerticalOptions="Center">
                            <Ellipse
                                Fill="#45C1A5"
                                HeightRequest="8"
                                VerticalOptions="Center"
                                WidthRequest="8" />
                            <Label
                                x:Name="ActiveCallStatusLabel"
                                Margin="8,0,0,0"
                                FontAttributes="Bold"
                                FontSize="14"
                                Text="Call in progress"
                                TextColor="White"
                                VerticalOptions="Center" />
                            <Label
                                x:Name="ActiveCallDurationLabel"
                                Margin="10,0,0,0"
                                FontSize="12"
                                Text="00:00"
                                TextColor="#E0E0E0"
                                VerticalOptions="Center" />
                        </StackLayout>

                        <StackLayout
                            Grid.Column="1"
                            Orientation="Horizontal"
                            Spacing="10">
                            <Button
                                x:Name="CallMuteButton"
                                BackgroundColor="#45C1A5"
                                Clicked="OnCallMuteClicked"
                                CornerRadius="16"
                                FontSize="14"
                                HeightRequest="32"
                                Text="🎤"
                                TextColor="White"
                                WidthRequest="32" />
                            <Button
                                x:Name="CallVideoButton"
                                BackgroundColor="#45C1A5"
                                Clicked="OnCallVideoClicked"
                                CornerRadius="16"
                                FontSize="14"
                                HeightRequest="32"
                                Text="📹"
                                TextColor="White"
                                WidthRequest="32" />
                            <Button
                                x:Name="CallEndButton"
                                BackgroundColor="#EF4444"
                                Clicked="OnCallEndClicked"
                                CornerRadius="16"
                                FontSize="14"
                                HeightRequest="32"
                                Text="📞"
                                TextColor="White"
                                WidthRequest="32" />
                        </StackLayout>
                    </Grid>
                </Frame>

                <CollectionView
                    x:Name="collection"
                    Grid.Row="1"
                    ItemTemplate="{StaticResource ChatTemplateSelector}"
                    ItemsSource="{Binding Items}"
                    ItemsUpdatingScrollMode="KeepItemsInView"
                    Scrolled="OnCollectionViewScrolled"
                    SizeChanged="CollectionView_SizeChanged" />
                <Grid
                    Grid.Row="2"
                    Padding="8"
                    BackgroundColor="White"
                    RowSpacing="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*" />
                        <ColumnDefinition Width="44" />
                    </Grid.ColumnDefinitions>
                    <Border
                        Margin="8,0,8,0"
                        BackgroundColor="#F4F4F5"
                        Stroke="#F4F4F5">
                        <Border.StrokeShape>
                            <RoundRectangle CornerRadius="45" />
                        </Border.StrokeShape>
                        <Grid>
                            <Editor
                                Grid.Column="0"
                                Margin="8,0,36,0"
                                FontSize="16"
                                Placeholder="Type a message"
                                Text="{Binding SelectedItem.Content, Mode=TwoWay}" />

                            <ImageButton
                                Margin="8,0,8,0"
                                HorizontalOptions="End"
                                MaximumHeightRequest="20"
                                MaximumWidthRequest="20"
                                Source="paperclip_regular.svg" />
                        </Grid>
                    </Border>
                    <ImageButton
                        Grid.Column="1"
                        Padding="12"
                        BackgroundColor="#3E97DA"
                        BorderColor="Transparent"
                        Command="{Binding SaveCommand}"
                        CornerRadius="45"
                        MaximumHeightRequest="44"
                        MaximumWidthRequest="44"
                        Source="paper_plane_top_solid.svg" />

                </Grid>
                <Border
                    x:Name="borderDateOverlay"
                    Margin="8"
                    BackgroundColor="#E5F3FF"
                    HeightRequest="32"
                    HorizontalOptions="Center"
                    IsVisible="False"
                    StrokeShape="RoundRectangle 8"
                    StrokeThickness="0"
                    VerticalOptions="Start"
                    WidthRequest="110">
                    <Label
                        x:Name="lblDateOverlay"
                        Padding="8"
                        FontAttributes="Bold"
                        FontSize="Small"
                        HorizontalOptions="Fill"
                        HorizontalTextAlignment="Center"
                        TextColor="#2875C7" />
                </Border>
            </Grid>
        </Border>
    </ContentPage.Content>
</local:ChatMessagesListingViewBase>
