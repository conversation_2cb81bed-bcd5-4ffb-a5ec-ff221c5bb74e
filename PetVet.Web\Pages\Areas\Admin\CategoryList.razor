﻿@page "/admin/manage/categories"
@inject ICategoryApi _CategoryApi
@inject IJSRuntime _JS
@inject IAppState _AppState

<PageTitle>Manage Categories</PageTitle>

<BsModal OnModalConfirmation="ConfirmDelete_Click"  
ButtonText="Delete" 
Title="Are you sure you want to delete this category?">
</BsModal>

@if(!isProcessing)
{
    <div class="bg-gray-50 p-8 min-h-screen w-full max-w-6xl mx-auto">
        <div class="bg-white rounded-3xl shadow-xl border border-gray-200 overflow-hidden">
            <!-- Header -->
            <div class="bg-primary-500 rounded-t-3xl p-8 text-center text-white">
                <h2 class="text-3xl font-bold py-2 font-acme">Category List <i class="fas fa-th-large ml-2"></i></h2>
            </div>
            
            <!-- Body -->
            <div class="p-8">
                <!-- Add New Category Button -->
                <div class="flex justify-end mb-6">
                    <a href="admin/manage/categories/create" 
                       class="inline-flex items-center space-x-2 bg-primary-500 hover:bg-primary-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 hover:transform hover:scale-105 shadow-lg">
                        <i class="fas fa-plus-circle"></i>
                        <span>Add New Category</span>
                    </a>
                </div>

                @if (!_categories.Any())
                {
                    <div class="text-center bg-yellow-50 border border-yellow-200 text-yellow-800 p-6 rounded-xl">
                        <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                        <p class="font-medium">No Categories Available.</p>
                    </div>
                }
                else
                {
                    <!-- Table Container -->
                    <div class="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900">#</th>
                                        <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900">Name</th>
                                        <th class="px-6 py-4 text-center text-sm font-semibold text-gray-900">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    @{
                                        int number = 1;
                                    }
                                    @foreach (var category in _categories)
                                    {
                                        <tr class="hover:bg-gray-50 transition-colors duration-150">
                                            <td class="px-6 py-4 text-sm text-gray-700">@(number++)</td>
                                            <td class="px-6 py-4 text-sm font-medium text-gray-900">@category.Name</td>
                                            <td class="px-6 py-4 text-center">
                                                <div class="flex justify-center space-x-2">
                                                    <a href="@($"/admin/manage/categories/update/{category.Id}")" 
                                                       class="inline-flex items-center space-x-1 bg-yellow-500 hover:bg-yellow-600 text-white text-xs font-medium py-2 px-3 rounded-lg transition-colors duration-200">
                                                        <i class="fas fa-edit"></i>
                                                        <span>Edit</span>
                                                    </a>
                                                    
                                                    <button @onclick="() => HandleDelete(category.Id)"
                                                            class="inline-flex items-center space-x-1 bg-red-500 hover:bg-red-600 text-white text-xs font-medium py-2 px-3 rounded-lg transition-colors duration-200">
                                                        <i class="fas fa-trash"></i>
                                                        <span>Delete</span>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
}

@code {
    private CategoryDto[] _categories = [];
    private int DeleteCategoryID { get; set; } = 0;
    private bool isProcessing { get; set; } = true;

    protected override async Task OnInitializedAsync() => await LoadCategoriesAsync();

    private async Task LoadCategoriesAsync()
    {
        isProcessing = true;
        _AppState.ShowLoader("Fetching Categories Data");
        _categories = await _CategoryApi.GetCategoriesAsync();
        _AppState.HideLoader();
        isProcessing = false;
    }

    private async Task HandleDelete(int id)
    {
        DeleteCategoryID = id;
        await _JS.InvokeVoidAsync("ShowConfirmationModal");
    }

    [JSInvokable]
    public async Task ConfirmDelete_Click(bool isConfirmed)
    {
        await _JS.InvokeVoidAsync("HideConfirmationModal");

        _AppState.ShowLoader("Deleting Category Data");

        if (isConfirmed && DeleteCategoryID != 0)
        {
            var result = await _CategoryApi.DeleteCategoryAsync(DeleteCategoryID);

            if (result.IsSuccess)
            {
                await _JS.InvokeVoidAsync("toastr.success", "Category deleted successfully.");
            }
            else
            {
                await _JS.InvokeVoidAsync("toastr.error", "Failed to delete category.");
            }
            await LoadCategoriesAsync();
        }
        DeleteCategoryID = 0;

        _AppState.HideLoader();

        StateHasChanged();
    }
}