﻿@page "/admin/manage/education/create"
@page "/admin/manage/education/update/{Id:int}"
@inject IEducationApi _EducationApi
@inject IJSRuntime _JS
@inject IAppState _AppState
@inject NavigationManager _NavigationManager

<PageTitle>@(Id == 0 ? "Create Education" : "Update Education")</PageTitle>

<div class="bg-gray-50 p-8 min-h-screen w-full max-w-2xl mx-auto">
    <div class="bg-white rounded-3xl shadow-xl border border-gray-200 overflow-hidden">
        <!-- Header -->
        <div class="bg-primary-500 rounded-t-3xl p-8 text-center text-white">
            <h2 class="text-3xl font-bold py-2 font-acme">
                @(Id == 0 ? "Create New Education" : "Update Education") 
                <i class="fas fa-graduation-cap ml-2"></i>
            </h2>
        </div>
        
        <!-- Body -->
        <div class="p-8">
            <EditForm Model="_education" OnValidSubmit="UpsertEducationAsync">
                <DataAnnotationsValidator />

                <!-- Form Fields -->
                <div class="space-y-6">
                    <div class="floating-label-group">
                        <InputText @bind-Value="_education.Name" class="floating-label-input" id="EducationName" placeholder=" " />
                        <label for="EducationName" class="floating-label">Education Name</label>
                        <ValidationMessage For="() => _education.Name" />
                    </div>
                </div>

                <!-- Error Alert -->
                @if (!string.IsNullOrEmpty(_error))
                {
                    <div class="bg-red-50 border border-red-200 text-red-800 p-4 rounded-xl mt-6 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle mr-3"></i>
                            <span>@_error</span>
                        </div>
                        <button type="button" class="text-red-600 hover:text-red-800 text-xl" @onclick="() => _error = null">
                            &times;
                        </button>
                    </div>
                }

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-between mt-8">
                    <div class="flex gap-4">
                        <button type="submit" 
                                class="inline-flex items-center space-x-2 bg-primary-500 hover:bg-primary-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 hover:transform hover:scale-105 shadow-lg">
                            <i class="fas @(Id == 0 ? "fa-plus" : "fa-save")"></i>
                            <span>@(Id == 0 ? "Create Education" : "Update Education")</span>
                        </button>
                    </div>

                    <div class="flex gap-4">
                        <a href="/admin/manage/education" 
                           class="inline-flex items-center space-x-2 bg-gray-500 hover:bg-gray-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 hover:transform hover:scale-105 shadow-lg">
                            <i class="fas fa-arrow-left"></i>
                            <span>Back To List</span>
                        </a>
                    </div>
                </div>
            </EditForm>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public int Id { get; set; } = 0;

    private EducationDto _education = new();
    private string _error = string.Empty;
    private bool _isBusy = false;

    protected override async Task OnInitializedAsync()
    {
        if (Id != 0)
        {
            _AppState.ShowLoader("Fetching Education Data");
            _education = await _EducationApi.GetEducationAsync(Id);
            _AppState.HideLoader();
        }
    }

    private async Task UpsertEducationAsync()
    {
        if (_isBusy) return;

        try
        {
            _error = string.Empty;
            _isBusy = true;
            _AppState.ShowLoader(Id == 0 ? "Creating Education" : "Updating Education");

            var result = await _EducationApi.SaveEducationAsync(_education);

            if (result.IsSuccess)
            {
                await _JS.InvokeVoidAsync("toastr.success", Id == 0 ? "Education created successfully." : "Education updated successfully.");
                _NavigationManager.NavigateTo("/admin/manage/education");
            }
            else
            {
                _error = result.ErrorMessage ?? "Operation failed.";
            }
        }
        catch (Exception ex)
        {
            _error = $"An error occurred: {ex.Message}";
        }
        finally
        {
            _isBusy = false;
            _AppState.HideLoader();
        }
    }
}