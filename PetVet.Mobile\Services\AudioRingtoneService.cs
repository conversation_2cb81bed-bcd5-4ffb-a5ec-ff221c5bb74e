using Microsoft.Extensions.Logging;
using Plugin.Maui.Audio;

namespace PetVet.Mobile.Services;

/// <summary>
/// Service for playing ringtones and call sounds
/// </summary>
public interface IAudioRingtoneService
{
    Task<bool> PlayIncomingCallRingtoneAsync();
    Task<bool> PlayOutgoingCallRingtoneAsync();
    Task<bool> PlayCallEndSoundAsync();
    Task<bool> PlayCallConnectedSoundAsync();
    Task<bool> PlayNotificationSoundAsync();
    Task StopAllSoundsAsync();
    bool IsPlaying { get; }
}

public class AudioRingtoneService : IAudioRingtoneService, IDisposable
{
    private readonly IAudioManager _audioManager;
    private readonly ILogger<AudioRingtoneService> _logger;
    
    private IAudioPlayer? _currentPlayer;
    private CancellationTokenSource? _playbackCancellation;
    private bool _isPlaying = false;

    public bool IsPlaying => _isPlaying;

    public AudioRingtoneService(IAudioManager audioManager, ILogger<AudioRingtoneService> logger)
    {
        _audioManager = audioManager;
        _logger = logger;
    }

    public async Task<bool> PlayIncomingCallRingtoneAsync()
    {
        try
        {
            await StopAllSoundsAsync();
            
            // Generate or load incoming call ringtone
            var audioData = GenerateIncomingCallRingtone();
            return await PlayAudioLoopAsync(audioData, "Incoming Call Ringtone");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error playing incoming call ringtone");
            return false;
        }
    }

    public async Task<bool> PlayOutgoingCallRingtoneAsync()
    {
        try
        {
            await StopAllSoundsAsync();
            
            // Generate or load outgoing call ringtone
            var audioData = GenerateOutgoingCallRingtone();
            return await PlayAudioLoopAsync(audioData, "Outgoing Call Ringtone");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error playing outgoing call ringtone");
            return false;
        }
    }

    public async Task<bool> PlayCallEndSoundAsync()
    {
        try
        {
            await StopAllSoundsAsync();
            
            // Generate or load call end sound
            var audioData = GenerateCallEndSound();
            return await PlayAudioOnceAsync(audioData, "Call End Sound");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error playing call end sound");
            return false;
        }
    }

    public async Task<bool> PlayCallConnectedSoundAsync()
    {
        try
        {
            await StopAllSoundsAsync();
            
            // Generate or load call connected sound
            var audioData = GenerateCallConnectedSound();
            return await PlayAudioOnceAsync(audioData, "Call Connected Sound");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error playing call connected sound");
            return false;
        }
    }

    public async Task<bool> PlayNotificationSoundAsync()
    {
        try
        {
            // Don't stop other sounds for notifications
            var audioData = GenerateNotificationSound();
            return await PlayAudioOnceAsync(audioData, "Notification Sound");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error playing notification sound");
            return false;
        }
    }

    public async Task StopAllSoundsAsync()
    {
        try
        {
            _playbackCancellation?.Cancel();
            
            if (_currentPlayer != null)
            {
                _currentPlayer.Stop();
                _currentPlayer.Dispose();
                _currentPlayer = null;
            }
            
            _isPlaying = false;
            _logger.LogDebug("All sounds stopped");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping sounds");
        }
    }

    private async Task<bool> PlayAudioLoopAsync(byte[] audioData, string soundName)
    {
        try
        {
            _playbackCancellation = new CancellationTokenSource();
            _isPlaying = true;
            
            _logger.LogInformation("Starting looped playback: {SoundName}", soundName);
            
            // Play in a loop until cancelled
            _ = Task.Run(async () =>
            {
                try
                {
                    while (!_playbackCancellation.Token.IsCancellationRequested)
                    {
                        using var stream = new MemoryStream(audioData);
                        _currentPlayer = _audioManager.CreatePlayer(stream);
                        
                        _currentPlayer.Play();
                        
                        // Wait for playback to complete or cancellation
                        while (_currentPlayer.IsPlaying && !_playbackCancellation.Token.IsCancellationRequested)
                        {
                            await Task.Delay(100, _playbackCancellation.Token);
                        }
                        
                        _currentPlayer?.Dispose();
                        _currentPlayer = null;
                        
                        // Small pause between loops
                        await Task.Delay(500, _playbackCancellation.Token);
                    }
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in audio loop playback");
                }
                finally
                {
                    _isPlaying = false;
                }
            }, _playbackCancellation.Token);
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting looped audio playback");
            _isPlaying = false;
            return false;
        }
    }

    private async Task<bool> PlayAudioOnceAsync(byte[] audioData, string soundName)
    {
        try
        {
            _logger.LogInformation("Playing sound: {SoundName}", soundName);
            
            using var stream = new MemoryStream(audioData);
            using var player = _audioManager.CreatePlayer(stream);
            
            player.Play();
            
            // Wait for playback to complete
            while (player.IsPlaying)
            {
                await Task.Delay(50);
            }
            
            _logger.LogDebug("Finished playing sound: {SoundName}", soundName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error playing audio once: {SoundName}", soundName);
            return false;
        }
    }

    private byte[] GenerateIncomingCallRingtone()
    {
        // Generate a pleasant incoming call ringtone (ascending tones)
        return GenerateToneSequence(new[]
        {
            (440.0, 0.3), // A4
            (523.25, 0.3), // C5
            (659.25, 0.3), // E5
            (783.99, 0.5)  // G5
        });
    }

    private byte[] GenerateOutgoingCallRingtone()
    {
        // Generate outgoing call ringtone (repeating beeps)
        return GenerateToneSequence(new[]
        {
            (800.0, 0.4), // High beep
            (0.0, 0.2),   // Silence
            (800.0, 0.4), // High beep
            (0.0, 1.0)    // Longer silence
        });
    }

    private byte[] GenerateCallEndSound()
    {
        // Generate call end sound (descending tones)
        return GenerateToneSequence(new[]
        {
            (659.25, 0.2), // E5
            (523.25, 0.2), // C5
            (440.0, 0.4)   // A4
        });
    }

    private byte[] GenerateCallConnectedSound()
    {
        // Generate call connected sound (quick ascending beep)
        return GenerateToneSequence(new[]
        {
            (523.25, 0.1), // C5
            (659.25, 0.1), // E5
            (783.99, 0.2)  // G5
        });
    }

    private byte[] GenerateNotificationSound()
    {
        // Generate notification sound (single pleasant tone)
        return GenerateToneSequence(new[]
        {
            (880.0, 0.3) // A5
        });
    }

    private byte[] GenerateToneSequence((double frequency, double duration)[] tones)
    {
        const int sampleRate = 44100;
        const int channels = 1;
        const int bitsPerSample = 16;
        
        var samples = new List<short>();
        
        foreach (var (frequency, duration) in tones)
        {
            var sampleCount = (int)(sampleRate * duration);
            
            for (int i = 0; i < sampleCount; i++)
            {
                if (frequency > 0)
                {
                    // Generate sine wave
                    var time = (double)i / sampleRate;
                    var amplitude = Math.Sin(2 * Math.PI * frequency * time);
                    var sample = (short)(amplitude * 16384); // 50% volume
                    samples.Add(sample);
                }
                else
                {
                    // Silence
                    samples.Add(0);
                }
            }
        }
        
        // Convert to WAV format
        return CreateWavFile(samples.ToArray(), sampleRate, channels, bitsPerSample);
    }

    private byte[] CreateWavFile(short[] samples, int sampleRate, int channels, int bitsPerSample)
    {
        using var stream = new MemoryStream();
        using var writer = new BinaryWriter(stream);
        
        var dataSize = samples.Length * sizeof(short);
        var fileSize = 36 + dataSize;
        
        // WAV header
        writer.Write("RIFF".ToCharArray());
        writer.Write(fileSize);
        writer.Write("WAVE".ToCharArray());
        
        // Format chunk
        writer.Write("fmt ".ToCharArray());
        writer.Write(16); // Chunk size
        writer.Write((short)1); // Audio format (PCM)
        writer.Write((short)channels);
        writer.Write(sampleRate);
        writer.Write(sampleRate * channels * bitsPerSample / 8); // Byte rate
        writer.Write((short)(channels * bitsPerSample / 8)); // Block align
        writer.Write((short)bitsPerSample);
        
        // Data chunk
        writer.Write("data".ToCharArray());
        writer.Write(dataSize);
        
        // Audio data
        foreach (var sample in samples)
        {
            writer.Write(sample);
        }
        
        return stream.ToArray();
    }

    public void Dispose()
    {
        _playbackCancellation?.Cancel();
        _currentPlayer?.Dispose();
        _playbackCancellation?.Dispose();
        
        _logger.LogInformation("AudioRingtoneService disposed");
    }
}
