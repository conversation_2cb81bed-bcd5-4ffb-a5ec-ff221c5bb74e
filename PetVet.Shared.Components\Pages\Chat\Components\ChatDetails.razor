﻿@inject IMessageApi _MessageApi
@inject PetVetAuthStateProvider _PetVetAuthStateProvider
@inject IJSRuntime _JS
@inject NavigationManager _NavigationManager
@inject IPetApi _PetApi
@inject IPlatform Platform


@if (SelectedUser is null)
{
    <!-- Empty State -->
    <div class="flex-1 flex items-center justify-center bg-gray-50">
        <div class="text-center p-8">
            <div class="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-comments text-gray-400 text-3xl"></i>
            </div>
            <h3 class="text-lg font-bold text-gray-800 mb-2">Select a Chat</h3>
            <p class="text-gray-500">Choose a conversation from the list to start messaging</p>
        </div>
    </div>
}
else
{
    <!-- Chat Details Container -->
    <div class="flex-1 flex flex-col h-full">
        <!-- Chat Header -->
        <div class="bg-gradient-to-r from-teal-500 to-blue-600 p-4 flex items-center justify-between">
            @if (!Platform.IsWeb)
            {
                <button @onclick="HandleCancelClick"
                        class="w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-all">
                    <i class="fas fa-arrow-left text-white"></i>
                </button>
            }

            <!-- User Info -->
            <div class="flex items-center gap-3 flex-1 @(Platform.IsWeb ? "justify-center" : "justify-start ml-3")">
                @if (!string.IsNullOrEmpty(SelectedUser.ImageUrl))
                {
                    <img src="@SelectedUser.ImageUrl"
                         alt="@SelectedUser.Name"
                         class="w-10 h-10 rounded-full object-cover border-2 border-white/30" />
                }
                else
                {
                    <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center border-2 border-white/30">
                        <i class="fas fa-user text-white"></i>
                    </div>
                }
                <div>
                    <h3 class="text-white font-bold">@SelectedUser.Name</h3>
                    <p class="text-white/80 text-sm">
                        @(SelectedUser.IsOnline == true ? "Online" : "Offline")
                    </p>
                </div>
            </div>

            @if (Platform.IsWeb)
            {
                <button @onclick="HandleCancelClick"
                        class="w-8 h-8 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-all">
                    <i class="fas fa-times text-white"></i>
                </button>
            }
        </div>

        <!-- Messages Area -->
        <div class="flex-1 overflow-y-auto p-4 bg-gray-50" id="Messages-Container">
            @if (_error != null)
            {
                <div class="bg-red-500 text-white rounded-2xl p-4 mb-4 flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>@_error</span>
                    </div>
                    <button @onclick="() => _error = null"
                            class="w-6 h-6 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-all">
                        <i class="fas fa-times text-sm"></i>
                    </button>
                </div>
            }

            @if (_infoMessage != null)
            {
                <div class="bg-blue-500 text-white rounded-2xl p-4 mb-4 text-center">
                    <div class="flex items-center justify-center gap-2">
                        <i class="fas fa-info-circle"></i>
                        <span>@_infoMessage</span>
                    </div>
                </div>
            }
            else
            {
                <div class="space-y-4" id="Messages-List">
                    @if (_loadingMessages)
                    {
                        <div class="text-center py-8">
                            <div class="w-8 h-8 border-2 border-teal-500 border-t-transparent rounded-full animate-spin mx-auto mb-3"></div>
                            <p class="text-gray-500">Loading messages...</p>
                        </div>
                    }

                    @foreach (var msg in _messages)
                    {
                        var isIncoming = msg.ToUserId == _currentUserId;
                        var isOutgoing = !isIncoming;

                        <div class="flex @(isIncoming ? "justify-start" : "justify-end") mb-4">
                            @if (IsPetProfileMessage(msg.Message))
                            {
                                var profileData = ParsePetProfileMessage(msg.Message);
                                @if (profileData.IsDeleted)
                                {
                                    <!-- Deleted Pet Profile Message -->
                                    <div class="max-w-xs @(isIncoming ? "bg-gray-200" : "bg-red-100") rounded-2xl p-4 @(isIncoming ? "rounded-tl-sm" : "rounded-tr-sm")">
                                        <div class="flex items-center gap-3 mb-2">
                                            <div class="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center">
                                                <i class="fas fa-trash text-red-600"></i>
                                            </div>
                                            <div>
                                                <h5 class="font-bold text-gray-800 text-sm">Pet Profile Deleted</h5>
                                                <p class="text-xs text-gray-600">Deleted by @profileData.SharedBy</p>
                                            </div>
                                        </div>
                                        <div class="text-xs text-gray-500 text-right">
                                            @if (msg.SentOn.Date == DateTime.Now.Date)
                                            {
                                                <span>@msg.SentOn.ToString("h:mm tt")</span>
                                            }
                                            else
                                            {
                                                <span>@msg.SentOn.ToString("MMM dd, h:mm tt")</span>
                                            }
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    @if (IsPetExists(profileData.PetId))
                                    {
                                        <!-- Active Pet Profile Message -->
                                        <div class="max-w-xs @(isIncoming ? "bg-white" : "bg-teal-500") rounded-2xl p-4 @(isIncoming ? "rounded-tl-sm" : "rounded-tr-sm") cursor-pointer hover:shadow-lg transition-all border @(isIncoming ? "border-gray-200" : "border-teal-600")"
                                             @onclick="() => NavigateToPetProfile(profileData.ProfileUrl)">
                                            <div class="flex items-center gap-3 mb-2">
                                                @if (!string.IsNullOrEmpty(profileData.PetImage))
                                                {
                                                    <img src="@profileData.PetImage"
                                                         alt="@profileData.PetName"
                                                         class="w-12 h-12 rounded-xl object-cover border-2 @(isIncoming ? "border-gray-200" : "border-white/30")" />
                                                }
                                                else
                                                {
                                                    <div class="w-12 h-12 @(isIncoming ? "bg-orange-100" : "bg-white/20") rounded-xl flex items-center justify-center border-2 @(isIncoming ? "border-gray-200" : "border-white/30")">
                                                        <i class="fas fa-paw @(isIncoming ? "text-orange-600" : "text-white")"></i>
                                                    </div>
                                                }
                                                <div>
                                                    <h5 class="font-bold @(isIncoming ? "text-gray-800" : "text-white") text-sm">@profileData.PetName</h5>
                                                    <p class="text-xs @(isIncoming ? "text-gray-600" : "text-white/80")">Tap to view profile</p>
                                                </div>
                                            </div>
                                            <div class="text-xs @(isIncoming ? "text-gray-500" : "text-white/70") text-right">
                                                @if (msg.SentOn.Date == DateTime.Now.Date)
                                                {
                                                    <span>@msg.SentOn.ToString("h:mm tt")</span>
                                                }
                                                else
                                                {
                                                    <span>@msg.SentOn.ToString("MMM dd, h:mm tt")</span>
                                                }
                                            </div>
                                        </div>
                                    }
                                    else
                                    {
                                        <!-- Non-existent Pet Profile Message -->
                                        <div class="max-w-xs @(isIncoming ? "bg-gray-200" : "bg-red-100") rounded-2xl p-4 @(isIncoming ? "rounded-tl-sm" : "rounded-tr-sm")">
                                            <div class="flex items-center gap-3 mb-2">
                                                <div class="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center">
                                                    <i class="fas fa-trash text-red-600"></i>
                                                </div>
                                                <div>
                                                    <h5 class="font-bold text-gray-800 text-sm">Pet Profile Deleted</h5>
                                                    <p class="text-xs text-gray-600">This pet profile no longer exists</p>
                                                </div>
                                            </div>
                                            <div class="text-xs text-gray-500 text-right">
                                                @if (msg.SentOn.Date == DateTime.Now.Date)
                                                {
                                                    <span>@msg.SentOn.ToString("h:mm tt")</span>
                                                }
                                                else
                                                {
                                                    <span>@msg.SentOn.ToString("MMM dd, h:mm tt")</span>
                                                }
                                            </div>
                                        </div>
                                    }
                                }
                            }
                            else
                            {
                                <!-- Regular Text Message -->
                                <div class="max-w-xs @(isIncoming ? "bg-white" : "bg-gradient-to-r from-teal-500 to-blue-600") rounded-2xl p-4 @(isIncoming ? "rounded-tl-sm" : "rounded-tr-sm") @(isIncoming ? "border border-gray-200" : "")">
                                    <p class="@(isIncoming ? "text-gray-800" : "text-white") mb-2">@msg.Message</p>
                                    <div class="text-xs @(isIncoming ? "text-gray-500" : "text-white/70") text-right">
                                        @if (msg.SentOn.Date == DateTime.Now.Date)
                                        {
                                            <span>@msg.SentOn.ToString("h:mm tt")</span>
                                        }
                                        else
                                        {
                                            <span>@msg.SentOn.ToString("MMM dd, h:mm tt")</span>
                                        }
                                    </div>
                                </div>
                            }
                        </div>
                    }
                </div>
            }
        </div>

        <!-- Message Input -->
        <div class="p-4 bg-white border-t border-gray-200">
            <form @onsubmit="SendMessageAsync" @onsubmit:preventDefault="true">
                <div class="flex gap-3 items-end">
                    <div class="flex-1">
                        <InputText @bind-Value="_newMessage"
                                   placeholder="Type your message..."
                                   class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent resize-none" />
                    </div>
                    <button type="submit"
                            disabled="@(string.IsNullOrWhiteSpace(_newMessage))"
                            class="w-12 h-12 bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 disabled:from-gray-300 disabled:to-gray-400 text-white rounded-2xl flex items-center justify-center transition-all shadow-lg disabled:shadow-none">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </form>
        </div>
    </div>
}

@code {
    private int _currentUserId => _PetVetAuthStateProvider?.User.Id ?? 0;

    [Parameter]
    public UserDto? SelectedUser { get; set; }

    [Parameter]
    public EventCallback<bool> OnCancel { get; set; }

    [Parameter]
    public EventCallback OnIncomingMessageRecieved { get; set; }

    [Parameter]
    public EventCallback<UserDto> OnMessageSent { get; set; }

    [Parameter]
    public MessageDto? NewIncomingMessage { get; set; }

    private List<MessageDto> _messages = new List<MessageDto>();

    private string _newMessage = "";

    private string? _error;

    private string? _infoMessage;

    private int previousSelectedUserId = 0;

    private bool _scrollToBottom = false;

    private bool _loadingMessages = false;

    // Track if chat was previously closed
    private bool _chatWasReset = false;

    private Dictionary<int, bool> _petExistenceCache = new();
    private HashSet<int> _petsBeingChecked = new();

    protected override async Task OnParametersSetAsync()
    {
        if (NewIncomingMessage is not null)
        {
            _messages.Add(NewIncomingMessage);
            await OnIncomingMessageRecieved.InvokeAsync();
            _scrollToBottom = true;

            // Check pet existence for new message if it's a pet profile
            if (IsPetProfileMessage(NewIncomingMessage.Message))
            {
                var profileData = ParsePetProfileMessage(NewIncomingMessage.Message);
                if (!profileData.IsDeleted)
                {
                    await CheckPetExists(profileData.PetId);
                }
            }
        }

        // Check if we have a selected user that is either new or was previously reset
        if (SelectedUser is not null && (SelectedUser.Id != previousSelectedUserId || _chatWasReset))
        {
            previousSelectedUserId = SelectedUser.Id;
            _chatWasReset = false; // Reset the flag
            await LoadMessagesAsync();
            _scrollToBottom = true;
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // On first render, always scroll to bottom
            await _JS.InvokeVoidAsync("scrollToLastMessage");
        }

        if (_scrollToBottom)
        {
            _scrollToBottom = false;
            await _JS.InvokeVoidAsync("scrollToLastMessage");
        }
    }

    private async Task LoadMessagesAsync()
    {
        try
        {
            _loadingMessages = true;
            _petExistenceCache.Clear();
            _petsBeingChecked.Clear();

            _messages.Clear();
            var allMessages = await _MessageApi.GetMessagesAsync(SelectedUser!.Id, _PetVetAuthStateProvider.User.Id);

            if (allMessages.Any())
            {
                // Make sure messages are sorted chronologically by sent time
                _messages = allMessages.OrderBy(m => m.SentOn).ToList();
                _error = null;
                _infoMessage = null;

                // Check existence for all pet profiles in messages
                foreach (var msg in _messages)
                {
                    if (IsPetProfileMessage(msg.Message))
                    {
                        var profileData = ParsePetProfileMessage(msg.Message);
                        if (!profileData.IsDeleted)
                        {
                            await CheckPetExists(profileData.PetId);
                        }
                    }
                }
            }
            else
            {
                _infoMessage = $"There is no conversation between you and {SelectedUser.Name}.";
            }
        }
        finally
        {
            _loadingMessages = false;
        }
    }

    private async Task HandleCancelClick()
    {
        var shouldRemoveFromChatList = false;

        // Only consider removing if not currently loading messages
        if (!_loadingMessages)
        {
            var ifUserHasMessages = _messages.Any();
            shouldRemoveFromChatList = !ifUserHasMessages;
        }

        // Set flag that chat was closed, so it will scroll to bottom when reopened
        _chatWasReset = true;

        await OnCancel.InvokeAsync(shouldRemoveFromChatList);
    }

    private async Task SendMessageAsync()
    {
        if (!string.IsNullOrWhiteSpace(_newMessage))
        {
            var messageSendDto = new MessageSendDto(SelectedUser!.Id, _PetVetAuthStateProvider.User.Id, _newMessage);

            var response = await _MessageApi.SendMessageAsync(messageSendDto);

            if (response.IsSuccess)
            {
                var messageDto = new MessageDto(SelectedUser!.Id, _PetVetAuthStateProvider.User.Id, _newMessage, DateTime.Now);
                _messages.Add(messageDto);
                _scrollToBottom = true;
                _newMessage = "";
                _error = null;
                _infoMessage = null;

                // Check pet existence if it's a pet profile message
                if (IsPetProfileMessage(_newMessage))
                {
                    var profileData = ParsePetProfileMessage(_newMessage);
                    if (!profileData.IsDeleted)
                    {
                        await CheckPetExists(profileData.PetId);
                    }
                }

                // Notify parent component that a message was sent
                if (SelectedUser is not null)
                {
                    await OnMessageSent.InvokeAsync(SelectedUser);
                }
            }
            else
            {
                _error = "Error sending message.";
            }
        }
    }

    private bool IsPetProfileMessage(string message)
    {
        if (string.IsNullOrEmpty(message))
            return false;

        return message.Contains("\"type\":\"pet_profile\"");
    }

    private PetProfileMessageData ParsePetProfileMessage(string message)
    {
        try
        {
            // Basic parsing of JSON message using string manipulation for simplicity
            // In a production app, you would use proper JSON deserialization
            var petId = GetJsonValue(message, "petId");
            var petName = GetJsonValue(message, "petName");
            var petImage = GetJsonValue(message, "petImage");
            var profileUrl = GetJsonValue(message, "profileUrl");
            var sharedBy = GetJsonValue(message, "sharedBy");
            var isDeleted = GetJsonValue(message, "isDeleted");

            return new PetProfileMessageData
                {
                    PetId = int.TryParse(petId, out var id) ? id : 0,
                    PetName = petName.Trim('"'),
                    PetImage = petImage.Trim('"'),
                    ProfileUrl = profileUrl.Trim('"'),
                    SharedBy = sharedBy.Trim('"'),
                    IsDeleted = bool.TryParse(isDeleted, out var deleted) && deleted
                };
        }
        catch
        {
            // If parsing fails, return default values
            return new PetProfileMessageData();
        }
    }

    private string GetJsonValue(string json, string key)
    {
        var keyPattern = $"\"{key}\":";
        var startIndex = json.IndexOf(keyPattern) + keyPattern.Length;
        if (startIndex < keyPattern.Length) return string.Empty;

        var endIndex = startIndex;
        bool isString = json[startIndex] == '"';

        if (isString)
        {
            endIndex = json.IndexOf('"', startIndex + 1) + 1;
        }
        else
        {
            var possibleEndChars = new[] { ',', '}' };
            foreach (var c in possibleEndChars)
            {
                var idx = json.IndexOf(c, startIndex);
                if (idx > 0 && (endIndex == startIndex || idx < endIndex))
                {
                    endIndex = idx;
                }
            }
        }

        if (endIndex > startIndex)
        {
            return json.Substring(startIndex, endIndex - startIndex);
        }

        return string.Empty;
    }

    private void NavigateToPetProfile(string profileUrl)
    {
        if (!string.IsNullOrEmpty(profileUrl))
        {
            // Add the selected user's ID as a query parameter
            var chatUserId = SelectedUser?.Id ?? 0;
            var separator = profileUrl.Contains("?") ? "&" : "?";
            _NavigationManager.NavigateTo($"{profileUrl}{separator}chatUserId={chatUserId}");
        }
    }

    private async Task CheckPetExists(int petId)
    {
        if (_petsBeingChecked.Contains(petId))
            return;

        _petsBeingChecked.Add(petId);
        try
        {
            var pet = await _PetApi.GetPetAsync(petId);
            _petExistenceCache[petId] = pet != null && pet.Id > 0;
        }
        catch
        {
            _petExistenceCache[petId] = false;
        }
        finally
        {
            _petsBeingChecked.Remove(petId);
        }
    }

    private bool IsPetExists(int petId)
    {
        return _petExistenceCache.TryGetValue(petId, out var exists) && exists;
    }

    private class PetProfileMessageData
    {
        public int PetId { get; set; }
        public string PetName { get; set; } = string.Empty;
        public string PetImage { get; set; } = string.Empty;
        public string ProfileUrl { get; set; } = string.Empty;
        public string SharedBy { get; set; } = string.Empty;
        public bool IsDeleted { get; set; }
    }
}
