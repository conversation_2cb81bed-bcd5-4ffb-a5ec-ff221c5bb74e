﻿@page "/petOwner/breedRecognition"
@inject IBreedApi _BreedApi
@inject IAppState _AppState
@inject IJSRuntime _JS
@inject NavigationManager _NavigationManager

<PageTitle>Breed Recognition</PageTitle>

<!-- Scanning Interface exactly like the provided image -->
<div class="min-h-screen bg-black relative overflow-hidden">

    @if (string.IsNullOrEmpty(imagePreviewUrl) && string.IsNullOrEmpty(topPrediction) && !isLoading)
    {
        <!-- Initial Camera/Upload View -->
        <div class="relative h-screen bg-gradient-to-b from-primary-500 to-secondary-500">
            <!-- Header -->
            <div class="absolute top-0 left-0 right-0 z-20 pt-12 pb-6 px-6">
                <div class="flex items-center justify-between text-white">
                    <button @onclick="NavigateBack" class="w-10 h-10 bg-black/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-arrow-left text-white"></i>
                    </button>
                    <div class="text-center">
                        <h1 class="text-lg font-semibold">Pet Breed Recognition</h1>
                    </div>
                    <button @onclick="ToggleSupportedBreeds" class="w-10 h-10 bg-black/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-list text-white"></i>
                    </button>
                </div>
            </div>

            <!-- Camera Viewfinder Frame -->
            <div class="absolute inset-0 flex items-center justify-center">
                <div class="relative w-80 h-80">
                    <!-- Scanning frame corners -->
                    <div class="absolute top-0 left-0 w-12 h-12 border-l-4 border-t-4 border-white"></div>
                    <div class="absolute top-0 right-0 w-12 h-12 border-r-4 border-t-4 border-white"></div>
                    <div class="absolute bottom-0 left-0 w-12 h-12 border-l-4 border-b-4 border-white"></div>
                    <div class="absolute bottom-0 right-0 w-12 h-12 border-r-4 border-b-4 border-white"></div>

                    <!-- Center area -->
                    <div class="w-full h-full flex items-center justify-center">
                        <div class="text-center text-white">
                            <i class="fas fa-camera text-6xl mb-4 opacity-60"></i>
                            <p class="text-lg">Position your Pet here</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bottom Upload Button -->
            <div class="absolute bottom-8 left-0 right-0 flex justify-center">
                <div class="relative">
                    <InputFile OnChange="HandleImageUpload"
                               class="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                              />
                    <div class="w-20 h-20 bg-white rounded-full flex items-center justify-center shadow-2xl">
                        <i class="fas fa-camera text-primary-600 text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>
    }

    @if (!string.IsNullOrEmpty(imagePreviewUrl) && isLoading)
    {
        <!-- Scanning View with uploaded image -->
        <div class="relative h-screen bg-gradient-to-b from-primary-500 to-secondary-500">
            <!-- Header -->
            <div class="absolute top-0 left-0 right-0 z-20 pt-12 pb-6 px-6">
                <div class="flex items-center justify-between text-white">
                    <button @onclick="NavigateBack" class="w-10 h-10 bg-black/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-arrow-left text-white"></i>
                    </button>
                    <div class="text-center">
                        <h1 class="text-lg font-semibold">Identifying Pet Breed...</h1>
                    </div>
                    <div class="w-10 h-10"></div>
                </div>
            </div>

            <!-- Scanning Frame with Image -->
            <div class="absolute inset-0 flex items-center justify-center">
                <div class="relative w-80 h-80">
                    <!-- Scanning frame corners -->
                    <div class="absolute top-0 left-0 w-12 h-12 border-l-4 border-t-4 border-white z-10"></div>
                    <div class="absolute top-0 right-0 w-12 h-12 border-r-4 border-t-4 border-white z-10"></div>
                    <div class="absolute bottom-0 left-0 w-12 h-12 border-l-4 border-b-4 border-white z-10"></div>
                    <div class="absolute bottom-0 right-0 w-12 h-12 border-r-4 border-b-4 border-white z-10"></div>

                    <!-- Pet Image -->
                    <img src="@imagePreviewUrl" alt="Pet" class="w-full h-full object-cover rounded-lg" />

                    <!-- Scanning Animation Overlay -->
                    <div class="absolute inset-0 bg-primary-500/20 rounded-lg">
                        <div class="scanning-line"></div>
                    </div>
                </div>
            </div>

            <!-- Scanning Progress -->
            <div class="absolute bottom-20 left-0 right-0 text-center text-white">
                <div class="w-16 h-16 border-4 border-white/30 border-t-white rounded-full animate-spin mx-auto mb-4"></div>
                <p class="text-lg font-medium">Analyzing...</p>
                <p class="text-sm opacity-80">Please wait while we identify the breed</p>
            </div>
        </div>
    }

    @if (!string.IsNullOrEmpty(topPrediction))
    {
        <!-- Results Modal -->
        <div class="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
            <div class="bg-white rounded-3xl max-w-sm w-full max-h-[80vh] overflow-y-auto">
                <!-- Modal Header -->
                <div class="p-6 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-bold text-gray-800">Breed Match</h2>
                        <button @onclick="ClearUploadedImage" class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-times text-gray-600"></i>
                        </button>
                    </div>
                </div>

                <!-- Pet Image -->
                <div class="p-6 text-center">
                    <img src="@imagePreviewUrl" alt="Pet" class="w-32 h-32 rounded-2xl object-cover mx-auto mb-4 shadow-lg" />
                </div>

                <!-- Main Result -->
                <div class="px-6 pb-4 text-center">
                    <div class="w-20 h-20 bg-secondary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-check text-secondary-600 text-2xl"></i>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-800 mb-2">@topPrediction</h3>
                    <div class="inline-flex items-center gap-2 bg-secondary-100 text-secondary-800 px-4 py-2 rounded-full text-sm font-medium mb-4">
                        <i class="fas fa-chart-line"></i>
                        <span>@topProbability confidence</span>
                    </div>
                </div>

                <!-- Breed Information -->
                <div class="px-6 pb-6">
                    <div class="bg-gray-50 rounded-2xl p-4 mb-4">
                        <h4 class="font-semibold text-gray-800 mb-2">About @topPrediction</h4>
                        <p class="text-gray-600 text-sm">This breed identification was made with @topProbability confidence using our advanced AI technology.</p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-3"> 
                        <button @onclick="ClearUploadedImage" class="w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-xl font-medium hover:bg-gray-200 transition-colors">
                            <i class="fas fa-camera mr-2"></i>Scan Another Pet
                        </button>
                    </div>
                </div>
            </div>
        </div>
    }

    @if (!string.IsNullOrEmpty(errorMessage))
    {
        <!-- Error Modal -->
        <div class="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
            <div class="bg-white rounded-3xl max-w-sm w-full p-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                    <h3 class="text-lg font-bold text-gray-800 mb-2">Analysis Failed</h3>
                    <p class="text-red-600 text-sm mb-6">@errorMessage</p>
                    <button @onclick="ClearUploadedImage" class="w-full bg-red-500 text-white py-3 px-4 rounded-xl font-medium hover:bg-red-600 transition-colors">
                        <i class="fas fa-redo mr-2"></i>Try Again
                    </button>
                </div>
            </div>
        </div>
    }

</div>

    @if (showSupportedBreeds)
    {
        <!-- Supported Breeds Modal -->
        <div class="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
            <div class="bg-white rounded-3xl max-w-md w-full max-h-[80vh] overflow-y-auto">
                <!-- Modal Header -->
                <div class="p-6 border-b border-gray-100">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-bold text-gray-800">Supported Breeds</h2>
                        <button @onclick="ToggleSupportedBreeds" class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-times text-gray-600"></i>
                        </button>
                    </div>
                </div>

                <!-- Breeds Grid -->
                <div class="p-6">
                    <div class="grid grid-cols-2 gap-3">
                        @foreach (var breed in supportedBreeds)
                        {
                            <div class="bg-gray-50 rounded-xl p-3 text-center">
                                <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                    <i class="fas fa-dog text-primary-600"></i>
                                </div>
                                <p class="text-sm font-medium text-gray-800">@breed</p>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    }

@code {
    private string result = string.Empty;
    private bool isLoading = false;
    private string topPrediction = string.Empty;
    private string topProbability = string.Empty;
    private string errorMessage = string.Empty;
    private string imagePreviewUrl = string.Empty;
    private bool showSupportedBreeds = false;
    private bool showBreedDetails = false;
    private List<(string Breed, string Confidence)> otherPredictions = new();

    private List<string> supportedBreeds = new()
    {
        "Labrador", "Golden Retriever", "German Shepherd", "Bulldog",
        "Poodle", "Beagle", "Rottweiler", "Yorkshire Terrier",
        "Dachshund", "Siberian Husky", "Boxer", "Border Collie",
        "Chihuahua", "Shih Tzu", "Boston Terrier", "Pomeranian"
    };

    private async Task HandleImageUpload(InputFileChangeEventArgs e)
    {
        try
        {
            var file = e.File;
            if (file != null)
            {
                // Reset previous results
                result = string.Empty;
                topPrediction = string.Empty;
                topProbability = string.Empty;
                errorMessage = string.Empty;
                otherPredictions.Clear();

                // Validate file type
                var allowedExtensions = new[] { ".png", ".jpg", ".jpeg" };
                var fileExtension = Path.GetExtension(file.Name).ToLower();

                if (!allowedExtensions.Contains(fileExtension))
                {
                    errorMessage = "The file format is invalid. Please upload an image in JPEG, PNG, or JPG format.";
                    await _JS.ToastrError(errorMessage);
                    return;
                }

                // Create image preview
                using var previewStream = file.OpenReadStream(maxAllowedSize: 5 * 1024 * 1024);
                using var previewMemoryStream = new MemoryStream();
                await previewStream.CopyToAsync(previewMemoryStream);
                var imageData = previewMemoryStream.ToArray();
                var base64Image = Convert.ToBase64String(imageData);
                imagePreviewUrl = $"data:{file.ContentType};base64,{base64Image}";

                // Show loading indicator
                isLoading = true;
                StateHasChanged();

                _AppState.ShowLoader("Analyzing image");

                // Create a copy of the image data to send to the API
                var fileContent = new MemoryStream(imageData);

                // Create StreamPart for Refit multipart request
                var streamPart = new Refit.StreamPart(fileContent, file.Name, file.ContentType);

                // Call the breed API
                var predictionResult = await _BreedApi.PredictBreedAsync(streamPart);

                // Handle the prediction result
                if (predictionResult != null)
                {
                    if (predictionResult.IsPet)
                    {
                        // Display the prediction results
                        topPrediction = predictionResult.TopBreed;
                        topProbability = $"{predictionResult.ConfidenceScore:P1}";
                        await _JS.ToastrSuccess("Analysis complete!");
                    }
                    else
                    {
                        // Not a pet, show error message
                        errorMessage = predictionResult.Message;
                        await _JS.ToastrError("No pet detected in image");
                    }
                }
                else
                {
                    errorMessage = "Failed to get a response from the breed prediction service";
                    await _JS.ToastrError(errorMessage);
                }
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Error processing image: {ex.Message}";
            await _JS.ToastrError(errorMessage);
        }
        finally
        {
            _AppState.HideLoader();
            isLoading = false;
            StateHasChanged();
        }
    }

    private void ClearUploadedImage()
    {
        imagePreviewUrl = string.Empty;
        topPrediction = string.Empty;
        topProbability = string.Empty;
        errorMessage = string.Empty;
        result = string.Empty;
        otherPredictions.Clear();
        StateHasChanged();
    }

    private void NavigateBack()
    {
        _NavigationManager.NavigateTo("/petOwner/home");
    }

    private void ToggleSupportedBreeds()
    {
        showSupportedBreeds = !showSupportedBreeds;
        StateHasChanged();
    }

    private void ShowBreedDetails()
    {
        showBreedDetails = true;
        StateHasChanged();
    }
} 