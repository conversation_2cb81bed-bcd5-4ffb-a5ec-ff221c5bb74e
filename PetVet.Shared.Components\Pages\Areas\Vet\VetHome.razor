﻿@page "/vet/home"
@inject NavigationManager _NavigationManager
@inject IAppointmentApi _AppointmentApi
@inject IUserApi _UserApi
@inject PetVetAuthStateProvider _PetVetAuthStateProvider
@inject IAppState _AppState
@inject IJSRuntime _JS

<PageTitle>Vet Home</PageTitle>

<!-- Modern Vet Home -->
<div class="px-4 py-6 space-y-6">

@if (isLoading)
{
    <!-- Loading State -->
    <div class="bg-gradient-to-r from-blue-500 via-purple-600 to-teal-500 rounded-3xl p-6 text-white relative overflow-hidden">
        <div class="relative z-10">
            <div class="flex items-center justify-center">
                <div class="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
                <span class="text-lg font-medium">Loading your dashboard...</span>
            </div>
        </div>
    </div>
}
else
{

    <!-- Welcome Header for Vet -->
    <div class="bg-gradient-to-r from-blue-500 via-purple-600 to-teal-500 rounded-3xl p-6 text-white relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full"></div>
            <div class="absolute bottom-4 left-4 w-20 h-20 bg-white rounded-full"></div>
            <div class="absolute top-1/2 right-1/3 w-16 h-16 bg-white rounded-full"></div>
        </div>

        <div class="relative z-10">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h1 class="text-2xl font-bold mb-1">Welcome, @(currentUser?.Name ?? "Doctor")! 👨‍⚕️</h1>
                    <p class="text-white/90 text-sm">Ready to help pets today?</p>
                </div>
                <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                    <i class="fas fa-stethoscope text-white text-xl"></i>
                </div>
            </div>

            <!-- Quick Stats for Vet -->
            <div class="flex gap-4 mt-6">
                <div class="bg-white/20 rounded-2xl p-3 flex-1 text-center">
                    <div class="text-2xl font-bold">@todayAppointments</div>
                    <div class="text-xs text-white/80">Today's Appointments</div>
                </div>
                <div class="bg-white/20 rounded-2xl p-3 flex-1 text-center">
                    <div class="text-2xl font-bold">@weekAppointments</div>
                    <div class="text-xs text-white/80">This Week</div>
                </div>
                <div class="bg-white/20 rounded-2xl p-3 flex-1 text-center">
                    <div class="text-2xl font-bold">@totalPatients</div>
                    <div class="text-xs text-white/80">Total Patients</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Schedule -->
    <div>
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-bold text-gray-800">Today's Schedule</h2>
            <button class="text-teal-600 text-sm font-medium">View All</button>
        </div>

        <div class="space-y-3">
            @if (todayAppointmentsList?.Any() == true)
            {
                @foreach (var appointment in todayAppointmentsList.Take(3))
                {
                    <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                        <div class="flex items-center gap-3">
                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                @if (!string.IsNullOrEmpty(appointment.PetOwnerNameDisplayPicture))
                                {
                                    <img src="@appointment.PetOwnerNameDisplayPicture" alt="Pet Owner" class="w-10 h-10 rounded-full object-cover">
                                }
                                else
                                {
                                    <i class="fas fa-user text-blue-600"></i>
                                }
                            </div>
                            <div class="flex-1">
                                <p class="font-medium text-gray-800 text-sm">@appointment.PetOwnerName</p>
                                <p class="text-gray-500 text-xs">@appointment.Time • @GetFormattedDate(appointment.AppointmentDate)</p>
                                @if (!string.IsNullOrEmpty(appointment.Notes))
                                {
                                    <p class="text-gray-400 text-xs mt-1">@appointment.Notes</p>
                                }
                            </div>
                            <div class="text-right">
                                <span class="@GetStatusClass(appointment.Status) px-2 py-1 rounded-full text-xs font-medium">@appointment.Status</span>
                            </div>
                        </div>
                    </div>
                }
            }
            else
            {
                <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 text-center">
                    <div class="text-gray-400 mb-2">
                        <i class="fas fa-calendar-check text-2xl"></i>
                    </div>
                    <p class="text-gray-500 text-sm">No appointments scheduled for today</p>
                    <p class="text-gray-400 text-xs mt-1">Enjoy your free time!</p>
                </div>
            }
        </div>
    </div>

    <!-- Quick Actions Grid -->
    <div>
        <h2 class="text-xl font-bold text-gray-800 mb-4">Quick Actions</h2>
        <div class="grid grid-cols-2 gap-4">
            @foreach (var card in CardData)
            {
                <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 transform hover:scale-105 cursor-pointer"
                     @onclick="() => NavigateTo(card.Route)"
                     role="button"
                     tabindex="0">

                    <div class="w-12 h-12 @card.BgColor rounded-2xl flex items-center justify-center mb-3">
                        @if (card.Title == "Calendar")
                        {
                            <i class="fas fa-calendar-alt text-yellow-600 text-xl"></i>
                        }
                        else
                        {
                            <img src="@card.Icon" alt="@card.Title" class="w-6 h-6" />
                        }
                    </div>

                    <h3 class="font-semibold text-gray-800 text-sm mb-1">@card.Title</h3>
                    <p class="text-gray-500 text-xs leading-relaxed">@card.Description</p>
                </div>
            }
        </div>
    </div>

    <!-- Professional Tips Section -->
    <div class="bg-gradient-to-r from-indigo-100 to-purple-100 rounded-2xl p-4">
        <div class="flex items-start gap-3">
            <div class="w-10 h-10 bg-indigo-200 rounded-full flex items-center justify-center flex-shrink-0">
                <i class="fas fa-graduation-cap text-indigo-600"></i>
            </div>
            <div>
                <h3 class="font-semibold text-gray-800 mb-1">Professional Tip</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Regular follow-ups with pet owners help build trust and ensure better treatment outcomes.
                    Consider sending reminder messages for upcoming appointments.
                </p>
            </div>
        </div>
    </div>

} <!-- End of loading check -->

</div>


@code {
    private int currentIndex = 0;
    private string bannerImage = "images/vets/Vet1.jpg";
    private Timer? _bannerTimer;

    // Real data properties
    private UserDto? currentUser;
    private int todayAppointments = 0;
    private int weekAppointments = 0;
    private int totalPatients = 0;
    private List<AppointmentDto>? todayAppointmentsList;
    private bool isLoading = true;

    private List<string> BannerImages = new List<string>
    {
        "images/vets/Vet1.jpg",
        "images/vets/Vet2.jpg",
    };

    protected override async Task OnInitializedAsync()
    {
        _bannerTimer = new Timer(UpdateBanner, null, 0, 4000);
        await LoadVetDataAsync();
    }

    private async Task LoadVetDataAsync()
    {
        try
        {
            if (_PetVetAuthStateProvider.IsLoggedIn)
            {
                var userId = _PetVetAuthStateProvider.User.Id;

                // Load current user profile
                currentUser = await _UserApi.GetCurrentVetUserAsync(userId);

                // Load all appointments for this vet
                var allAppointments = await _AppointmentApi.GetVetAppointmentsAsync(userId);

                if (allAppointments?.Any() == true)
                {
                    var today = DateTime.Today;
                    var startOfWeek = today.AddDays(-(int)today.DayOfWeek);
                    var endOfWeek = startOfWeek.AddDays(7);

                    // Calculate today's appointments
                    todayAppointmentsList = allAppointments
                        .Where(a => DateTime.TryParse(a.AppointmentDate, out var date) && date.Date == today)
                        .OrderBy(a => a.Time)
                        .ToList();

                    todayAppointments = todayAppointmentsList.Count;

                    // Calculate this week's appointments
                    weekAppointments = allAppointments
                        .Count(a => DateTime.TryParse(a.AppointmentDate, out var date) &&
                                   date.Date >= startOfWeek && date.Date < endOfWeek);

                    // Calculate total unique patients (unique pet owner IDs)
                    totalPatients = allAppointments
                        .Select(a => a.PetOwnerId)
                        .Distinct()
                        .Count();
                }

                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error loading vet data: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void UpdateBanner(object? state)
    {
        currentIndex = (currentIndex + 1) % BannerImages.Count;
        bannerImage = BannerImages[currentIndex];
        InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        _bannerTimer?.Dispose();
    }

    private void NavigateTo(string route)
    {
        _NavigationManager.NavigateTo(route);
    }

    private string GetStatusClass(string status)
    {
        return status switch
        {
            nameof(AppointmentStatus.Confirmed) => "bg-green-100 text-green-800",
            nameof(AppointmentStatus.Completed) => "bg-blue-100 text-blue-800",
            nameof(AppointmentStatus.Cancelled) => "bg-red-100 text-red-800",
            nameof(AppointmentStatus.Expired) => "bg-gray-100 text-gray-800",
            _ => "bg-yellow-100 text-yellow-800"
        };
    }

    private string GetFormattedDate(string dateString)
    {
        if (DateTime.TryParse(dateString, out var date))
        {
            return date.ToString("MMM dd");
        }
        return dateString;
    }

    private List<CardModel> CardData = new()
    {
        new CardModel
        {
            Icon = "images/icons/clinic.png",
            Title = "Appointments",
            Color = "#FEA195",
            BgColor = "bg-blue-100",
            Description = "View & manage appointments",
            Route = "/vet/appointments"
        },
        new CardModel
        {
            Icon = "images/icons/schedule.png",
            Title = "Schedule",
            Color = "#10B981",
            BgColor = "bg-green-100",
            Description = "Manage clinic hours",
            Route = "/vet/schedule"
        },
        new CardModel
        {
            Icon = "images/icons/chat.png",
            Title = "Messages",
            Color = "#8B5CF6",
            BgColor = "bg-purple-100",
            Description = "Chat with pet owners",
            Route = "/vet/chat"
        },
        new CardModel
        {
            Icon = "images/icons/calendar.png",
            Title = "Calendar",
            Color = "#F59E0B",
            BgColor = "bg-yellow-100",
            Description = "View calendar overview",
            Route = "/vet/calendar"
        }
    };

    private class CardModel
    {
        public string Icon { get; set; } = "";
        public string Title { get; set; } = "";
        public string Color { get; set; } = "";
        public string BgColor { get; set; } = "";
        public string Description { get; set; } = "";
        public string Route { get; set; } = "";
    }
} 