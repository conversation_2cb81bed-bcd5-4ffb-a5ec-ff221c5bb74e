using PetVet.Shared.Models.AzureCommunication;

namespace PetVet.Shared.Services;

public interface IAzureCommunicationService
{
    // Call Management
    Task<StartCallResponse> StartCallAsync(StartCallRequest request);
    Task<JoinCallResponse> JoinCallAsync(Join<PERSON><PERSON>Request request);
    Task<bool> EndCallAsync(EndCallRequest request);
    Task<CallSession?> GetCallSessionAsync(string callId);
    Task<List<CallSession>> GetActiveCallsAsync(string userId);

    // Call Invitations
    Task<bool> SendCallInvitationAsync(CallInvitation invitation);
    Task<bool> AcceptCallInvitationAsync(string invitationId, string userId);
    Task<bool> DeclineCallInvitationAsync(string invitationId, string userId);
    Task<List<CallInvitation>> GetPendingInvitationsAsync(string userId);

    // Call Controls
    Task<bool> ToggleAudioAsync(string callId, string userId, bool enabled);
    Task<bool> ToggleVideoAsync(string callId, string userId, bool enabled);
    Task<bool> StartScreenShareAsync(string callId, string userId);
    Task<bool> StopScreenShareAsync(string callId, string userId);
    Task<bool> HoldCallAsync(string callId, string userId);
    Task<bool> ResumeCallAsync(string callId, string userId);

    // Participant Management
    Task<bool> AddParticipantAsync(string callId, string userId, string displayName);
    Task<bool> RemoveParticipantAsync(string callId, string userId);
    Task<List<CallParticipant>> GetCallParticipantsAsync(string callId);

    // Emergency Calls
    Task<EmergencyCallResponse> StartEmergencyCallAsync(EmergencyCallRequest request);
    Task<List<string>> GetAvailableVeterinariansAsync();

    // Call Quality and Monitoring
    Task<bool> ReportCallQualityAsync(CallQualityMetrics metrics);
    Task<List<CallQualityMetrics>> GetCallQualityHistoryAsync(string callId);

    // Authentication and Tokens
    Task<string> GetAccessTokenAsync(string userId);
    Task<bool> RefreshTokenAsync(string userId);

    // Service Health
    Task<bool> IsServiceHealthyAsync();
    Task<Dictionary<string, object>> GetServiceStatsAsync();

    // Events and Notifications
    event EventHandler<CallStatusUpdate>? CallStatusChanged;
    event EventHandler<CallInvitation>? CallInvitationReceived;
    event EventHandler<CallParticipant>? ParticipantJoined;
    event EventHandler<CallParticipant>? ParticipantLeft;
}
