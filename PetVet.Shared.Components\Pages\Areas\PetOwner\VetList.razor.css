﻿/* Base styles - Desktop first */
.VetList-CONTAINER {
    background: #f2f6f9;
    padding: 2rem;
    min-height: 100vh;
    width: 100%;
    max-width: 72rem;
    margin: 0 auto;
    font-family: 'Inter', sans-serif;
}

/* Search section */
.search-container {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    position: sticky;
    top: 0;
    z-index: 10;
    background: inherit;
    padding: 1rem 0;
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

.search-box {
    flex-grow: 1;
    position: relative;
    transition: all 0.3s ease;
}

    .search-box input {
        width: 100%;
        padding: 0.875rem 2.5rem;
        background-color: white;
        border: 1px solid #E5E7EB;
        border-radius: 1rem;
        font-size: 1rem;
        color: #374151;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

        .search-box input:focus {
            outline: none;
            border-color: #3B82F6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6B7280;
    font-size: 1rem;
    transition: color 0.3s ease;
}

.search-box:focus-within .search-icon {
    color: #3B82F6;
}

/* Specialist section */
.section-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1F2937;
    margin-bottom: 1.5rem;
    letter-spacing: -0.025em;
    font-family: 'Acme', sans-serif;
}

.specialist-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(12rem, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
    font-family: 'Acme', sans-serif;
}

.specialist-card {
    background: #157BAB;
    border-radius: 1.25rem;
    padding: 1.5rem;
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    cursor: pointer;
    user-select: none;
    -webkit-user-select: none;
}

    .specialist-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.15), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
    }

    .specialist-card:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
    }

    .specialist-card:focus-visible {
        outline: 2px solid white;
        outline-offset: 2px;
    }

.specialist-image {
    width: 6rem;
    height: 6rem;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
}

.specialist-name {
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
    line-height: 1.25;
    text-align: center;
}

.specialist-role {
    font-size: 0.875rem;
    color: #BFDBFE;
    margin: 0.25rem 0 0;
    line-height: 1.25;
    text-align: center;
}

/* Quick access icons */
.quick-access {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    background-color: white;
    border-radius: 1.25rem;
    padding: 1.5rem;
    margin: 2rem 0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.quick-access-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #3B82F6;
    font-size: 0.875rem;
    font-weight: 600;
    text-align: center;
    transition: transform 0.3s ease;
    font-family: 'Acme', sans-serif;
}

    .quick-access-item:hover {
        transform: scale(1.05);
    }

.quick-access-icon {
    width: 2.5rem;
    height: 2.5rem;
    margin-bottom: 0.75rem;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* Available doctors section */
.available-doctors {
    margin-top: 2rem;
}

.available-doctors-title {
    font-size: 1.25rem;
    color: #1F2937;
    font-weight: 700;
    margin-bottom: 1.5rem;
    letter-spacing: -0.025em;
    font-family: 'Acme', sans-serif;
}

.doctor-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(24rem, 1fr));
    gap: 1.5rem;
}

.doctor-card {
    background: #157BAB;
    border-radius: 1.25rem;
    padding: 1.5rem;
    color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    cursor: pointer;
    user-select: none;
    -webkit-user-select: none;
}

    .doctor-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.15), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
    }

    .doctor-card:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
    }

    .doctor-card:focus-visible {
        outline: 2px solid white;
        outline-offset: 2px;
    }

.doctor-card-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1.25rem;
    opacity: 0.9;
    
}

.doctor-info {
    display: flex;
    align-items: center;
    margin-bottom: 1.25rem;
    gap: 1.25rem;
    font-family: 'Acme', sans-serif;
}

.doctor-image {
    width: 4rem;
    height: 4rem;
    border-radius: 1rem;
    object-fit: cover;
    border: 2px solid rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.doctor-details h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin: 0;
    line-height: 1.25;
}

.doctor-details p {
    font-size: 0.875rem;
    color: #BFDBFE;
    margin: 0.25rem 0 0;
    line-height: 1.25;
}

.doctor-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 0.75rem;
    border-radius: 0.75rem;
    -webkit-backdrop-filter: blur(4px);
    backdrop-filter: blur(4px);
}

    .doctor-rating i {
        color: #FCD34D;
        filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
    }


/* Tablet Layout */
@media (max-width: 1023px) {
    .VetList-CONTAINER {
        max-width: 48rem;
        padding: 1.5rem;
    }

    .specialist-cards {
        gap: 1.25rem;
    }

    .quick-access {
        gap: 1.25rem;
        padding: 1.25rem;
    }

    .doctor-cards {
        grid-template-columns: repeat(auto-fit, minmax(20rem, 1fr));
        gap: 1.25rem;
    }
}

/* Mobile Layout */
@media (max-width: 639px) {
    .VetList-CONTAINER {
        padding: 1rem;
        max-width: 28rem;
    }

    .search-container {
        gap: 0.75rem;
        margin-bottom: 1.5rem;
        padding: 0.5rem 0;
    }

    .search-box input {
        padding: 0.75rem 2.5rem;
        font-size: 0.875rem;
    }

    .section-title {
        font-size: 1rem;
        margin-bottom: 1rem;
    }

    .specialist-cards {
        display: flex;
        gap: 1rem;
        overflow-x: auto;
        padding: 0.5rem 0.25rem 1rem;
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }

    .specialist-card {
        flex-shrink: 0;
        width: 9rem;
        padding: 1rem;
    }

    .specialist-image {
        width: 5rem;
        height: 5rem;
        margin-bottom: 0.75rem;
    }

    .specialist-name {
        font-size: 0.875rem;
    }

    .specialist-role {
        font-size: 0.75rem;
    }

    .quick-access {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
        padding: 1.25rem;
        margin: 1.5rem 0;
    }

    .quick-access-icon {
        width: 2rem;
        height: 2rem;
        margin-bottom: 0.5rem;
    }

    .doctor-cards {
        display: flex;
        gap: 1rem;
        overflow-x: auto;
        padding: 0.5rem 0.25rem 1rem;
        scroll-behavior: smooth;
        -webkit-overflow-scrolling: touch;
    }

    .doctor-card {
        flex-shrink: 0;
        width: 18rem;
        padding: 1.25rem;
    }

    .doctor-image {
        width: 3.5rem;
        height: 3.5rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.VetList-CONTAINER {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
