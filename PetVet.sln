﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.12.35527.113
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PetVet.Web", "PetVet.Web\PetVet.Web.csproj", "{8B0982CC-7F86-4EED-9744-487DCDD40C80}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PetVet.Shared", "PetVet.Shared\PetVet.Shared.csproj", "{5116D6F3-8131-4B51-A5CE-802036EF5483}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PetVet.Shared.Components", "PetVet.Shared.Components\PetVet.Shared.Components.csproj", "{9F2A29EA-06F5-43CB-90BF-C45A7E8C1CF0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MobileMigrationHelper", "MobileMigrationHelper\MobileMigrationHelper.csproj", "{AB1F7C59-F494-461E-A2EF-064BA6938E1C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PetVet.Mobile.Data", "PetVet.Mobile.Data\PetVet.Mobile.Data.csproj", "{41479527-0639-4CCA-8929-756A6C95E106}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "PetVet.Mobile", "PetVet.Mobile\PetVet.Mobile.csproj", "{534EAB19-BD4D-EBF9-ABC7-0407548904DB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Platform.Framework.Maui", "MauiShared\Platform.Framework.Maui.csproj", "{A3C7D8AC-10A8-9E1B-D159-0BA5A33C2B25}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{8B0982CC-7F86-4EED-9744-487DCDD40C80}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8B0982CC-7F86-4EED-9744-487DCDD40C80}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8B0982CC-7F86-4EED-9744-487DCDD40C80}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8B0982CC-7F86-4EED-9744-487DCDD40C80}.Release|Any CPU.Build.0 = Release|Any CPU
		{5116D6F3-8131-4B51-A5CE-802036EF5483}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5116D6F3-8131-4B51-A5CE-802036EF5483}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5116D6F3-8131-4B51-A5CE-802036EF5483}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5116D6F3-8131-4B51-A5CE-802036EF5483}.Release|Any CPU.Build.0 = Release|Any CPU
		{9F2A29EA-06F5-43CB-90BF-C45A7E8C1CF0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9F2A29EA-06F5-43CB-90BF-C45A7E8C1CF0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9F2A29EA-06F5-43CB-90BF-C45A7E8C1CF0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9F2A29EA-06F5-43CB-90BF-C45A7E8C1CF0}.Release|Any CPU.Build.0 = Release|Any CPU
		{AB1F7C59-F494-461E-A2EF-064BA6938E1C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AB1F7C59-F494-461E-A2EF-064BA6938E1C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AB1F7C59-F494-461E-A2EF-064BA6938E1C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AB1F7C59-F494-461E-A2EF-064BA6938E1C}.Release|Any CPU.Build.0 = Release|Any CPU
		{41479527-0639-4CCA-8929-756A6C95E106}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{41479527-0639-4CCA-8929-756A6C95E106}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{41479527-0639-4CCA-8929-756A6C95E106}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{41479527-0639-4CCA-8929-756A6C95E106}.Release|Any CPU.Build.0 = Release|Any CPU
		{534EAB19-BD4D-EBF9-ABC7-0407548904DB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{534EAB19-BD4D-EBF9-ABC7-0407548904DB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{534EAB19-BD4D-EBF9-ABC7-0407548904DB}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{534EAB19-BD4D-EBF9-ABC7-0407548904DB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{534EAB19-BD4D-EBF9-ABC7-0407548904DB}.Release|Any CPU.Build.0 = Release|Any CPU
		{534EAB19-BD4D-EBF9-ABC7-0407548904DB}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{A3C7D8AC-10A8-9E1B-D159-0BA5A33C2B25}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3C7D8AC-10A8-9E1B-D159-0BA5A33C2B25}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3C7D8AC-10A8-9E1B-D159-0BA5A33C2B25}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3C7D8AC-10A8-9E1B-D159-0BA5A33C2B25}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {43D5EBC4-10C1-467D-825A-E1168B856A9D}
	EndGlobalSection
EndGlobal
