﻿html, body {
    font-family: 'Jost', sans-serif;
}

/* CSS Custom Properties for Theme Colors */
:root {
    --primary-50: #eff8ff;
    --primary-100: #dbefff;
    --primary-200: #c0e4ff;
    --primary-300: #94d5ff;
    --primary-400: #61bcfd;
    --primary-500: #3E97DA;
    --primary-600: #3E97DA;
    --primary-700: #2875c7;
    --primary-800: #2562a1;
    --primary-900: #25537f;
    
    --secondary-50: #f0fdf6;
    --secondary-100: #dcfce8;
    --secondary-200: #bbf7d2;
    --secondary-300: #86efad;
    --secondary-400: #4ade80;
    --secondary-500: #45C1A5;
    --secondary-600: #45C1A5;
    --secondary-700: #15993e;
    --secondary-800: #16783a;
    --secondary-900: #146233;
    
    --accent-color: #14b8a6;
    --success-color: #22c55e;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
}

a {
    color: var(--primary-600);
    transition: color 0.2s ease;
}

a:hover {
    color: var(--primary-700);
}

/* Mobile-specific styles - Tailwind compatible */
.mobile-app {
    @apply antialiased;
}

.content {
    padding-top: 1.1rem;
}

h1:focus {
    outline: none;
}

.valid.modified:not([type=checkbox]) {
    outline: 1px solid var(--success-color);
}

.invalid {
    outline: 1px solid var(--error-color);
}

.validation-message {
    color: var(--error-color);
}

/* Primary Theme Button Styles */
.btn-primary-custom {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(62, 151, 218, 0.25);
}

.btn-primary-custom:hover {
    background: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    box-shadow: 0 6px 16px rgba(62, 151, 218, 0.35);
    transform: translateY(-1px);
}

/* Secondary Theme Button Styles */
.btn-secondary-custom {
    background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(69, 193, 165, 0.25);
}

.btn-secondary-custom:hover {
    background: linear-gradient(135deg, var(--secondary-600), var(--secondary-700));
    box-shadow: 0 6px 16px rgba(69, 193, 165, 0.35);
    transform: translateY(-1px);
}

/* Professional Card Styling */
.card-primary {
    background: linear-gradient(145deg, var(--primary-50), white);
    border: 1px solid var(--primary-100);
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(62, 151, 218, 0.08);
}

.card-secondary {
    background: linear-gradient(145deg, var(--secondary-50), white);
    border: 1px solid var(--secondary-100);
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(69, 193, 165, 0.08);
}

/* Gradient Backgrounds */
.bg-gradient-primary {
    background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
}

.bg-gradient-secondary {
    background: linear-gradient(135deg, var(--secondary-500), var(--secondary-600));
}

.bg-gradient-accent {
    background: linear-gradient(135deg, var(--primary-400), var(--secondary-400));
}

#blazor-error-ui {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }

.blazor-error-boundary {
    background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTYiIGhlaWdodD0iNDkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIG92ZXJmbG93PSJoaWRkZW4iPjxkZWZzPjxjbGlwUGF0aCBpZD0iY2xpcDAiPjxyZWN0IHg9IjIzNSIgeT0iNTEiIHdpZHRoPSI1NiIgaGVpZ2h0PSI0OSIvPjwvY2xpcFBhdGg+PC9kZWZzPjxnIGNsaXAtcGF0aD0idXJsKCNjbGlwMCkiIHRyYW5zZm9ybT0idHJhbnNsYXRlKC0yMzUgLTUxKSI+PHBhdGggZD0iTTI2My41MDYgNTFDMjY0LjcxNyA1MSAyNjUuODEzIDUxLjQ4MzcgMjY2LjYwNiA1Mi4yNjU4TDI2Ny4wNTIgNTIuNzk4NyAyNjcuNTM5IDUzLjYyODMgMjkwLjE4NSA5Mi4xODMxIDI5MC41NDUgOTIuNzk1IDI5MC42NTYgOTIuOTk2QzI5MC44NzcgOTMuNTEzIDI5MSA5NC4wODE1IDI5MSA5NC42NzgyIDI5MSA5Ny4wNjUxIDI4OS4wMzggOTkgMjg2LjYxNyA5OUwyNDAuMzgzIDk5QzIzNy45NjMgOTkgMjM2IDk3LjA2NTEgMjM2IDk0LjY3ODIgMjM2IDk0LjM3OTkgMjM2LjAzMSA5NC4wODg2IDIzNi4wODkgOTMuODA3MkwyMzYuMzM4IDkzLjAxNjIgMjM2Ljg1OCA5Mi4xMzE0IDI1OS40NzMgNTMuNjI5NCAyNTkuOTYxIDUyLjc5ODUgMjYwLjQwNyA1Mi4yNjU4QzI2MS4yIDUxLjQ4MzcgMjYyLjI5NiA1MSAyNjMuNTA2IDUxWk0yNjMuNTg2IDY2LjAxODNDMjYwLjczNyA2Ni4wMTgzIDI1OS4zMTMgNjcuMTI0NSAyNTkuMzEzIDY5LjMzNyAyNTkuMzEzIDY5LjYxMDIgMjU5LjMzMiA2OS44NjA4IDI1OS4zNzEgNzAuMDg4N0wyNjEuNzk1IDg0LjAxNjEgMjY1LjM4IDg0LjAxNjEgMjY3LjgyMSA2OS43NDc1QzI2Ny44NiA2OS43MzA5IDI2Ny44NzkgNjkuNTg3NyAyNjcuODc5IDY5LjMxNzkgMjY3Ljg3OSA2Ny4xMTgyIDI2Ni40NDggNjYuMDE4MyAyNjMuNTg2IDY2LjAxODNaTTI2My41NzYgODYuMDU0N0MyNjEuMDQ5IDg2LjA1NDcgMjU5Ljc4NiA4Ny4zMDA1IDI1OS43ODYgODkuNzkyMSAyNTkuNzg2IDkyLjI4MzcgMjYxLjA0OSA5My41Mjk1IDI2My41NzYgOTMuNTI5NSAyNjYuMTE2IDkzLjUyOTUgMjY3LjM4NyA5Mi4yODM3IDI2Ny4zODcgODkuNzkyMSAyNjcuMzg3IDg3LjMwMDUgMjY2LjExNiA4Ni4wNTQ3IDI2My41NzYgODYuMDU0N1oiIGZpbGw9IiNGRkU1MDAiIGZpbGwtcnVsZT0iZXZlbm9kZCIvPjwvZz48L3N2Zz4=) no-repeat 1rem/1.8rem, #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
}

    .blazor-error-boundary::after {
        content: "An error has occurred."
    }

.status-bar-safe-area {
    display: none;
}

@supports (-webkit-touch-callout: none) {
    .status-bar-safe-area {
        display: flex;
        position: sticky;
        top: 0;
        height: env(safe-area-inset-top);
        background-color: #f7f7f7;
        width: 100%;
        z-index: 1;
    }

    .flex-col {
        padding-left: env(safe-area-inset-left);
    }
}
