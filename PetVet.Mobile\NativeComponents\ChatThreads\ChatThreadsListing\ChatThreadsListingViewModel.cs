﻿namespace Platform.Client.Common.Features.Conversation;
public class ChatThreadsListingViewModel
{
    public string Id { get; set; } = null!;

    public string Avatar { get; set; } = null!;

    public ImageSource? ProfileImage =>
      !string.IsNullOrEmpty(Avatar)
          ? ImageSource.FromStream(() => new MemoryStream(Convert.FromBase64String(Avatar)))
          : null;


    public string Name { get; set; } = null!;

    public int FriendId { get; set; }

    public string? LastMessage { get; set; }

    public DateTime LastMessageTime { get; set; }

    public string LastMessageTimeString
    {
        get
        {
            var today = DateTime.UtcNow.Date;
            if (LastMessageTime.Date == today)
                return LastMessageTime.ToString("hh:mm tt");
            else if (LastMessageTime.Date == today.AddDays(-1))
                return "Yesterday";
            else
                return LastMessageTime.ToString("dd/MM/yyyy");
        }
    }
}
