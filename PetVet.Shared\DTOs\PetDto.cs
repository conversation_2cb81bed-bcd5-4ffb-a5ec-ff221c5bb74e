﻿using System.ComponentModel.DataAnnotations;

namespace PetVet.Shared.DTOs
{
    public class PetDto
    {
        public int Id { get; set; }

        [Required, MaxLength(35)]
        public string Name { get; set; } = string.Empty;

        public string? ImageUrl { get; set; }

        // Age can be as little as 0 (newborn)
        [Range(0, 50, ErrorMessage = "For individual pets, age must be between 1 day and 50 years.")]
        public int Age { get; set; }

        // Dynamic age calculation properties
        [System.Text.Json.Serialization.JsonIgnore]
        public string AgeDisplay
        {
            get
            {
                if (!DateOfBirth.HasValue)
                    return $"{Age} year(s)";

                var today = DateTime.Today;
                var birthDate = DateOfBirth.Value;

                // For future dates or same day, show 1 day
                if (birthDate > today || birthDate.Date == today.Date)
                    return "1 day";

                var years = today.Year - birthDate.Year;
                if (birthDate.Date > today.AddYears(-years)) years--;

                var months = today.Month - birthDate.Month;
                if (today.Day < birthDate.Day) months--;
                if (months < 0) months += 12;

                // Calculate remaining days
                var calcDate = birthDate.AddYears(years).AddMonths(months);
                var days = (today - calcDate).Days;

                if (years == 0 && months == 0)
                    return days > 0 ? $"{days} day(s)" : "1 day";
                else if (years == 0)
                    return days > 0
                        ? $"{months} month(s) {days} day(s)"
                        : $"{months} month(s)";
                else
                    return days > 0 || months > 0
                        ? $"{years} year(s) {months} month(s) {days} day(s)"
                        : $"{years} year(s)";
            }
        }

        // Weight is only required for individual pets, not group profiles
        // For group profiles, it represents an approximate average weight
        [Range(0.1, 100, ErrorMessage = "Weight must be between 0.1 and 100 kg.")]
        public double Weight { get; set; } = 0.1; // Default to minimum valid weight

        [Required, MaxLength(35)]
        public string Gender { get; set; } = string.Empty;

        [Required, MaxLength(35)]
        public string Breed { get; set; } = string.Empty;

        [Required]
        public DateTime? DateOfBirth { get; set; }

        [Required, MaxLength(35)]
        public string VaccinationStatus { get; set; } = string.Empty;

        [Required, Range(1, int.MaxValue, ErrorMessage = "Please select a valid category.")]
        public int CategoryId { get; set; }

        public string? CategoryName { get; set; }

        public int UserId { get; set; }

        // Number of animals in this group (for group categories)
        public int Count { get; set; } = 1;

        // Flag to check if this is a group category
        public bool IsGroupCategory { get; set; }
    }
}
