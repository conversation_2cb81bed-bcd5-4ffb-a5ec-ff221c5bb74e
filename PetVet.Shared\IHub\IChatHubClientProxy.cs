﻿using PetVet.ServiceContracts.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace PetVet.Shared.IHub
{
    public interface IChatHubClientProxy
    {
        Task OnNewMessage(int toUserId, string message);

        Task OnMessageDeliveryUpdate(int toUserId, string message);

        Task AcknowledgeMessageAsync(string messageId, DeliveryStatus deliveryStatus, DateTime timeStamp);

        Task OnCallAction(int toUserId, string message);

        Task Logout();
    }
}
