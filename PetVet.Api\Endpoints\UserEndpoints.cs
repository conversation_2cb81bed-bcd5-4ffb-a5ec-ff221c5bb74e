﻿using PetVet.Api.Services;
using PetVet.Shared;
using PetVet.Shared.DTOs;

namespace PetVet.Api.Endpoints
{
    public static class UserEndpoints
    {
        public static IEndpointRouteBuilder MapUserEndpoints(this IEndpointRouteBuilder app)
        {
            app.MapGet("/api/admin/homeData", async (UserService userService) =>
                Results.Ok(await userService.GetAdminHomeDataAsync()))
                .RequireAuthorization(p => p.RequireRole(nameof(UserRole.Admin))); ;

            var userGroup = app.MapGroup("/api/users")
                .RequireAuthorization();

            userGroup.MapGet("/{id:int}", async (int id, UserService userService) =>
                Results.Ok(await userService.GetUserAsync(id)));

            userGroup.MapGet("", async (UserService userService) =>
                Results.Ok(await userService.GetUsersAsync()));

            userGroup.MapGet("/vets/{id:int}", async (int id, UserService userService) =>
                Results.Ok(await userService.GetVetUserAsync(id)));

            userGroup.MapGet("/vets", async (UserService userService) =>
                Results.Ok(await userService.GetVetUsersAsync()));

            userGroup.MapGet("/vets/current/{userId:int}", async (int userId, UserService userService) =>
                Results.Ok(await userService.GetCurrentVetUserAsync(userId)));

            userGroup.MapPost("/vets/update", async (UserDto dto, UserService userService) =>
                Results.Ok(await userService.UpdateVetProfileAsync(dto)));

            userGroup.MapGet("/petOwners/current/{userId:int}", async (int userId, UserService userService) =>
                Results.Ok(await userService.GetCurrentPetOwnerUserAsync(userId)));

            userGroup.MapPost("/petOwners/update", async (UserDto dto, UserService userService) =>
                Results.Ok(await userService.UpdatePetOwnerProfileAsync(dto)));

            userGroup.MapPost("", async (UserDto dto, UserService userService) =>
                Results.Ok(await userService.SaveUserAsync(dto)))
                .RequireAuthorization(p => p.RequireRole(nameof(UserRole.Admin)));

            userGroup.MapDelete("/{id:int}", async (int id, UserService userService) =>
                Results.Ok(await userService.DeleteUserAsync(id)))
                .RequireAuthorization(p => p.RequireRole(nameof(UserRole.Admin)));

            userGroup.MapPatch("/{userId:int}/toggleStatus", async (int userId, UserService userService) =>
            {
                await userService.ToggleUserApprovedStatus(userId);
                return Results.Ok();
            });

            userGroup.MapGet("/{currentUserId:int}/chatUsers", async (int currentUserId, UserService userService) =>
                Results.Ok(await userService.GetChatUsersAsync(currentUserId)));

            return app;
        }
    }
}
