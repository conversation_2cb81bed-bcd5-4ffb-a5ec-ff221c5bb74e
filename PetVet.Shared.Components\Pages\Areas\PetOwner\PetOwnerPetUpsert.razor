﻿@page "/petOwner/myPets/profile/create"
@page "/petOwner/myPets/profile/update/{Id:int}"
@inject IPetApi _PetApi
@inject ICategoryApi _CategoryApi
@inject IJSRuntime _JS
@inject NavigationManager _NavigationManager
@inject IAppState _AppState
@inject PetVetAuthStateProvider _PetVetAuthStateProvider
@using System.ComponentModel.DataAnnotations

<PageTitle>@(Id > 0 ? "Update Pet Profile" : "Create Pet Profile")</PageTitle>

<!-- Modern Pet Profile Form -->
<div class="px-4 py-6 space-y-6">

    <!-- Header Section -->
    <div class="text-center mb-6">
        <div class="w-16 h-16 bg-gradient-to-r from-teal-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <i class="fas fa-paw text-white text-2xl"></i>
        </div>
        <h1 class="text-2xl font-bold text-gray-800 mb-2">@(Id > 0 ? "Update Pet Profile" : "Create Pet Profile")</h1>
        <p class="text-gray-600">@(Id > 0 ? "Update your pet's information and health records" : "Create a new profile for your pet")</p>
    </div>

    <!-- Main Content -->
    <EditForm Model="_pet" OnValidSubmit="ValidateAndSavePetAsync">
        <DataAnnotationsValidator />

        <!-- Profile Type Selection -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6">
            <h3 class="text-lg font-bold text-gray-800 mb-4">Profile Type</h3>
            <div class="grid grid-cols-2 gap-4">
                <div class="cursor-pointer" @onclick="() => profileType = ProfileType.Individual">
                    <div class="p-4 rounded-xl border-2 transition-all @(profileType == ProfileType.Individual ? "border-teal-500 bg-teal-50" : "border-gray-200 hover:border-gray-300")">
                        <div class="text-center">
                            <div class="w-12 h-12 @(profileType == ProfileType.Individual ? "bg-teal-500" : "bg-gray-400") rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-paw text-white"></i>
                            </div>
                            <InputRadioGroup @bind-Value="profileType">
                                <InputRadio Value="@ProfileType.Individual" id="individual" class="hidden" />
                            </InputRadioGroup>
                            <label class="font-medium text-gray-800 cursor-pointer" for="individual">Individual</label>
                        </div>
                    </div>
                    <p class="text-center text-sm text-gray-600 mt-2">Create a profile for a single pet</p>
                </div>
                <div class="cursor-pointer" @onclick="() => profileType = ProfileType.Group">
                    <div class="p-4 rounded-xl border-2 transition-all @(profileType == ProfileType.Group ? "border-teal-500 bg-teal-50" : "border-gray-200 hover:border-gray-300")">
                        <div class="text-center">
                            <div class="w-12 h-12 @(profileType == ProfileType.Group ? "bg-teal-500" : "bg-gray-400") rounded-full flex items-center justify-center mx-auto mb-3">
                                <i class="fas fa-paw text-white"></i>
                                <i class="fas fa-paw text-white -ml-2"></i>
                            </div>
                            <InputRadioGroup @bind-Value="profileType">
                                <InputRadio Value="@ProfileType.Group" id="group" class="hidden" />
                            </InputRadioGroup>
                            <label class="font-medium text-gray-800 cursor-pointer" for="group">Group</label>
                        </div>
                    </div>
                    <p class="text-center text-sm text-gray-600 mt-2">Create a profile for multiple pets</p>
                </div>
            </div>
        </div>

        <!-- Pet Information Form -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6">
            <h3 class="text-lg font-bold text-gray-800 mb-6">Pet Information</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Pet Name -->
                <div>
                    <label for="Name" class="block text-sm font-medium text-gray-700 mb-2">Name</label>
                    <InputText @bind-Value="_pet.Name"
                               class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all"
                               id="Name"
                               placeholder="Enter pet name" />
                    <ValidationMessage For="@(() => _pet.Name)" class="text-red-500 text-sm mt-1" />
                </div>

                <!-- Pet Category -->
                <div>
                    <label for="Category" class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                    <InputSelect @bind-Value="_pet.CategoryId"
                                 class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all"
                                 id="Category"
                                 @onchange="HandleCategoryChange">
                        <option value="0">Select Category</option>
                        @foreach (var category in _categories)
                        {
                            <option value="@category.Id">
                                @category.Name
                            </option>
                        }
                    </InputSelect>
                    <ValidationMessage For="@(() => _pet.CategoryId)" class="text-red-500 text-sm mt-1" />
                </div>

                <!-- Pet Count (Only for group profiles) -->
                @if (profileType == ProfileType.Group)
                {
                    <div>
                        <label for="Count" class="block text-sm font-medium text-gray-700 mb-2">Number of Animals</label>
                        <InputNumber @bind-Value="_pet.Count"
                                     class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all"
                                     id="Count"
                                     placeholder="Enter number of animals"
                                     min="1" />
                        <ValidationMessage For="@(() => _pet.Count)" class="text-red-500 text-sm mt-1" />
                        <div class="text-sm text-gray-500 mt-1">For group pets, specify the total count.</div>
                    </div>
                }

                <!-- Pet Weight -->
                <div>
                    <label for="Weight" class="block text-sm font-medium text-gray-700 mb-2">
                        @(profileType == ProfileType.Group ? "Average Weight (kg) - Optional" : "Weight (kg)")
                    </label>
                    <InputNumber @bind-Value="_pet.Weight"
                                 class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all"
                                 id="Weight"
                                 placeholder="Enter weight"
                                 step="0.1" />
                    <ValidationMessage For="@(() => _pet.Weight)" class="text-red-500 text-sm mt-1" />
                    <div class="text-sm text-gray-500 mt-1">
                        @(profileType == ProfileType.Group
                            ? "Enter approximate average weight if known (optional)"
                            : "Enter weight in kilograms (e.g., 0.5, 1.2, 5.0)")
                    </div>
                </div>

                <!-- Pet Gender -->
                <div>
                    <label for="Gender" class="block text-sm font-medium text-gray-700 mb-2">Gender</label>
                    <InputText @bind-Value="_pet.Gender"
                               class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all"
                               id="Gender"
                               placeholder="Enter gender" />
                    <ValidationMessage For="@(() => _pet.Gender)" class="text-red-500 text-sm mt-1" />
                </div>

                <!-- Pet Breed -->
                <div>
                    <label for="Breed" class="block text-sm font-medium text-gray-700 mb-2">Breed</label>
                    <InputText @bind-Value="_pet.Breed"
                               class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all"
                               id="Breed"
                               placeholder="Enter breed" />
                    <ValidationMessage For="@(() => _pet.Breed)" class="text-red-500 text-sm mt-1" />
                </div>

                <!-- Pet Date of Birth -->
                <div>
                    <label for="DateOfBirth" class="block text-sm font-medium text-gray-700 mb-2">Date of Birth</label>
                    <InputDate @bind-Value="_pet.DateOfBirth"
                               class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all"
                               id="DateOfBirth"
                               placeholder="Select date of birth"
                               max="@DateTime.Today.ToString("yyyy-MM-dd")"
                               min="@DateTime.Today.AddYears(-50).ToString("yyyy-MM-dd")" />
                    <ValidationMessage For="@(() => _pet.DateOfBirth)" class="text-red-500 text-sm mt-1" />
                    <div class="text-sm text-gray-500 mt-1">
                        @if (profileType == ProfileType.Group)
                        {
                            <span>Date when the group was formed or acquired (1 day to 50 years range)</span>
                        }
                        else
                        {
                            <span>Birth date of the pet (1 day to 50 years range)</span>
                        }
                    </div>
                </div>

                <!-- Pet Vaccination Status -->
                <div>
                    <label for="VaccinationStatus" class="block text-sm font-medium text-gray-700 mb-2">Vaccination Status</label>
                    <InputText @bind-Value="_pet.VaccinationStatus"
                               class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all"
                               id="VaccinationStatus"
                               placeholder="Enter vaccination status" />
                    <ValidationMessage For="@(() => _pet.VaccinationStatus)" class="text-red-500 text-sm mt-1" />
                </div>
            </div>
        </div>

        <!-- Pet Image Section -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 mb-6">
            <h3 class="text-lg font-bold text-gray-800 mb-4">Pet Image</h3>

            @if (!string.IsNullOrEmpty(_pet.ImageUrl))
            {
                <div class="flex items-start gap-4">
                    <div class="flex-shrink-0">
                        <img src="@_pet.ImageUrl"
                             alt="petImage"
                             class="w-32 h-32 object-cover rounded-2xl border border-gray-200" />
                    </div>
                    <div class="flex-1">
                        <div class="bg-green-50 border border-green-200 rounded-xl p-4 mb-4">
                            <div class="flex items-center gap-2 text-green-700">
                                <i class="fas fa-check-circle"></i>
                                <span class="text-sm font-medium">Image uploaded successfully</span>
                            </div>
                        </div>
                        <button type="button"
                                class="px-4 py-2 bg-red-100 text-red-700 rounded-xl text-sm font-medium hover:bg-red-200 transition-all"
                                @onclick="() => HandleImageDeleteConfirmation()">
                            <i class="fas fa-trash mr-2"></i>Remove Image
                        </button>
                    </div>
                </div>
            }
            else
            {
                <div class="border-2 border-dashed border-gray-300 rounded-2xl p-8 text-center hover:border-teal-400 transition-all">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-cloud-upload-alt text-gray-400 text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-medium text-gray-800 mb-2">Upload Pet Image</h4>
                    <p class="text-gray-500 mb-4">Choose a clear photo of your pet</p>
                    <label class="inline-block px-6 py-3 bg-teal-500 text-white rounded-xl font-medium hover:bg-teal-600 transition-all cursor-pointer">
                        <i class="fas fa-upload mr-2"></i>Choose File
                        <InputFile OnChange="LoadFiles" class="hidden" id="CustomFile" accept="image/x-png,image/jpeg" />
                    </label>
                    <p class="text-sm text-gray-400 mt-2">PNG, JPG or JPEG (Max 5MB)</p>
                </div>
            }
        </div>

        <!-- Error Message -->
        @if (_error != null)
        {
            <div class="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
                <div class="flex items-start justify-between">
                    <div class="flex items-center gap-2 text-red-700">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span class="text-sm">@_error</span>
                    </div>
                    <button type="button"
                            class="text-red-400 hover:text-red-600 transition-colors"
                            @onclick="() => _error = null">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        }

        <!-- Action Buttons -->
        <div class="flex gap-4">
            @if (!_isBusy)
            {
                <button type="submit"
                        class="flex-1 bg-gradient-to-r from-teal-500 to-blue-600 text-white py-4 px-6 rounded-2xl font-medium hover:from-teal-600 hover:to-blue-700 transition-all transform hover:scale-105 shadow-lg flex items-center justify-center gap-2"
                        disabled="@_isBusy">
                    <i class="fas @(Id > 0 ? "fa-save" : "fa-plus")"></i>
                    <span>@(Id > 0 ? "Update Pet" : "Create Pet")</span>
                </button>
            }
            else
            {
                <button type="button"
                        class="flex-1 bg-gray-400 text-white py-4 px-6 rounded-2xl font-medium cursor-not-allowed flex items-center justify-center gap-2"
                        disabled>
                    <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span>@(Id > 0 ? "Updating..." : "Creating...")</span>
                </button>
            }

            <a href="petOwner/myPets"
               class="flex-1 bg-gray-100 text-gray-700 py-4 px-6 rounded-2xl font-medium hover:bg-gray-200 transition-all flex items-center justify-center gap-2 @(_isBusy ? "pointer-events-none opacity-50" : "")">
                <i class="fas fa-arrow-left"></i>
                <span>Back To List</span>
            </a>
        </div>
    </EditForm>

</div>

<!-- Modal for Image Deletion -->
<BsModal OnModalConfirmation="ConfirmDeleteImage"
         ButtonText="Delete"
         Title="Are you sure you want to delete this image?">
</BsModal>

@code {
    public enum ProfileType { Individual, Group }

    [Parameter] public int Id { get; set; }
    private CategoryDto[] _categories = [];
    private PetDto _pet = new();
    private bool _isBusy;
    private string? _error;
    private string? _directoryPath { get; set; }
    private bool isProcessing { get; set; } = true;
    private int _currentUserId => _PetVetAuthStateProvider?.User.Id ?? 0;
    private bool isGroupCategory { get; set; } = false;
    private ProfileType profileType { get; set; } = ProfileType.Individual;

    protected override async Task OnInitializedAsync() => await LoadPetAsync();

    private async Task LoadPetAsync()
    {
        if (Id > 0)
        {
            isProcessing = true;
            _AppState.ShowLoader("Fetching Pet Data");
            _pet = await _PetApi.GetPetAsync(Id);

            // Check if this is a group category
            if (_pet.CategoryId > 0)
            {
                CheckIfGroupCategory(_pet.CategoryId);
            }

            // Set profile type based on pet count
            profileType = _pet.Count > 1 ? ProfileType.Group : ProfileType.Individual;
        }

        _categories = await _CategoryApi.GetCategoriesAsync();
        _AppState.HideLoader();
        isProcessing = false;
 
    }

    private void CheckIfGroupCategory(int categoryId)
    {
        var category = _categories.FirstOrDefault(c => c.Id == categoryId);
        isGroupCategory = category?.IsGroupCategory ?? false;
    }

    private void HandleCategoryChange(ChangeEventArgs e)
    {
        if (int.TryParse(e.Value?.ToString(), out int categoryId))
        {
            CheckIfGroupCategory(categoryId);
        }
    }

    private void OnEditPet(PetDto pet)
    {
        _pet = pet;
        _NavigationManager.NavigateTo("/petOwner/myPets");
    }

    private async Task ValidateAndSavePetAsync()
    {
        // Validate birth date - should not be in the future and should ensure min age of 1 day
        if (_pet.DateOfBirth.HasValue)
        {
            if (_pet.DateOfBirth.Value > DateTime.Today)
            {
                _error = "Date of birth cannot be in the future.";
                await _JS.ToastrError(_error);
                return;
            }

            // Ensure pet is not too old (max 50 years)
            if (_pet.DateOfBirth.Value < DateTime.Today.AddYears(-50))
            {
                _error = "Pet age cannot be more than 50 years.";
                await _JS.ToastrError(_error);
                return;
            }
        }

        // Custom validation for group vs individual profiles
        if (profileType == ProfileType.Group)
        {
            // For group profiles, set default values for age and weight if they're invalid
            if (_pet.Count < 1)
            {
                _pet.Count = 1;
            }

            // Set a default weight for group pets, but don't validate it
            if (_pet.Weight <= 0)
            {
                _pet.Weight = 0.1; // Minimal default weight
            }
        }
        else // Individual profile
        {
            // Validate weight for individual pets only
            if (_pet.Weight < 0.1 || _pet.Weight > 100)
            {
                _error = "Weight must be between 0.1 and 100 kg.";
                await _JS.ToastrError(_error);
                return;
            }

            // Validate age for individual pets
            if (_pet.Age < 0 || _pet.Age > 50)
            {
                _error = "For individual pets, age must be between 1 day and 50 years.";
                await _JS.ToastrError(_error);
                return;
            }
        }

        // Continue with saving
        await SavePetAsync();
    }

    private async Task SavePetAsync()
    {
        _error = null;
        _AppState.ShowLoader("Saving Pet Data");
        _isBusy = true;
        isProcessing = true;

        try
        {
            // Update count based on profile type
            if (profileType == ProfileType.Individual)
            {
                _pet.Count = 1;
            }
            // Explicitly set IsGroupCategory to true when using the Group profile type
            else if (profileType == ProfileType.Group)
            {
                _pet.IsGroupCategory = true;
            }

            // Set the current user ID only for new pets
            // When updating, the server will preserve the original owner
            if (Id == 0) // New pet
            {
                _pet.UserId = _currentUserId;
            }

            var response = await _PetApi.SavePetAsync(_pet);

            if (Id > 0)
            {
                OnEditPet(_pet);
                await _JS.ToastrSuccess("Pet profile updated successfully.");
            }
            else
            {
                if (!response.IsSuccess)
                {
                    // Error message on UI
                    _error = response.ErrorMessage ?? "An unknown error has occurred.";
                    await _JS.ToastrError(_error);
                    return;
                }

                // Reset the form
                _pet = new();
                await _JS.ToastrSuccess("Pet profile created successfully.");
                _NavigationManager.NavigateTo("/petOwner/myPets");
            }
        }
        catch (Exception ex)
        {
            await _JS.ToastrError("An unknown error has occurred.");
            _error = ex.Message;
        }
        finally
        {
            _AppState.HideLoader();
            _isBusy = false;
            isProcessing = false;
        }
    }

    private async Task LoadFiles(InputFileChangeEventArgs e)
    {
        _AppState.ShowLoader("Uploading Image");

        var file = e.File;

        // Check file type
        if (file.ContentType != "image/jpeg" && file.ContentType != "image/png" && file.ContentType != "image/jpg")
        {
            _error = "The file format is invalid. Please upload an image in JPEG, PNG, or JPG format.";
            await _JS.ToastrError(_error);
            _AppState.HideLoader();
            return;
        }

        var fileContent = new byte[file.Size];
        await file.OpenReadStream().ReadAsync(fileContent);

        var base64Image = Convert.ToBase64String(fileContent);
        _pet.ImageUrl = $"data:image/jpeg;base64,{base64Image}";

        _AppState.HideLoader();
    }

    private void HandleImageDeleteConfirmation()
    {
        _JS.InvokeVoidAsync("ShowConfirmationModal");
    }

    public async Task ConfirmDeleteImage(bool isConfirmed)
    {
        if (isConfirmed)
        {
            _pet.ImageUrl = null;
            await _JS.InvokeVoidAsync("HideConfirmationModal");
            await _JS.ToastrSuccess("Image deleted successfully.");
        }

        _AppState.HideLoader();
        StateHasChanged();
    }
}
