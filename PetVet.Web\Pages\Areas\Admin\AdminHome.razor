﻿@page "/admin/home"
@inject IAppState _AppState
@inject IUserApi _UserApi
@inject IJSRuntime JSRuntime

<PageTitle>Admin Dashboard | PetVet</PageTitle>

@if (!isProcessing)
{
    @if (_data != null)
    {
        <div class="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 p-4 md:p-8 admin-dashboard">
            <!-- Dashboard Header -->
            <div class="mb-8 animate-fade-scale">
                <div class="bg-gradient-to-r from-primary-600 to-primary-700 rounded-2xl p-8 text-white shadow-xl relative overflow-hidden">
                    <div class="absolute top-0 right-0 w-32 h-32 bg-white opacity-10 rounded-full -translate-y-8 translate-x-8"></div>
                    <div class="absolute bottom-0 left-0 w-24 h-24 bg-white opacity-10 rounded-full translate-y-4 -translate-x-4"></div>
                    <div class="relative z-10">
                        <h1 class="text-4xl font-bold mb-2 animate-fadeIn">PetVet Admin Dashboard</h1>
                        <p class="text-primary-100 text-lg">Comprehensive overview of your veterinary platform</p>
                        <div class="mt-4 text-sm text-primary-200">
                            Last updated: @DateTime.Now.ToString("MMM dd, yyyy - HH:mm")
                        </div>
                    </div>
                </div>
            </div>

            <!-- Key Metrics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Total Users Card -->
                <div class="metric-card animate-slide-up" style="animation-delay: 0.1s">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600 mb-1">Total Users</p>
                            <div class="flex items-baseline">
                                <p class="text-3xl font-bold text-primary-600 counter-animation" data-target="@_data.TotalUsers">@_data.TotalUsers</p>
                                <span class="ml-2 status-badge status-badge-success">Active</span>
                            </div>
                        </div>
                        <div class="bg-gradient-primary-admin p-3 rounded-xl">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="bg-primary-50 rounded-lg p-3">
                            <div class="flex justify-between text-xs text-gray-600 mb-1">
                                <span>Pet Owners: @_data.TotalPetOwners</span>
                                <span>Vets: @_data.TotalVets</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-gradient-to-r from-secondary-500 to-primary-500 h-2 rounded-full loading-bar" 
                                     style="width: @((_data.TotalPetOwners * 100.0 / _data.TotalUsers))%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Total Pets Card -->
                <div class="metric-card animate-slide-up" style="animation-delay: 0.2s">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600 mb-1">Total Pets</p>
                            <div class="flex items-baseline">
                                <p class="text-3xl font-bold text-secondary-600 counter-animation" data-target="@_data.TotalPets">@_data.TotalPets</p>
                                <span class="ml-2 status-badge status-badge-warning">Registered</span>
                            </div>
                        </div>
                        <div class="bg-gradient-secondary-admin p-3 rounded-xl">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="bg-secondary-50 rounded-lg p-3">
                            <div class="text-xs text-gray-600 mb-1">Pet Distribution</div>
                            <div class="flex justify-between text-xs text-gray-500 mb-1">
                                <span>Total: @_data.TotalPets</span>
                                <span>Average per Owner: @((_data.TotalPets > 0 && _data.TotalPetOwners > 0) ? Math.Round((double)_data.TotalPets / _data.TotalPetOwners, 1) : 0)</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-gradient-to-r from-secondary-500 to-secondary-600 h-2 rounded-full loading-bar-delay-1" 
                                     style="width: @(Math.Min(_data.TotalPets * 2, 100))%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Categories Card -->
                <div class="metric-card animate-slide-up" style="animation-delay: 0.3s">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600 mb-1">Categories</p>
                            <div class="flex items-baseline">
                                <p class="text-3xl font-bold text-warning-600 counter-animation" data-target="@(_data.TotalPetCategories + _data.TotalEducationCategories)">@(_data.TotalPetCategories + _data.TotalEducationCategories)</p>
                                <span class="ml-2 status-badge status-badge-secondary">Active</span>
                            </div>
                        </div>
                        <div class="bg-gradient-warning-admin p-3 rounded-xl">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-4H5m14 8H5"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="bg-warning-50 rounded-lg p-3">
                            <div class="flex justify-between text-xs text-gray-600 mb-1">
                                <span>Pet Categories: @_data.TotalPetCategories</span>
                                <span>Education: @_data.TotalEducationCategories</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-gradient-to-r from-warning-500 to-warning-600 h-2 rounded-full loading-bar-delay-2" 
                                     style="width: @((_data.TotalPetCategories * 100.0 / Math.Max(_data.TotalPetCategories + _data.TotalEducationCategories, 1)))%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Education Content Card -->
                <div class="metric-card animate-slide-up" style="animation-delay: 0.4s">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-600 mb-1">Education Content</p>
                            <div class="flex items-baseline">
                                <p class="text-3xl font-bold text-accent-600 counter-animation" data-target="@_data.TotalEducationCategories">@_data.TotalEducationCategories</p>
                                <span class="ml-2 status-badge status-badge-primary">Published</span>
                            </div>
                        </div>
                        <div class="bg-gradient-accent-admin p-3 rounded-xl">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="bg-accent-50 rounded-lg p-3">
                            <div class="text-xs text-gray-600 mb-1">Content Library Growth</div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div class="bg-gradient-to-r from-accent-500 to-accent-600 h-2 rounded-full loading-bar-delay-3" 
                                     style="width: @(Math.Min(_data.TotalEducationCategories * 3, 100))%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts and Analytics Section -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <!-- User Distribution Chart -->
                <div class="admin-card p-6 animate-slide-right" style="animation-delay: 0.5s">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-bold text-gray-800">User Distribution</h3>
                        <div class="bg-primary-100 p-2 rounded-lg">
                            <svg class="w-5 h-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="relative chart-container">
                        <!-- Circular Progress Chart -->
                        <div class="flex items-center justify-center mb-6">
                            <div class="relative w-48 h-48">
                                <svg class="w-48 h-48 transform -rotate-90" viewBox="0 0 100 100">
                                    <!-- Background circle -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#f3f4f6" stroke-width="8"/>
                                    <!-- Pet Owners arc -->
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="url(#petOwnersGradient)" stroke-width="8"
                                            stroke-dasharray="@((_data.TotalPetOwners * 251.2 / _data.TotalUsers)), 251.2"
                                            stroke-linecap="round" class="loading-circle"/>
                                    <!-- Vets arc -->
                                    <circle cx="50" cy="50" r="30" fill="none" stroke="url(#vetsGradient)" stroke-width="8"
                                            stroke-dasharray="@((_data.TotalVets * 188.4 / _data.TotalUsers)), 188.4"
                                            stroke-linecap="round" class="loading-circle-delay"/>
                                    <defs>
                                        <linearGradient id="petOwnersGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#45C1A5;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#3E97DA;stop-opacity:1" />
                                        </linearGradient>
                                        <linearGradient id="vetsGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#f59e0b;stop-opacity:1" />
                                            <stop offset="100%" style="stop-color:#ef4444;stop-opacity:1" />
                                        </linearGradient>
                                    </defs>
                                </svg>
                                <div class="absolute inset-0 flex items-center justify-center">
                                    <div class="text-center">
                                        <div class="text-2xl font-bold text-gray-800 counter-animation" data-target="@_data.TotalUsers">@_data.TotalUsers</div>
                                        <div class="text-sm text-gray-600">Total Users</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Legend -->
                        <div class="chart-legend">
                            <div class="legend-item">
                                <div class="legend-color bg-gradient-to-r from-secondary-500 to-primary-500"></div>
                                <span>Pet Owners (@_data.TotalPetOwners)</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color bg-gradient-to-r from-warning-500 to-error-500"></div>
                                <span>Vets (@_data.TotalVets)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Overview -->
                <div class="admin-card p-6 animate-slide-right" style="animation-delay: 0.6s">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-bold text-gray-800">System Overview</h3>
                        <div class="bg-secondary-100 p-2 rounded-lg">
                            <svg class="w-5 h-5 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="space-y-6">
                        <!-- Pets Progress -->
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">Registered Pets</span>
                                <span class="text-sm text-gray-500">@_data.TotalPets</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div class="bg-gradient-to-r from-secondary-500 to-secondary-600 h-3 rounded-full loading-bar-delay-1 progress-bar" 
                                     style="width: @(Math.Min(_data.TotalPets * 2, 100))%"></div>
                            </div>
                        </div>
                        
                        <!-- Categories Progress -->
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">Pet Categories</span>
                                <span class="text-sm text-gray-500">@_data.TotalPetCategories</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div class="bg-gradient-to-r from-warning-500 to-warning-600 h-3 rounded-full loading-bar-delay-2 progress-bar" 
                                     style="width: @(Math.Min(_data.TotalPetCategories * 5, 100))%"></div>
                            </div>
                        </div>
                        
                        <!-- Education Progress -->
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">Education Content</span>
                                <span class="text-sm text-gray-500">@_data.TotalEducationCategories</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div class="bg-gradient-to-r from-accent-500 to-accent-600 h-3 rounded-full loading-bar-delay-3 progress-bar" 
                                     style="width: @(Math.Min(_data.TotalEducationCategories * 4, 100))%"></div>
                            </div>
                        </div>
                        
                        <!-- System Health -->
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm font-medium text-gray-700">Platform Activity</span>
                                <span class="text-sm text-success-600 font-medium">@systemHealthStatus</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-3">
                                <div class="bg-gradient-to-r from-success-500 to-success-600 h-3 rounded-full loading-bar-delay-4 progress-bar" 
                                     style="width: @systemHealthPercentage%"></div>
                            </div>
                        </div>
                    </div>
                </div>
                </div>

            <!-- Platform Statistics and Quick Actions -->
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Platform Statistics -->
                <div class="lg:col-span-2 admin-card p-6 animate-slide-up" style="animation-delay: 0.7s">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-bold text-gray-800">Platform Statistics</h3>
                        <div class="bg-accent-100 p-2 rounded-lg">
                            <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="text-center p-4 bg-primary-50 rounded-xl">
                            <div class="flex justify-center mb-2">
                                <svg class="w-8 h-8 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-bold text-primary-600 counter-animation" data-target="@_data.TotalUsers">@_data.TotalUsers</div>
                            <div class="text-xs text-gray-600 mt-1">Total Users</div>
                        </div>
                        <div class="text-center p-4 bg-secondary-50 rounded-xl">
                            <div class="flex justify-center mb-2">
                                <svg class="w-8 h-8 text-secondary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-bold text-secondary-600 counter-animation" data-target="@_data.TotalPets">@_data.TotalPets</div>
                            <div class="text-xs text-gray-600 mt-1">Registered Pets</div>
                        </div>
                        <div class="text-center p-4 bg-warning-50 rounded-xl">
                            <div class="flex justify-center mb-2">
                                <svg class="w-8 h-8 text-warning-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-4H5m14 8H5"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-bold text-warning-600 counter-animation" data-target="@_data.TotalPetCategories">@_data.TotalPetCategories</div>
                            <div class="text-xs text-gray-600 mt-1">Pet Categories</div>
                        </div>
                        <div class="text-center p-4 bg-accent-50 rounded-xl">
                            <div class="flex justify-center mb-2">
                                <svg class="w-8 h-8 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                </svg>
                            </div>
                            <div class="text-2xl font-bold text-accent-600 counter-animation" data-target="@_data.TotalEducationCategories">@_data.TotalEducationCategories</div>
                            <div class="text-xs text-gray-600 mt-1">Education Content</div>
                        </div>
                    </div>
                    
                    <!-- Additional Statistics -->
                    <div class="mt-6 pt-6 border-t border-gray-200">
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Pet Owners:</span>
                                <span class="font-semibold text-secondary-600">@_data.TotalPetOwners</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Veterinarians:</span>
                                <span class="font-semibold text-primary-600">@_data.TotalVets</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Avg Pets/Owner:</span>
                                <span class="font-semibold text-warning-600">@((_data.TotalPets > 0 && _data.TotalPetOwners > 0) ? Math.Round((double)_data.TotalPets / _data.TotalPetOwners, 1) : 0)</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Total Categories:</span>
                                <span class="font-semibold text-accent-600">@(_data.TotalPetCategories + _data.TotalEducationCategories)</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Platform Load:</span>
                                <span class="font-semibold text-success-600">@(((double)(_data.TotalPets + _data.TotalUsers) / 1000 * 100).ToString("F1"))%</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Activity Score:</span>
                                <span class="font-semibold text-success-600">@systemHealthPercentage%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="admin-card p-6 animate-slide-up" style="animation-delay: 0.8s">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-bold text-gray-800">Quick Actions</h3>
                        <div class="bg-success-100 p-2 rounded-lg">
                            <svg class="w-5 h-5 text-success-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="space-y-4">
                        <a href="/admin/manage/users/profile" class="group block w-full text-white rounded-2xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 admin-quick-btn-primary">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4 group-hover:bg-opacity-30 transition-all duration-300">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-lg">Manage Users</h4>
                                        <p class="text-primary-100 text-sm">@_data.TotalUsers registered users</p>
                                    </div>
                                </div>
                                <svg class="w-5 h-5 text-white text-opacity-70 group-hover:text-opacity-100 group-hover:translate-x-1 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                        </a>

                        <a href="/admin/manage/pets/profile" class="group block w-full text-white rounded-2xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 admin-quick-btn-secondary">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4 group-hover:bg-opacity-30 transition-all duration-300">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-lg">Manage Pets</h4>
                                        <p class="text-secondary-100 text-sm">@_data.TotalPets registered pets</p>
                                    </div>
                                </div>
                                <svg class="w-5 h-5 text-white text-opacity-70 group-hover:text-opacity-100 group-hover:translate-x-1 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                        </a>

                        <a href="/admin/manage/categories" class="group block w-full text-white rounded-2xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 admin-quick-btn-warning">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4 group-hover:bg-opacity-30 transition-all duration-300">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14-4H5m14 8H5"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-lg">Categories</h4>
                                        <p class="text-warning-100 text-sm">@_data.TotalPetCategories categories</p>
                                    </div>
                                </div>
                                <svg class="w-5 h-5 text-white text-opacity-70 group-hover:text-opacity-100 group-hover:translate-x-1 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                        </a>

                        <a href="/admin/manage/education" class="group block w-full text-white rounded-2xl p-4 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 hover:-translate-y-1 admin-quick-btn-accent">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="bg-white bg-opacity-20 p-3 rounded-xl mr-4 group-hover:bg-opacity-30 transition-all duration-300">
                                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-lg">Education</h4>
                                        <p class="text-accent-100 text-sm">@_data.TotalEducationCategories articles</p>
                                    </div>
                                </div>
                                <svg class="w-5 h-5 text-white text-opacity-70 group-hover:text-opacity-100 group-hover:translate-x-1 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    }
}
else
{
    <div class="flex items-center justify-center min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
        <div class="text-center">
            <div class="loading-shimmer w-16 h-16 rounded-full mx-auto mb-4"></div>
            <div class="inline-flex items-center px-6 py-3 font-semibold leading-6 text-sm shadow rounded-xl text-white bg-gradient-primary-admin transition ease-in-out duration-150">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading Dashboard Data...
            </div>
        </div>
    </div>
}

<style>
    .counter-animation {
        animation: countUp 2s ease-out forwards;
    }
    
    .loading-bar {
        animation: loadBar 2s ease-out forwards;
        width: 0% !important;
    }
    
    .loading-bar-delay-1 {
        animation: loadBar 2s ease-out 0.3s forwards;
        width: 0% !important;
    }
    
    .loading-bar-delay-2 {
        animation: loadBar 2s ease-out 0.6s forwards;
        width: 0% !important;
    }
    
    .loading-bar-delay-3 {
        animation: loadBar 2s ease-out 0.9s forwards;
        width: 0% !important;
    }
    
    .loading-bar-delay-4 {
        animation: loadBar 2s ease-out 1.2s forwards;
        width: 0% !important;
    }
    
    .loading-circle {
        animation: loadCircle 2s ease-out forwards;
        stroke-dasharray: 0, 251.2;
    }
    
    .loading-circle-delay {
        animation: loadCircle 2s ease-out 0.5s forwards;
        stroke-dasharray: 0, 188.4;
    }
    
    @@keyframes loadBar {
        from { width: 0%; }
        to { width: var(--target-width, 100%); }
    }
    
    @@keyframes loadCircle {
        from { stroke-dasharray: 0, 1000; }
        to { stroke-dasharray: var(--target-dash, 50), 1000; }
    }
    
    @@keyframes countUp {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>

@code {
    private AdminHomeDataDto? _data;
    private bool isProcessing { get; set; } = true;
    
    // Calculated properties
    private string systemHealthStatus = "Excellent";
    private int systemHealthPercentage = 95;

    protected override async Task OnInitializedAsync()
    {
        isProcessing = true;
        _AppState.ShowLoader("Loading Dashboard Data...");
        
        try
        {
            // Load admin data using only registered services
        _data = await _UserApi.GetAdminHomeDataAsync();
            
            // Calculate system health based on data
            CalculateSystemHealth();
        }
        catch (Exception ex)
        {
            // Handle error gracefully
            Console.WriteLine($"Error loading admin data: {ex.Message}");
            systemHealthStatus = "Warning";
            systemHealthPercentage = 60;
        }
        finally
        {
        _AppState.HideLoader();
        isProcessing = false;
        }
    }

    private void CalculateSystemHealth()
    {
        if (_data != null)
        {
            // Calculate platform activity score based on data
            var totalActivity = _data.TotalUsers + _data.TotalPets + _data.TotalPetCategories + _data.TotalEducationCategories;
            var activityScore = Math.Min(100, totalActivity / 2); // Scale activity
            
            systemHealthPercentage = Math.Max(60, activityScore);
            systemHealthStatus = systemHealthPercentage switch
            {
                >= 90 => "Excellent",
                >= 80 => "Good", 
                >= 70 => "Fair",
                _ => "Warning"
            };
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && _data != null)
        {
            try
            {
                // Initialize simple animations without complex JavaScript
                await JSRuntime.InvokeVoidAsync("eval", @"
                    setTimeout(() => {
                        document.querySelectorAll('.loading-bar, .loading-bar-delay-1, .loading-bar-delay-2, .loading-bar-delay-3, .loading-bar-delay-4').forEach((bar, index) => {
                            const targetWidth = bar.style.width;
                            bar.style.width = '0%';
                            setTimeout(() => {
                                bar.style.transition = 'width 2s ease-out';
                                bar.style.width = targetWidth;
                            }, index * 300);
                        });
                    }, 500);
                ");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Animation initialization error: {ex.Message}");
            }
        }
    }
}
