.Calendar-CONTAINER {
    background: #f2f6f9;
    padding: 2rem;
    min-height: 100vh;
    width: 100%;
    max-width: 72rem;
    margin: 0 auto;
    font-family: 'Inter', sans-serif;
}

/* Header Section */
.header-section {
    background: #157BAB;
    border-radius: 1.25rem;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    text-align: center;
}

.header-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    font-family: 'Acme', sans-serif;
}

.header-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    
}

/* Calendar Controls */
.calendar-controls {
    background: white;
    border-radius: 1.25rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.controls-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.control-btn {
    background: #FFC107;
    color: #1F2937;
    border: none;
    border-radius: 1.25rem;
    padding: 0.75rem 1.25rem;
    
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

    .control-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(251, 191, 36, 0.5);
    }

.current-month {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1F2937;
    font-family: 'Acme', sans-serif;
    margin: 0;
}

/* Calendar Grid */
.calendar-grid {
    background: white;
    border-radius: 1.25rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.calendar-header {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    background: #157BAB;
    padding: 1rem 0;
}

.week-day {
    text-align: center;
    color: white;
    font-weight: 600;
    font-family: 'Acme', sans-serif;
}

.calendar-body {
    display: flex;
    flex-direction: column;
}

.calendar-week {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    border-bottom: 1px solid #E5E7EB;
}

    .calendar-week:last-child {
        border-bottom: none;
    }

.calendar-day {
    min-height: 120px;
    padding: 0.75rem;
    border-right: 1px solid #E5E7EB;
    background: white;
    transition: background-color 0.3s ease;
}

    .calendar-day:hover {
        background: #F3F4F6;
    }

    .calendar-day:last-child {
        border-right: none;
    }

    .calendar-day.other-month {
        background: #F9FAFB;
        color: #9CA3AF;
    }

    .calendar-day.today {
        background: #EFF6FF;
    }

.day-header {
    display: flex;
    justify-content: center;
    margin-bottom: 0.75rem;
}

.day-number {
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 9999px;
    font-weight: 600;
    font-family: 'Acme', sans-serif;
}

.today .day-number {
    background: #157BAB;
    color: white;
}

/* Appointment Items */
.day-appointments {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.appointment-item {
    padding: 0.5rem;
    border-radius: 0.75rem;
    font-size: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    
}

    .appointment-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .appointment-item.status-confirmed {
        background: #DBEAFE;
        border-left: 3px solid #3B82F6;
    }

    .appointment-item.status-completed {
        background: #D1FAE5;
        border-left: 3px solid #10B981;
    }

    .appointment-item.status-cancelled {
        background: #FEE2E2;
        border-left: 3px solid #EF4444;
    }

    .appointment-item.status-expired {
        background: #F3F4F6;
        border-left: 3px solid #6B7280;
    }

.appointment-time {
    font-weight: 600;
    color: #1F2937;
}

.appointment-patient {
    color: #6B7280;
}

.more-appointments {
    font-size: 0.75rem;
    color: #6B7280;
    text-align: center;
    padding: 0.5rem;
    cursor: pointer;
    border-radius: 0.75rem;
    background: #F3F4F6;
    transition: all 0.3s ease;
}

    .more-appointments:hover {
        background: #E5E7EB;
        transform: translateY(-1px);
    }

/* Quick Actions */
.appointments-link {
    text-align: right;
    margin-top: 2rem;
}

.appointments-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    background: #157BAB;
    color: white;
    border-radius: 1.25rem;
    text-decoration: none;
    
    font-weight: 600;
    transition: all 0.3s ease;
}

    .appointments-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.5);
    }

/* Modal Styles */
.appointment-details,
.day-appointments {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.appointment-card {
    display: flex;
    align-items: stretch;
    background: white;
    border-radius: 1.25rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

    .appointment-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.15);
    }

.appointment-date {
    background: #157BAB;
    color: white;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 150px;
}

.date-badge {
    text-align: center;
    margin-bottom: 0.5rem;
}

.month {
    font-size: 0.875rem;
    text-transform: uppercase;
    font-weight: 600;
    opacity: 0.9;
    font-family: 'Acme', sans-serif;
}

.day {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
    font-family: 'Acme', sans-serif;
}

.time {
    font-size: 0.875rem;
    opacity: 0.9;
}

.status-badge {
    margin-top: 0.5rem;
    padding: 0.25rem 0.75rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
}

.appointment-info {
    flex: 1;
    padding: 1.5rem;
}

.patient-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 0.5rem;
    font-family: 'Acme', sans-serif;
}

.notes {
    color: #6B7280;
    font-size: 0.875rem;
    
}

.appointment-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    padding: 1.5rem;
    border-left: 1px solid #E5E7EB;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: 1.25rem;
    
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    cursor: pointer;
}

.details-btn {
    background: #F3F4F6;
    color: #1F2937;
}

    .details-btn:hover {
        background: #E5E7EB;
    }

.message-btn {
    background: #157BAB;
    color: white;
}

    .message-btn:hover {
        background: #1d4ed8;
    }

.cancel-btn {
    background: #EF4444;
    color: white;
}

    .cancel-btn:hover {
        background: #DC2626;
    }

/* Status Colors */
.status-confirmed {
    border-left: 4px solid #3B82F6;
}

.status-completed {
    border-left: 4px solid #10B981;
}

.status-cancelled {
    border-left: 4px solid #EF4444;
}

.status-expired {
    border-left: 4px solid #6B7280;
}

/* Responsive Design */
@media (max-width: 768px) {
    .Calendar-CONTAINER {
        padding: 1rem;
    }

    .header-section {
        padding: 1.5rem;
    }

    .header-title {
        font-size: 1.5rem;
    }

    .calendar-controls {
        padding: 1rem;
    }

    .current-month {
        font-size: 1.25rem;
    }

    .calendar-day {
        min-height: 100px;
        padding: 0.5rem;
    }

    .day-number {
        width: 1.75rem;
        height: 1.75rem;
        font-size: 0.875rem;
    }

    .appointment-item {
        padding: 0.375rem;
        font-size: 0.625rem;
    }

    .appointment-card {
        flex-direction: column;
    }

    .appointment-date {
        flex-direction: row;
        justify-content: space-between;
        min-width: auto;
    }

    .date-badge {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0;
    }

    .appointment-actions {
        flex-wrap: wrap;
        border-left: none;
        border-top: 1px solid #E5E7EB;
    }

    .action-btn {
        flex: 1;
        min-width: 0;
    }

    .appointments-btn {
        width: 100%;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.Calendar-CONTAINER {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
