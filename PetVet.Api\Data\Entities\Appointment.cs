using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PetVet.Api.Data.Entities
{
    public class Appointment
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int PetOwnerId { get; set; }

        [ForeignKey(nameof(PetOwnerId))]
        public virtual User? PetOwner { get; set; }

        [Required]
        public int VetId { get; set; }

        [ForeignKey(nameof(VetId))]
        public virtual User? Vet { get; set; }

        [Required]
        public int TimeSlotId { get; set; }

        [ForeignKey(nameof(TimeSlotId))]
        public virtual VetTimeSlot? TimeSlot { get; set; }

        [Required]
        public string AppointmentDate { get; set; } = "";

        [Required]
        public string Status { get; set; } = "Confirmed";

        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime? UpdatedAt { get; set; }
    }
} 