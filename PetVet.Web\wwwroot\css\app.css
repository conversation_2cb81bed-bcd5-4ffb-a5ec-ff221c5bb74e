@tailwind base;
@tailwind components;
@tailwind utilities;

* {
    margin: 0;
    padding: 0;
}

html, body {
    font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

h1:focus {
    outline: none;
}

a {
    @apply text-blue-600 hover:text-blue-800 transition-colors;
}

.btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-medium py-2 px-4 rounded-lg transition-colors;
}

.btn:focus, .btn:active:focus, input:focus, select:focus {
    @apply outline-none ring-2 ring-primary-200 ring-offset-2;
}

.content {
    @apply pt-4;
}

/* Floating label components */
.floating-label-group {
    @apply relative mb-6;
}

.floating-label-input {
    @apply block w-full px-4 pt-6 pb-2 text-base bg-white border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 placeholder-transparent transition-all duration-200;
}

.floating-label {
    @apply absolute left-4 top-2 text-sm text-gray-500 transition-all duration-200 ease-out transform origin-left pointer-events-none;
}

.floating-label-input:focus ~ .floating-label,
.floating-label-input:not(:placeholder-shown) ~ .floating-label {
    @apply text-xs text-primary-600 -translate-y-1 scale-75;
}

.floating-label-input:disabled {
    @apply bg-gray-50 cursor-not-allowed;
}

.valid.modified:not([type=checkbox]) {
    @apply ring-2 ring-green-500 border-green-500;
}

.invalid {
    @apply ring-2 ring-red-500 border-red-500;
}

.validation-message {
    @apply text-red-500 text-sm mt-1;
}

/* Custom components for consistent styling */
@layer components {
    .btn {
        @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
    }
    
    .btn-sm {
        @apply px-3 py-1.5 text-xs;
    }
    
    .btn-lg {
        @apply px-6 py-3 text-base;
    }
    
    .card {
        @apply bg-white rounded-xl shadow-lg border border-gray-200;
    }
    
    .card-header {
        @apply px-6 py-4 border-b border-gray-200 rounded-t-xl;
    }
    
    .card-body {
        @apply px-6 py-4;
    }
    
    .table {
        @apply w-full border-collapse;
    }
    
    .table th {
        @apply px-4 py-3 text-left text-sm font-semibold text-gray-900 bg-gray-50 border-b border-gray-200;
    }
    
    .table td {
        @apply px-4 py-3 text-sm text-gray-700 border-b border-gray-200;
    }
    
    .table-striped tr:nth-child(even) {
        @apply bg-gray-50;
    }
    
    .table-bordered {
        @apply border border-gray-200 rounded-lg overflow-hidden;
    }
    
    .alert {
        @apply p-4 rounded-lg border;
    }
    
    .alert-warning {
        @apply bg-yellow-50 border-yellow-200 text-yellow-800;
    }
    
    .alert-success {
        @apply bg-green-50 border-green-200 text-green-800;
    }
    
    .alert-danger {
        @apply bg-red-50 border-red-200 text-red-800;
    }
    
    .navbar {
        @apply bg-gray-900 text-white;
    }
    
    .navbar-brand {
        @apply text-xl font-bold text-white;
    }
    
    .nav-link {
        @apply text-gray-300 hover:text-white transition-colors duration-200;
    }
    
    .nav-link.active {
        @apply text-white bg-primary-600 rounded-lg;
    }
}

#blazor-error-ui {
    background: #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
    position: relative;
}

#blazor-error-ui::before {
    content: "⚠";
    position: absolute;
    left: 1rem;
    top: 1rem;
    font-size: 1.5rem;
    color: yellow;
}

.blazor-error-boundary {
    background: #b32121;
    padding: 1rem 1rem 1rem 3.7rem;
    color: white;
    position: relative;
}

.blazor-error-boundary::after {
    content: "An error has occurred.";
}

code {
    @apply text-pink-600 bg-gray-100 px-1 rounded;
}

/* Admin Dashboard Specific Styles */
.admin-dashboard {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

.admin-card {
    @apply bg-white rounded-2xl shadow-lg border border-gray-100 transition-all duration-300;
}

.admin-card:hover {
    @apply shadow-xl transform -translate-y-1;
}

.metric-card {
    @apply bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100;
}

.circular-progress {
    transition: stroke-dasharray 2s ease-out;
}

.progress-bar {
    transition: width 2s ease-out;
}

/* Custom gradient backgrounds matching mobile theme */
.bg-gradient-primary-admin {
    background: linear-gradient(135deg, #3E97DA 0%, #2875c7 100%);
}

.bg-gradient-secondary-admin {
    background: linear-gradient(135deg, #45C1A5 0%, #15993e 100%);
}

.bg-gradient-warning-admin {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.bg-gradient-accent-admin {
    background: linear-gradient(135deg, #14b8a6 0%, #0d9488 100%);
}

/* Chart container styles */
.chart-container {
    @apply relative overflow-hidden;
}

.chart-legend {
    @apply flex justify-center space-x-6 mt-4;
}

.legend-item {
    @apply flex items-center text-sm text-gray-600;
}

.legend-color {
    @apply w-4 h-4 rounded-full mr-2;
}

/* Animation keyframes for admin dashboard */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInScale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.animate-slide-up {
    animation: slideInUp 0.6s ease-out;
}

.animate-slide-right {
    animation: slideInRight 0.6s ease-out;
}

.animate-fade-scale {
    animation: fadeInScale 0.6s ease-out;
}

/* Custom button styles for admin dashboard */
.admin-btn {
    @apply inline-flex items-center px-4 py-2 rounded-xl font-medium transition-all duration-300 transform hover:scale-105;
}

.admin-btn-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:from-primary-600 hover:to-primary-700 shadow-lg hover:shadow-xl;
}

.admin-btn-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-600 text-white hover:from-secondary-600 hover:to-secondary-700 shadow-lg hover:shadow-xl;
}

.admin-btn-warning {
    @apply bg-gradient-to-r from-warning-500 to-warning-600 text-white hover:from-warning-600 hover:to-warning-700 shadow-lg hover:shadow-xl;
}

.admin-btn-accent {
    @apply bg-gradient-to-r from-accent-500 to-accent-600 text-white hover:from-accent-600 hover:to-accent-700 shadow-lg hover:shadow-xl;
}

/* Status badges */
.status-badge {
    @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
}

.status-badge-success {
    @apply bg-success-100 text-success-800;
}

.status-badge-warning {
    @apply bg-warning-100 text-warning-800;
}

.status-badge-primary {
    @apply bg-primary-100 text-primary-800;
}

.status-badge-secondary {
    @apply bg-secondary-100 text-secondary-800;
}

/* Loading animations */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, transparent 37%, #f0f0f0 63%);
    background-size: 400% 100%;
    animation: shimmer 1.5s ease-in-out infinite;
}

@keyframes shimmer {
    0% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* Quick Action Button Styles */
.admin-quick-btn-primary {
    background: linear-gradient(135deg, #3E97DA 0%, #2875c7 100%);
}

.admin-quick-btn-primary:hover {
    background: linear-gradient(135deg, #2875c7 0%, #1e5fa3 100%);
}

.admin-quick-btn-secondary {
    background: linear-gradient(135deg, #45C1A5 0%, #15993e 100%);
}

.admin-quick-btn-secondary:hover {
    background: linear-gradient(135deg, #15993e 0%, #0f7a2e 100%);
}

.admin-quick-btn-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.admin-quick-btn-warning:hover {
    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
}

.admin-quick-btn-accent {
    background: linear-gradient(135deg, #14b8a6 0%, #0d9488 100%);
}

.admin-quick-btn-accent:hover {
    background: linear-gradient(135deg, #0d9488 0%, #0f766e 100%);
}

/* Responsive adjustments for admin dashboard */
@media (max-width: 768px) {
    .admin-card {
        @apply mx-2;
    }
    
    .metric-card {
        @apply p-4;
    }
    
    .chart-container {
        @apply px-2;
    }
}
