﻿@inject IUserApi _UserApi
@inject IJSRuntime _JS
@inject IAppState _AppState
@inject IPlatform Platform

<!-- Modern Chat List -->
<div class="h-full flex flex-col">
    <!-- Header -->
    <div class="p-4 border-b border-gray-200">
        <h3 class="text-lg font-bold text-gray-800">Messages</h3>
    </div>

    <!-- Chat List -->
    <div class="flex-1 overflow-y-auto">
        @if (Loading)
        {
            <div class="p-6 text-center">
                <div class="w-8 h-8 border-2 border-teal-500 border-t-transparent rounded-full animate-spin mx-auto mb-3"></div>
                <p class="text-gray-500">Loading chats...</p>
            </div>
        }
        else if (Chats == null || !Chats.Any())
        {
            <div class="p-8 text-center">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-comment-slash text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-bold text-gray-800 mb-2">No Chats Available</h3>
                <p class="text-gray-500">Start a conversation with a veterinarian or pet owner</p>
            </div>
        }
        else
        {
            <div class="space-y-1 p-2">
                @foreach (var chat in Chats)
                {
                    <div class="p-3 rounded-2xl cursor-pointer transition-all hover:bg-gray-100 @(chat.IsSelected ? "bg-gradient-to-r from-teal-500 to-blue-600 text-white hover:from-teal-600 hover:to-blue-700" : "")"
                         @onclick="() => HandleUserClick(chat)">
                        <div class="flex items-center gap-3">
                            <!-- Avatar -->
                            <div class="relative">
                                @if (!string.IsNullOrEmpty(chat.ImageUrl))
                                {
                                    <img src="@chat.ImageUrl"
                                         alt="@chat.Name"
                                         class="w-12 h-12 rounded-full object-cover border-2 @(chat.IsSelected ? "border-white/30" : "border-gray-200")" />
                                }
                                else
                                {
                                    <div class="w-12 h-12 rounded-full @(chat.IsSelected ? "bg-white/20" : "bg-gray-200") flex items-center justify-center border-2 @(chat.IsSelected ? "border-white/30" : "border-gray-200")">
                                        <i class="fas fa-user @(chat.IsSelected ? "text-white" : "text-gray-500")"></i>
                                    </div>
                                }

                                <!-- Online Status -->
                                @if (chat.IsOnline == true)
                                {
                                    <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white"></div>
                                }
                            </div>

                            <!-- Chat Info -->
                            <div class="flex-1 min-w-0">
                                <h4 class="font-bold @(chat.IsSelected ? "text-white" : "text-gray-800") truncate">@chat.Name</h4>
                                @if (!Platform.IsWeb)
                                {
                                    <p class="text-xs @(chat.IsSelected ? "text-white/80" : "text-gray-500")">Tap to open chat</p>
                                }
                                else
                                {
                                    <p class="text-xs @(chat.IsSelected ? "text-white/80" : "text-gray-500")">
                                        @(chat.IsOnline == true ? "Online" : "Offline")
                                    </p>
                                }
                            </div>

                            <!-- Arrow Icon -->
                            <div class="flex-shrink-0">
                                <i class="fas fa-chevron-right @(chat.IsSelected ? "text-white/60" : "text-gray-400") text-sm"></i>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
    </div>
</div>


@code {
    [Parameter]
    public ICollection<UserDto> Chats { get; set; } = new HashSet<UserDto>();

    [Parameter]
    public EventCallback<UserDto> OnUserSelected { get; set; }

    [Parameter]
    public bool Loading { get; set; }

    private async Task HandleUserClick(UserDto chat)
    {
        await OnUserSelected.InvokeAsync(chat);
    }
}
