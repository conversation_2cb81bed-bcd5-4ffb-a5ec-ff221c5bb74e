﻿using PetVet.Shared.DTOs;
using Refit;

namespace PetVet.Shared.Components.Apis
{
    [Headers("Authorization: Bearer ")]
    public interface IMessageApi
    {
        [Post("/api/messages")]
        Task<PetVetApiResponse> SendMessageAsync(MessageSendDto messageDto);

        [Get("/api/messages/{otherUserId}/{currentUserId}")]
        Task<IEnumerable<MessageDto>> GetMessagesAsync(int otherUserId, int currentUserId);
    }
}
