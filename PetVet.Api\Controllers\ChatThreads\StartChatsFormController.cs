﻿using Microsoft.AspNetCore.Mvc;
using PetVet.ServiceContracts.Features.Conversation;
namespace PetVet.Server.WebApis.Controllers.Conversation.ChatThreads;
[ApiController, Route("api/[controller]/[action]")]
public class StartChatsFormController : <PERSON>Base, IStartChatFormDataService
{

	private readonly IStartChatFormDataService dataService;

	public StartChatsFormController(IStartChatFormDataService dataService)
	{
		this.dataService = dataService;
	}
	
	[HttpPost]
	public async Task<string> SaveAsync([FromBody] StartChatFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	}
	
	[HttpGet]
	public async Task<StartChatFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await dataService.GetItemByIdAsync(id);
	}
}
