using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using PetVet.Api.Data;
using PetVet.Api.Services;
using PetVet.Shared;
using PetVet.Shared.DTOs;

namespace PetVet.Api.Controllers
{
    /// <summary>
    /// Controller for managing SMS notifications and configurations
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class SmsNotificationController : ControllerBase
    {
        private readonly IDbContextFactory<PetVetContext> _contextFactory;
        private readonly SmsNotificationService _smsNotificationService;
        private readonly TwilioSmsService _twilioSmsService;
        private readonly ILogger<SmsNotificationController> _logger;

        public SmsNotificationController(
            IDbContextFactory<PetVetContext> contextFactory,
            SmsNotificationService smsNotificationService,
            TwilioSmsService twilioSmsService,
            ILogger<SmsNotificationController> logger)
        {
            _contextFactory = contextFactory;
            _smsNotificationService = smsNotificationService;
            _twilioSmsService = twilioSmsService;
            _logger = logger;
        }

        /// <summary>
        /// Get SMS notification logs for the current user
        /// </summary>
        [HttpGet("logs")]
        public async Task<ActionResult<List<object>>> GetUserNotificationLogs([FromQuery] int take = 50)
        {
            try
            {
                var userIdClaim = User.FindFirst("UserId")?.Value;
                if (!int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized("Invalid user ID");
                }

                var logs = await _smsNotificationService.GetUserNotificationLogsAsync(userId, take);

                var result = logs.Select(log => new
                {
                    log.Id,
                    log.PhoneNumber,
                    log.NotificationType,
                    log.MessageContent,
                    log.Status,
                    log.TwilioStatus,
                    log.CreatedAt,
                    log.DeliveredAt,
                    log.RetryCount,
                    log.Cost,
                    log.CostUnit,
                    log.ErrorMessage
                }).ToList();

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving SMS notification logs");
                return StatusCode(500, "Failed to retrieve notification logs");
            }
        }

        /// <summary>
        /// Get SMS notification statistics for the current user
        /// </summary>
        [HttpGet("stats")]
        public async Task<ActionResult<object>> GetNotificationStats()
        {
            try
            {
                var userIdClaim = User.FindFirst("UserId")?.Value;
                if (!int.TryParse(userIdClaim, out int userId))
                {
                    return Unauthorized("Invalid user ID");
                }

                using var context = _contextFactory.CreateDbContext();

                var stats = await context.SmsNotificationLogs
                    .Where(s => s.UserId == userId)
                    .GroupBy(s => s.Status)
                    .Select(g => new { Status = g.Key, Count = g.Count() })
                    .ToListAsync();

                var totalCost = await context.SmsNotificationLogs
                    .Where(s => s.UserId == userId && s.Cost.HasValue)
                    .SumAsync(s => s.Cost.Value);

                var result = new
                {
                    TotalNotifications = stats.Sum(s => s.Count),
                    StatusBreakdown = stats.ToDictionary(s => s.Status, s => s.Count),
                    TotalCost = totalCost,
                    CostUnit = "USD" // Assuming USD, could be made configurable
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving SMS notification statistics");
                return StatusCode(500, "Failed to retrieve notification statistics");
            }
        }

        /// <summary>
        /// Test SMS notification (Admin only)
        /// </summary>
        [HttpPost("test")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<PetVetApiResponse>> TestSmsNotification([FromBody] TestSmsRequest request)
        {
            try
            {
                if (string.IsNullOrEmpty(request.PhoneNumber) || string.IsNullOrEmpty(request.Message))
                {
                    return BadRequest("Phone number and message are required");
                }

                var result = await _twilioSmsService.SendSmsAsync(request.PhoneNumber, request.Message, "Test");

                if (result.IsSuccess)
                {
                    _logger.LogInformation("Test SMS sent successfully to {PhoneNumber}", request.PhoneNumber);
                    return Ok(PetVetApiResponse.Success());
                }
                else
                {
                    _logger.LogWarning("Test SMS failed for {PhoneNumber}: {Error}", request.PhoneNumber, result.ErrorMessage);
                    return Ok(PetVetApiResponse.Fail(result.ErrorMessage ?? "Failed to send test SMS"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending test SMS");
                return StatusCode(500, PetVetApiResponse.Fail("Failed to send test SMS"));
            }
        }

        /// <summary>
        /// Retry failed SMS notifications (Admin only)
        /// </summary>
        [HttpPost("retry-failed")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<PetVetApiResponse>> RetryFailedNotifications()
        {
            try
            {
                var result = await _smsNotificationService.RetryFailedNotificationsAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrying failed SMS notifications");
                return StatusCode(500, PetVetApiResponse.Fail("Failed to retry notifications"));
            }
        }

        /// <summary>
        /// Get Twilio account information (Admin only)
        /// </summary>
        [HttpGet("twilio-info")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<object>> GetTwilioAccountInfo()
        {
            try
            {
                var accountInfo = await _twilioSmsService.GetAccountInfoAsync();
                return Ok(accountInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving Twilio account information");
                return StatusCode(500, "Failed to retrieve Twilio account information");
            }
        }

        /// <summary>
        /// Get SMS notification configurations (Admin only)
        /// </summary>
        [HttpGet("configs")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<List<object>>> GetNotificationConfigs()
        {
            try
            {
                using var context = _contextFactory.CreateDbContext();

                var configs = await context.SmsNotificationConfigs
                    .Select(c => new
                    {
                        c.Id,
                        c.NotificationType,
                        c.IsEnabled,
                        c.DelayMinutes,
                        c.MaxRetries,
                        c.RetryIntervalMinutes,
                        c.MessageTemplate,
                        c.EnableForVets,
                        c.EnableForPetOwners,
                        c.CreatedAt,
                        c.UpdatedAt
                    })
                    .ToListAsync();

                return Ok(configs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving SMS notification configurations");
                return StatusCode(500, "Failed to retrieve notification configurations");
            }
        }

        /// <summary>
        /// Update SMS notification configuration (Admin only)
        /// </summary>
        [HttpPut("configs/{id}")]
        [Authorize(Roles = "Admin")]
        public async Task<ActionResult<PetVetApiResponse>> UpdateNotificationConfig(int id, [FromBody] UpdateConfigRequest request)
        {
            try
            {
                using var context = _contextFactory.CreateDbContext();

                var config = await context.SmsNotificationConfigs.FindAsync(id);
                if (config == null)
                {
                    return NotFound(PetVetApiResponse.Fail("Configuration not found"));
                }

                config.IsEnabled = request.IsEnabled;
                config.DelayMinutes = request.DelayMinutes;
                config.MaxRetries = request.MaxRetries;
                config.RetryIntervalMinutes = request.RetryIntervalMinutes;
                config.MessageTemplate = request.MessageTemplate;
                config.EnableForVets = request.EnableForVets;
                config.EnableForPetOwners = request.EnableForPetOwners;
                config.UpdatedAt = DateTime.UtcNow;

                await context.SaveChangesAsync();

                _logger.LogInformation("Updated SMS notification configuration {Id}", id);
                return Ok(PetVetApiResponse.Success());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating SMS notification configuration {Id}", id);
                return StatusCode(500, PetVetApiResponse.Fail("Failed to update configuration"));
            }
        }
    }

    public class TestSmsRequest
    {
        public string PhoneNumber { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
    }

    public class UpdateConfigRequest
    {
        public bool IsEnabled { get; set; }
        public int DelayMinutes { get; set; }
        public int MaxRetries { get; set; }
        public int RetryIntervalMinutes { get; set; }
        public string? MessageTemplate { get; set; }
        public bool EnableForVets { get; set; }
        public bool EnableForPetOwners { get; set; }
    }
}
