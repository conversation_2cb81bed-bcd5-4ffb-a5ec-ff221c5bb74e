﻿using DeepMessage.ServiceContracts.Features.Configurations;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using PetVet.Api.Data;
using PetVet.Server.DataServices.Data;
namespace DeepMessage.Server.DataServices.Features.Configurations;
public class DeviceTokenServerSideFormDataService : IDeviceTokenFormDataService
{

    private readonly PetVetContext _context;
    private readonly IHttpContextAccessor contextAccessor;

    public DeviceTokenServerSideFormDataService(PetVetContext context, IHttpContextAccessor contextAccessor)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
    }

    public async Task<string> SaveAsync(DeviceTokenFormBusinessObject formBusinessObject)
    {
        var userId = contextAccessor.HttpContext.User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        var device = await _context.UserDevices.FirstOrDefaultAsync(x => x.Id == formBusinessObject.Id);
        if (device == null)
        {
            device = new UserDevice
            {
                Id = formBusinessObject.Id,
                UserId = Convert.ToInt32(userId),
                Name = formBusinessObject.DeviceName,
                LastLogin = DateTime.UtcNow,
                Platform = formBusinessObject.Platform,
            };
            _context.UserDevices.Add(device);
        }

        device.DeviceToken = formBusinessObject.DeviceToken;
        await _context.SaveChangesAsync();
        return device.Id!;
    }

    public async Task<DeviceTokenFormBusinessObject?> GetItemByIdAsync(string id)
    {
        throw new NotImplementedException();
    }
}
