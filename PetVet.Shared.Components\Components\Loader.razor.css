﻿.fullscreen {
    position: fixed;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2049;
}

.overlay {
    background-color: rgba(0, 0, 0, 0.6);
    position: absolute;
    width: 100%;
    height: 100%;
}

.loader-box {
    background-color: #157BAB;
    padding: 2rem;
    border-radius: 1.25rem;
    text-align: center;
    color: white;
    font-family: 'Acme', sans-serif;
    font-weight: 600;
    box-shadow: 0px 0px 20px rgba(90, 169, 239, 0.8);
    z-index: 2050;
    width: 25%;
}

.loader-icon {
    color: white;
    margin-bottom: 1rem;
    animation: spin 1.5s infinite linear;
}

.loader-text {
    font-size: 1.5rem;
    display: flex;
    align-items: center;
    cursor: default;
}

.dots::after {
    content: " ";
    display: inline-block;
    animation: dots 1.5s infinite;
}

@keyframes spin {
    100% {
        transform: rotate(360deg);
    }
}

@keyframes dots {
    0% {
        content: "";
    }

    25% {
        content: ".";
    }

    50% {
        content: "..";
    }

    75% {
        content: "...";
    }

    100% {
        content: "";
    }
}

@media (max-width: 768px) {
    .loader-box {
        padding: 1.25rem;
        border-radius: 1.25rem;
        width: 85%;
    }

    .loader-icon {
        color: white;
        margin-bottom: 1rem;
        animation: spin 1.5s infinite linear;
    }

    .loader-text {
        font-size: 1.25rem;
        display: flex;
        align-items: center;
    }
}