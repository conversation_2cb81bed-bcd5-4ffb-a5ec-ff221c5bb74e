﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace PetVet.Api.Data.Migrations
{
    /// <inheritdoc />
    public partial class PasswordUpdate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEOHhBRVarbEvvPC6BUCeMllclUITMjS0UHc9MvKiFQ36kg0F7fvq0ELLYClhtZbI/Q==");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "Users",
                keyColumn: "Id",
                keyValue: 1,
                column: "PasswordHash",
                value: "AQAAAAIAAYagAAAAEIqDZU8xfllDwKEmqXx61ssvprUDLeezGv7z3GaEkiRQFUt6I1Sd4VehdNEBzW7rwg==");
        }
    }
}
