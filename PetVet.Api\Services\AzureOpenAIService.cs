using Microsoft.Extensions.Options;
using PetVet.Shared.Models.AzureOpenAI;
using PetVet.Shared.Services;
using System.Collections.Concurrent;
using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;
using static System.Net.Mime.MediaTypeNames;

namespace PetVet.Api.Services;

public class AzureOpenAIService : IAzureOpenAIService
{
    private readonly HttpClient _httpClient;
    private readonly AzureOpenAIConfig _config;
    private readonly ILogger<AzureOpenAIService> _logger;
    
    // In-memory storage for conversations (in production, use a database)
    private readonly ConcurrentDictionary<string, ChatConversation> _conversations = new();
    private readonly ConcurrentDictionary<string, List<string>> _userConversations = new();
    
    private readonly string _systemPrompt = @"You are PetVet AI, a helpful and knowledgeable virtual assistant specializing in pet care and veterinary advice. 

Your role:
- Provide helpful, accurate information about pet care, nutrition, grooming, training, and general wellness
- Offer guidance on common pet health concerns while emphasizing the importance of professional veterinary care
- Be friendly, empathetic, and supportive to pet owners
- Always recommend consulting with a veterinarian for serious health issues, emergencies, or specific medical advice

Guidelines:
-Strictly answer only about pet care and its information, health etc or question about pet or petcare
- Keep responses concise but informative
- Use a warm, caring tone
- Include practical tips when appropriate
- Never provide specific medical diagnoses or prescribe treatments
- Encourage responsible pet ownership
- Be helpful for both dogs, cats, and other pets and mention if advice is species-specific
- Strictly provide output in html format with no additional text or formatting

Remember: You're here to support pet owners with general guidance, but professional veterinary care is irreplaceable for health concerns.";

    public AzureOpenAIService(HttpClient httpClient, IOptions<AzureOpenAIConfig> config, ILogger<AzureOpenAIService> logger)
    {
        _httpClient = httpClient;
        _config = config.Value;
        _logger = logger;
        
        ConfigureHttpClient();
    }

    private void ConfigureHttpClient()
    {
        _httpClient.DefaultRequestHeaders.Clear();
        _httpClient.DefaultRequestHeaders.Add("api-key", _config.ApiKey);
        _httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
    }

    public async Task<ChatApiResponse> SendChatMessageAsync(string message, string? conversationId = null, string? userId = null)
    {
        try
        {
            // Get or create conversation
            var conversation = await GetOrCreateConversationAsync(conversationId, userId);
            
            // Add user message to conversation
            conversation.Messages.Add(new ChatMessage
            {
                Role = "user",
                Content = message
            });

            // Prepare request
            var request = new ChatRequest
            {
                Messages = PrepareMessagesForRequest(conversation.Messages),
                MaxTokens = _config.MaxTokens,
                Temperature = _config.Temperature
            };

            // Send to Azure OpenAI (or use dummy response)
            var response = await SendToAzureOpenAIAsync(request);
            
            if (response.Success && !string.IsNullOrEmpty(response.Response))
            {
                // Add AI response to conversation
                conversation.Messages.Add(new ChatMessage
                {
                    Role = "assistant",
                    Content = response.Response
                });
                
                conversation.LastUpdated = DateTime.UtcNow;
                response.ConversationId = conversation.ConversationId;
            }

            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending chat message");
            return new ChatApiResponse
            {
                Success = false,
                Message = "An error occurred while processing your message. Please try again.",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    public async Task<ChatApiResponse> SendChatMessageAsync(ChatRequest request, string? conversationId = null, string? userId = null)
    {
        try
        {
            var response = await SendToAzureOpenAIAsync(request);
            
            if (!string.IsNullOrEmpty(conversationId))
            {
                response.ConversationId = conversationId;
            }
            
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending chat request");
            return new ChatApiResponse
            {
                Success = false,
                Message = "An error occurred while processing your request. Please try again.",
                ErrorCode = "INTERNAL_ERROR"
            };
        }
    }

    private async Task<ChatApiResponse> SendToAzureOpenAIAsync(ChatRequest request)
    { 
        try
        {
            var url = $"{_config.Endpoint}/openai/deployments/{_config.DeploymentName}/chat/completions?api-version={_config.ApiVersion}";
            var json = JsonSerializer.Serialize(request);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync(url, content);
            
            if (response.IsSuccessStatusCode)
            {
                var responseJson = await response.Content.ReadAsStringAsync();
                var chatResponse = JsonSerializer.Deserialize<ChatResponse>(responseJson.Replace("```html", string.Empty).Replace("```", string.Empty));
                
                return new ChatApiResponse
                {
                    Success = true,
                    Response = chatResponse?.Choices?.FirstOrDefault()?.Message?.Content ?? "No response generated.",
                    TokensUsed = chatResponse?.Usage?.TotalTokens ?? 0,
                    Model = chatResponse?.Model ?? _config.DeploymentName
                };
            }
            else
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Azure OpenAI API error: {StatusCode} - {Content}", response.StatusCode, errorContent);
                
                return new ChatApiResponse
                {
                    Success = false,
                    Message = "Failed to get response from AI service. Please try again.",
                    ErrorCode = $"API_ERROR_{response.StatusCode}"
                };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calling Azure OpenAI API");
            throw;
        }
    }
 
    private async Task<ChatConversation> GetOrCreateConversationAsync(string? conversationId, string? userId)
    {
        conversationId ??= Guid.NewGuid().ToString();
        userId ??= "anonymous";

        if (_conversations.TryGetValue(conversationId, out var existing))
        {
            return existing;
        }

        var conversation = new ChatConversation
        {
            ConversationId = conversationId,
            UserId = userId,
            Messages = new List<ChatMessage>
            {
                new ChatMessage
                {
                    Role = "system",
                    Content = _systemPrompt
                }
            }
        };

        _conversations[conversationId] = conversation;
        
        if (!_userConversations.ContainsKey(userId))
        {
            _userConversations[userId] = new List<string>();
        }
        _userConversations[userId].Add(conversationId);

        return conversation;
    }

    private List<ChatMessage> PrepareMessagesForRequest(List<ChatMessage> messages)
    {
        // Keep system message and last 10 messages to manage token usage
        var systemMessage = messages.FirstOrDefault(m => m.Role == "system");
        var recentMessages = messages.Where(m => m.Role != "system").TakeLast(10).ToList();
        
        var result = new List<ChatMessage>();
        if (systemMessage != null)
        {
            result.Add(systemMessage);
        }
        result.AddRange(recentMessages);
        
        return result;
    }

    public async Task<ChatConversation?> GetConversationAsync(string conversationId)
    {
        await Task.CompletedTask;
        return _conversations.TryGetValue(conversationId, out var conversation) ? conversation : null;
    }

    public async Task<List<ChatConversation>> GetUserConversationsAsync(string userId)
    {
        await Task.CompletedTask;
        
        if (!_userConversations.TryGetValue(userId, out var conversationIds))
        {
            return new List<ChatConversation>();
        }

        return conversationIds
            .Select(id => _conversations.TryGetValue(id, out var conv) ? conv : null)
            .Where(conv => conv != null)
            .Cast<ChatConversation>()
            .OrderByDescending(c => c.LastUpdated)
            .ToList();
    }

    public async Task<bool> DeleteConversationAsync(string conversationId)
    {
        await Task.CompletedTask;
        
        if (_conversations.TryRemove(conversationId, out var conversation))
        {
            if (_userConversations.TryGetValue(conversation.UserId, out var userConvs))
            {
                userConvs.Remove(conversationId);
            }
            return true;
        }
        
        return false;
    }

    public async Task<bool> ClearUserConversationsAsync(string userId)
    {
        await Task.CompletedTask;
        
        if (_userConversations.TryGetValue(userId, out var conversationIds))
        {
            foreach (var id in conversationIds)
            {
                _conversations.TryRemove(id, out _);
            }
            _userConversations.TryRemove(userId, out _);
            return true;
        }
        
        return false;
    }

    public async Task<bool> IsHealthyAsync()
    {
        try
        {
            // Simple health check - in production, you might ping the Azure OpenAI endpoint
            await Task.Delay(100);
            return !string.IsNullOrEmpty(_config.Endpoint) && !string.IsNullOrEmpty(_config.ApiKey);
        }
        catch
        {
            return false;
        }
    }

    public async Task<ChatServiceStats> GetStatsAsync()
    {
        await Task.CompletedTask;
        
        var totalMessages = _conversations.Values.Sum(c => c.Messages.Count);
        var lastActivity = _conversations.Values.Any() 
            ? _conversations.Values.Max(c => c.LastUpdated) 
            : DateTime.MinValue;

        return new ChatServiceStats
        {
            TotalConversations = _conversations.Count,
            TotalMessages = totalMessages,
            TotalTokensUsed = totalMessages * 50, // Rough estimate
            LastActivity = lastActivity,
            IsConnected = await IsHealthyAsync()
        };
    }
}
