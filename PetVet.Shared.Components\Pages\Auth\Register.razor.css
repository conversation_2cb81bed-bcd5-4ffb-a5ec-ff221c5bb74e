﻿.Register-CONTAINER {
    background: #f2f6f9;
    display: flex;
    justify-content: center;
    align-items: flex-start;
}

.Register-CARD {
    border-radius: 1.25rem;
    width: 95%;
}

.Register-HEADER {
    background: #157BAB;
    color: #ffffff;
    border-radius: 1.25rem;
}

.Register-HEADING {
    font-size: 1.5rem;
    font-family: 'Acme', sans-serif;
    font-weight: bold;
    cursor: default;
    margin: 0;
}

.Register-CARD-BODY {
    padding: 2rem;
}

.Role-BUTTON {
    background-color: #FFC107;
    border: none;
    border-radius: 0.625rem;
    color: #333;
    
    font-weight: bold;
    margin: 0.5rem;
    width: 50%;
    text-align: center;
    transition: background-color 0.3s ease, transform 0.2s ease;
    padding: 0.5rem 1rem;
}

    .Role-BUTTON.active {
        background: #157BAB;
        color: #ffffff;
    }


.Register-ROLE_TITLE {
    font-family: 'Acme', sans-serif;
    cursor: default;
}

.Register-BTN {
    display: inline-block;
    background-color: #FFC107;
    border: none;
    border-radius: 0.625rem;
    padding: 0.625rem 1.25rem;
    color: #333;
    
    font-weight: bold;
    cursor: pointer;
    text-align: center;
    margin-top: 0.625rem;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

    .Register-BTN:hover {
        background-color: #e0a406;
        transform: scale(1.05);
    }

@media (max-width: 768px) {

    .Register-CARD {
        border-radius: 1.25rem;
        width: 100%;
    }

    .Register-BUTTON-Div {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .Register-BUTTON {
        width: 100%;
    }

    .Register-CARD-BODY {
        padding: 1rem;
    }

    .Register-HEADING {
        font-size: 1.5rem;
    }

    .Role-BUTTON {
        background-color: #FFC107;
        border: none;
        border-radius: 0.625rem;
        color: #333;
        
        font-weight: bold;
        margin: 0.5rem;
        width: 65%;
        text-align: center;
        transition: background-color 0.3s ease, transform 0.2s ease;
        padding: 0.5rem 1rem;
    }
}
