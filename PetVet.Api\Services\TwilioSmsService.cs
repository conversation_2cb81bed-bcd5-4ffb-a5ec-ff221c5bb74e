using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using PetVet.Shared;
using System.Text.Json;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using Twilio.Types;

namespace PetVet.Api.Services
{
    /// <summary>
    /// Twilio SMS service for sending appointment notifications
    /// </summary>
    public class TwilioSmsService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<TwilioSmsService> _logger;
        private readonly bool _isConfigured;

        public TwilioSmsService(IConfiguration configuration, ILogger<TwilioSmsService> logger)
        {
            _configuration = configuration;
            _logger = logger;
            _isConfigured = InitializeTwilio();
        }

        private bool InitializeTwilio()
        {
            try
            {
                var accountSid = _configuration["Twilio:AccountSid"];
                var authToken = _configuration["Twilio:AuthToken"];

                if (string.IsNullOrEmpty(accountSid) || string.IsNullOrEmpty(authToken))
                {
                    _logger.LogWarning("Twilio credentials not configured. SMS notifications will be disabled.");
                    return false;
                }

                TwilioClient.Init(accountSid, authToken);
                _logger.LogInformation("Twilio SMS service initialized successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize Twilio SMS service");
                return false;
            }
        }

        /// <summary>
        /// Send SMS notification with Twilio
        /// </summary>
        public async Task<TwilioSmsResult> SendSmsAsync(string toPhoneNumber, string message, string? notificationType = null)
        {
            if (!_isConfigured)
            {
                _logger.LogWarning("Twilio not configured, cannot send SMS to {PhoneNumber}", toPhoneNumber);
                return TwilioSmsResult.Fail("Twilio SMS service not configured");
            }

            if (string.IsNullOrEmpty(toPhoneNumber))
            {
                return TwilioSmsResult.Fail("Phone number is required");
            }

            if (string.IsNullOrEmpty(message))
            {
                return TwilioSmsResult.Fail("Message content is required");
            }

            try
            {
                var fromPhoneNumber = _configuration["Twilio:FromPhoneNumber"];
                if (string.IsNullOrEmpty(fromPhoneNumber))
                {
                    return TwilioSmsResult.Fail("Twilio FromPhoneNumber not configured");
                }

                // Format phone number for international use
                var formattedPhoneNumber = FormatPhoneNumber(toPhoneNumber);

                // Ensure message is within SMS limits (160 characters for single SMS)
                var truncatedMessage = TruncateMessage(message, 160);

                _logger.LogInformation("Sending SMS to {PhoneNumber} via Twilio", formattedPhoneNumber);

                var messageResource = await MessageResource.CreateAsync(
                    body: truncatedMessage,
                    from: new PhoneNumber(fromPhoneNumber),
                    to: new PhoneNumber(formattedPhoneNumber)
                );

                _logger.LogInformation("SMS sent successfully. Twilio SID: {MessageSid}, Status: {Status}", 
                    messageResource.Sid, messageResource.Status);

                return TwilioSmsResult.Success(
                    messageResource.Sid,
                    messageResource.Status?.ToString() ?? "Unknown"
                );
            }
            catch (Twilio.Exceptions.ApiException apiEx)
            {
                _logger.LogError(apiEx, "Twilio API error sending SMS to {PhoneNumber}: {ErrorCode} - {ErrorMessage}", 
                    toPhoneNumber, apiEx.Code, apiEx.Message);
                return TwilioSmsResult.Fail($"Twilio API error: {apiEx.Message}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error sending SMS to {PhoneNumber}", toPhoneNumber);
                return TwilioSmsResult.Fail($"Failed to send SMS: {ex.Message}");
            }
        }

        /// <summary>
        /// Format phone number for international use
        /// </summary>
        private string FormatPhoneNumber(string phoneNumber)
        {
            // Remove any spaces, dashes, or parentheses
            var cleaned = phoneNumber.Replace(" ", "").Replace("-", "").Replace("(", "").Replace(")", "");

            // If it already starts with +, return as is
            if (cleaned.StartsWith("+"))
            {
                return cleaned;
            }

            // For Pakistani numbers (default country)
            if (cleaned.StartsWith("0"))
            {
                // Remove leading 0 and add +92
                return "+92" + cleaned.TrimStart('0');
            }
            else if (cleaned.StartsWith("92"))
            {
                // Add + if it starts with 92 but no +
                return "+" + cleaned;
            }
            else if (cleaned.Length >= 10)
            {
                // Assume it's a Pakistani number without country code
                return "+92" + cleaned;
            }

            // Return as is if we can't determine the format
            return cleaned.StartsWith("+") ? cleaned : "+" + cleaned;
        }

        /// <summary>
        /// Truncate message to fit SMS character limits
        /// </summary>
        private string TruncateMessage(string message, int maxLength)
        {
            if (message.Length <= maxLength)
            {
                return message;
            }

            // Truncate and add ellipsis
            return message.Substring(0, maxLength - 3) + "...";
        }

        /// <summary>
        /// Check if Twilio service is properly configured
        /// </summary>
        public bool IsConfigured => _isConfigured;

        /// <summary>
        /// Get Twilio account information for diagnostics
        /// </summary>
        public async Task<TwilioAccountInfo> GetAccountInfoAsync()
        {
            if (!_isConfigured)
            {
                return new TwilioAccountInfo { IsConfigured = false, ErrorMessage = "Twilio not configured" };
            }

            try
            {
                var account = await Twilio.Rest.Api.V2010.AccountResource.FetchAsync();
                return new TwilioAccountInfo
                {
                    IsConfigured = true,
                    AccountSid = account.Sid,
                    FriendlyName = account.FriendlyName,
                    Status = account.Status?.ToString() ?? "Unknown",
                    Type = account.Type?.ToString() ?? "Unknown"
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error fetching Twilio account information");
                return new TwilioAccountInfo { IsConfigured = false, ErrorMessage = ex.Message };
            }
        }
    }

    /// <summary>
    /// Result of Twilio SMS operation
    /// </summary>
    public class TwilioSmsResult
    {
        public bool IsSuccess { get; set; }
        public string? ErrorMessage { get; set; }
        public string? MessageSid { get; set; }
        public string? Status { get; set; }
        public decimal? Price { get; set; }
        public string? PriceUnit { get; set; }

        public static TwilioSmsResult Success(string messageSid, string status, decimal? price = null, string? priceUnit = null)
        {
            return new TwilioSmsResult
            {
                IsSuccess = true,
                MessageSid = messageSid,
                Status = status,
                Price = price,
                PriceUnit = priceUnit
            };
        }

        public static TwilioSmsResult Fail(string errorMessage)
        {
            return new TwilioSmsResult
            {
                IsSuccess = false,
                ErrorMessage = errorMessage
            };
        }
    }

    /// <summary>
    /// Twilio account information
    /// </summary>
    public class TwilioAccountInfo
    {
        public bool IsConfigured { get; set; }
        public string? AccountSid { get; set; }
        public string? FriendlyName { get; set; }
        public string? Status { get; set; }
        public string? Type { get; set; }
        public string? ErrorMessage { get; set; }
    }
}
