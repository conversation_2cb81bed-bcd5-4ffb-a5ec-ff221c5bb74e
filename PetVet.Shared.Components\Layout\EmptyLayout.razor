﻿@inherits LayoutComponentBase
@inject PetVetAuthStateProvider _PetVetAuthStateProvider


@if (_PetVetAuthStateProvider.IsInitializing)
{
    <div class="alert alert-warning text-center">
        <p class="m-0">Initializing State...</p>
    </div>
}
else
{
    @Body
}


@code {
    protected override async Task OnInitializedAsync()
    {
        while (_PetVetAuthStateProvider.IsInitializing)
        {
            await Task.Delay(500);
        }
    }
}