﻿using System.ComponentModel.DataAnnotations;

namespace PetVet.Shared.DTOs
{
    public class VetTimeSlotDto
    {
        public int Id { get; set; }

        [Required]
        public int VetId { get; set; }

        [Required]
        public string Day { get; set; } = string.Empty;

        [Required]
        public string Time { get; set; } = string.Empty;

        public bool IsBooked { get; set; }

        public bool IsExpired { get; set; }
    }
}
