@page "/petOwner/account"
@inject IUserApi _UserApi
@inject IAuthApi _AuthApi
@inject IPetApi _PetApi
@inject IAppointmentApi _AppointmentApi
@inject IAppState _AppState
@inject IJSRuntime _JS
@inject PetVetAuthStateProvider _PetVetAuthStateProvider
@inject NavigationManager _NavigationManager

<PageTitle>My Account</PageTitle>

<!-- Modern Pet Owner Account Page -->
<div class="px-4 py-6 space-y-6">

    <!-- Profile Header Card -->
    <div class="bg-gradient-to-r from-primary-400 via-primary-500 to-secondary-400 rounded-3xl p-6 text-white relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full"></div>
            <div class="absolute bottom-4 left-4 w-20 h-20 bg-white rounded-full"></div>
        </div>

        <div class="relative z-10">
            <div class="flex items-center gap-4 mb-4">
                <!-- Profile Image -->
                <div class="relative">
                    @if (!string.IsNullOrEmpty(petOwnerProfile.ImageUrl))
                    {
                        <img src="@GetImageSrc(petOwnerProfile.ImageUrl)"
                             alt="Profile Image"
                             class="w-20 h-20 rounded-full object-cover border-4 border-white/30" />
                    }
                    else
                    {
                        <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center border-4 border-white/30">
                            <i class="fas fa-user text-white text-2xl"></i>
                        </div>
                    }

                    <!-- Camera Icon for Upload -->
                    <label class="absolute bottom-0 right-0 w-8 h-8 bg-white rounded-full flex items-center justify-center cursor-pointer shadow-lg hover:shadow-xl transition-all transform hover:scale-105">
                        <i class="fas fa-camera text-primary-600 text-sm"></i>
                        <InputFile OnChange="HandleImageUpload" accept="image/*" class="hidden" />
                    </label>
                </div>

                <!-- Profile Info -->
                <div class="flex-1">
                    <h1 class="text-2xl font-bold mb-1">@petOwnerProfile.Name</h1>
                    <p class="text-white/90 text-sm mb-2">@petOwnerProfile.Email</p>
                    @if (!string.IsNullOrEmpty(petOwnerProfile.ImageUrl))
                    {
                        <button @onclick="HandleImageDeleteConfirmation"
                                class="text-white/80 text-xs hover:text-white transition-colors">
                            <i class="fas fa-trash mr-1"></i>Remove Photo
                        </button>
                    }
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="flex gap-4">
                <div class="bg-white/20 rounded-2xl p-3 flex-1 text-center">
                    <div class="text-xl font-bold">@petCount</div>
                    <div class="text-xs text-white/80">My Pets</div>
                </div>
                <div class="bg-white/20 rounded-2xl p-3 flex-1 text-center">
                    <div class="text-xl font-bold">@appointmentCount</div>
                    <div class="text-xs text-white/80">Appointments</div>
                </div>
                <div class="bg-white/20 rounded-2xl p-3 flex-1 text-center">
                    <div class="text-xl font-bold">@trustedVets</div>
                    <div class="text-xs text-white/80">Trusted Vets</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="flex bg-gray-100 rounded-2xl p-1 mb-6">
        <button class="flex-1 py-3 px-4 rounded-xl text-sm font-medium transition-all @(activeTab == "profile" ? "bg-white text-gray-800 shadow-sm" : "text-gray-500 hover:text-gray-700")"
                @onclick='() => activeTab = "profile"'>
            <i class="fas fa-user mr-2"></i>Profile Information
        </button>
        <button class="flex-1 py-3 px-4 rounded-xl text-sm font-medium transition-all @(activeTab == "password" ? "bg-white text-gray-800 shadow-sm" : "text-gray-500 hover:text-gray-700")"
                @onclick='() => activeTab = "password"'>
            <i class="fas fa-key mr-2"></i>Security Settings
        </button>
    </div>

    <!-- Tab Content -->
    @if (activeTab == "profile")
    {
        <!-- Profile Information Card -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div class="flex items-center gap-3 mb-6">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-blue-600"></i>
                </div>
                <div>
                    <h2 class="text-lg font-bold text-gray-800">Profile Information</h2>
                    <p class="text-gray-500 text-sm">Update your personal details</p>
                </div>
            </div>

            <EditForm Model="petOwnerProfile" OnValidSubmit="UpdateProfileAsync">
                <DataAnnotationsValidator />

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                        <InputText @bind-Value="petOwnerProfile.Name"
                                   class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                                   placeholder="Enter your full name" />
                        <ValidationMessage For="() => petOwnerProfile.Name" class="text-red-500 text-sm mt-1" />
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                        <InputText @bind-Value="petOwnerProfile.Email"
                                   class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                                   placeholder="Enter your email" />
                        <ValidationMessage For="() => petOwnerProfile.Email" class="text-red-500 text-sm mt-1" />
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                        <InputText @bind-Value="petOwnerProfile.Phone"
                                   class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                                   placeholder="Enter your phone number" />
                        <ValidationMessage For="() => petOwnerProfile.Phone" class="text-red-500 text-sm mt-1" />
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                        <InputText @bind-Value="petOwnerProfile.Address"
                                   class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all"
                                   placeholder="Enter your address" />
                        <ValidationMessage For="() => petOwnerProfile.Address" class="text-red-500 text-sm mt-1" />
                    </div>
                </div>

                <div class="mt-6">
                    <button type="submit"
                            class="w-full bg-gradient-to-r from-primary-500 to-primary-600 text-white py-3 px-6 rounded-xl font-medium hover:from-primary-600 hover:to-primary-700 transition-all transform hover:scale-105 shadow-lg">
                        <i class="fas fa-save mr-2"></i>Save Changes
                    </button>
                </div>
            </EditForm>
        </div>
    }
    else if (activeTab == "password")
    {
        <!-- Security Settings Card -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div class="flex items-center gap-3 mb-6">
                <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-key text-red-600"></i>
                </div>
                <div>
                    <h2 class="text-lg font-bold text-gray-800">Security Settings</h2>
                    <p class="text-gray-500 text-sm">Update your password and security preferences</p>
                </div>
            </div>

            <EditForm Model="passwordModel" OnValidSubmit="ChangePasswordAsync">
                <DataAnnotationsValidator />

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                        <InputText type="password" @bind-Value="passwordModel.CurrentPassword"
                                   class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all"
                                   placeholder="Enter current password" />
                        <ValidationMessage For="() => passwordModel.CurrentPassword" class="text-red-500 text-sm mt-1" />
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                        <InputText type="password" @bind-Value="passwordModel.NewPassword"
                                   class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all"
                                   placeholder="Enter new password" />
                        <ValidationMessage For="() => passwordModel.NewPassword" class="text-red-500 text-sm mt-1" />
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                        <InputText type="password" @bind-Value="passwordModel.ConfirmPassword"
                                   class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all"
                                   placeholder="Confirm new password" />
                        <ValidationMessage For="() => passwordModel.ConfirmPassword" class="text-red-500 text-sm mt-1" />
                    </div>
                </div>

                @if (passwordError != null)
                {
                    <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-xl">
                        <div class="flex items-center gap-2 text-red-700">
                            <i class="fas fa-exclamation-circle"></i>
                            <span class="text-sm">@passwordError</span>
                        </div>
                    </div>
                }

                <div class="mt-6">
                    <button type="submit"
                            disabled="@isChangingPassword"
                            class="w-full bg-gradient-to-r from-red-500 to-pink-600 text-white py-3 px-6 rounded-xl font-medium hover:from-red-600 hover:to-pink-700 transition-all transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none">
                        @if (isChangingPassword)
                        {
                            <div class="flex items-center justify-center gap-2">
                                <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                <span>Processing...</span>
                            </div>
                        }
                        else
                        {
                            <div class="flex items-center justify-center gap-2">
                                <i class="fas fa-key"></i>
                                <span>Update Password</span>
                            </div>
                        }
                    </button>
                </div>
            </EditForm>
        </div>
    }

    <!-- Account Actions -->
    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div class="flex items-center gap-3 mb-4">
            <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                <i class="fas fa-cog text-gray-600"></i>
            </div>
            <div>
                <h2 class="text-lg font-bold text-gray-800">Account Actions</h2>
                <p class="text-gray-500 text-sm">Manage your account settings</p>
            </div>
        </div>

        <div class="space-y-3">
            <button @onclick="LogoutAsync"
                    class="w-full flex items-center justify-between p-4 bg-red-50 hover:bg-red-100 rounded-xl transition-all group">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-red-100 group-hover:bg-red-200 rounded-full flex items-center justify-center">
                        <i class="fas fa-sign-out-alt text-red-600 text-sm"></i>
                    </div>
                    <div class="text-left">
                        <div class="font-medium text-red-700">Logout</div>
                        <div class="text-red-600 text-sm">Sign out of your account</div>
                    </div>
                </div>
                <i class="fas fa-chevron-right text-red-400 group-hover:text-red-600"></i>
            </button>
        </div>
    </div>

</div>

<!-- Modal for Image Deletion -->
<BsModal OnModalConfirmation="ConfirmDeleteImage"
         ButtonText="Remove"
         Title="Are you sure you want to remove this profile image?">
</BsModal>

@code {
    private string activeTab = "profile";
    private UserDto petOwnerProfile = new();
    private bool isLoading = true;
    private string _error;

    private ChangePasswordDto passwordModel = new();
    private string passwordError;
    private bool isChangingPassword = false;
    private string originalProfileImage;

    // Stats for the profile header
    private int petCount = 0;
    private int appointmentCount = 0;
    private int trustedVets = 0;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            _AppState.ShowLoader("Loading Profile");
            
            // Check for tab query parameter
            var uri = new Uri(_NavigationManager.Uri);
            if (uri.Query.Contains("tab=security"))
            {
                activeTab = "password";
            }
            
            await LoadProfileDataAsync();
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error loading profile: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
            isLoading = false;
        }
    }

    private async Task LoadProfileDataAsync()
    {
        int userId = _PetVetAuthStateProvider.User.Id;
        petOwnerProfile = await _UserApi.GetCurrentPetOwnerUserAsync(userId);
        passwordModel.UserId = userId;
        originalProfileImage = petOwnerProfile.ImageUrl;

        // Load real stats from APIs
        await LoadRealStatsAsync(userId);
    }

    private async Task LoadRealStatsAsync(int userId)
    {
        try
        {
            // Load actual pet count
            var pets = await _PetApi.GetPetsByUserAsync(userId);
            petCount = pets?.Length ?? 0;

            // Load actual appointment count (total appointments)
            var appointments = await _AppointmentApi.GetPetOwnerAppointmentsAsync(userId);
            appointmentCount = appointments?.Count ?? 0;

            // Calculate trusted vets (unique vets the pet owner has appointments with)
            if (appointments?.Any() == true)
            {
                trustedVets = appointments
                    .Select(a => a.VetId)
                    .Distinct()
                    .Count();
            }
            else
            {
                trustedVets = 0;
            }
        }
        catch (Exception ex)
        {
            // If API calls fail, keep default values
            petCount = 0;
            appointmentCount = 0;
            trustedVets = 0;
        }
    }

    private async Task UpdateProfileAsync()
    {
        try
        {
            _AppState.ShowLoader("Updating Profile");
            petOwnerProfile.Role = nameof(UserRole.PetOwner); 

            if (petOwnerProfile.ImageUrl != null && petOwnerProfile.ImageUrl.StartsWith("data:"))
            {
                var base64Index = petOwnerProfile.ImageUrl.IndexOf("base64,");
                if (base64Index > 0)
                {
                    petOwnerProfile.ImageUrl = petOwnerProfile.ImageUrl.Substring(base64Index + 7);
                }
            }

            var response = await _UserApi.UpdatePetOwnerProfileAsync(petOwnerProfile);

            if (response.IsSuccess)
            {
                originalProfileImage = petOwnerProfile.ImageUrl;
                await _JS.ToastrSuccess("Profile updated successfully");
            }
            else
            {
                await _JS.ToastrError($"Failed to update profile: {response.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error updating profile: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private async Task ChangePasswordAsync()
    {
        try
        {
            passwordError = null;
            isChangingPassword = true;
            passwordModel.UserId = _PetVetAuthStateProvider.User.Id;

            var response = await _AuthApi.ChangePasswordAsync(passwordModel);

            if (response.IsSuccess)
            {
                await _JS.ToastrSuccess("Password changed successfully");
                passwordModel = new() { UserId = _PetVetAuthStateProvider.User.Id };
            }
            else
            {
                passwordError = response.ErrorMessage;
            }
        }
        catch (Exception ex)
        {
            passwordError = $"Error changing password: {ex.Message}";
        }
        finally
        {
            isChangingPassword = false;
        }
    }

    private async Task HandleImageUpload(InputFileChangeEventArgs e)
    {
        _AppState.ShowLoader("Uploading Image");

        try
        {
            var file = e.File;
            var allowedExtensions = new[] { ".png", ".jpg", ".jpeg" };
            var fileExtension = Path.GetExtension(file.Name).ToLower();

            if (!allowedExtensions.Contains(fileExtension))
            {
                _error = "Please upload a JPEG or PNG image.";
                await _JS.ToastrError(_error);
                return;
            }

            var stream = file.OpenReadStream(5 * 1024 * 1024);
            var memoryStream = new MemoryStream();
            await stream.CopyToAsync(memoryStream);
            petOwnerProfile.ImageUrl = Convert.ToBase64String(memoryStream.ToArray());
            await _JS.ToastrSuccess("Image uploaded successfully");
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error uploading image: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private void HandleImageDeleteConfirmation()
    {
        _JS.InvokeVoidAsync("ShowConfirmationModal");
    }

    public async Task ConfirmDeleteImage(bool isConfirmed)
    {
        if (isConfirmed)
        {
            petOwnerProfile.ImageUrl = null;
            await _JS.InvokeVoidAsync("HideConfirmationModal");
            await _JS.ToastrSuccess("Profile image removed successfully.");
        }
        _AppState.HideLoader();
        StateHasChanged();
    }

    private async Task LogoutAsync()
    {
        try
        {
            _AppState.ShowLoader("Signing out...");
            await _PetVetAuthStateProvider.SetLogoutAsync();
            _NavigationManager.NavigateTo("/auth/login", true);
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error during logout: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private string GetImageSrc(string? imageUrl)
    {
        if (string.IsNullOrEmpty(imageUrl))
            return string.Empty;

        // If it's already a data URL, return as is
        if (imageUrl.StartsWith("data:"))
            return imageUrl;

        // If it's a regular URL (http/https), return as is
        if (imageUrl.StartsWith("http"))
            return imageUrl;

        // Otherwise, treat it as base64 and create data URL
        return $"data:image/jpeg;base64,{imageUrl}";
    }
}