﻿using PetVet.Api.Services;
using PetVet.Shared.DTOs;
using PetVet.Shared;
using System.Security.Claims;

namespace PetVet.Api.Endpoints
{
    public static class PetEndpoints
    {
        public static IEndpointRouteBuilder MapPetEndpoints(this IEndpointRouteBuilder app)
        {
            var petGroup = app.MapGroup("/api/pets")
                .RequireAuthorization();

            petGroup.MapGet("/{id:int}", async (int id, PetService petService, HttpContext context) =>
            {
                var pet = await petService.GetPetAsync(id);
                if (pet == null)
                {
                    return Results.NotFound();
                }

                // Allow access if:
                // 1. User is the pet owner
                // 2. User is a vet and the pet was shared with them
                // 3. User is an admin
                var userRole = context.User.FindFirst(ClaimTypes.Role)?.Value;
                var userId = int.Parse(context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");

                if (pet.UserId == userId || // Pet owner
                    userRole == nameof(UserRole.Admin) || // Admin
                    userRole == nameof(UserRole.Vet)) // Vet (can view shared profiles)
                {
                    return Results.Ok(pet);
                }

                return Results.Unauthorized();
            });

            petGroup.MapGet("", async (PetService petService) =>
                Results.Ok(await petService.GetPetsAsync()));

            petGroup.MapGet("/user/{userId:int}", async (int userId, PetService petService, HttpContext context) =>
            {
                // Only allow users to view their own pets or admins to view any pets
                var userRole = context.User.FindFirst(ClaimTypes.Role)?.Value;
                var currentUserId = int.Parse(context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");

                if (userId != currentUserId && userRole != nameof(UserRole.Admin))
                {
                    return Results.Unauthorized();
                }

                return Results.Ok(await petService.GetPetsAsync(userId));
            });

            petGroup.MapPost("", async (PetDto dto, PetService petService, HttpContext context) =>
            {
                // When updating an existing pet, get the original data to preserve the owner
                if (dto.Id > 0)
                {
                    var existingPet = await petService.GetPetAsync(dto.Id);
                    if (existingPet != null)
                    {
                        // Only allow pet owner or admin to update
                        var userRole = context.User.FindFirst(ClaimTypes.Role)?.Value;
                        var userId = int.Parse(context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");

                        if (existingPet.UserId != userId && userRole != nameof(UserRole.Admin))
                        {
                            return Results.Unauthorized();
                        }

                        // Preserve the original owner
                        dto.UserId = existingPet.UserId;
                    }
                    else
                    {
                        return Results.NotFound($"Pet with ID {dto.Id} not found.");
                    }
                }
                else
                {
                    // For new pets, extract user ID from the JWT token
                    if (context.User.Identity?.IsAuthenticated == true)
                    {
                        var userIdClaim = context.User.FindFirst(ClaimTypes.NameIdentifier);
                        if (userIdClaim != null && int.TryParse(userIdClaim.Value, out int userId))
                        {
                            dto.UserId = userId; // Set the user ID from the token
                        }
                        else
                        {
                            return Results.BadRequest("Could not determine user ID from token.");
                        }
                    }
                    else
                    {
                        return Results.Unauthorized();
                    }
                }

                return Results.Ok(await petService.SavePetAsync(dto));
            });

            petGroup.MapDelete("/{id:int}", async (int id, PetService petService, HttpContext context) =>
            {
                var pet = await petService.GetPetAsync(id);
                if (pet == null)
                {
                    return Results.NotFound();
                }

                // Only allow pet owner or admin to delete
                var userRole = context.User.FindFirst(ClaimTypes.Role)?.Value;
                var userId = int.Parse(context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0");

                if (pet.UserId != userId && userRole != nameof(UserRole.Admin))
                {
                    return Results.Unauthorized();
                }

                return Results.Ok(await petService.DeletePetAsync(id));
            });

            return app;
        }
    }
}
