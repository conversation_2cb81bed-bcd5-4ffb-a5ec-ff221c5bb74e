﻿@inject IPetApi _PetApi
@inject IJSRuntime _JS
@inject IAppState _AppState
@inject PetVetAuthStateProvider _PetVetAuthStateProvider
@inject NavigationManager _NavigationManager

@if (!isProcessing)
{
    <!-- Modern Pet Cards Grid -->
    <div class="space-y-4">
        @if (_filteredPets.Any())
        {
            <div class="grid grid-cols-1 gap-4">
                @foreach (var pet in _filteredPets)
                {
                    var cardColor = GetPetCardColor(Array.IndexOf(_filteredPets, pet));

                    <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 cursor-pointer"
                         @onclick="() => NavigateToPetProfile(pet.Id)">

                        <div class="flex items-center gap-4">
                            <!-- Pet Image -->
                            <div class="relative">
                                @if (!string.IsNullOrEmpty(pet.ImageUrl))
                                {
                                    <img src="@pet.ImageUrl"
                                         alt="@pet.Name"
                                         class="w-16 h-16 rounded-2xl object-cover border-2 border-white shadow-sm" />
                                }
                                else
                                {
                                    <div class="w-16 h-16 @cardColor rounded-2xl flex items-center justify-center">
                                        <i class="fas fa-paw text-white text-xl"></i>
                                    </div>
                                }

                                <!-- Status Indicators -->
                                <div class="absolute -top-1 -right-1 flex gap-1">
                                    @if (IsVaccinated(pet.VaccinationStatus))
                                    {
                                        <div class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                                            <i class="fas fa-check text-white text-xs"></i>
                                        </div>
                                    }
                                </div>
                            </div>

                            <!-- Pet Info -->
                            <div class="flex-1">
                                <div class="flex items-center gap-2 mb-1">
                                    <h3 class="font-semibold text-gray-800 text-lg">@pet.Name</h3>
                                    @if (pet.Count > 1)
                                    {
                                        <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium">
                                            Group (@pet.Count)
                                        </span>
                                    }
                                </div>

                                <div class="flex items-center gap-4 text-sm text-gray-500 mb-2">
                                    <span class="flex items-center gap-1">
                                        <i class="fas fa-birthday-cake text-xs"></i>
                                        @GetPetAge(pet.DateOfBirth)
                                    </span>
                                    <span class="flex items-center gap-1">
                                        <i class="fas fa-tag text-xs"></i>
                                        @pet.Breed
                                    </span>
                                </div>

                                <!-- Status Tags -->
                                <div class="flex gap-2">
                                    @if (IsVaccinated(pet.VaccinationStatus))
                                    {
                                        <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
                                            Vaccinated
                                        </span>
                                    }
                                    <span class="bg-gray-100 text-gray-800 px-2 py-1 rounded-full text-xs font-medium">
                                        @pet.Gender
                                    </span>
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">
                                        @pet.VaccinationStatus
                                    </span>
                                </div>
                            </div>

                            <!-- Action Button -->
                            <div class="flex flex-col items-center gap-2">
                                <button class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center hover:bg-red-200 transition-colors">
                                    <i class="fas fa-heart text-red-500"></i>
                                </button>
                                <i class="fas fa-chevron-right text-gray-400 text-sm"></i>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
        else
        {
            <!-- Empty State -->
            <div class="text-center py-12">
                <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-paw text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-gray-800 mb-2">No pets found</h3>
                <p class="text-gray-500 text-sm mb-4">Add your first pet to get started</p>
                <button class="bg-teal-500 text-white px-6 py-2 rounded-full font-medium hover:bg-teal-600 transition-colors">
                    Add Pet
                </button>
            </div>
        }
    </div>
}

@code {
    private PetDto[] _pets = [];
    private PetDto[] _filteredPets = [];
    private bool isProcessing { get; set; } = true;
    private int? selectedCategoryId = null;
    private int _currentUserId => _PetVetAuthStateProvider?.User.Id ?? 0;
    private MyPetsFilters.PetFilterCriteria _currentFilters = new();
    private string _searchTerm = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadPetsAsync();
    }

    private async Task LoadPetsAsync()
    {
        isProcessing = true;
        _AppState.ShowLoader("Fetching Pets Data");

        try
        {
            // Get pets for the current user only
            _pets = await _PetApi.GetPetsByUserAsync(_currentUserId);

            _filteredPets = _pets.OrderByDescending(p => p.Id).Take(3).ToArray();
        }
        catch (Exception ex)
        {
            await _JS.ToastrError("Error loading pets");
        }
        finally
        {
            _AppState.HideLoader();
            isProcessing = false;
        }
    }

    private List<string> PetCardBGColors = new List<string>
    {
        "bg-blue-500",
        "bg-purple-500",
        "bg-green-500",
        "bg-pink-500",
        "bg-yellow-500",
        "bg-indigo-500"
    };

    private async Task HandleCategoryChanged(int? categoryId)
    {
        selectedCategoryId = categoryId;
        await ApplyFilters();
    }

    private async Task HandleFilterChanged(MyPetsFilters.PetFilterCriteria criteria)
    {
        _currentFilters = criteria;
        await ApplyFilters();
    }

    public async Task HandleSearch(string searchTerm)
    {
        _searchTerm = searchTerm?.Trim() ?? "";
        await ApplyFilters();
        StateHasChanged();
    }

    private Task ApplyFilters()
    {
        // Start with all pets for the current user
        var query = _pets.AsQueryable();

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(_searchTerm))
        {
            query = query.Where(p => p.Name != null &&
                                   p.Name.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase));
        }

        // Apply category filter from dropdown
        if (selectedCategoryId.HasValue)
        {
            query = query.Where(p => p.CategoryId == selectedCategoryId.Value);
        }

        // Apply category filters from checkboxes
        if (_currentFilters.CategoryIds.Any())
        {
            query = query.Where(p => _currentFilters.CategoryIds.Contains(p.CategoryId));
        }

        // Apply vaccination status filters
        if (_currentFilters.VaccinationStatuses.Any())
        {
            query = query.Where(p => _currentFilters.VaccinationStatuses.Contains(p.VaccinationStatus));
        }

        // For the main page, we only take the first 3 pets
        _filteredPets = query.OrderByDescending(p => p.Id).Take(3).ToArray();

        return Task.CompletedTask;
    }

    private string GetPetCardColor(int index)
    {
        return PetCardBGColors[index % PetCardBGColors.Count];
    }

    private void NavigateToPetProfile(int petId)
    {
        _NavigationManager.NavigateTo($"/petOwner/myPets/petprofile/{petId}");
    }

    private string GetPetAge(DateTime? dateOfBirth)
    {
        if (!dateOfBirth.HasValue)
            return "Unknown age";

        var age = DateTime.Now - dateOfBirth.Value;
        var years = (int)(age.TotalDays / 365.25);
        var months = (int)((age.TotalDays % 365.25) / 30.44);

        if (years > 0)
            return $"{years}y {months}m";
        else if (months > 0)
            return $"{months} months";
        else
            return "Newborn";
    }

    private bool IsVaccinated(string vaccinationStatus)
    {
        return !string.IsNullOrEmpty(vaccinationStatus) &&
               (vaccinationStatus.ToLower().Contains("vaccinated") ||
                vaccinationStatus.ToLower().Contains("complete") ||
                vaccinationStatus.ToLower().Contains("up to date"));
    }
}
