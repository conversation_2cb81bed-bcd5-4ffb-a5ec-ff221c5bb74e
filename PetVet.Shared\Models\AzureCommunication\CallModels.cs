using System.Text.Json.Serialization;

namespace PetVet.Shared.Models.AzureCommunication;

// Configuration for Azure Communication Services
public class AzureCommunicationConfig
{
    public string ConnectionString { get; set; } = "";
    public string EndpointUrl { get; set; } = "";
    public string AccessKey { get; set; } = "";
    public bool EnableLogging { get; set; } = true;
    public int CallTimeoutMinutes { get; set; } = 30;
}

// Call participant information
public class CallParticipant
{
    public string Id { get; set; } = "";
    public string DisplayName { get; set; } = "";
    public string AvatarUrl { get; set; } = "";
    public ParticipantRole Role { get; set; } = ParticipantRole.User;
    public bool IsVideoEnabled { get; set; } = false;
    public bool IsAudioEnabled { get; set; } = true;
    public bool IsMuted { get; set; } = false;
    public DateTime JoinedAt { get; set; } = DateTime.UtcNow;
    public ParticipantStatus Status { get; set; } = ParticipantStatus.Connecting;
}

public enum ParticipantRole
{
    User,
    Veterinarian,
    Assistant,
    Observer
}

public enum ParticipantStatus
{
    Connecting,
    Connected,
    Disconnected,
    InCall,
    OnHold
}

// Call session information
public class CallSession
{
    public string CallId { get; set; } = Guid.NewGuid().ToString();
    public string ThreadId { get; set; } = "";
    public CallType Type { get; set; } = CallType.AudioVideo;
    public CallStatus Status { get; set; } = CallStatus.Initiating;
    public DateTime StartTime { get; set; } = DateTime.UtcNow;
    public DateTime? EndTime { get; set; }
    public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? DateTime.UtcNow.Subtract(StartTime);
    public List<CallParticipant> Participants { get; set; } = new();
    public string InitiatorId { get; set; } = "";
    public string? RecordingUrl { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

public enum CallType
{
    Audio,
    Video,
    AudioVideo,
    ScreenShare
}

public enum CallStatus
{
    Initiating,
    Ringing,
    Connecting,
    Connected,
    OnHold,
    Ended,
    Failed,
    Cancelled
}

// Call invitation/request models
public class CallInvitation
{
    public string InvitationId { get; set; } = Guid.NewGuid().ToString();
    public string CallId { get; set; } = "";
    public string FromUserId { get; set; } = "";
    public string ToUserId { get; set; } = "";
    public string FromDisplayName { get; set; } = "";
    public CallType CallType { get; set; } = CallType.AudioVideo;
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    public DateTime ExpiresAt { get; set; } = DateTime.UtcNow.AddMinutes(2);
    public InvitationStatus Status { get; set; } = InvitationStatus.Pending;
    public string? Message { get; set; }
}

public enum InvitationStatus
{
    Pending,
    Accepted,
    Declined,
    Cancelled,
    Expired
}

// API Request/Response models
public class StartCallRequest
{
    public string ThreadId { get; set; } = "";
    public string InitiatorId { get; set; } = "";
    public string InitiatorName { get; set; } = "Initials";
    public CallType CallType { get; set; } = CallType.AudioVideo;
    public List<string> ParticipantIds { get; set; } = new();
    public string? Subject { get; set; }
    public Dictionary<string, object>? Metadata { get; set; }
}

public class StartCallResponse
{
    public bool Success { get; set; }
    public string? CallId { get; set; }
    public string? GroupCallId { get; set; }
    public string? AccessToken { get; set; }
    public string? UserId { get; set; }
    public string? Message { get; set; }
    public string? ErrorCode { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

public class JoinCallRequest
{
    public string CallId { get; set; } = "";
    public string UserId { get; set; } = "";
    public string DisplayName { get; set; } = "";
    public bool EnableVideo { get; set; } = false;
    public bool EnableAudio { get; set; } = true;
}

public class JoinCallResponse
{
    public bool Success { get; set; }
    public string? AccessToken { get; set; }
    public string? UserId { get; set; }
    public CallSession? CallSession { get; set; }
    public string? Message { get; set; }
    public string? ErrorCode { get; set; }
}

public class EndCallRequest
{
    public string CallId { get; set; } = "";
    public string UserId { get; set; } = "";
    public string? Reason { get; set; }
}

public class CallStatusUpdate
{
    public string CallId { get; set; } = "";
    public CallStatus Status { get; set; }
    public string? UserId { get; set; }
    public string? Message { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public Dictionary<string, object>? Data { get; set; }
}

// Call controls and actions
public class CallControlAction
{
    public string CallId { get; set; } = "";
    public string UserId { get; set; } = "";
    public CallControlType Action { get; set; }
    public bool Enabled { get; set; }
    public Dictionary<string, object>? Parameters { get; set; }
}

public enum CallControlType
{
    MuteAudio,
    UnmuteAudio,
    EnableVideo,
    DisableVideo,
    StartScreenShare,
    StopScreenShare,
    HoldCall,
    ResumeCall,
    TransferCall,
    RecordCall,
    StopRecording
}

// Call statistics and quality metrics
public class CallQualityMetrics
{
    public string CallId { get; set; } = "";
    public string UserId { get; set; } = "";
    public int AudioBitrate { get; set; }
    public int VideoBitrate { get; set; }
    public int PacketLoss { get; set; }
    public int Latency { get; set; }
    public int Jitter { get; set; }
    public string NetworkType { get; set; } = "";
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

// Emergency call features
public class EmergencyCallRequest
{
    public string UserId { get; set; } = "";
    public string PetId { get; set; } = "";
    public EmergencyType EmergencyType { get; set; }
    public string Description { get; set; } = "";
    public string? Location { get; set; }
    public string? ContactNumber { get; set; }
    public bool RequiresImmediate { get; set; } = true;
}

public enum EmergencyType
{
    Critical,
    Urgent,
    Emergency,
    Consultation
}

public class EmergencyCallResponse
{
    public bool Success { get; set; }
    public string? CallId { get; set; }
    public string? VeterinarianId { get; set; }
    public string? VeterinarianName { get; set; }
    public int EstimatedWaitTime { get; set; }
    public string? Message { get; set; }
    public string? ErrorCode { get; set; }
}
