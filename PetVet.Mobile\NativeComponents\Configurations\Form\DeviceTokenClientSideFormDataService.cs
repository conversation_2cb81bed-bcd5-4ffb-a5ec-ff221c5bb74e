﻿using DeepMessage.ServiceContracts.Features.Configurations;
using PetVet.Platform.Razor;
namespace Platform.Client.Common.Features.Configurations;
public class DeviceTokenClientSideFormDataService : IDeviceTokenFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public DeviceTokenClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}

	public async Task<string> SaveAsync(DeviceTokenFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<string>($"api/DeviceTokensForm/Save", formBusinessObject);
	}

	public async Task<DeviceTokenFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await _httpClient.GetFromJsonAsync<DeviceTokenFormBusinessObject>($"api/DeviceTokensForm/GetItemById?id=" + id);
	}
}
