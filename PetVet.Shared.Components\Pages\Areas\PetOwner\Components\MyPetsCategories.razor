﻿@inject ICategoryApi _CategoryApi

<!-- Modern Category Filter with Theme Styling -->
<div class="mb-6">
    <!-- Category Pills -->
    <div class="flex items-center gap-3 overflow-x-auto pb-2">
        <!-- Filter Toggle Button -->
        <button @onclick="ToggleFilters"
                class="flex-shrink-0 w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-2xl flex items-center justify-center transition-all">
            <i class="fas fa-sliders text-gray-600 text-sm"></i>
        </button>

        <!-- All Category -->
        <button @onclick="() => ToggleAllCategory()"
                class="flex-shrink-0 px-4 py-2 rounded-2xl font-medium text-sm transition-all @(selectedCategoryId == null ? "bg-orange-400 text-white shadow-sm" : "bg-gray-100 text-gray-600 hover:bg-gray-200")">
            <div class="flex items-center gap-2">
                <i class="fas fa-paw text-xs"></i>
                <span>All</span>
            </div>
        </button>

        <!-- Category Pills -->
        @foreach (var category in _categories)
        {
            <button @onclick="() => SelectCategory(category)"
                    class="flex-shrink-0 px-4 py-2 rounded-2xl font-medium text-sm transition-all @(selectedCategoryId == category.Id ? "bg-teal-400 text-white shadow-sm" : "bg-gray-100 text-gray-600 hover:bg-gray-200")">
                <div class="flex items-center gap-2">
                    <i class="@GetCategoryIcon(category.Name) text-xs"></i>
                    <span>@category.Name</span>
                </div>
            </button>
        }
    </div>

    <!-- Sliding Filter Panel -->
    <div class="fixed inset-0 z-50 transition-transform duration-300 @(ShowFilters ? "translate-x-0" : "-translate-x-full")"
         style="background: rgba(0,0,0,0.5);">
        <div class="w-80 h-full bg-white shadow-xl">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 class="text-lg font-bold text-gray-800">Filters</h3>
                    <button @onclick="ToggleFilters"
                            class="w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-full flex items-center justify-center transition-all">
                        <i class="fas fa-times text-gray-600 text-sm"></i>
                    </button>
                </div>

                <MyPetsFilters OnFilterChanged="HandleFilterChanged" />
            </div>
        </div>
    </div>
</div>

@code {
    private bool ShowFilters { get; set; } = false;
    private bool ShowCategories { get; set; } = false;
    private CategoryDto[] _categories = [];
    private int? selectedCategoryId = null;


    protected override async Task OnInitializedAsync()
    {
        await LoadCategoriesAsync();
    }


    private async Task LoadCategoriesAsync()
    {
        // Fetch categories from the database
        _categories = (await _CategoryApi.GetCategoriesAsync()).ToArray();

        // "All" is selected by default (null represents "All")
        if (_categories.Any())
        {
            selectedCategoryId = null;
        }
    }



    private void ToggleFilters() => ShowFilters = !ShowFilters;

    private void ToggleCategories() => ShowCategories = !ShowCategories;


    private void SelectCategory(CategoryDto category)
    {
        selectedCategoryId = category.Id;
        OnCategoryChanged.InvokeAsync(selectedCategoryId);
    }

    // Toggle behavior for the "All" category
    private void ToggleAllCategory()
    {
        if (selectedCategoryId == null)
        {
            selectedCategoryId = null;
        }
        else
        {
            selectedCategoryId = null;
        }

        OnCategoryChanged.InvokeAsync(selectedCategoryId);
    }

    private void HandleFilterChanged(MyPetsFilters.PetFilterCriteria criteria)
    {
        // Pass the filter criteria to the parent component
        OnFilterChanged.InvokeAsync(criteria);
    }

    private string GetCategoryIcon(string categoryName)
    {
        return categoryName?.ToLower() switch
        {
            "cat" => "fas fa-cat",
            "dog" => "fas fa-dog",
            "bird" => "fas fa-dove",
            "fish" => "fas fa-fish",
            "rabbit" => "fas fa-rabbit",
            "hamster" => "fas fa-hamster",
            "reptile" => "fas fa-dragon",
            _ => "fas fa-paw"
        };
    }

    [Parameter]
    public EventCallback<int?> OnCategoryChanged { get; set; }

    [Parameter]
    public EventCallback<MyPetsFilters.PetFilterCriteria> OnFilterChanged { get; set; }
}
