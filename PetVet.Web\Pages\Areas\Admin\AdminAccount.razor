﻿@page "/admin/account"
@inject IUserApi _UserApi
@inject IAuthApi _AuthApi
@inject IAppState _AppState
@inject IJSRuntime _JS
@inject PetVetAuthStateProvider _PetVetAuthStateProvider
@inject NavigationManager _NavigationManager

<PageTitle>My Account</PageTitle>

<BsModal OnModalConfirmation="ConfirmDeleteImage"
         ButtonText="Remove"
         Title="Are you sure you want to remove this profile image?">
</BsModal>

<div class="bg-gray-50 p-8 min-h-screen w-full max-w-6xl mx-auto">
    <!-- Profile Header Section -->
    <div class="bg-white rounded-3xl shadow-xl border border-gray-200 p-8 mb-8">
        <div class="flex flex-col lg:flex-row items-center gap-8">
            <!-- Profile Image Section -->
            <div class="flex flex-col items-center">
                @if (!string.IsNullOrEmpty(adminProfile.ImageUrl))
                {
                    <img src="@(adminProfile.ImageUrl.StartsWith("data:") ? adminProfile.ImageUrl : $"data:image/jpeg;base64,{adminProfile.ImageUrl}")"
                         alt="Profile Image" class="w-32 h-32 rounded-full object-cover shadow-lg" />
                }
                else
                {
                    <div class="w-32 h-32 bg-gray-200 rounded-full flex items-center justify-center shadow-lg">
                        <i class="fas fa-user-shield text-4xl text-gray-400"></i>
                    </div>
                }
                
                <!-- Image Actions -->
                <div class="flex flex-col sm:flex-row gap-3 mt-4">
                    <label class="inline-flex items-center space-x-2 bg-primary-500 hover:bg-primary-600 text-white font-medium py-2 px-4 rounded-lg cursor-pointer transition-colors duration-200">
                        <i class="fas fa-upload"></i>
                        <span>Upload Photo</span>
                        <InputFile OnChange="HandleImageUpload" accept="image/*" class="hidden" />
                    </label>
                    @if (!string.IsNullOrEmpty(adminProfile.ImageUrl))
                    {
                        <button type="button" @onclick="HandleImageDeleteConfirmation"
                                class="inline-flex items-center space-x-2 bg-red-500 hover:bg-red-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
                            <i class="fas fa-trash"></i>
                            <span>Remove Photo</span>
                        </button>
                    }
                </div>
            </div>
            
            <!-- Profile Info -->
            <div class="flex-1 text-center lg:text-left">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">@adminProfile.Name</h1>
                <p class="text-lg text-gray-600 mb-4">@adminProfile.Email</p>
                
                <!-- Stats Section -->
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    <div class="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-xl text-center">
                        <div class="flex items-center justify-center mb-2">
                            <img src="images/icons/team.png" alt="Users" class="w-8 h-8" />
                        </div>
                        <div class="text-sm font-medium">Total Users</div>
                        <div class="text-2xl font-bold">@totalUsers</div>
                    </div>
                    
                    <div class="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-xl text-center">
                        <div class="flex items-center justify-center mb-2">
                            <img src="images/icons/vets.png" alt="Vets" class="w-8 h-8" />
                        </div>
                        <div class="text-sm font-medium">Total Vets</div>
                        <div class="text-2xl font-bold">@totalVets</div>
                    </div>
                    
                    <div class="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-xl text-center">
                        <div class="flex items-center justify-center mb-2">
                            <img src="images/icons/pets.png" alt="Pet Owners" class="w-8 h-8" />
                        </div>
                        <div class="text-sm font-medium">Total Pet Owners</div>
                        <div class="text-2xl font-bold">@totalPetOwners</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Section -->
    <div class="bg-white rounded-3xl shadow-xl border border-gray-200 overflow-hidden">
        <!-- Tab Navigation -->
        <div class="border-b border-gray-200">
            <nav class="flex space-x-8 px-8 pt-6">
                <button class="pb-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 @(activeTab == "profile" ? "border-primary-500 text-primary-600" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")" 
                        @onclick='() => activeTab = "profile"'>
                    <i class="fas fa-user mr-2"></i>Profile Information
                </button>
                <button class="pb-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 @(activeTab == "password" ? "border-primary-500 text-primary-600" : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300")" 
                        @onclick='() => activeTab = "password"'>
                    <i class="fas fa-key mr-2"></i>Security Settings
                </button>
            </nav>
        </div>

        <!-- Tab Content -->
        <div class="p-8">
            @if (activeTab == "profile")
            {
                <EditForm Model="adminProfile" OnValidSubmit="UpdateProfileAsync">
                    <DataAnnotationsValidator />

                    <div class="space-y-6">
                        <div class="floating-label-group">
                            <InputText @bind-Value="adminProfile.Name" class="floating-label-input" id="Name" placeholder=" " />
                            <label for="Name" class="floating-label">Full Name</label>
                            <ValidationMessage For="() => adminProfile.Name" />
                        </div>

                        <div class="floating-label-group">
                            <InputText @bind-Value="adminProfile.Email" class="floating-label-input" id="Email" placeholder=" " />
                            <label for="Email" class="floating-label">Email Address</label>
                            <ValidationMessage For="() => adminProfile.Email" />
                        </div>

                        <div class="floating-label-group">
                            <InputText @bind-Value="adminProfile.Phone" class="floating-label-input" id="Phone" placeholder=" " />
                            <label for="Phone" class="floating-label">Phone Number</label>
                            <ValidationMessage For="() => adminProfile.Phone" />
                        </div>

                        <div class="floating-label-group">
                            <InputText @bind-Value="adminProfile.Address" class="floating-label-input" id="Address" placeholder=" " />
                            <label for="Address" class="floating-label">Address</label>
                            <ValidationMessage For="() => adminProfile.Address" />
                        </div>
                    </div>

                    <div class="flex justify-end mt-8">
                        <button type="submit" class="inline-flex items-center space-x-2 bg-primary-500 hover:bg-primary-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 hover:transform hover:scale-105 shadow-lg">
                            <i class="fas fa-save"></i>
                            <span>Save Changes</span>
                        </button>
                    </div>
                </EditForm>
            }
            else
            {
                <EditForm Model="passwordModel" OnValidSubmit="ChangePasswordAsync">
                    <DataAnnotationsValidator />

                    <div class="space-y-6">
                        <div class="floating-label-group">
                            <InputText type="password" @bind-Value="passwordModel.CurrentPassword" class="floating-label-input" id="CurrentPassword" placeholder=" " />
                            <label for="CurrentPassword" class="floating-label">Current Password</label>
                            <ValidationMessage For="() => passwordModel.CurrentPassword" />
                        </div>

                        <div class="floating-label-group">
                            <InputText type="password" @bind-Value="passwordModel.NewPassword" class="floating-label-input" id="NewPassword" placeholder=" " />
                            <label for="NewPassword" class="floating-label">New Password</label>
                            <ValidationMessage For="() => passwordModel.NewPassword" />
                        </div>

                        <div class="floating-label-group">
                            <InputText type="password" @bind-Value="passwordModel.ConfirmPassword" class="floating-label-input" id="ConfirmPassword" placeholder=" " />
                            <label for="ConfirmPassword" class="floating-label">Confirm Password</label>
                            <ValidationMessage For="() => passwordModel.ConfirmPassword" />
                        </div>
                    </div>

                    @if (passwordError != null)
                    {
                        <div class="bg-red-50 border border-red-200 text-red-800 p-4 rounded-xl mt-6 flex items-center">
                            <i class="fas fa-exclamation-circle mr-3"></i>
                            <span>@passwordError</span>
                        </div>
                    }

                    <div class="flex justify-end mt-8">
                        <button type="submit" disabled="@isChangingPassword"
                                class="inline-flex items-center space-x-2 bg-primary-500 hover:bg-primary-600 disabled:bg-gray-400 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 hover:transform hover:scale-105 shadow-lg disabled:hover:transform-none disabled:cursor-not-allowed">
                            @if (isChangingPassword)
                            {
                                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                <span>Processing...</span>
                            }
                            else
                            {
                                <i class="fas fa-key"></i>
                                <span>Update Password</span>
                            }
                        </button>
                    </div>
                </EditForm>
            }
        </div>
    </div>
</div>

@code {
    private string activeTab = "profile";
    private UserDto adminProfile = new();
    private string _error = string.Empty;

    private ChangePasswordDto passwordModel = new();
    private string passwordError = string.Empty;
    private bool isChangingPassword = false;
    private string originalProfileImage = string.Empty;

    // Admin stats
    private int totalUsers;
    private int totalVets;
    private int totalPetOwners;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            _AppState.ShowLoader("Loading Profile");
            await LoadProfileDataAsync();
            await LoadAdminStatsAsync();
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error loading profile: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private async Task LoadProfileDataAsync()
    {
        var currentUserId = _PetVetAuthStateProvider.User.Id;
        adminProfile = await _UserApi.GetUserAsync(currentUserId);
        originalProfileImage = adminProfile.ImageUrl ?? string.Empty;
    }

    private async Task LoadAdminStatsAsync()
    {
        var adminData = await _UserApi.GetAdminHomeDataAsync();
        totalUsers = adminData.TotalUsers;
        totalVets = adminData.TotalVets;
        totalPetOwners = adminData.TotalPetOwners;
    }

    private async Task UpdateProfileAsync()
    {
        try
        {
            _AppState.ShowLoader("Updating Profile");
            var result = await _UserApi.SaveUserAsync(adminProfile);
            
            if (result.IsSuccess)
            {
                await _JS.ToastrSuccess("Profile updated successfully.");
                // Update the auth state with the current user info - preserve the existing token
                var currentUser = _PetVetAuthStateProvider.User;
                var loggedInUser = new LoggedInUser(
                    adminProfile.Id, 
                    adminProfile.Name, 
                    adminProfile.Role, 
                    currentUser.Token
                );
                await _PetVetAuthStateProvider.SetLoginAsync(loggedInUser);
            }
            else
            {
                await _JS.ToastrError("Failed to update profile.");
            }
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error updating profile: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private async Task ChangePasswordAsync()
    {
        if (isChangingPassword) return;
        
        passwordError = string.Empty;
        isChangingPassword = true;

        try
        {
            var result = await _AuthApi.ChangePasswordAsync(passwordModel);
            
            if (result.IsSuccess)
            {
                await _JS.ToastrSuccess("Password updated successfully.");
                passwordModel = new ChangePasswordDto();
            }
            else
            {
                passwordError = result.ErrorMessage ?? "Failed to update password.";
            }
        }
        catch (Exception ex)
        {
            passwordError = $"Error updating password: {ex.Message}";
        }
        finally
        {
            isChangingPassword = false;
        }
    }

    private async Task HandleImageUpload(InputFileChangeEventArgs e)
    {
        var file = e.File;
        if (file != null && file.Size <= 5 * 1024 * 1024) // 5MB limit
        {
            try
            {
                _AppState.ShowLoader("Uploading Image");
                
                using var stream = file.OpenReadStream(5 * 1024 * 1024);
                using var ms = new MemoryStream();
                await stream.CopyToAsync(ms);
                var base64 = Convert.ToBase64String(ms.ToArray());
                
                adminProfile.ImageUrl = base64;
                await UpdateProfileAsync();
            }
            catch (Exception ex)
            {
                await _JS.ToastrError($"Error uploading image: {ex.Message}");
            }
            finally
            {
                _AppState.HideLoader();
            }
        }
        else
        {
            await _JS.ToastrError("Please select an image file smaller than 5MB.");
        }
    }

    private async Task HandleImageDeleteConfirmation()
    {
        await _JS.InvokeVoidAsync("ShowConfirmationModal");
    }

    [JSInvokable]
    public async Task ConfirmDeleteImage(bool isConfirmed)
    {
        await _JS.InvokeVoidAsync("HideConfirmationModal");
        
        if (isConfirmed)
        {
            try
            {
                _AppState.ShowLoader("Removing Image");
                adminProfile.ImageUrl = string.Empty;
                await UpdateProfileAsync();
            }
            catch (Exception ex)
            {
                await _JS.ToastrError($"Error removing image: {ex.Message}");
            }
            finally
            {
                _AppState.HideLoader();
            }
        }
    }
} 