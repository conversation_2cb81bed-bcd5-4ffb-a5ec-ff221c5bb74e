.VetArticle-CONTAINER {
    background: #f2f6f9;
    padding: 2rem;
    min-height: 100vh;
    width: 100%;
    max-width: 72rem;
    margin: 0 auto;
    font-family: 'Inter', sans-serif;
}

.back-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #3B82F6;
    font-size: 1rem;
    font-weight: 500;
    padding: 0.75rem 1rem;
    border: none;
    background: white;
    border-radius: 0.75rem;
    margin-bottom: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.back-button:hover {
    transform: translateX(-4px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.article-content {
    background: white;
    border-radius: 1.25rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.article-header {
    padding: 2rem;
    background: #157BAB;
    color: white;
}

.article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.author-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-image {
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid rgba(255, 255, 255, 0.5);
}

.author-details {
    display: flex;
    flex-direction: column;
}

.author-name {
    font-weight: 600;
    font-size: 1rem;
}

.post-date {
    font-size: 0.875rem;
    color: #E5E7EB;
}

.article-tags {
    display: flex;
    gap: 0.5rem;
}

.tag {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
}

.article-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
    font-family: 'Acme', sans-serif;
}

.article-summary {
    font-size: 1.125rem;
    color: #E5E7EB;
    line-height: 1.5;
}

.article-image-container {
    width: 100%;
    height: 24rem;
    overflow: hidden;
}

.article-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.article-body {
    padding: 2rem;
    font-family: 'Lora', serif;
    color: #374151;
    line-height: 1.8;
}

.article-body h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1F2937;
    margin: 2rem 0 1rem;
    font-family: 'Acme', sans-serif;
}

.article-body p {
    margin-bottom: 1.5rem;
}

.article-body ul {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.article-body li {
    margin-bottom: 0.5rem;
}

.article-footer {
    padding: 2rem;
    border-top: 1px solid #E5E7EB;
}

.share-section {
    text-align: center;
}

.share-section h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 1rem;
    font-family: 'Acme', sans-serif;
}

.share-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.share-button {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    border: none;
    background: #F3F4F6;
    color: #3B82F6;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.share-button:hover {
    background: #3B82F6;
    color: white;
    transform: translateY(-2px);
}

/* Mobile Layout */
@media (max-width: 639px) {
    .VetArticle-CONTAINER {
        padding: 1rem;
    }

    .article-header {
        padding: 1.5rem;
    }

    .article-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .article-tags {
        flex-wrap: wrap;
    }

    .article-title {
        font-size: 1.5rem;
    }

    .article-image-container {
        height: 16rem;
    }

    .article-body {
        padding: 1.5rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.VetArticle-CONTAINER {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}