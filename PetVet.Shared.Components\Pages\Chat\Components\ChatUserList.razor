﻿@inject IUserApi _UserApi
@inject IJSRuntime _JS
@inject IAppState _AppState


<h3>Users</h3>

<ul class="list-group list-group-flush">
    @foreach (var user in Users)
    {
        <li class="list-group-item" @onclick="() => HandleUserClick(user)">
            @user.Name
            <span class="badge rounded-pill bg-@(user.IsOnline == true ? "success" : "danger")">.</span>
        </li>
    }
</ul>

@code {
    [Parameter]
    public ICollection<UserDto> Users { get; set; } = new HashSet<UserDto>();

    [Parameter]
    public EventCallback<UserDto> OnUserSelected { get; set; }


    private async Task HandleUserClick(UserDto user)
    {
        await OnUserSelected.InvokeAsync(user);
    }
}
