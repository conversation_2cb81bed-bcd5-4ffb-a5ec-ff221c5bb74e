﻿@using System.Net.Http
@using System.Net.Http.Json
@using System.Security.Claims
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.AspNetCore.Components.WebAssembly.Http
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Authorization
@using PetVet.Web
@using PetVet.Web.Layout
@using PetVet.Shared.DTOs
@using PetVet.Shared
@using PetVet.Shared.Components.Components
@using PetVet.Shared.AppState
@using PetVet.Shared.Components.Services.Auth
@using PetVet.Shared.Components.Services.Extensions
@using PetVet.Shared.Components.Apis
