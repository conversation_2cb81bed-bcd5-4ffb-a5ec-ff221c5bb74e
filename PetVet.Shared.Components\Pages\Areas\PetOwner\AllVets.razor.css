.AllVets-CONTAINER {
    background: linear-gradient(135deg, #F9FAFB 0%, #EDF2F7 100%);
    padding: 2rem;
    min-height: 100vh;
    width: 100%;
    max-width: 72rem;
    margin: 0 auto; 
}

/* Search and Filter Section */
.search-filter-container {
    position: sticky;
    top: 0;
    z-index: 10;
    background: inherit;
    padding: 1rem 0;
    margin-bottom: 2rem;
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
}

.search-box {
    position: relative;
    margin-bottom: 1rem;
}

.search-box input {
    width: 100%;
    padding: 0.875rem 2.5rem;
    background-color: white;
    border: 1px solid #E5E7EB;
    border-radius: 1rem;
    font-size: 1rem;
    color: #374151;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #3B82F6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6B7280;
    font-size: 1rem;
}

.filter-options {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.filter-options select {
    flex: 1;
    padding: 0.75rem 1rem;
    border: 1px solid #E5E7EB;
    border-radius: 0.75rem;
    background-color: white;
    color: #374151;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-options select:focus {
    outline: none;
    border-color: #3B82F6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Vets Grid */
.vets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(20rem, 1fr));
    gap: 1.5rem;
    padding: 0.5rem;
}

.vet-card {
    background: white;
    border-radius: 1.25rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.vet-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.15);
}

.vet-card:active {
    transform: translateY(-2px);
}

.vet-image-container {
    width: 100%;
    height: 12rem;
    overflow: hidden;
}

.vet-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.vet-card:hover .vet-image {
    transform: scale(1.05);
}

.vet-info {
    padding: 1.5rem;
}

.vet-info h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1F2937;
    margin: 0; 
}

.specialization {
    color: #3B82F6;
    font-size: 0.875rem;
    font-weight: 500;
    margin: 0.5rem 0;
}

.clinic-name {
    color: #6B7280;
    font-size: 0.875rem;
    margin: 0.25rem 0 1rem;
}

.rating {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.rating i {
    color: #FCD34D;
    font-size: 0.875rem;
}

.rating span {
    color: #6B7280;
    font-size: 0.875rem;
    margin-left: 0.5rem;
}

.experience,
.location {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #6B7280;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.experience i,
.location i {
    color: #3B82F6;
    width: 1rem;
    text-align: center;
}

/* Mobile Layout */
@media (max-width: 639px) {
    .AllVets-CONTAINER {
        padding: 1rem;
    }

    .vets-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .filter-options {
        flex-direction: column;
    }

    .vet-image-container {
        height: 10rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.AllVets-CONTAINER {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}