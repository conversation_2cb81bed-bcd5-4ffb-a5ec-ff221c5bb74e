﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <SupportedPlatform Include="browser" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.Web" Version="9.0.5" />
	  <PackageReference Include="Microsoft.AspNetCore.Components.Authorization" Version="9.0.5" />
	  <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="9.0.5" />
	  <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.12.0" />
	  <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PetVet.Shared\PetVet.Shared.csproj" />
  </ItemGroup>

  
</Project>
