﻿using Microsoft.AspNetCore.Mvc;
using PetVet.ServiceContracts.Features.Conversation;
namespace PetVet.Server.WebApis.Controller.Conversation;
[ApiController, Route("api/[controller]/[action]")]
public class ChatMessagesSyncsFormController : ControllerBase, IChatMessagesSyncFormDataService
{

	private readonly IChatMessagesSyncFormDataService dataService;

	public ChatMessagesSyncsFormController(IChatMessagesSyncFormDataService dataService)
	{
		this.dataService = dataService;
	}
	
	[HttpPost]
	public async Task<string> SaveAsync([FromBody] ChatMessagesSyncFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	}
	
	[HttpGet]
	public async Task<ChatMessagesSyncFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await dataService.GetItemByIdAsync(id);
	}
}
