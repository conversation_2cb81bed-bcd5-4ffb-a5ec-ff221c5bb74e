using PetVet.Shared.Models.AzureOpenAI;

namespace PetVet.Shared.Services;

public interface IAzureOpenAIService
{
    /// <summary>
    /// Send a chat message and get AI response
    /// </summary>
    Task<ChatApiResponse> SendChatMessageAsync(string message, string? conversationId = null, string? userId = null);

    /// <summary>
    /// Send a chat message with full conversation context
    /// </summary>
    Task<ChatApiResponse> SendChatMessageAsync(ChatRequest request, string? conversationId = null, string? userId = null);

    /// <summary>
    /// Get conversation history
    /// </summary>
    Task<ChatConversation?> GetConversationAsync(string conversationId);

    /// <summary>
    /// Get all conversations for a user
    /// </summary>
    Task<List<ChatConversation>> GetUserConversationsAsync(string userId);

    /// <summary>
    /// Delete a conversation
    /// </summary>
    Task<bool> DeleteConversationAsync(string conversationId);

    /// <summary>
    /// Clear all conversations for a user
    /// </summary>
    Task<bool> ClearUserConversationsAsync(string userId);

    /// <summary>
    /// Check if the service is healthy and can connect to Azure OpenAI
    /// </summary>
    Task<bool> IsHealthyAsync();

    /// <summary>
    /// Get service statistics
    /// </summary>
    Task<ChatServiceStats> GetStatsAsync();
}

public class ChatServiceStats
{
    public int TotalConversations { get; set; }
    public int TotalMessages { get; set; }
    public int TotalTokensUsed { get; set; }
    public DateTime LastActivity { get; set; }
    public bool IsConnected { get; set; }
    public string? LastError { get; set; }
    public Dictionary<string, object> AdditionalMetrics { get; set; } = new();
}
