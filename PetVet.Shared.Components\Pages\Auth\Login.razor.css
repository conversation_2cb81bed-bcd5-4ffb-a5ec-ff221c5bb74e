﻿.Login-CONTAINER {
    background: #f2f6f9;
    display: flex;
    justify-content: center;
    align-items: flex-start;
}

.Login-CARD {
    border-radius: 1.25rem;
    width: 95%;
}

.Login-HEADER {
    background: #157BAB;
    color: #ffffff;
    border-radius: 1.25rem;
}

.Login-HEADING {
    font-size: 1.5rem;
    font-family: 'Acme', sans-serif;
    font-weight: bold;
    cursor: default;
    margin: 0;
}

.Login-CARD-BODY {
    padding: 2rem;
}

.Login-BUTTON-Div {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 1rem;
}

.Login-BTN {
    display: inline-block;
    background-color: #FFC107;
    border: none;
    border-radius: 0.625rem;
    padding: 0.625rem 1.25rem;
    color: #333;
    
    font-weight: bold;
    cursor: pointer;
    margin-top: 0.625rem;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.Login-BTN-ELSE {
    display: inline-block;
    border: none;
    border-radius: 0.625rem;
    padding: 0.625rem 1.25rem;
    color: #333;
    
    font-weight: bold;
    cursor: pointer;
    margin-top: 0.625rem;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.Login-BTN:hover {
    background-color: #e0a406;
    transform: scale(1.05);
}

.Login-NoAccountText {
    
    cursor: default;
}


.Login-Register-Link,
.Login-Forgot-Link {
    color: #157BAB;
    
    text-decoration: underline;
    cursor: pointer;
}

    .Login-Register-Link:hover,
    .Login-Forgot-Link:hover {
        color: #FFC107;
    }

@media (max-width: 768px) {
    .Login-CARD-BODY {
        padding: 1rem;
    }

    .Login-HEADING {
        font-size: 1.5rem;
    }

    .Login-BTN {
        width: 100%;
    }
}

@media (max-width: 768px) {

    .Login-CARD {
        border-radius: 1.25rem;
        width: 100%;
    }

    .Login-BUTTON-Div {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .Login-BUTTON {
        width: 100%;
    }

    .Login-CARD-BODY {
        padding: 1rem;
    }

    .Login-HEADING {
        font-size: 1.5rem;
    }
}

/* Login Component Styles */

/* Keyframe Animations */
@keyframes shake {
    0%, 100% { 
        transform: translateX(0); 
    }
    25% { 
        transform: translateX(-5px); 
    }
    75% { 
        transform: translateX(5px); 
    }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInFromLeft {
    0% {
        opacity: 0;
        transform: translateX(-30px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Custom Animation Classes */
.animate-shake {
    animation: shake 0.5s ease-in-out;
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slideInFromLeft 0.5s ease-out;
}

/* Enhanced Focus States */
.group:focus-within .fas {
    color: #3B82F6 !important;
    transform: scale(1.1);
}

.group:focus-within input {
    background-color: white;
    border-color: #3B82F6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Custom Button Styles */
.login-button {
    position: relative;
    overflow: hidden;
}

.login-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.login-button:hover::before {
    left: 100%;
}

/* Loading Spinner Enhancement */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Input Field Enhancements */
.form-input {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-input:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Card Hover Effects */
.login-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.login-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* Logo Animation */
.logo-container {
    position: relative;
}

.logo-container::after {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 50%;
    background: linear-gradient(45deg, #3B82F6, #8B5CF6);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
    filter: blur(15px);
}

.logo-container:hover::after {
    opacity: 0.2;
}

/* Error Message Styles */
.error-message {
    animation: shake 0.5s ease-in-out, fadeInUp 0.3s ease-out;
}

/* Footer Animation */
.footer-item {
    transition: transform 0.2s ease, color 0.2s ease;
}

.footer-item:hover {
    transform: translateY(-1px);
    color: #3B82F6;
}

/* Base Responsive Styles */
@media (max-width: 480px) {
    .login-card {
        margin: 0.5rem;
        border-radius: 1rem;
        min-height: auto;
    }
    
    .form-input {
        padding: 0.75rem 0.75rem 0.75rem 2.5rem;
        font-size: 16px; /* Prevent zoom on iOS */
        border-radius: 0.75rem;
    }
    
    .login-button {
        padding: 0.875rem 1rem;
        font-size: 0.875rem;
        border-radius: 0.75rem;
    }
    
    /* Reduce spacing on very small screens */
    .form-input:focus {
        transform: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .login-card:hover {
        transform: none;
    }
}

/* Small Mobile Devices */
@media (max-width: 640px) {
    .login-card {
        margin: 0.75rem;
        border-radius: 1.25rem;
    }
    
    .form-input {
        padding: 0.875rem 0.875rem 0.875rem 2.75rem;
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .login-button {
        padding: 1rem 1.25rem;
        font-size: 1rem;
    }
    
    /* Optimize footer for mobile */
    .footer-item span {
        display: none;
    }
    
    .footer-item i {
        font-size: 1rem;
    }
}

/* Medium screens and up */
@media (min-width: 641px) {
    .footer-item span {
        display: inline;
    }
}

/* Tablet and larger */
@media (min-width: 768px) {
    .form-input:focus {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
    }
    
    .login-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
    }
}

/* Large screens */
@media (min-width: 1024px) {
    .logo-container::after {
        filter: blur(20px);
    }
    
    .logo-container:hover::after {
        opacity: 0.3;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .form-input:focus {
        transform: none;
    }
    
    .login-card:hover {
        transform: none;
    }
    
    .footer-item:hover {
        transform: none;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .form-input {
        border-width: 2px;
        border-color: #000;
    }
    
    .login-button {
        border: 2px solid transparent;
    }
    
    .login-button:focus {
        border-color: white;
        outline: 2px solid white;
        outline-offset: 2px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .login-card {
        background-color: rgba(31, 41, 55, 0.95);
        border-color: rgba(75, 85, 99, 0.3);
    }
    
    .form-input {
        background-color: rgba(55, 65, 81, 0.5);
        border-color: rgba(75, 85, 99, 0.5);
        color: white;
    }
    
    .form-input::placeholder {
        color: rgba(156, 163, 175, 0.8);
    }
    
    .form-input:focus {
        background-color: rgba(55, 65, 81, 0.8);
        border-color: #3B82F6;
    }
}

/* Landscape orientation on mobile */
@media (max-width: 640px) and (orientation: landscape) {
    .login-card {
        max-height: 90vh;
        overflow-y: auto;
    }
    
    /* Reduce vertical spacing in landscape */
    .text-center.mb-4 {
        margin-bottom: 0.5rem;
    }
    
    .mb-4 {
        margin-bottom: 0.75rem;
    }
}

/* Very small screens (iPhone SE, etc.) */
@media (max-width: 375px) {
    .login-card {
        margin: 0.25rem;
        padding: 0.5rem;
    }
    
    .form-input {
        padding: 0.625rem 0.625rem 0.625rem 2.25rem;
        font-size: 16px;
    }
    
    .login-button {
        padding: 0.75rem 0.875rem;
        font-size: 0.875rem;
    }
    
    /* Hide footer text on very small screens */
    .footer-item span {
        display: none;
    }
    
    .footer-item {
        padding: 0.25rem;
    }
}

/* Ensure minimum touch targets on mobile */
@media (max-width: 640px) {
    .login-button,
    .form-input,
    input[type="checkbox"] {
        min-height: 44px; /* iOS accessibility guideline */
    }
    
    /* Ensure clickable areas are large enough */
    .footer-item {
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.5rem;
    }
}
