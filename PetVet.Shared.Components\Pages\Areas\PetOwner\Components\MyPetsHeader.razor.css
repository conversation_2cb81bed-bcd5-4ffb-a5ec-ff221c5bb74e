﻿.header-container {
    position: relative;
    margin-bottom: 2rem;
}

.search-section {
    display: flex;
    gap: 1rem;
    align-items: center;
    justify-content: center;
}

.search-box {
    flex: 1;
    position: relative;
    max-width: 32rem;
}

.search-input {
    width: 100%;
    padding: 0.875rem 2.75rem;
    border: 2px solid #E5E7EB;
    border-radius: 1.25rem;
    
    font-size: 0.875rem;
    color: #1F2937;
    background: white;
    transition: all 0.3s ease;
}

    .search-input:focus {
        outline: none;
        border-color: #3B82F6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .search-input::placeholder {
        color: #9CA3AF;
    }

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9CA3AF;
    font-size: 0.875rem;
}

.clear-btn {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #9CA3AF;
    cursor: pointer;
    padding: 0.25rem;
    transition: color 0.3s ease;
}

    .clear-btn:hover {
        color: #6B7280;
    }

.filter-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.25rem;
    background: white;
    border: 2px solid #E5E7EB;
    border-radius: 1.25rem;
    color: #4B5563;
    
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

    .filter-btn:hover {
        border-color: #3B82F6;
        color: #3B82F6;
    }

.filter-badge {
    background: #3B82F6;
    color: white;
    padding: 0.125rem 0.375rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
}

/* Filters Panel */
.filters-panel {
    position: absolute;
    top: calc(100% + 0.5rem);
    right: 0;
    width: 100%;
    max-width: 24rem;
    background: white;
    border-radius: 1.25rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    z-index: 10;
    animation: slideDown 0.3s ease-out forwards;
}

.filters-content {
    padding: 1.5rem;
}

.filter-group {
    margin-bottom: 1.5rem;
}

    .filter-group:last-child {
        margin-bottom: 0;
    }

    .filter-group h3 {
        font-size: 0.875rem;
        font-weight: 600;
        color: #4B5563;
        margin-bottom: 0.75rem;
        font-family: 'Acme', sans-serif;
    }

.filter-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.75rem;
}

.filter-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    
    font-size: 0.875rem;
    color: #6B7280;
}

    .filter-checkbox input[type="checkbox"] {
        width: 1rem;
        height: 1rem;
        border: 2px solid #E5E7EB;
        border-radius: 0.25rem;
        cursor: pointer;
    }

        .filter-checkbox input[type="checkbox"]:checked {
            background-color: #3B82F6;
            border-color: #3B82F6;
        }

.filters-actions {
    display: flex;
    gap: 1rem;
    padding: 1rem 1.5rem;
    background: #F9FAFB;
    border-top: 1px solid #E5E7EB;
    border-radius: 0 0 1.25rem 1.25rem;
}

.clear-filters-btn,
.apply-filters-btn {
    flex: 1;
    padding: 0.75rem;
    border-radius: 0.75rem;
    
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.clear-filters-btn {
    background: white;
    border: 1px solid #E5E7EB;
    color: #4B5563;
}

    .clear-filters-btn:hover {
        border-color: #9CA3AF;
        color: #1F2937;
    }

.apply-filters-btn {
    background: #3B82F6;
    border: none;
    color: white;
}

    .apply-filters-btn:hover {
        background: #2563EB;
    }

/* Animations */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    

    .search-box {
        max-width: 100%;
    }

    .filter-btn {
        width: 100%;
        justify-content: center;
    }

    .filters-panel {
        max-width: 100%;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
