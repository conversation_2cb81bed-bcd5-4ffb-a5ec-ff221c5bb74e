﻿<Router AppAssembly="@typeof(App).Assembly" AdditionalAssemblies="new []{typeof(PetVet.Shared.Components.Services.Auth.PetVetAuthStateProvider).Assembly}">
    <Found Context="routeData">
        <AuthorizeRouteView RouteData="@routeData" DefaultLayout="@typeof(MainLayout)">
            <NotAuthorized>
                <div class="alert alert-danger text-center">
                    <p class="m-0">You are not authorized to view this content.</p>
                </div>
            </NotAuthorized>
        </AuthorizeRouteView>
        <FocusOnNavigate RouteData="@routeData" Selector="h1" />
    </Found>
    <NotFound>
        <PageTitle>Not found</PageTitle>
        <LayoutView Layout="@typeof(MainLayout)">
            <div class="alert alert-warning text-center">
                <p class="m-0">Sorry, there's nothing at this address.</p>
            </div>
        </LayoutView>
    </NotFound>
</Router>

<Loader />

@inject PetVetAuthStateProvider _PetVetAuthStateProvider

@code {
    protected override async Task OnInitializedAsync()
    {
        await _PetVetAuthStateProvider.InitializeAsync();
    }
}
