﻿using Microsoft.AspNetCore.Mvc;
using PetVet.ServiceContracts.Features.Conversation;
namespace PetVet.Server.WebApis.Controller.Conversation;
[ApiController, Route("api/[controller]/[action]")]
public class ChatMessageFormController : ControllerBase, IChatMessageFormDataService
{

	private readonly IChatMessageFormDataService dataService;

	public ChatMessageFormController(IChatMessageFormDataService dataService)
	{
		this.dataService = dataService;
	}
	
	[HttpPost]
	public async Task<string> SaveAsync([FromBody] ChatMessageFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	}
	
	[HttpGet]
	public async Task<ChatMessageFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await dataService.GetItemByIdAsync(id);
	}
}
