﻿@page "/vet/schedule"
@inject IAppointmentApi _AppointmentApi
@inject IAppState _AppState
@inject NavigationManager _NavigationManager
@inject IJSRuntime _JS
@inject PetVetAuthStateProvider _PetVetAuthStateProvider

<PageTitle>Manage Working Schedule</PageTitle>

<!-- Modern Vet Schedule Page -->
<div class="px-4 py-6 space-y-6">

    <!-- Header Section -->
    <div class="text-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-2">Manage Working Schedule</h1>
        <p class="text-gray-600">Set your availability for patient appointments</p>
    </div>

    <!-- Working Days Section -->
    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div class="flex items-center gap-3 mb-4">
            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <i class="fas fa-calendar text-blue-600"></i>
            </div>
            <div>
                <h3 class="text-lg font-bold text-gray-800">Working Days</h3>
                <p class="text-gray-500 text-sm">Select the days you're available</p>
            </div>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3">
            @foreach (var day in DaysOfWeek)
            {
                <button class="p-3 rounded-xl border-2 transition-all text-sm font-medium @(selectedDays.Contains(day) ? "border-blue-500 bg-blue-50 text-blue-700" : "border-gray-200 hover:border-blue-300 hover:bg-blue-50 text-gray-700")"
                        @onclick="() => ToggleDay(day)">
                    <div class="text-center">
                        <div class="mb-1">@day.Substring(0, 3)</div>
                        <i class="fas @(selectedDays.Contains(day) ? "fa-check-circle text-blue-600" : "fa-circle text-gray-300")"></i>
                    </div>
                </button>
            }
        </div>
    </div>

    <!-- Time Slots Section -->
    <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
        <div class="flex items-center gap-3 mb-4">
            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                <i class="fas fa-clock text-green-600"></i>
            </div>
            <div>
                <h3 class="text-lg font-bold text-gray-800">Available Time Slots</h3>
                <p class="text-gray-500 text-sm">Choose your preferred appointment times</p>
            </div>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
            @foreach (var timeSlot in DefaultTimeSlots)
            {
                <button class="p-3 rounded-xl border-2 transition-all text-sm font-medium @(selectedTimeSlots.Contains(timeSlot) ? "border-green-500 bg-green-50 text-green-700" : "border-gray-200 hover:border-green-300 hover:bg-green-50 text-gray-700")"
                        @onclick="() => ToggleTimeSlot(timeSlot)">
                    <div class="text-center">
                        <div class="mb-1">@timeSlot</div>
                        <i class="fas @(selectedTimeSlots.Contains(timeSlot) ? "fa-check-circle text-green-600" : "fa-circle text-gray-300")"></i>
                    </div>
                </button>
            }
        </div>
    </div>

    <!-- Current Schedule Preview -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
        <div class="p-4 bg-gray-50 border-b border-gray-200 cursor-pointer" @onclick="ToggleScheduleView">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-table-cells text-purple-600 text-sm"></i>
                    </div>
                    <h3 class="text-lg font-bold text-gray-800">Current Schedule Preview</h3>
                </div>
                <i class="fas @(isScheduleExpanded ? "fa-chevron-up" : "fa-chevron-down") text-gray-500"></i>
            </div>
        </div>

        @if (isScheduleExpanded)
        {
            <div class="p-6">
                @if (selectedDays.Any() && selectedTimeSlots.Any())
                {
                    <div class="space-y-4">
                        @foreach (var day in selectedDays.OrderBy(d => DaysOfWeek.IndexOf(d)))
                        {
                            <div class="bg-gray-50 rounded-xl p-4">
                                <h4 class="font-bold text-gray-800 mb-3">@day</h4>
                                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                                    @foreach (var slot in selectedTimeSlots.OrderBy(s => s))
                                    {
                                        var timeSlot = existingTimeSlots.FirstOrDefault(ts => ts.Day == day && ts.Time == slot);
                                        <div class="p-2 rounded-lg text-sm @(timeSlot?.IsBooked == true ? "bg-red-100 text-red-700 border border-red-200" : "bg-green-100 text-green-700 border border-green-200")">
                                            <div class="flex items-center justify-between">
                                                <span class="font-medium">@slot</span>
                                                @if (timeSlot?.IsBooked == true)
                                                {
                                                    <span class="text-xs bg-red-200 text-red-800 px-2 py-1 rounded-full">Booked</span>
                                                }
                                                else
                                                {
                                                    <i class="fas fa-check text-green-600"></i>
                                                }
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-center py-8">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-calendar-times text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-800 mb-2">No Schedule Set</h3>
                        <p class="text-gray-500">Select working days and time slots to create your schedule</p>
                    </div>
                }
            </div>
        }
    </div>

    <!-- Save Button -->
    <div class="mt-6">
        <button class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-4 px-6 rounded-2xl font-medium hover:from-blue-600 hover:to-purple-700 transition-all transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                @onclick="SaveSchedule"
                disabled="@(!HasChanges)">
            <i class="fas fa-save mr-2"></i>
            <span>Save Schedule Changes</span>
        </button>
    </div>

</div>

@code {
    private int _currentUserId;
    private List<string> DaysOfWeek = new()
    {
        "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"
    };

    private List<string> DefaultTimeSlots = new()
    {
        "09:00 AM", "09:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM",
        "12:00 PM", "12:30 PM", "02:00 PM", "02:30 PM", "03:00 PM", "03:30 PM",
        "04:00 PM", "04:30 PM", "05:00 PM"
    };

    private HashSet<string> selectedDays = new();
    private HashSet<string> selectedTimeSlots = new();
    private List<VetTimeSlotDto> existingTimeSlots = new();
    private bool HasChanges => selectedDays.Any() && selectedTimeSlots.Any();
    private int currentVetId;
    private bool isScheduleExpanded = true;

    protected override async Task OnInitializedAsync()
    {
        _currentUserId = _PetVetAuthStateProvider.User.Id;
        // Set currentVetId to the user ID from auth state
        currentVetId = _currentUserId;
        
        await LoadExistingSchedule();
    }

    private async Task LoadExistingSchedule()
    {
        try
        {
            _AppState.ShowLoader("Loading Schedule");
            existingTimeSlots = await _AppointmentApi.GetVetTimeSlotsAsync(currentVetId);

            // Initialize selected days and time slots from existing schedule
            selectedDays = existingTimeSlots.Select(ts => ts.Day).Distinct().ToHashSet();
            selectedTimeSlots = existingTimeSlots.Select(ts => ts.Time).Distinct().ToHashSet();
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error loading schedule: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private void ToggleDay(string day)
    {
        if (selectedDays.Contains(day))
            selectedDays.Remove(day);
        else
            selectedDays.Add(day);
    }

    private void ToggleTimeSlot(string timeSlot)
    {
        if (selectedTimeSlots.Contains(timeSlot))
            selectedTimeSlots.Remove(timeSlot);
        else
            selectedTimeSlots.Add(timeSlot);
    }

    private async Task SaveSchedule()
    {
        try
        {
            _AppState.ShowLoader("Saving Schedule");

            var newTimeSlots = new List<VetTimeSlotDto>();

            // Create time slots for each selected day and time combination
            foreach (var day in selectedDays)
            {
                foreach (var time in selectedTimeSlots)
                {
                    // Check if the slot already exists and is booked
                    var existingSlot = existingTimeSlots.FirstOrDefault(ts => ts.Day == day && ts.Time == time);

                    // If slot exists and is booked, keep it as is
                    if (existingSlot?.IsBooked == true)
                    {
                        newTimeSlots.Add(existingSlot);
                    }
                    else
                    {
                        // Add new slot or update existing unbooked slot
                        newTimeSlots.Add(new VetTimeSlotDto
                            {
                                VetId = currentVetId,
                                Day = day,
                                Time = time,
                                IsBooked = false
                            });
                    }
                }
            }

            var response = await _AppointmentApi.SaveVetTimeSlotsAsync(currentVetId, newTimeSlots);

            if (response.IsSuccess)
            {
                await _JS.ToastrSuccess("Schedule saved successfully!");
                await LoadExistingSchedule(); // Reload the schedule to show updated data
            }
            else
            {
                await _JS.ToastrError("Failed to save schedule.");
            }
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error saving schedule: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private void ToggleScheduleView()
    {
        isScheduleExpanded = !isScheduleExpanded;
    }
} 