﻿/* Base container */
.VetProfile-CONTAINER {
    background: #f2f6f9;
    padding: 2rem;
    min-height: 100vh;
    width: 100%;
    max-width: 72rem;
    margin: 0 auto;
    font-family: 'Inter', sans-serif;
    overflow-x: hidden;
}

/* Header section */
.profile-header {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1rem;
    padding: 0;
    background-color: transparent;
    font-family: 'Acme', sans-serif;
}

.header-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2rem;
}

.profile-image-container {
    width: 7rem;
    height: 7rem;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: transform 0.3s ease;
}

    .profile-image-container:hover {
        transform: scale(1.05);
    }

.profile-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Stats section */
.stats-section {
    display: flex;
    justify-content: space-around;
    width: 100%;
    margin: 2rem 0;
    padding: 1.5rem;
    background: #157BAB;
    border-radius: 1.25rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    color: white;
    text-align: center;
    font-family: 'Acme', sans-serif;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    transition: transform 0.3s ease;
}

    .stat-item:hover {
        transform: translateY(-2px);
    }

.stat-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: #BFDBFE;
}

.stat-value {
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 1.4;
}

/* Profile content */
.profile-content {
    width: 100%;
    background-color: white;
    border-radius: 1.25rem;
    padding: 2rem;
    margin-top: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.vet-name {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1a1a1a;
    margin: 0;
    text-align: center;
    font-family: 'Acme', sans-serif;
}

.vet-specialization {
    font-size: 1rem;
    color: #6b7280;
    margin: 0.5rem 0 1rem;
    text-align: center;
    font-family: 'Acme', sans-serif;
}

.rating {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

    .rating i {
        color: #fbbf24;
        font-size: 1.25rem;
        filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
    }

/* Content sections */
.content-section {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

    .content-section h2 {
        font-size: 1.125rem;
        font-weight: 600;
        color: #1a1a1a;
        margin: 0 0 1rem;
        font-family: 'Acme', sans-serif;
    }

.info-list {
    display: grid;
    gap: 1rem;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background-color: #F9FAFB;
    border-radius: 0.75rem;
    transition: transform 0.2s;
    
}

    .info-item:hover {
        transform: translateX(4px);
    }

    .info-item i {
        color: #3B82F6;
        font-size: 1.125rem;
        width: 1.5rem;
        text-align: center;
    }

/* About section */
.about-section {
    background: #157BAB;
    border-radius: 1.25rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    color: white;
    text-align: justify;
}

    .about-section h2 {
        color: white;
        font-size: 1.25rem;
        margin-bottom: 1rem;
        font-family: 'Acme', sans-serif;
    }

.about-text {
    font-size: 0.875rem;
    line-height: 1.6;
    color: #E5E7EB;
    
}

/* Action buttons */
.schedule-btn,
.message-btn {
    width: 100%;
    background: #157BAB;
    color: white;
    font-size: 1rem;
    font-weight: 600;
    padding: 1rem;
    border-radius: 1rem;
    border: none;
    margin-top: 0.75rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    
}

    .schedule-btn:hover,
    .message-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.5);
    }

    .schedule-btn:active,
    .message-btn:active {
        transform: translateY(0);
    }

/* Document buttons */
.document-btn {
    width: 100%;
    background-color: #F3F4F6;
    color: #374151;
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.75rem;
    border-radius: 0.75rem;
    border: 1px solid #E5E7EB;
    margin-top: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    transition: all 0.2s;
    text-decoration: none;
    
}

    .document-btn:hover {
        background-color: #E5E7EB;
        transform: translateY(-1px);
    }

    .document-btn i {
        color: #3B82F6;
    }

/* Modal styles */
.date-picker,
.time-slots {
    margin-bottom: 1.5rem;
}

    .date-picker label,
    .time-slots label {
        font-size: 0.875rem;
        font-weight: 500;
        color: #374151;
        margin-bottom: 0.5rem;
        display: block;
    }

    .date-picker input {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #E5E7EB;
        border-radius: 0.75rem;
        font-size: 0.875rem;
        transition: border-color 0.2s;
    }

        .date-picker input:focus {
            outline: none;
            border-color: #3B82F6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

.slot-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(6rem, 1fr));
    gap: 0.75rem;
}

.time-slot {
    font-size: 0.875rem;
    padding: 0.75rem;
    border-radius: 0.75rem;
    background: #F3F4F6;
    border: 1px solid #E5E7EB;
    cursor: pointer;
    transition: all 0.2s;
}

    .time-slot:hover:not(.booked):not(.expired) {
        background: #3B82F6;
        color: white;
        transform: translateY(-1px);
    }

    .time-slot.selected {
        background: #3B82F6;
        color: white;
        font-weight: 500;
    }

/* Mobile Layout */
@media (max-width: 639px) {
    .VetProfile-CONTAINER {
        padding: 0.5rem;
        padding-bottom: 0;
        max-width: 28rem;
        background: #157BAB;
    }

    .profile-content {
        border-radius: 1.5rem 1.5rem 0 0;
        margin-top: auto;
        padding: 1.5rem;
    }

    .profile-header {
        color: #ffffff;
    }

    .stats-section {
        background: transparent;
        box-shadow: none;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.VetProfile-CONTAINER {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
