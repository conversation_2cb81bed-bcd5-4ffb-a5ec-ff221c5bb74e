﻿.ChatDetails-Message-List {
    height: calc(100vh - 118px);
    overflow-y: auto;
    border: 3px solid lightgray;
}

.Chat-Message-Input {
    border-bottom-left-radius: 1.25rem;
    border-bottom-right-radius: 1.25rem;
}

.ChatDetails-Header {
    background-color: #157BAB;
    color: #ffffff;
    border-top-left-radius: 1.25rem;
    border-top-right-radius: 1.25rem;
}

.ChatDetails-BACK-ICON {
    font-size: 1.5rem;
    color: #FFC107;
    cursor: pointer;
    transition: color 0.3s ease, transform 0.2s ease;
}

    .ChatDetails-BACK-ICON:hover {
        color: #e0a406;
        transform: scale(1.1);
    }

.ChatDetails-Heading {
    font-size: 1.125rem;
    font-family: 'Acme', sans-serif;
    font-weight: bold;
    cursor: default;
}

.Single-Message {
    border: 2px solid lightgray;
    padding: 5px 10px;
    border-radius: 25px;
    
}

.Me {
    border-bottom-right-radius: 0;
    background-color: #FFC107
}

.Other {
    border-bottom-left-radius: 0;
    background-color: #157BAB;
    color: #ffffff;
}

.msg-dt {
    font-size: 12px;
    
}

.Me .msg-dt {
   display: block;
   width: 100%;
   text-align: right;
}

.pet-profile-card {
    padding: 10px;
    border-radius: 8px;
    margin: 5px;
    max-width: 80%;
    cursor: pointer;
    
    transition: transform 0.2s;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

    .pet-profile-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    .pet-profile-card.Me {
        background-color: #FFC107;
        align-self: flex-end;
    }

    .pet-profile-card.Other {
        background-color: #157BAB;
        align-self: flex-start;
    }

.pet-profile-image {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

    .pet-profile-image.Me {
        border: 2px solid #157BAB;
    }

    .pet-profile-image.Other {
        border: 2px solid #FFC107;
    }

.pet-profile-placeholder {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 24px;
}

    .pet-profile-placeholder.Me {
        background-color: #157BAB;
    }

    .pet-profile-placeholder.Other {
        background-color: #FFC107;
    }

.pet-profile-deleted {
    padding: 10px;
    border-radius: 8px;
    margin: 5px;
    max-width: 80%;
    
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

    .pet-profile-deleted.Me {
        background-color: #c40606;
        align-self: flex-end;
    }

    .pet-profile-deleted.Other {
        background-color: #c40606;
        align-self: flex-start;
    }

.Send-Message-BTN {
    display: inline-block;
    background-color: #FFC107;
    border: none;
    border-radius: 0.625rem;
    padding: 0.625rem 1.25rem;
    color: #333;
    
    font-weight: bold;
    cursor: pointer;
    text-decoration: none;
}

@media (max-width: 768px) {
    .ChatDetails-HEADING {
        font-size: 1.25rem;
    }
}