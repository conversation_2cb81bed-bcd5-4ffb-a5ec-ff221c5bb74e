﻿using System.ComponentModel.DataAnnotations;

namespace PetVet.Shared.DTOs
{
    public class CategoryDto
    {
        public int Id { get; set; }
        [Required, MaxLength(50)]
        public string Name { get; set; } = string.Empty;
        // Flag to indicate if this category represents fast-breeding pets that should be grouped together
        public bool IsGroupCategory { get; set; }
    }
}
