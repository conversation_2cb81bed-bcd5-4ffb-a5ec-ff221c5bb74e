using Microsoft.Extensions.Logging;
using PetVet.MauiApp.Services;
using PetVet.Mobile.Models;
using PetVet.Mobile.Pages;
using System.Text;

namespace PetVet.Mobile.Services;

/// <summary>
/// Debug service to trace audio flow and identify issues
/// </summary>
public interface IAudioFlowDebugService
{
    Task<AudioFlowReport> DiagnoseAudioFlowAsync(string callId);
    void LogAudioEvent(string eventType, string details, object? data = null);
    Task<string> GetAudioFlowLogAsync();
    void ClearLog();
}

public class AudioFlowDebugService : IAudioFlowDebugService
{
    private readonly IChatCallService _chatCallService;
    private readonly IMediaCaptureService _mediaCaptureService;
    private readonly IAudioStreamingService _audioStreamingService;
    private readonly SignalRClientService _signalRService;
    private readonly ILogger<CallTestPage> _logger;
    
    private readonly StringBuilder _audioFlowLog = new();
    private readonly object _logLock = new();
    private int _eventCounter = 0;

    public AudioFlowDebugService(
        IChatCallService chatCallService,
        IMediaCaptureService mediaCaptureService,
        IAudioStreamingService audioStreamingService,
        SignalRClientService signalRService,
        ILogger<CallTestPage> logger)
    {
        _chatCallService = chatCallService;
        _mediaCaptureService = mediaCaptureService;
        _audioStreamingService = audioStreamingService;
        _signalRService = signalRService;
        _logger = logger;
    }

    public async Task<AudioFlowReport> DiagnoseAudioFlowAsync(string callId)
    {
        var report = new AudioFlowReport
        {
            CallId = callId,
            Timestamp = DateTime.UtcNow
        };

        try
        {
            LogAudioEvent("DIAGNOSIS_START", $"Starting audio flow diagnosis for call {callId}");

            // Check 1: Call Session Status
            var callSession = _chatCallService.GetActiveCalls().FirstOrDefault(c => c.CallId == callId);
            report.CallSessionExists = callSession != null;
            report.CallStatus = callSession?.Status.ToString() ?? "NOT_FOUND";
            report.ParticipantCount = callSession?.Participants.Count ?? 0;
            
            LogAudioEvent("CALL_SESSION_CHECK", $"Call exists: {report.CallSessionExists}, Status: {report.CallStatus}, Participants: {report.ParticipantCount}");

            // Check 2: Media Capture Status
            report.IsAudioCapturing = _mediaCaptureService.IsAudioCapturing;
            report.IsAudioMuted = _mediaCaptureService.IsMuted;
            report.AudioVolume = _mediaCaptureService.Volume;
            
            LogAudioEvent("MEDIA_CAPTURE_CHECK", $"Capturing: {report.IsAudioCapturing}, Muted: {report.IsAudioMuted}, Volume: {report.AudioVolume}");

            // Check 3: Audio Streaming Status
            report.IsAudioStreaming = _audioStreamingService.IsStreaming;
            report.IsStreamingMuted = _audioStreamingService.IsMuted;
            
            LogAudioEvent("AUDIO_STREAMING_CHECK", $"Streaming: {report.IsAudioStreaming}, Muted: {report.IsStreamingMuted}");

            // Check 4: SignalR Connection
            report.IsSignalRConnected = _signalRService.IsConnected;
            
            LogAudioEvent("SIGNALR_CHECK", $"Connected: {report.IsSignalRConnected}");

            // Check 5: Audio Quality Metrics
            var audioMetrics = _mediaCaptureService.GetAudioQualityMetrics();
            var streamingMetrics = _audioStreamingService.GetQualityMetrics(callId);
            
            report.AudioLevel = audioMetrics.AudioLevel;
            report.Latency = streamingMetrics.Latency;
            report.PacketLossRate = streamingMetrics.PacketLossRate;
            
            LogAudioEvent("QUALITY_METRICS_CHECK", $"Audio Level: {report.AudioLevel}, Latency: {report.Latency}ms, Packet Loss: {report.PacketLossRate * 100:F1}%");

            // Check 6: Participant Audio States
            if (callSession != null)
            {
                foreach (var participant in callSession.Participants)
                {
                    report.ParticipantAudioStates.Add(new ParticipantAudioState
                    {
                        UserId = participant.UserId,
                        UserName = participant.UserName,
                        IsAudioEnabled = participant.IsAudioEnabled,
                        IsMuted = participant.IsMuted
                    });
                    
                    LogAudioEvent("PARTICIPANT_CHECK", $"User {participant.UserName}: Audio={participant.IsAudioEnabled}, Muted={participant.IsMuted}");
                }
            }

            // Overall Assessment
            report.OverallStatus = DetermineOverallStatus(report);
            report.Issues = IdentifyIssues(report);
            report.Recommendations = GenerateRecommendations(report);

            LogAudioEvent("DIAGNOSIS_COMPLETE", $"Overall Status: {report.OverallStatus}, Issues: {report.Issues.Count}");

            return report;
        }
        catch (Exception ex)
        {
            report.OverallStatus = "ERROR";
            report.Issues.Add($"Diagnosis failed: {ex.Message}");
            LogAudioEvent("DIAGNOSIS_ERROR", ex.Message, ex);
            _logger.LogError(ex, "Error during audio flow diagnosis");
            return report;
        }
    }

    public void LogAudioEvent(string eventType, string details, object? data = null)
    {
        lock (_logLock)
        {
            var eventId = Interlocked.Increment(ref _eventCounter);
            var timestamp = DateTime.UtcNow.ToString("HH:mm:ss.fff");
            var logEntry = $"[{timestamp}] #{eventId:D4} {eventType}: {details}";
            
            if (data != null)
            {
                logEntry += $" | Data: {System.Text.Json.JsonSerializer.Serialize(data)}";
            }
            
            _audioFlowLog.AppendLine(logEntry);
            _logger.LogDebug("AUDIO_FLOW: {EventType} - {Details}", eventType, details);
        }
    }

    public async Task<string> GetAudioFlowLogAsync()
    {
        lock (_logLock)
        {
            return _audioFlowLog.ToString();
        }
    }

    public void ClearLog()
    {
        lock (_logLock)
        {
            _audioFlowLog.Clear();
            _eventCounter = 0;
        }
    }

    private string DetermineOverallStatus(AudioFlowReport report)
    {
        if (!report.CallSessionExists)
            return "NO_CALL";
        
        if (report.CallStatus != "Connected")
            return "CALL_NOT_CONNECTED";
        
        if (!report.IsSignalRConnected)
            return "SIGNALR_DISCONNECTED";
        
        if (!report.IsAudioCapturing && !report.IsAudioStreaming)
            return "AUDIO_NOT_STARTED";
        
        if (report.IsAudioMuted && report.IsStreamingMuted)
            return "AUDIO_MUTED";
        
        if (report.ParticipantCount < 2)
            return "INSUFFICIENT_PARTICIPANTS";
        
        if (report.PacketLossRate > 0.1)
            return "HIGH_PACKET_LOSS";
        
        if (report.Latency > 500)
            return "HIGH_LATENCY";
        
        return "HEALTHY";
    }

    private List<string> IdentifyIssues(AudioFlowReport report)
    {
        var issues = new List<string>();
        
        if (!report.CallSessionExists)
            issues.Add("Call session not found");
        
        if (report.CallStatus != "Connected")
            issues.Add($"Call status is {report.CallStatus}, expected Connected");
        
        if (!report.IsSignalRConnected)
            issues.Add("SignalR connection is down");
        
        if (!report.IsAudioCapturing)
            issues.Add("Audio capture is not active");
        
        if (!report.IsAudioStreaming)
            issues.Add("Audio streaming is not active");
        
        if (report.IsAudioMuted)
            issues.Add("Media capture is muted");
        
        if (report.IsStreamingMuted)
            issues.Add("Audio streaming is muted");
        
        if (report.AudioVolume <= 0)
            issues.Add("Audio volume is zero");
        
        if (report.ParticipantCount < 2)
            issues.Add($"Only {report.ParticipantCount} participants in call");
        
        if (report.AudioLevel <= 0)
            issues.Add("No audio input detected");
        
        if (report.PacketLossRate > 0.05)
            issues.Add($"High packet loss: {report.PacketLossRate * 100:F1}%");
        
        if (report.Latency > 200)
            issues.Add($"High latency: {report.Latency:F0}ms");
        
        return issues;
    }

    private List<string> GenerateRecommendations(AudioFlowReport report)
    {
        var recommendations = new List<string>();
        
        if (!report.CallSessionExists)
            recommendations.Add("Ensure call is properly initiated and accepted");
        
        if (!report.IsSignalRConnected)
            recommendations.Add("Check network connection and reconnect to SignalR");
        
        if (!report.IsAudioCapturing)
            recommendations.Add("Start audio capture with proper permissions");
        
        if (!report.IsAudioStreaming)
            recommendations.Add("Initialize audio streaming service");
        
        if (report.IsAudioMuted || report.IsStreamingMuted)
            recommendations.Add("Unmute audio capture and streaming");
        
        if (report.AudioVolume <= 0)
            recommendations.Add("Increase audio volume");
        
        if (report.ParticipantCount < 2)
            recommendations.Add("Ensure both caller and receiver are properly added as participants");
        
        if (report.AudioLevel <= 0)
            recommendations.Add("Check microphone permissions and hardware");
        
        if (report.PacketLossRate > 0.05)
            recommendations.Add("Reduce audio quality or check network stability");
        
        if (report.Latency > 200)
            recommendations.Add("Optimize network connection or reduce audio buffer size");
        
        return recommendations;
    }
}

public class AudioFlowReport
{
    public string CallId { get; set; } = "";
    public DateTime Timestamp { get; set; }
    
    // Call Session
    public bool CallSessionExists { get; set; }
    public string CallStatus { get; set; } = "";
    public int ParticipantCount { get; set; }
    
    // Media Capture
    public bool IsAudioCapturing { get; set; }
    public bool IsAudioMuted { get; set; }
    public double AudioVolume { get; set; }
    
    // Audio Streaming
    public bool IsAudioStreaming { get; set; }
    public bool IsStreamingMuted { get; set; }
    
    // Network
    public bool IsSignalRConnected { get; set; }
    
    // Quality Metrics
    public double AudioLevel { get; set; }
    public double Latency { get; set; }
    public double PacketLossRate { get; set; }
    
    // Participants
    public List<ParticipantAudioState> ParticipantAudioStates { get; set; } = new();
    
    // Assessment
    public string OverallStatus { get; set; } = "";
    public List<string> Issues { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    
    public string GetSummary()
    {
        var summary = $"Audio Flow Report for Call {CallId}\n";
        summary += $"Status: {OverallStatus}\n";
        summary += $"Call: {CallStatus} ({ParticipantCount} participants)\n";
        summary += $"Capture: {(IsAudioCapturing ? "Active" : "Inactive")} {(IsAudioMuted ? "(Muted)" : "")}\n";
        summary += $"Streaming: {(IsAudioStreaming ? "Active" : "Inactive")} {(IsStreamingMuted ? "(Muted)" : "")}\n";
        summary += $"Network: {(IsSignalRConnected ? "Connected" : "Disconnected")}\n";
        summary += $"Quality: Level={AudioLevel:F2}, Latency={Latency:F0}ms, Loss={PacketLossRate * 100:F1}%\n";
        
        if (Issues.Any())
        {
            summary += $"\nIssues ({Issues.Count}):\n";
            foreach (var issue in Issues)
            {
                summary += $"- {issue}\n";
            }
        }
        
        if (Recommendations.Any())
        {
            summary += $"\nRecommendations:\n";
            foreach (var rec in Recommendations.Take(3))
            {
                summary += $"- {rec}\n";
            }
        }
        
        return summary;
    }
}

public class ParticipantAudioState
{
    public int UserId { get; set; }
    public string UserName { get; set; } = "";
    public bool IsAudioEnabled { get; set; }
    public bool IsMuted { get; set; }
}
