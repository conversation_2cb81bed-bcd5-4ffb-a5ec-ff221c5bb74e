﻿@page "/auth/login"
@using PetVet.Shared.Components.Framework
@using System.Security.Claims
@layout EmptyLayout
@inject IAuthApi _AuthApi
@inject NavigationManager _NavigationManager
@inject IJSRuntime _JS
@inject PetVetAuthStateProvider _PetVetAuthStateProvider
@inject IAppState _AppState

<PageTitle>Login - PetVet Care System</PageTitle>

<!-- Enhanced Responsive Login Page -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 flex items-center justify-center p-2 sm:p-4 relative overflow-hidden">
    
    <!-- Background Decorative Elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <!-- Floating Shapes -->
        <div class="absolute top-5 left-5 w-16 h-16 sm:w-24 sm:h-24 lg:w-32 lg:h-32 bg-gradient-to-r from-primary-200/30 to-blue-200/30 rounded-full blur-xl animate-pulse"></div>
        <div class="absolute top-1/4 right-10 w-12 h-12 sm:w-16 sm:h-16 lg:w-24 lg:h-24 bg-gradient-to-r from-purple-200/30 to-pink-200/30 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div class="absolute bottom-10 left-1/4 w-20 h-20 sm:w-28 sm:h-28 lg:w-40 lg:h-40 bg-gradient-to-r from-teal-200/30 to-blue-200/30 rounded-full blur-xl animate-pulse delay-2000"></div>
        <div class="absolute bottom-1/4 right-1/3 w-14 h-14 sm:w-20 sm:h-20 lg:w-28 lg:h-28 bg-gradient-to-r from-indigo-200/30 to-purple-200/30 rounded-full blur-xl animate-pulse delay-3000"></div>
        
        <!-- Gradient Orbs -->
        <div class="absolute top-1/3 right-1/4 w-32 h-32 sm:w-48 sm:h-48 lg:w-64 lg:h-64 bg-gradient-to-r from-primary-300/20 to-blue-300/20 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-1/3 left-1/4 w-24 h-24 sm:w-36 sm:h-36 lg:w-48 lg:h-48 bg-gradient-to-r from-purple-300/20 to-pink-300/20 rounded-full blur-3xl animate-pulse delay-1500"></div>
    </div>

    <!-- Main Content Container -->
    <div class="relative z-10 w-full max-w-sm sm:max-w-md lg:max-w-lg mx-auto">
        
        <!-- Header Section -->
        <div class="text-center mb-4 sm:mb-6 lg:mb-8">
            <!-- Logo -->
            <div class="relative mb-3 sm:mb-4 lg:mb-6 logo-container">
                <div class="w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 bg-gradient-to-r from-primary-500 to-blue-600 rounded-full flex items-center justify-center mx-auto shadow-2xl transform hover:scale-105 transition-transform duration-300">
                    <i class="fas fa-paw text-white text-lg sm:text-xl lg:text-3xl"></i>
                </div>
                <!-- Logo Glow Effect -->
                <div class="absolute inset-0 w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 bg-gradient-to-r from-primary-400 to-blue-500 rounded-full mx-auto blur-xl opacity-50 -z-10"></div>
            </div>
            
            <!-- Welcome Text -->
            <h1 class="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-2 sm:mb-3 font-acme">
                Welcome Back
            </h1>
            <p class="text-gray-600 text-sm sm:text-base lg:text-lg font-lora">Sign in to your PetVet account</p>
        </div>

        <!-- Login Form Card -->
        <div class="bg-white/95 backdrop-blur-sm rounded-2xl sm:rounded-3xl shadow-2xl border border-white/20 overflow-hidden login-card">
            <!-- Card Header -->
            <div class="bg-gradient-to-r from-primary-500 to-blue-600 p-4 sm:p-5 lg:p-6 text-center">
                <h2 class="text-lg sm:text-xl font-semibold text-white font-acme">
                    <i class="fas fa-sign-in-alt mr-2"></i>
                    Account Login
                </h2>
            </div>
            
            <!-- Card Body -->
            <div class="p-4 sm:p-6 lg:p-8">
                <EditForm Model="_model" OnValidSubmit="LoginAsync">
                    <DataAnnotationsValidator />

                    <!-- Email Field -->
                    <div class="mb-4 sm:mb-5 lg:mb-6">
                        <label for="Email" class="block text-xs sm:text-sm font-medium text-gray-700 mb-2 sm:mb-3">
                            <i class="fas fa-envelope mr-2 text-primary-500"></i>
                            Email Address
                        </label>
                        <div class="relative group">
                            <div class="absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none">
                                <i class="fas fa-envelope text-gray-400 transition-colors text-sm"></i>
                            </div>
                            <InputText id="Email"
                                       class="w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-3 sm:py-4 border border-gray-300 rounded-xl sm:rounded-2xl focus:outline-none focus:ring-2 sm:focus:ring-3 focus:ring-primary-500/30 focus:border-primary-500 transition-all duration-300 bg-gray-50 focus:bg-white text-gray-800 placeholder-gray-500 form-input text-sm sm:text-base"
                                       @bind-Value="_model.Username"
                                       type="email"
                                       placeholder="Enter your email address" />
                        </div>
                        <ValidationMessage For="() => _model.Username" class="text-red-500 text-xs sm:text-sm mt-1 sm:mt-2 flex items-center">
                            <i class="fas fa-exclamation-circle mr-1"></i>
                        </ValidationMessage>
                    </div>

                    <!-- Password Field -->
                    <div class="mb-4 sm:mb-5 lg:mb-6">
                        <label for="Password" class="block text-xs sm:text-sm font-medium text-gray-700 mb-2 sm:mb-3">
                            <i class="fas fa-lock mr-2 text-primary-500"></i>
                            Password
                        </label>
                        <div class="relative group">
                            <div class="absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-gray-400 transition-colors text-sm"></i>
                            </div>
                            <InputText id="Password"
                                       class="w-full pl-10 sm:pl-12 pr-3 sm:pr-4 py-3 sm:py-4 border border-gray-300 rounded-xl sm:rounded-2xl focus:outline-none focus:ring-2 sm:focus:ring-3 focus:ring-primary-500/30 focus:border-primary-500 transition-all duration-300 bg-gray-50 focus:bg-white text-gray-800 placeholder-gray-500 form-input text-sm sm:text-base"
                                       @bind-Value="_model.Password"
                                       type="password"
                                       placeholder="Enter your password" />
                        </div>
                        <ValidationMessage For="() => _model.Password" class="text-red-500 text-xs sm:text-sm mt-1 sm:mt-2 flex items-center">
                            <i class="fas fa-exclamation-circle mr-1"></i>
                        </ValidationMessage>
                    </div>

                    <!-- Error Message -->
                    @if (_error != null)
                    {
                        <div class="mb-4 sm:mb-5 lg:mb-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-xl sm:rounded-2xl animate-shake error-message">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-exclamation-triangle text-red-500 mr-2 sm:mr-3 text-sm"></i>
                                    <span class="text-red-700 text-xs sm:text-sm font-medium">@_error</span>
                                </div>
                                <button type="button" @onclick="() => _error = null" 
                                        class="text-red-500 hover:text-red-700 transition-colors p-1 rounded-full hover:bg-red-100">
                                    <i class="fas fa-times text-sm"></i>
                                </button>
                            </div>
                        </div>
                    }

                    <!-- Remember Me & Forgot Password -->
                    <div class="flex items-center justify-between mb-4 sm:mb-5 lg:mb-6">
                        <label class="flex items-center cursor-pointer">
                            <input type="checkbox" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500 mr-2 w-4 h-4">
                            <span class="text-xs sm:text-sm text-gray-600">Remember me</span>
                        </label>
                        <a href="#" class="text-xs sm:text-sm text-primary-600 hover:text-primary-700 font-medium hover:underline transition-colors">
                            Forgot password?
                        </a>
                    </div>

                    <!-- Login Button -->
                    <div class="mb-4 sm:mb-5 lg:mb-6">
                        @if (!_isBusy)
                        {
                            <button type="submit" 
                                    class="w-full bg-gradient-to-r from-primary-500 to-blue-600 text-white py-3 sm:py-4 px-4 sm:px-6 rounded-xl sm:rounded-2xl font-semibold hover:from-primary-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl active:scale-95 flex items-center justify-center space-x-2 login-button text-sm sm:text-base">
                                <i class="fas fa-sign-in-alt"></i>
                                <span>Sign In to PetVet</span>
                            </button>
                        }
                        else
                        {
                            <button disabled type="button" 
                                    class="w-full bg-gray-400 text-white py-3 sm:py-4 px-4 sm:px-6 rounded-xl sm:rounded-2xl font-semibold cursor-not-allowed flex items-center justify-center space-x-2 text-sm sm:text-base">
                                <i class="fas fa-spinner fa-spin loading-spinner"></i>
                                <span>Signing In...</span>
                            </button>
                        }
                    </div>
                </EditForm>

                <!-- Divider -->
                <div class="relative my-4 sm:my-5 lg:my-6">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-xs sm:text-sm">
                        <span class="px-3 sm:px-4 bg-white text-gray-500">New to PetVet?</span>
                    </div>
                </div>

                <!-- Create Account Link -->
                <div class="text-center">
                    <a href="auth/register" 
                       class="inline-flex items-center space-x-2 text-primary-600 hover:text-primary-700 font-medium hover:underline transition-colors text-xs sm:text-sm">
                        <i class="fas fa-user-plus"></i>
                        <span>Create New Account</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-4 sm:mt-6 lg:mt-8">
            <div class="flex items-center justify-center space-x-3 sm:space-x-4 lg:space-x-6 mb-3 sm:mb-4">
                <div class="flex items-center space-x-1 sm:space-x-2 text-gray-500 footer-item">
                    <i class="fas fa-shield-alt text-green-500 text-xs sm:text-sm"></i>
                    <span class="text-xs sm:text-sm">Secure</span>
                </div>
                <div class="flex items-center space-x-1 sm:space-x-2 text-gray-500 footer-item">
                    <i class="fas fa-lock text-blue-500 text-xs sm:text-sm"></i>
                    <span class="text-xs sm:text-sm">Protected</span>
                </div>
                <div class="flex items-center space-x-1 sm:space-x-2 text-gray-500 footer-item">
                    <i class="fas fa-heart text-red-500 text-xs sm:text-sm"></i>
                    <span class="text-xs sm:text-sm">Pet Care</span>
                </div>
            </div>
            <p class="text-gray-400 text-xs sm:text-sm">&copy; 2025 PetVet Care System. All rights reserved.</p>
        </div>
    </div>
</div>

@code {
    private LoginDto _model = new();
    private bool _isBusy;
    private string? _error;

    [Inject]
    public ILocalStorageService? LocalStorage { get; set; }
    
    protected override void OnInitialized()
    {
        if (_PetVetAuthStateProvider.IsLoggedIn)
        {
            RedirectToHome(replace: true);
        }
    }

    private async Task LoginAsync()
    {
        _error = null;
        _AppState.ShowLoader("Signing In");
        _isBusy = true;

        try
        {
            var authResponse = await _AuthApi.LoginAsync(_model);

            if (authResponse.HasError && authResponse.ErrorMessage != null)
            {
                _error = authResponse.ErrorMessage;
                await _JS.ToastrError(_error);
                return;
            }

            var loggedInUser = authResponse.User;
            await _PetVetAuthStateProvider.SetLoginAsync(loggedInUser);
            await LocalStorage.SetValue(authResponse.User.Id.ToString(), ClaimTypes.NameIdentifier);
            await LocalStorage.SetValue(authResponse.User.Token, "auth_token");
            
            await _JS.ToastrSuccess("Welcome back to PetVet!");
            RedirectToHome();
        }
        catch (Exception ex)
        {
            await _JS.ToastrError("An error occurred while signing in. Please try again.");
            _error = ex.Message;
        }
        finally
        {
            _AppState.HideLoader();
            _isBusy = false;
        }
    }

    private void RedirectToHome(bool replace = false)
    {
        var redirectTo = _PetVetAuthStateProvider.User?.Role switch
        {
            nameof(UserRole.PetOwner) => "petOwner/home",
            nameof(UserRole.Vet) => "vet/home",
            nameof(UserRole.Admin) => "admin/home",
            _ => "/"
        };

        _NavigationManager.NavigateTo(redirectTo, replace);
    }
}