﻿using CommunityToolkit.Mvvm.Messaging;
using Microsoft.Maui.Platform;
using PetVet.MauiApp.Services;
using PetVet.MauiShared;
using PetVet.ServiceContracts.Enums;
using PetVet.ServiceContracts.Features.Conversation;
using PetVet.Shared.Interfaces;
using PetVet.Mobile.Models;
using PetVet.Mobile.Services;
using PetVet.Mobile.Components;
using System.Windows.Input;
using System.Timers;
using PetVet.Shared.Components.Framework;
using Microsoft.Extensions.Logging;
using PetVet.Mobile.Pages;
using System.Security.Claims;
namespace Platform.Client.Common.Features.Conversation;
public class ChatMessagesListingViewBase : CrudBaseMaui<IChatItem, ChatMessagesListingBusinessObject,
                                           ChatMessagesFilterViewModel, ChatMessagesFilterBusinessObject, IChatMessagesListingDataService,
                                           ChatMessageFormBusinessObject, ChatMessageFormViewModel, string, IChatMessageFormDataService>
{
    public ChatMessagesListingViewBase(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
    }
}

public partial class ChatMessagesListingView : ChatMessagesListingViewBase
{
    private readonly string conversationId;
    private readonly IChatCallService _chatCallService;
    private readonly IMediaCaptureService _mediaCaptureService;
    private string? _currentCallId;
    private System.Timers.Timer? _callTimer;
    private DateTime _callStartTime;
    private bool _isAudioMuted = false;
    private bool _isVideoEnabled = false;
    private CallSession? _activeCall;

    public ICommand BackCommand => new Command(async () => await Navigation.PopAsync());
    private string _friendName;

    public string FriendName
    {
        get { return _friendName; }
        set
        {
            _friendName = value;
            NotifyPropertyChanged();
        }
    }


    private ImageSource? _profileImage;

    public ImageSource? ProfileImage
    {
        get { return _profileImage; }
        set
        {
            _profileImage = value;
            NotifyPropertyChanged();
        }
    }

    public ILocalStorageService localStorage { get; set; }
    public string? currentUserId { get; set; }
    public int friendId { get; set; }
    public override int RowsPerPage { get => 5; }
    public override bool LoadItemsOnEveryAppear => false;
    public ChatMessagesListingView(IServiceScopeFactory scopeFactory, string conversationId,
        string friendName, int friendId, ImageSource? profileImage) : base(scopeFactory)
    {
        var scope = ScopeFactory.CreateScope();
        localStorage = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
        this.friendId = friendId;
        this.conversationId = conversationId;
        FriendName = friendName;
        ProfileImage = profileImage;

        // Get services from DI
        _chatCallService = scope.ServiceProvider.GetRequiredService<IChatCallService>();
        _mediaCaptureService = scope.ServiceProvider.GetRequiredService<IMediaCaptureService>();

        InitializeComponent();
        BindingContext = this;
        FilterViewModel.ConversationId = conversationId;

        // Subscribe to call events
        _chatCallService.CallInvitationReceived += OnCallInvitationReceived;
        _chatCallService.CallResponseReceived += OnCallResponseReceived;
        _chatCallService.CallStatusChanged += OnCallStatusChanged;

        InitializeCallTimer();
        InitializeCallService();
    }

    private async void InitializeCallService()
    {
        try
        {
            // Initialize the call service using proper authentication
            var initialized = await _chatCallService.InitializeAsync();

            if (initialized)
            {
                // Get current user ID for local use
                currentUserId = await localStorage.GetValue(ClaimTypes.NameIdentifier);
                System.Diagnostics.Debug.WriteLine($"Call service initialized successfully for user: {currentUserId}");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("Failed to initialize call service - user not authenticated");
            }
        }
        catch (Exception ex)
        {
            // Log error but don't crash
            System.Diagnostics.Debug.WriteLine($"Error initializing call service: {ex.Message}");
        }
    }

    protected override IChatItem[] ConvertListingBusinessItemsToListingViewModelItems(List<ChatMessagesListingBusinessObject?> listBusinessObjects)
    {
        var orderedItems = new List<IChatItem>();
        DateTime lastDate = DateTime.MinValue;
        foreach (var item in listBusinessObjects.OrderBy(x => x.Timestamp))
        {
            if (lastDate.Date != item.Timestamp.GetValueOrDefault().Date)
            {
                orderedItems.Add(new DateSeparatorItem(item.Timestamp.GetValueOrDefault()));
                lastDate = item.Timestamp.GetValueOrDefault();
            }
            orderedItems.Add(new ChatMessagesListingViewModel()
            {
                Id = item.Id,
                Content = item.Content,
                IsIncoming = item.IsIncoming,
                DeliveryStatus = item.DeliveryStatus,
                Timestamp = item.Timestamp
            });
        }
        return orderedItems.ToArray();
    }

    bool firstTime = true;
    protected override void OnAppearing()
    {
        SetStatusBarColor(Color.FromArgb("#157BAB"));

        Shell.SetTabBarIsVisible(this, false);
        var signalRClientService = ScopeFactory.CreateScope().ServiceProvider.GetRequiredService<SignalRClientService>();
        if (firstTime)
        {
            firstTime = false;
            WeakReferenceMessenger.Default.Register<ChatSyncItem>(this, (r, m) =>
            {
                if (m.SyncType == 1 || m.SyncType == 3)
                    _ = LoadItems(false);
            });
            WeakReferenceMessenger.Default.Register<ChatMessageStatus>(this, async (r, m) =>
            {
                var item = Items.First(x => x.Id == m.Id);
                var chatMessage = item as ChatMessagesListingViewModel;
                if (chatMessage != null && chatMessage.DeliveryStatus != m.DeliveryStatus)
                {
                    chatMessage.DeliveryStatus = m.DeliveryStatus;
                    chatMessage.Timestamp = m.Timestamp;
                    await signalRClientService.AcknowledgeMessageRead(chatMessage.Id, DateTime.UtcNow);
                    chatMessage.DeliveryStatus = DeliveryStatus.ReadByEndUser;
                }

            });
        }

        base.OnAppearing();

        if (Items.Count == 0)
            return;
        collection.ScrollTo(Items.Count - 1);
    }
    void SetStatusBarColor(Color color)
    {
#if ANDROID
        var window = Microsoft.Maui.ApplicationModel.Platform.CurrentActivity?.Window;

        if (window != null)
        {
            window.ClearFlags(Android.Views.WindowManagerFlags.TranslucentStatus);
            window.AddFlags(Android.Views.WindowManagerFlags.DrawsSystemBarBackgrounds);

            if (OperatingSystem.IsAndroidVersionAtLeast(35))
            {
                // Use the recommended alternative for Android 35.0 and later
                window.DecorView.WindowInsetsController?.SetSystemBarsAppearance(
                    (int)Android.Views.WindowInsetsControllerAppearance.LightStatusBars,
                    (int)Android.Views.WindowInsetsControllerAppearance.LightStatusBars);
            }
            else
            {
                // Use the existing method for Android versions below 35.0
                window.SetStatusBarColor(color.ToPlatform());
            }
        }
#endif
    }


    public override async Task OnAfterSaveAsync(string key)
    {
        SelectedItem.Content = null;
        await LoadItems(false);

    }

    public override void UpdateItems(PagedDataList<ChatMessagesListingBusinessObject> pagedItems,
        IChatItem[] viewModelItems)
    {
        foreach (var item in viewModelItems)
        {
            if (!Items.Any(x => x.Id == item.Id))
            {
                Items.Add(item);
            }

            //if(Items.Any(x => x.Id == item.Id && x.ContentHash != item.ContentHash && x is ChatMessagesListingViewModel msg))
            //{
            //    var existingItem = Items.FirstOrDefault(x => x.ContentHash == item.ContentHash && x is ChatMessagesListingViewModel);
            //    if (existingItem is ChatMessagesListingViewModel existingMsg)
            //    {
            //        existingMsg.DeliveryStatus = ((ChatMessagesListingViewModel)item).DeliveryStatus;
            //        existingMsg.Timestamp = ((ChatMessagesListingViewModel)item).Timestamp;
            //        existingMsg.Content = ((ChatMessagesListingViewModel)item).Content;
            //    }
            //}
        }
    }
    protected override async Task ItemsLoaded(IChatMessagesListingDataService service)
    {
        collection.ScrollTo(Items.Count - 1);
        var pendingUpdates = Items.Where(x => x is ChatMessagesListingViewModel message
        && message.DeliveryStatus != DeliveryStatus.ReadByEndUser
        && message.IsIncoming);
        if (pendingUpdates.Any())
        {
            var scope = ScopeFactory.CreateScope();
            var signalRClientService = scope.ServiceProvider.GetRequiredService<SignalRClientService>();
            foreach (ChatMessagesListingViewModel item in pendingUpdates)
            {
                try
                {
                    await signalRClientService.AcknowledgeMessageRead(item.Id, DateTime.UtcNow);
                    item.DeliveryStatus = DeliveryStatus.ReadByEndUser;
                    //todo: update db for read status?
                }
                catch (Exception ex)
                {
                    // Handle the exception, e.g., log it
                    Console.WriteLine($"Error acknowledging message read: {ex.Message}");
                }
            }
        }
    }

    protected override Task<ChatMessageFormViewModel> CreateSelectedItem()
    {
        return Task.FromResult(new ChatMessageFormViewModel()
        {
            ConversationId = conversationId
        });
    }

    private void CollectionView_SizeChanged(object sender, EventArgs e)
    {
        if (Items.Count == 0)
            return;

        collection.ScrollTo(Items.Count - 1);
    }

    private CancellationTokenSource _hideCts;

    void OnCollectionViewScrolled(object sender, ItemsViewScrolledEventArgs e)
    {
        // e.FirstVisibleItemIndex is the flat index in ItemsList
        var vm = BindingContext as ChatMessagesListingView;
        var idx = e.FirstVisibleItemIndex;
        if (vm == null || idx < 0 || idx >= vm.Items.Count)
            return;

        // pick the date to show
        string dateText;
        var item = vm.Items[idx];
        if (item is DateSeparatorItem ds)
            dateText = ds.Date.ToString("dd MMM yyyy");
        else if (item is ChatMessagesListingViewModel msg)
            dateText = msg.Timestamp.GetValueOrDefault().Date.ToString("dd MMM yyyy");
        else
            return;

        lblDateOverlay.Text = dateText;
        borderDateOverlay.IsVisible = true;

        // reset the hide timer
        _hideCts?.Cancel();
        _hideCts = new CancellationTokenSource();
        _ = HideOverlayAfterDelayAsync(_hideCts.Token);
    }

    async Task HideOverlayAfterDelayAsync(CancellationToken token)
    {
        try
        {
            await Task.Delay(1000, token);
            borderDateOverlay.IsVisible = false;
        }
        catch (TaskCanceledException) { }
    }

    #region Call Functionality

    private void InitializeCallTimer()
    {
        _callTimer = new System.Timers.Timer(1000);
        _callTimer.Elapsed += OnCallTimerElapsed;
    }

    private void OnCallTimerElapsed(object? sender, ElapsedEventArgs e)
    {
        if (_currentCallId != null)
        {
            var duration = DateTime.UtcNow - _callStartTime;
            var formattedDuration = $"{duration.Minutes:D2}:{duration.Seconds:D2}";

            MainThread.BeginInvokeOnMainThread(() =>
            {
                ActiveCallDurationLabel.Text = formattedDuration;
            });
        }
    }

    private async void OnAudioCallClicked(object sender, EventArgs e)
    {
        try
        {
            var scope = ScopeFactory.CreateScope();
            var chatCallService = scope.ServiceProvider.GetRequiredService<IChatCallService>();
            var mediaCaptureService = scope.ServiceProvider.GetRequiredService<IMediaCaptureService>();
            var signalRClientService = scope.ServiceProvider.GetRequiredService<SignalRClientService>();
            var callDebugService = scope.ServiceProvider.GetRequiredService<ICallDebugService>();
            var logger = scope.ServiceProvider.GetRequiredService<ILogger<CallTestPage>>();
            var localStorageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
            await Navigation.PushAsync(new NavigationPage(new CallTestPage(chatCallService,
                mediaCaptureService,
                signalRClientService,
                callDebugService,
                localStorageService,
                conversationId,
                friendId,
                FriendName,
                logger)));
            // await StartCall(CallType.Audio);
        }
        catch (Exception ex)
        {
            await DisplayAlert("Error", $"Failed to start audio call: {ex.Message}", "OK");
        }
    }

    private async void OnVideoCallClicked(object sender, EventArgs e)
    {
        try
        {
            await StartCall(CallType.AudioVideo);
        }
        catch (Exception ex)
        {
            await DisplayAlert("Error", $"Failed to start video call: {ex.Message}", "OK");
        }
    }



    private async Task StartCall(CallType callType)
    {
        try
        {
            var message = $"{callType} call invitation";
            var success = await _chatCallService.SendCallInvitationAsync(conversationId, friendId, callType, message);

            if (success)
            {
                _activeCall = _chatCallService.GetActiveCall(conversationId);
                if (_activeCall != null)
                {
                    _currentCallId = _activeCall.CallId;
                    _callStartTime = DateTime.UtcNow;
                    _isVideoEnabled = callType == CallType.AudioVideo || callType == CallType.Video;

                    ShowActiveCallOverlay(true);
                    UpdateCallButtons();
                    _callTimer?.Start();

                    // Update status label
                    CallStatusLabel.Text = "Calling...";
                    ActiveCallStatusLabel.Text = "Calling...";
                }
            }
            else
            {
                await DisplayAlert("Error", "Failed to start call", "OK");
            }
        }
        catch (Exception ex)
        {
            await DisplayAlert("Error", $"Failed to start call: {ex.Message}", "OK");
        }
    }

    private async void OnCallMuteClicked(object sender, EventArgs e)
    {
        if (string.IsNullOrEmpty(_currentCallId)) return;

        try
        {
            _isAudioMuted = !_isAudioMuted;
            var success = await _chatCallService.ToggleAudioAsync(_currentCallId, !_isAudioMuted);

            if (success)
            {
                UpdateCallButtons();
            }
        }
        catch (Exception ex)
        {
            await DisplayAlert("Error", $"Failed to toggle audio: {ex.Message}", "OK");
        }
    }

    private async void OnCallVideoClicked(object sender, EventArgs e)
    {
        if (string.IsNullOrEmpty(_currentCallId)) return;

        try
        {
            _isVideoEnabled = !_isVideoEnabled;
            var success = await _chatCallService.ToggleVideoAsync(_currentCallId, _isVideoEnabled);

            if (success)
            {
                UpdateCallButtons();
            }
        }
        catch (Exception ex)
        {
            await DisplayAlert("Error", $"Failed to toggle video: {ex.Message}", "OK");
        }
    }

    private async void OnCallEndClicked(object sender, EventArgs e)
    {
        if (string.IsNullOrEmpty(_currentCallId)) return;

        try
        {
            var success = await _chatCallService.EndCallAsync(_currentCallId);

            if (success)
            {
                EndCurrentCall();
            }
        }
        catch (Exception ex)
        {
            await DisplayAlert("Error", $"Failed to end call: {ex.Message}", "OK");
        }
    }

    private void ShowActiveCallOverlay(bool show)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            ActiveCallFrame.IsVisible = show;
        });
    }

    private void UpdateCallButtons()
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            CallMuteButton.Text = _isAudioMuted ? "🔇" : "🎤";
            CallMuteButton.BackgroundColor = _isAudioMuted ? Colors.Red : Color.FromArgb("#8FBFA8");

            CallVideoButton.Text = _isVideoEnabled ? "📹" : "📷";
            CallVideoButton.BackgroundColor = _isVideoEnabled ? Color.FromArgb("#8FBFA8") : Colors.Gray;
        });
    }

    private void EndCurrentCall()
    {
        _callTimer?.Stop();
        _currentCallId = null;
        _isAudioMuted = false;
        _isVideoEnabled = false;

        MainThread.BeginInvokeOnMainThread(() =>
        {
            ShowActiveCallOverlay(false);
            CallStatusLabel.Text = "Online";
            UpdateCallButtons();
        });
    }

    private void OnCallInvitationReceived(object? sender, CallInviteMessage e)
    {
        MainThread.BeginInvokeOnMainThread(async () =>
        {
            try
            {
                // Only show invitation if it's for this conversation
                if (e.ThreadId != conversationId)
                    return;

                // Create and show call invitation popup
                var scope = ScopeFactory.CreateScope();
                var logger = scope.ServiceProvider.GetRequiredService<ILogger<CallInvitationPopup>>();

                var callInvitationPopup = new CallInvitationPopup(e, _chatCallService, logger);
                await Navigation.PushModalAsync(callInvitationPopup);

                // Wait for user response
                var accepted = await callInvitationPopup.WaitForResponseAsync();

                if (accepted)
                {
                    // Call was accepted, update UI
                    _activeCall = _chatCallService.GetActiveCall(conversationId);
                    if (_activeCall != null)
                    {
                        _currentCallId = _activeCall.CallId;
                        _callStartTime = DateTime.UtcNow;
                        _isVideoEnabled = _activeCall.CallType == CallType.Video ||
                                         _activeCall.CallType == CallType.AudioVideo;

                        ShowActiveCallOverlay(true);
                        UpdateCallButtons();
                        _callTimer?.Start();

                        CallStatusLabel.Text = "In call";
                        ActiveCallStatusLabel.Text = "Call in progress";
                    }
                }
            }
            catch (Exception ex)
            {
                await DisplayAlert("Error", $"Failed to handle call invitation: {ex.Message}", "OK");
            }
        });
    }

    private void OnCallResponseReceived(object? sender, CallResponseMessage e)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            if (e.CallId == _currentCallId)
            {
                if (e.Accepted)
                {
                    CallStatusLabel.Text = "Connected";
                    ActiveCallStatusLabel.Text = "Call in progress";
                }
                else
                {
                    CallStatusLabel.Text = "Call declined";
                    EndCurrentCall();
                }
            }
        });
    }

    private void OnCallStatusChanged(object? sender, CallStatusMessage e)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            if (e.CallId == _currentCallId)
            {
                ActiveCallStatusLabel.Text = e.Status switch
                {
                    CallStatus.Initiating => "Initiating...",
                    CallStatus.Ringing => "Ringing...",
                    CallStatus.Connected => "Call in progress",
                    CallStatus.OnHold => "Call on hold",
                    CallStatus.Ended => "Call ended",
                    CallStatus.Failed => "Call failed",
                    _ => e.Status.ToString()
                };

                if (e.Status == CallStatus.Ended || e.Status == CallStatus.Failed)
                {
                    EndCurrentCall();
                }
                else if (e.Status == CallStatus.Connected)
                {
                    CallStatusLabel.Text = "In call";
                }
            }
        });
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();

        // Cleanup call resources
        _callTimer?.Stop();

        // Unsubscribe from events
        if (_chatCallService != null)
        {
            _chatCallService.CallInvitationReceived -= OnCallInvitationReceived;
            _chatCallService.CallResponseReceived -= OnCallResponseReceived;
            _chatCallService.CallStatusChanged -= OnCallStatusChanged;
        }
    }

    #endregion
}

public class ChatTemplateSelector : DataTemplateSelector
{
    public DataTemplate MessageTemplate { get; set; }
    public DataTemplate DateSeparatorTemplate { get; set; }

    protected override DataTemplate OnSelectTemplate(object item, BindableObject container)
    {
        return item switch
        {
            DateSeparatorItem => DateSeparatorTemplate,
            ChatMessagesListingViewModel => MessageTemplate,
            _ => MessageTemplate
        };
    }
}
// To fix the CS0311 error, the type 'IChatItem' must implement 'IEquatable<IChatItem>'.
// Adding the implementation to the 'IChatItem' interface and its relevant classes.



