﻿using PetVet.Shared.DTOs;
using Refit;

namespace PetVet.Shared.Components.Apis
{
    [Headers("Authorization: Bearer ")]
    public interface IUserApi
    {
        [Post("/api/users")]
        Task<PetVetApiResponse> SaveUserAsync(UserDto userDto);

        [Get("/api/users")]
        Task<UserDto[]> GetUsersAsync();

        [Get("/api/users/{id}")]
        Task<UserDto> GetUserAsync(int id);

        [Get("/api/users/vets")]
        Task<UserDto[]> GetVetUsersAsync();

        [Get("/api/users/vets/{id}")]
        Task<UserDto> GetVetUserAsync(int id);

        [Get("/api/users/vets/current/{userId}")]
        Task<UserDto> GetCurrentVetUserAsync(int userId);

        [Post("/api/users/vets/update")]
        Task<PetVetApiResponse> UpdateVetProfileAsync(UserDto userDto);

        [Get("/api/users/petOwners/current/{userId}")]
        Task<UserDto> GetCurrentPetOwnerUserAsync(int userId);

        [Post("/api/users/petOwners/update")]
        Task<PetVetApiResponse> UpdatePetOwnerProfileAsync(UserDto userDto);

        [Delete("/api/users/{id}")]
        Task<PetVetApiResponse> DeleteUserAsync(int id);

        [Patch("/api/users/{userId}/toggleStatus")]
        Task ToggleUserApprovedStatus(int userId);

        [Get("/api/admin/homeData")]
        Task<AdminHomeDataDto> GetAdminHomeDataAsync();

        [Get("/api/users/{currentUserId}/chatUsers")]
        Task<UserDto[]> GetChatUsersAsync(int currentUserId);
    }
}
