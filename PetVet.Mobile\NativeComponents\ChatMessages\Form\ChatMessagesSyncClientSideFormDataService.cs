﻿using PetVet.ServiceContracts.Features.Conversation;
using PetVet.Platform.Razor;
namespace Platform.Client.Common.Features.Conversation;
public class ChatMessagesSyncClientSideFormDataService : IChatMessagesSyncFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public ChatMessagesSyncClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}

	public async Task<string> SaveAsync(ChatMessagesSyncFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<string>($"api/ChatMessagesSyncsForm/Save", formBusinessObject);
	}
	
	public async Task<ChatMessagesSyncFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await _httpClient.GetFromJsonAsync<ChatMessagesSyncFormBusinessObject>($"api/ChatMessagesSyncsForm/GetItemById?id=" + id);
	}
}
