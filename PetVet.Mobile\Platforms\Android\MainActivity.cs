﻿using Android;
using Android.App;
using Android.Content;
using Android.Content.PM;
using Android.OS;
using Android.Runtime;
using Android.Util;
using AndroidX.Core.App;
using AndroidX.Core.Content;
using Firebase;
using Plugin.Firebase.CloudMessaging;
using Plugin.Maui.Audio;
using Color = Android.Graphics.Color;
namespace PetVet.Mobile
{
    [Activity(Theme = "@style/Maui.SplashTheme", MainLauncher = true,
        ConfigurationChanges = ConfigChanges.ScreenSize | ConfigChanges.Orientation |
                               ConfigChanges.UiMode | ConfigChanges.ScreenLayout |
                               ConfigChanges.SmallestScreenSize | ConfigChanges.Density)]
    public class MainActivity : MauiAppCompatActivity
    {
        protected override void OnCreate(Bundle? savedInstanceState)
        {

            base.OnCreate(savedInstanceState);
            Window?.SetSoftInputMode(Android.Views.SoftInput.AdjustResize);
            HandleIntent(Intent);
            CreateNotificationChannelIfNeeded();
            RequestPushNotificationsPermission();
            _ = CheckAndRequestPermissionsAsync();
            _ = CrossFirebaseCloudMessaging.Current.CheckIfValidAsync();
        }

        private static void HandleIntent(Intent intent)
        {
            FirebaseCloudMessagingImplementation.OnNewIntent(intent);
            //FirebaseDynamicLinksImplementation.HandleDynamicLinkAsync(intent);
        }

        public async Task<bool> CheckAndRequestPermissionsAsync()
        {
            var cameraStatus = await Permissions.CheckStatusAsync<Permissions.Camera>();
            if (cameraStatus != PermissionStatus.Granted)
                cameraStatus = await Permissions.RequestAsync<Permissions.Camera>();

            var micStatus = await Permissions.CheckStatusAsync<Permissions.Microphone>();
            if (micStatus != PermissionStatus.Granted)
                micStatus = await Permissions.RequestAsync<Permissions.Microphone>();

            var speechStatus = await Permissions.CheckStatusAsync<Permissions.Speech>();
            if (speechStatus != PermissionStatus.Granted)
                speechStatus = await Permissions.RequestAsync<Permissions.Speech>();

            return cameraStatus == PermissionStatus.Granted && micStatus == PermissionStatus.Granted && speechStatus == PermissionStatus.Granted;
        }

        private void RequestPushNotificationsPermission()
        {
            if (Build.VERSION.SdkInt >= BuildVersionCodes.Tiramisu && ContextCompat.CheckSelfPermission(this, Manifest.Permission.PostNotifications) != Permission.Granted)
            {
                ActivityCompat.RequestPermissions(this, new[] { Manifest.Permission.PostNotifications }, 0); ;
            }
        }

        private void CreateNotificationChannelIfNeeded()
        {
            if (Build.VERSION.SdkInt >= BuildVersionCodes.O)
            {
                CreateNotificationChannel();
            }
        }

        private void CreateNotificationChannel()
        {
            var channelId = $"{PackageName}.general";
            var notificationManager = (NotificationManager)GetSystemService(NotificationService);
            var channel = new NotificationChannel(channelId, "General", NotificationImportance.Default);
            notificationManager.CreateNotificationChannel(channel);
            FirebaseCloudMessagingImplementation.ChannelId = channelId;
            FirebaseCloudMessagingImplementation.SmallIconRef = Resource.Drawable.mtrl_switch_thumb_pressed_unchecked;
        }

        public override void OnRequestPermissionsResult(int requestCode, string[] permissions, [GeneratedEnum] Permission[] grantResults)
        {
            Microsoft.Maui.ApplicationModel.Platform.OnRequestPermissionsResult(requestCode, permissions, grantResults);
            base.OnRequestPermissionsResult(requestCode, permissions, grantResults);
        }


        protected override void OnNewIntent(Intent intent)
        {
            base.OnNewIntent(intent);
            HandleIntent(intent);
        }
    }
}
