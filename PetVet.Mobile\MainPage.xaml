﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="PetVet.Mobile.MainPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:PetVet.Mobile"
    NavigationPage.HasNavigationBar="False">

    <BlazorWebView x:Name="blazorWebView" HostPage="wwwroot/index.html">
        <BlazorWebView.RootComponents>
            <RootComponent ComponentType="{x:Type local:Components.Routes}" Selector="#app" />
        </BlazorWebView.RootComponents>
    </BlazorWebView>

</ContentPage>
