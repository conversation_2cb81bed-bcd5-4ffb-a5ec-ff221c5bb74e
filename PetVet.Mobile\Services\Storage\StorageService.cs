﻿using PetVet.Shared.Components.Framework;
using PetVet.Shared.IStorage;
using System.Runtime.CompilerServices;

namespace PetVet.Mobile.Services.Storage
{
    public class StorageService : IStorageService
    {
        public ValueTask<string?> GetItem(string key) =>
           ValueTask.FromResult(Preferences.Default.Get<string?>(key, null));

        public ValueTask RemoveItem(string key)
        {
            Preferences.Default.Remove(key);
            return ValueTask.CompletedTask;
        }

        public ValueTask SetItem(string key, string value)
        {
            Preferences.Default.Set<string>(key, value);
            return ValueTask.CompletedTask;
        }
    }
    public class LocalStorageService : ILocalStorageService
    {

        public async Task<string?> GetValue([CallerMemberName] string memberName = "")
        {
            var value = await SecureStorage.Default.GetAsync($"__{memberName}__");
            return value;
        }

        public async Task SetValue(string? value, [CallerMemberName] string memberName = "")
        {
            if (string.IsNullOrEmpty(value))
            {
                SecureStorage.Default.Remove($"__{memberName}__");
            }
            else
            {
                await SecureStorage.Default.SetAsync($"__{memberName}__", value);
            }
        }

        public Task RemoveValue([CallerMemberName] string memberName = "")
        {
            SecureStorage.Default.Remove($"__{memberName}__");
            return Task.CompletedTask;
        }


    }

}
