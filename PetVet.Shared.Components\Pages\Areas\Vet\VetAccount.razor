﻿@page "/vet/account"
@inject IUserApi _UserApi
@inject IAuthApi _AuthApi
@inject IEducationApi _EducationApi
@inject IAppointmentApi _AppointmentApi
@inject IAppState _AppState
@inject IJSRuntime _JS
@inject PetVetAuthStateProvider _PetVetAuthStateProvider
@inject NavigationManager _NavigationManager

<PageTitle>My Account</PageTitle>

<!-- Modern Vet Account Page -->
<div class="px-4 py-6 space-y-6">

    <!-- Header Section -->
    <div class="text-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-2">My Account</h1>
        <p class="text-gray-600">Manage your veterinary profile and account settings</p>
    </div>

    <!-- Profile Header Card -->
    <div class="bg-gradient-to-r from-blue-500 via-purple-600 to-indigo-600 rounded-3xl p-6 text-white relative overflow-hidden">

    @if (isLoading)
    {
        <!-- Loading State -->
        <div class="relative z-10">
            <div class="flex items-center justify-center py-8">
                <div class="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
                <span class="text-lg font-medium">Loading your profile...</span>
            </div>
        </div>
    }
    else
    {
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full"></div>
            <div class="absolute bottom-4 left-4 w-20 h-20 bg-white rounded-full"></div>
        </div>

        <div class="relative z-10">
            <div class="flex items-center gap-4 mb-4">
                <!-- Profile Image -->
                <div class="relative">
                    @if (!string.IsNullOrEmpty(vetProfile.ImageUrl))
                    {
                        <img src="@GetImageSrc(vetProfile.ImageUrl)"
                             alt="Profile Image"
                             class="w-20 h-20 rounded-full object-cover border-4 border-white/30"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
                        <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center border-4 border-white/30" style="display: none;">
                            <i class="fas fa-user-md text-white text-2xl"></i>
                        </div>
                    }
                    else
                    {
                        <div class="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center border-4 border-white/30">
                            <i class="fas fa-user-md text-white text-2xl"></i>
                        </div>
                    }

                    <!-- Camera Icon for Upload -->
                    <label class="absolute bottom-0 right-0 w-8 h-8 bg-white rounded-full flex items-center justify-center cursor-pointer shadow-lg hover:shadow-xl transition-all transform hover:scale-105">
                        <i class="fas fa-camera text-blue-600 text-sm"></i>
                        <InputFile OnChange="HandleImageUpload" accept="image/*" class="hidden" />
                    </label>
                </div>

                <!-- Profile Info -->
                <div class="flex-1">
                    <h1 class="text-2xl font-bold mb-1">Dr. @vetProfile.Name</h1>
                    <p class="text-white/90 text-sm mb-2">@vetProfile.Email</p>
                    <p class="text-white/80 text-sm">@vetProfile.Specialization • @vetProfile.ClinicName</p>
                    @if (!string.IsNullOrEmpty(vetProfile.ImageUrl))
                    {
                        <button @onclick="HandleImageDeleteConfirmation"
                                class="text-white/80 text-xs hover:text-white transition-colors mt-2">
                            <i class="fas fa-trash mr-1"></i>Remove Photo
                        </button>
                    }
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="flex gap-4">
                <div class="bg-white/20 rounded-2xl p-3 flex-1 text-center">
                    <div class="text-xl font-bold">@totalAppointments</div>
                    <div class="text-xs text-white/80">Appointments</div>
                </div>
                <div class="bg-white/20 rounded-2xl p-3 flex-1 text-center">
                    <div class="text-xl font-bold">@totalPatients</div>
                    <div class="text-xs text-white/80">Active Patients</div>
                </div>
                <div class="bg-white/20 rounded-2xl p-3 flex-1 text-center">
                    <div class="text-xl font-bold">@vetProfile.YearsOfExperience</div>
                    <div class="text-xs text-white/80">Years Experience</div>
                </div>
            </div>
        </div>
    } <!-- End of loading check -->
    </div>

    <!-- Tab Navigation -->
    <div class="flex bg-gray-100 rounded-2xl p-1 mb-6">
        <button class="flex-1 py-3 px-4 rounded-xl text-sm font-medium transition-all @(activeTab == "profile" ? "bg-white text-gray-800 shadow-sm" : "text-gray-500 hover:text-gray-700")"
                @onclick='() => activeTab = "profile"'>
            <i class="fas fa-user-md mr-2"></i>Profile Information
        </button>
        <button class="flex-1 py-3 px-4 rounded-xl text-sm font-medium transition-all @(activeTab == "password" ? "bg-white text-gray-800 shadow-sm" : "text-gray-500 hover:text-gray-700")"
                @onclick='() => activeTab = "password"'>
            <i class="fas fa-key mr-2"></i>Security Settings
        </button>
    </div>

    <!-- Tab Content -->
    @if (!isLoading && vetProfile != null)
    {
        @if (activeTab == "profile")
        {
            <!-- Profile Information Card -->
            <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                <div class="flex items-center gap-3 mb-6">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-user-md text-blue-600"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-gray-800">Professional Information</h2>
                        <p class="text-gray-500 text-sm">Update your veterinary profile details</p>
                    </div>
                </div>

                <EditForm Model="vetProfile" OnValidSubmit="UpdateProfileAsync">
                    <DataAnnotationsValidator />
                    <ValidationSummary class="text-red-700" />
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                            <InputText @bind-Value="vetProfile.Name"
                                       class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                       placeholder="Enter your full name" />
                            <ValidationMessage For="() => vetProfile.Name" class="text-red-500 text-sm mt-1" />
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                            <InputText @bind-Value="vetProfile.Email"
                                       class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                       placeholder="Enter your email" />
                            <ValidationMessage For="() => vetProfile.Email" class="text-red-500 text-sm mt-1" />
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                            <InputText @bind-Value="vetProfile.Phone"
                                       class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                       placeholder="Enter your phone number" />
                            <ValidationMessage For="() => vetProfile.Phone" class="text-red-500 text-sm mt-1" />
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                            <InputText @bind-Value="vetProfile.Address"
                                       class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                       placeholder="Enter your address" />
                            <ValidationMessage For="() => vetProfile.Address" class="text-red-500 text-sm mt-1" />
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Clinic Name</label>
                            <InputText @bind-Value="vetProfile.ClinicName"
                                       class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                       placeholder="Enter clinic name" />
                            <ValidationMessage For="() => vetProfile.ClinicName" class="text-red-500 text-sm mt-1" />
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Specialization</label>
                            <InputText @bind-Value="vetProfile.Specialization"
                                       class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                       placeholder="Enter your specialization" />
                            <ValidationMessage For="() => vetProfile.Specialization" class="text-red-500 text-sm mt-1" />
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Years of Experience</label>
                            <InputNumber @bind-Value="vetProfile.YearsOfExperience"
                                         class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                         placeholder="Enter years of experience" />
                            <ValidationMessage For="() => vetProfile.YearsOfExperience" class="text-red-500 text-sm mt-1" />
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Education</label>
                            <InputSelect @bind-Value="vetProfile.EducationId"
                                         class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all">
                                <option value="0">Select Education</option>
                                @foreach (var education in educations)
                                {
                                    <option value="@education.Id">@education.Name</option>
                                }
                            </InputSelect>
                            <ValidationMessage For="() => vetProfile.EducationId" class="text-red-500 text-sm mt-1" />
                        </div>
                    </div>

                    <div class="mt-6">
                        <button type="submit"
                                class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-6 rounded-xl font-medium hover:from-blue-600 hover:to-purple-700 transition-all transform hover:scale-105 shadow-lg">
                            <i class="fas fa-save mr-2"></i>
                            <span>Save Changes</span>
                        </button>
                    </div>
                </EditForm>
            </div>
        }
        else if (activeTab == "password")
        {
            <!-- Security Settings Card -->
            <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                <div class="flex items-center gap-3 mb-6">
                    <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-key text-red-600"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-gray-800">Security Settings</h2>
                        <p class="text-gray-500 text-sm">Update your password and security preferences</p>
                    </div>
                </div>

                <EditForm Model="passwordModel" OnValidSubmit="ChangePasswordAsync">
                    <DataAnnotationsValidator />

                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
                            <InputText type="password"
                                       @bind-Value="passwordModel.CurrentPassword"
                                       class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all"
                                       placeholder="Enter current password" />
                            <ValidationMessage For="() => passwordModel.CurrentPassword" class="text-red-500 text-sm mt-1" />
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">New Password</label>
                            <InputText type="password"
                                       @bind-Value="passwordModel.NewPassword"
                                       class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all"
                                       placeholder="Enter new password" />
                            <ValidationMessage For="() => passwordModel.NewPassword" class="text-red-500 text-sm mt-1" />
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
                            <InputText type="password"
                                       @bind-Value="passwordModel.ConfirmPassword"
                                       class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent transition-all"
                                       placeholder="Confirm new password" />
                            <ValidationMessage For="() => passwordModel.ConfirmPassword" class="text-red-500 text-sm mt-1" />
                        </div>
                    </div>

                    @if (passwordError != null)
                    {
                        <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-xl">
                            <div class="flex items-center gap-2 text-red-700">
                                <i class="fas fa-exclamation-circle"></i>
                                <span class="text-sm">@passwordError</span>
                            </div>
                        </div>
                    }

                    <div class="mt-6">
                        <button type="submit"
                                disabled="@isChangingPassword"
                                class="w-full bg-gradient-to-r from-red-500 to-pink-600 text-white py-3 px-6 rounded-xl font-medium hover:from-red-600 hover:to-pink-700 transition-all transform hover:scale-105 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none">
                            @if (isChangingPassword)
                            {
                                <div class="flex items-center justify-center gap-2">
                                    <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                    <span>Changing Password...</span>
                                </div>
                            }
                            else
                            {
                                <div class="flex items-center justify-center gap-2">
                                    <i class="fas fa-key"></i>
                                    <span>Change Password</span>
                                </div>
                            }
                        </button>
                    </div>
                </EditForm>
            </div>
        }
    }
    else
    {
        <!-- Loading State for Tab Content -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div class="flex items-center justify-center py-12">
                <div class="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mr-3"></div>
                <span class="text-lg font-medium text-gray-600">
                    @if (isLoading)
                    {
                        <text>Loading profile data...</text>
                    }
                    else if (vetProfile == null)
                    {
                        <text>Profile data not available. Please refresh the page.</text>
                    }
                </span>
            </div>

            @if (!isLoading && vetProfile == null)
            {
                <div class="mt-4 text-center">
                    <button @onclick="async () => await LoadProfileDataAsync()"
                            class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                        <i class="fas fa-refresh mr-2"></i>Retry Loading
                    </button>
                </div>
            }
        </div>
    }

</div>

<!-- Modal for Image Deletion -->
<BsModal OnModalConfirmation="ConfirmDeleteImage"
         ButtonText="Remove"
         Title="Are you sure you want to remove this profile image?">
</BsModal>

@code {
    private string activeTab = "profile";
    private UserDto vetProfile = new();
    private EducationDto[] educations = Array.Empty<EducationDto>();
    private bool isLoading = true;
    private string _error;

    private ChangePasswordDto passwordModel = new();
    private string passwordError;
    private bool isChangingPassword = false;
    private string originalProfileImage;

    // Statistics
    private int totalAppointments = 0;
    private int totalPatients = 0;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            _AppState.ShowLoader("Loading Profile");

            // Check if user is logged in
            if (!_PetVetAuthStateProvider.IsLoggedIn)
            {
                await _JS.ToastrError("Please log in to view your profile");
                _NavigationManager.NavigateTo("/auth/login");
                return;
            }

            // Check for tab query parameter
            var uri = new Uri(_NavigationManager.Uri);
            if (uri.Query.Contains("tab=security"))
            {
                activeTab = "password";
            }

            await LoadProfileDataAsync();
            educations = await _EducationApi.GetEducationListAsync();
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error loading profile: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
            isLoading = false;
        }
    }

    private async Task LoadProfileDataAsync()
    {
        try
        {
            int userId = _PetVetAuthStateProvider.User.Id;
            Console.WriteLine($"Loading profile for user ID: {userId}");

            vetProfile = await _UserApi.GetCurrentVetUserAsync(userId);

            if (vetProfile == null)
            {
                Console.WriteLine("VetProfile is null!");
                await _JS.ToastrError("Failed to load profile data");
                return;
            }

            Console.WriteLine($"Loaded profile: {vetProfile.Name}, Email: {vetProfile.Email}");
            passwordModel.UserId = userId;
            originalProfileImage = vetProfile.ImageUrl;

            // Load statistics
            await LoadStatisticsAsync(userId);

            // Force UI update
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in LoadProfileDataAsync: {ex.Message}");
            await _JS.ToastrError($"Error loading profile data: {ex.Message}");
        }
    }

    private async Task LoadStatisticsAsync(int vetId)
    {
        try
        {
            // Load all appointments for this vet
            var allAppointments = await _AppointmentApi.GetVetAppointmentsAsync(vetId);

            if (allAppointments?.Any() == true)
            {
                // Calculate total appointments
                totalAppointments = allAppointments.Count;

                // Calculate total unique patients (unique pet owner IDs)
                totalPatients = allAppointments
                    .Select(a => a.PetOwnerId)
                    .Distinct()
                    .Count();
            }
            else
            {
                totalAppointments = 0;
                totalPatients = 0;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading statistics: {ex.Message}");
            totalAppointments = 0;
            totalPatients = 0;
        }
    }

    private async Task UpdateProfileAsync()
    {
        try
        {
            _AppState.ShowLoader("Updating Profile");
            vetProfile.Role = nameof(UserRole.Vet);
            
            if (vetProfile.ImageUrl != null && vetProfile.ImageUrl.StartsWith("data:"))
            {
                var base64Index = vetProfile.ImageUrl.IndexOf("base64,");
                if (base64Index > 0)
                {
                    vetProfile.ImageUrl = vetProfile.ImageUrl.Substring(base64Index + 7);
                }
            }

            var response = await _UserApi.UpdateVetProfileAsync(vetProfile);

            if (response.IsSuccess)
            {
                // Reload the profile to get the updated data from server
                await LoadProfileDataAsync();
                await _JS.ToastrSuccess("Profile updated successfully");
            }
            else
            {
                await _JS.ToastrError($"Failed to update profile: {response.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error updating profile: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private async Task ChangePasswordAsync()
    {
        try
        {
            passwordError = null;
            isChangingPassword = true;
            passwordModel.UserId = _PetVetAuthStateProvider.User.Id;

            var response = await _AuthApi.ChangePasswordAsync(passwordModel);

            if (response.IsSuccess)
            {
                await _JS.ToastrSuccess("Password changed successfully");
                passwordModel = new() { UserId = _PetVetAuthStateProvider.User.Id };
            }
            else
            {
                passwordError = response.ErrorMessage;
            }
        }
        catch (Exception ex)
        {
            passwordError = $"Error changing password: {ex.Message}";
        }
        finally
        {
            isChangingPassword = false;
        }
    }

    private async Task HandleImageUpload(InputFileChangeEventArgs e)
    {
        _AppState.ShowLoader("Uploading Image");

        try
        {
            var file = e.File;
            var allowedExtensions = new[] { ".png", ".jpg", ".jpeg" };
            var fileExtension = Path.GetExtension(file.Name).ToLower();

            if (!allowedExtensions.Contains(fileExtension))
            {
                _error = "Please upload a JPEG or PNG image.";
                await _JS.ToastrError(_error);
                return;
            }

            // Check file size (max 5MB)
            if (file.Size > 5 * 1024 * 1024)
            {
                await _JS.ToastrError("Image size must be less than 5MB.");
                return;
            }

            var stream = file.OpenReadStream(5 * 1024 * 1024);
            var memoryStream = new MemoryStream();
            await stream.CopyToAsync(memoryStream);

            var base64String = Convert.ToBase64String(memoryStream.ToArray());

            // Validate that we have a valid base64 string
            if (string.IsNullOrEmpty(base64String))
            {
                await _JS.ToastrError("Failed to process the image. Please try again.");
                return;
            }

            vetProfile.ImageUrl = base64String;
            await _JS.ToastrSuccess("Image uploaded successfully");
            StateHasChanged(); // Force UI refresh
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error uploading image: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private async Task HandleImageDeleteConfirmation()
    {
        await _JS.InvokeVoidAsync("ShowConfirmationModal");
    }

    private async Task ConfirmDeleteImage()
    {
        try
        {
            _AppState.ShowLoader("Removing Image");
            vetProfile.ImageUrl = null;
            vetProfile.Role = nameof(UserRole.Vet); 

            var response = await _UserApi.UpdateVetProfileAsync(vetProfile);

            if (response.IsSuccess)
            {
                originalProfileImage = null;
                await _JS.ToastrSuccess("Profile image removed successfully");
                StateHasChanged(); // Force UI refresh
            }
            else
            {
                vetProfile.ImageUrl = originalProfileImage;
                await _JS.ToastrError($"Failed to remove profile image: {response.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            vetProfile.ImageUrl = originalProfileImage;
            await _JS.ToastrError($"Error removing profile image: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private string GetImageSrc(string? imageUrl)
    {
        if (string.IsNullOrEmpty(imageUrl))
            return string.Empty;

        // Debug: Log the image URL format
        Console.WriteLine($"Image URL: {imageUrl?.Substring(0, Math.Min(50, imageUrl?.Length ?? 0))}...");

        // If it's already a data URL, return as is
        if (imageUrl.StartsWith("data:"))
            return imageUrl;

        // If it's a regular URL (http/https), return as is
        if (imageUrl.StartsWith("http"))
            return imageUrl;

        // Otherwise, treat it as base64 and create data URL
        return $"data:image/jpeg;base64,{imageUrl}";
    }
}