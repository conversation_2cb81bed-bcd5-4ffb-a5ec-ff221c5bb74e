﻿using System.ComponentModel.DataAnnotations;

namespace PetVet.Shared.DTOs
{
    public class VetDto 
    {
        public int Id { get; set; }

        [Required, MaxLength(20)]
        public string Name { get; set; } = string.Empty;

        [Required, EmailAddress, DataType(DataType.EmailAddress)]
        public string Email { get; set; } = string.Empty;

        [Required, StringLength(15, MinimumLength = 10)]
        public string Phone { get; set; } = string.Empty;

        [Required, MaxLength(250)]
        public string Password { get; set; } = string.Empty;

        [MaxLength(15)]
        public string Role { get; set; } = string.Empty;

        public bool IsApproved { get; set; }

        [Required, MaxLength(250)]
        public string Address { get; set; } = string.Empty;

        // Vet-specific fields (nullable for PetOwner)
        [Required]
        public string? ClinicName { get; set; }

        [Required]
        public string? Specialization { get; set; }

        [Required, Range(1, 100, ErrorMessage = "Years of experience must be between 1 and 100")]
        public int YearsOfExperience { get; set; }

        [Required]
        public string? LicenceDocumentUrl { get; set; }

        public bool? IsOnline { get; set; }

        public bool IsSelected { get; set; }

        public string? ImageUrl { get; set; }

        public double? Rating { get; set; }

        public string? CertificationsUrl { get; set; }

        public List<WorkingHourDto>? WorkingHours { get; set; } = new List<WorkingHourDto>();

        [Required, Range(1, int.MaxValue, ErrorMessage = "Please select a valid education.")]
        public int EducationId { get; set; }

        public string? EducationName { get; set; }
    }
}
