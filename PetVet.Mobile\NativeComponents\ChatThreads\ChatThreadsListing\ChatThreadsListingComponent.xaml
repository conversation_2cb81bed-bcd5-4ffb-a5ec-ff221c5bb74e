﻿<?xml version="1.0" encoding="utf-8" ?>
<local:ChatThreadsListingViewBase
    x:Class="Platform.Client.Common.Features.Conversation.ChatThreadsListingView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:local="clr-namespace:Platform.Client.Common.Features.Conversation"
    Title="Chats"
    x:DataType="local:ChatThreadsListingView"
    BackgroundColor="#3E97DA"
    NavigationPage.HasBackButton="False"
    NavigationPage.IconColor="White">
    <NavigationPage.TitleView>
        <Grid ColumnDefinitions="36,*">
            <Grid.GestureRecognizers>
                <TapGestureRecognizer Command="{Binding BackCommand}" />
            </Grid.GestureRecognizers>
            <Image
                HorizontalOptions="Start"
                MaximumHeightRequest="24"
                MaximumWidthRequest="24"
                Source="left.svg" />
            <Label
                Grid.Column="1"
                FontAttributes="Bold"
                FontSize="16"
                Text="My Chats"
                TextColor="White"
                VerticalOptions="Center"
                VerticalTextAlignment="Center" />
        </Grid>
    </NavigationPage.TitleView>
    <ContentPage.ToolbarItems>
        <ToolbarItem IconImageSource="search.svg" />
        <ToolbarItem Command="{Binding SyncDownItemsCommand}" IconImageSource="refresh.svg" />
        <ToolbarItem IconImageSource="menu.svg" />
    </ContentPage.ToolbarItems>
    <Border
        BackgroundColor="{AppThemeBinding Light=WhiteSmoke,
                                          Dark=Black}"
        Stroke="{AppThemeBinding Light=Gainsboro,
                                 Dark=#101010}"
        StrokeThickness="1">
        <Border.StrokeShape>
            <RoundRectangle CornerRadius="16,16,0,0" />
        </Border.StrokeShape>
        <CollectionView ItemsSource="{Binding Items}" ItemsUpdatingScrollMode="KeepLastItemInView">
            <CollectionView.ItemTemplate>
                <DataTemplate x:DataType="local:ChatThreadsListingViewModel">
                    <Grid
                        Padding="12"
                        ColumnDefinitions="50,*,80"
                        ColumnSpacing="10">
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={RelativeSource AncestorType={x:Type local:ChatThreadsListingView}}, Path=BindingContext.MessageTappedCommand}" CommandParameter="{Binding .}" />
                        </Grid.GestureRecognizers>
                        <Border
                            Grid.Column="0"
                            BackgroundColor="Gray"
                            HeightRequest="50"
                            WidthRequest="50">
                            <Border.StrokeShape>
                                <RoundRectangle CornerRadius="45" />
                            </Border.StrokeShape>
                            <Image
                                Aspect="AspectFill"
                                HeightRequest="50"
                                Source="{Binding ProfileImage}"
                                WidthRequest="50" />
                        </Border>
                        <StackLayout Grid.Column="1" Spacing="2">
                            <Grid>
                                <StackLayout Orientation="Horizontal" Spacing="8">
                                    <Label
                                        FontAttributes="Bold"
                                        FontSize="18"
                                        Text="{Binding Name}"
                                        TextColor="{AppThemeBinding Light=Black,
                                                                    Dark=White}"
                                        VerticalOptions="Center" />


                                    <!--  Online Status Indicator  -->
                                    <Ellipse
                                        x:Name="OnlineStatusIndicator"
                                        Fill="{Binding OnlineStatusColor}"
                                        HeightRequest="8"
                                        IsVisible="{Binding ShowOnlineStatus}"
                                        VerticalOptions="Center"
                                        WidthRequest="8" />
                                </StackLayout>

                                <Label
                                    FontSize="14"
                                    HorizontalOptions="End"
                                    Text="{Binding LastMessageTimeString}"
                                    TextColor="{AppThemeBinding Light=Gray,
                                                                Dark=LightGray}" />

                            </Grid>
                            <Label
                                FontSize="16"
                                LineBreakMode="TailTruncation"
                                MaxLines="1"
                                Text="{Binding LastMessage}"
                                TextColor="{AppThemeBinding Light=Gray,
                                                            Dark=LightGray}" />
                        </StackLayout>

                        <!--  Call Action Buttons  -->
                        <StackLayout
                            Grid.Column="2"
                            HorizontalOptions="End"
                            Orientation="Horizontal"
                            Spacing="8"
                            VerticalOptions="Center">

                            <!--  Audio Call Button  -->
                            <ImageButton
                                x:Name="AudioCallButton"
                                BackgroundColor="#45C1A5"
                                Clicked="OnAudioCallClicked"
                                CommandParameter="{Binding .}"
                                CornerRadius="16"
                                HeightRequest="32"
                                Source="phone_solid.png"
                                ToolTipProperties.Text="Audio Call"
                                WidthRequest="32" />

                            <!--  Video Call Button  -->
                            <ImageButton
                                x:Name="VideoCallButton"
                                BackgroundColor="#3E97DA"
                                Clicked="OnVideoCallClicked"
                                CommandParameter="{Binding .}"
                                CornerRadius="16"
                                HeightRequest="32"
                                Source="video_solid.svg"
                                ToolTipProperties.Text="Video Call"
                                WidthRequest="32" />
                        </StackLayout>

                    </Grid>
                </DataTemplate>
            </CollectionView.ItemTemplate>
        </CollectionView>
    </Border>
</local:ChatThreadsListingViewBase>
