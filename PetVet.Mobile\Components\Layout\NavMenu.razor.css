.nav-tab-item {
    @apply flex flex-col items-center justify-center p-2 rounded-xl transition-all duration-300 min-w-0 flex-1 text-gray-500 hover:text-primary-600 hover:bg-primary-50;
}

.nav-tab-item.active {
    @apply text-primary-600 bg-primary-50;
}

.nav-tab-content {
    @apply flex flex-col items-center gap-1;
}

.nav-tab-icon {
    @apply relative text-lg;
}

.nav-tab-label {
    @apply text-xs font-medium truncate;
}

.nav-badge {
    @apply absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full;
}

/* Active state animation */
.nav-tab-item.active .nav-tab-icon {
    animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -4px, 0);
    }
    70% {
        transform: translate3d(0, -2px, 0);
    }
    90% {
        transform: translate3d(0, -1px, 0);
    }
}
