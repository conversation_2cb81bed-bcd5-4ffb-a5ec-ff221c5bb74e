﻿using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using PetVet.Api.Data;
using PetVet.Api.Data.Entities;
using PetVet.Api.Hubs;
using PetVet.Shared.DTOs;
using PetVet.Shared.IHub;
using System.Security.Claims;

namespace PetVet.Api.Services
{
    public class MessageService
    {
        private readonly PetVetContext _context;
        private readonly IHubContext<PetVetChatHub, IPetVetChatHubClient> _hubContext;

        public MessageService(
            PetVetContext context,
            IHubContext<PetVetChatHub, IPetVetChatHubClient> hubContext)
        {
            _context = context;
            _hubContext = hubContext;
        }

        public async Task<PetVetApiResponse> SendMessageAsync(MessageSendDto dto)
        {
            if (dto.ToUserId <= 0)
            {
                return PetVetApiResponse.Fail("Invalid recipient. Please provide a valid user.");
            }

            if (string.IsNullOrWhiteSpace(dto.Message))
            {
                return PetVetApiResponse.Fail("Message cannot be empty.");
            }


            var message = new Message
            {
                FromId = dto.FromUserId,
                ToId = dto.ToUserId,
                Content = dto.Message,
                SentOn = DateTime.Now,
            };

            _context.Messages.Add(message);

            try
            {
                await _context.SaveChangesAsync();

                var messageDto = new MessageDto(message.ToId, message.FromId, message.Content, message.SentOn);

                await _hubContext.Clients
                    .User(dto.ToUserId.ToString())
                    .MessageReceived(messageDto);
                return PetVetApiResponse.Success();
            }
            catch (Exception ex)
            {
                return PetVetApiResponse.Fail(ex.Message);
            }
        }



        public async Task<IEnumerable<MessageDto>> GetMessagesAsync(int otherUserId, int currentUserId)
        {
            var messages = await _context.Messages
                            .AsNoTracking()
                            .Where(m => (m.FromId == otherUserId && m.ToId == currentUserId) ||
                                  (m.ToId == otherUserId && m.FromId == currentUserId))
                            .Select(m => new MessageDto(m.ToId, m.FromId, m.Content, m.SentOn))
                            .ToListAsync();

            return messages;
        }
    }
}
