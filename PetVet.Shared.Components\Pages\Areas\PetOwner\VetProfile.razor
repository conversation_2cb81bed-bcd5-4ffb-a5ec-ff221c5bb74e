﻿@page "/petOwner/vetList/vet/{Id:int}"
@inject IUserApi _UserApi
@inject IAppointmentApi _AppointmentApi
@inject IAppState _AppState
@inject NavigationManager _NavigationManager
@inject IJSRuntime _JS
@inject PetVetAuthStateProvider _PetVetAuthStateProvider

<PageTitle>Dr. @_vetUser.Name - Vet Profile</PageTitle>

<!-- Modern Vet Profile Page -->
<div class="px-4 py-6 space-y-6">

    @if (!isProcessing)
    {
        <!-- Header with Back Button -->
        <div class="flex items-center gap-4 mb-6">
            <button @onclick="NavigateBack"
                    class="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-xl flex items-center justify-center transition-all">
                <i class="fas fa-arrow-left text-gray-600"></i>
            </button>
            <h1 class="text-2xl font-bold text-gray-800">Doctor Profile</h1>
        </div>

        <!-- Profile Header Card -->
        <div class="bg-gradient-to-r from-blue-500 via-purple-600 to-pink-600 rounded-3xl p-6 text-white relative overflow-hidden">
            <!-- Background Pattern -->
            <div class="absolute inset-0 opacity-10">
                <div class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full"></div>
                <div class="absolute bottom-4 left-4 w-20 h-20 bg-white rounded-full"></div>
            </div>

            <div class="relative z-10">
                <!-- Profile Image -->
                <div class="flex flex-col items-center mb-6">
                    <div class="w-32 h-32 rounded-full border-4 border-white/30 overflow-hidden mb-4 shadow-xl">
                        <img src="@GetVetImageSrc(_vetUser.ImageUrl)"
                             alt="@_vetUser.Name"
                             class="w-full h-full object-cover" />
                    </div>

                    <!-- Name and Specialization -->
                    <h2 class="text-2xl font-bold mb-1">Dr. @_vetUser.Name</h2>
                    <p class="text-white/90 text-center mb-4">@_vetUser.Specialization</p>
                    <p class="text-white/80 text-sm text-center">@_vetUser.ClinicName</p>
                </div>

                <!-- Stats -->
                <div class="grid grid-cols-3 gap-4">
                    <div class="bg-white/20 rounded-2xl p-4 text-center">
                        <div class="w-8 h-8 bg-white/30 rounded-full flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-heart text-sm"></i>
                        </div>
                        <div class="text-lg font-bold">150+</div>
                        <div class="text-xs text-white/80">Patients</div>
                    </div>
                    <div class="bg-white/20 rounded-2xl p-4 text-center">
                        <div class="w-8 h-8 bg-white/30 rounded-full flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-crown text-sm"></i>
                        </div>
                        <div class="text-lg font-bold">@_vetUser.YearsOfExperience</div>
                        <div class="text-xs text-white/80">Years Exp.</div>
                    </div>
                    <div class="bg-white/20 rounded-2xl p-4 text-center">
                        <div class="w-8 h-8 bg-white/30 rounded-full flex items-center justify-center mx-auto mb-2">
                            <i class="fas fa-star text-sm"></i>
                        </div>
                        <div class="text-lg font-bold">@(_vetUser.Rating?.ToString("0.0") ?? "0.0")</div>
                        <div class="text-xs text-white/80">Rating</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rating Section -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-bold text-gray-800">Patient Reviews</h3>
                <div class="flex items-center gap-2">
                    <div class="flex items-center gap-1">
                        @for (int i = 0; i < 5; i++)
                        {
                            if (i < Math.Floor(_vetUser.Rating ?? 0))
                            {
                                <i class="fas fa-star text-yellow-400"></i>
                            }
                            else if (i < Math.Ceiling(_vetUser.Rating ?? 0))
                            {
                                <i class="fas fa-star-half-alt text-yellow-400"></i>
                            }
                            else
                            {
                                <i class="far fa-star text-gray-300"></i>
                            }
                        }
                    </div>
                    <span class="text-sm text-gray-600">(@(_vetUser.Rating?.ToString("0.0") ?? "0.0"))</span>
                </div>
            </div>
            <p class="text-gray-600 text-sm">Based on 150+ patient reviews</p>
        </div>

        <!-- About Section -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div class="flex items-center gap-3 mb-4">
                <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-user-md text-blue-600"></i>
                </div>
                <h3 class="text-lg font-bold text-gray-800">About Doctor</h3>
            </div>
            <p class="text-gray-600 leading-relaxed">
                Dr. @_vetUser.Name is a highly qualified @_vetUser.Specialization specialist at @_vetUser.ClinicName.
                With @_vetUser.YearsOfExperience years of experience, they provide expert veterinary care and are available for consultations.
                Their dedication to animal health and well-being has earned them a reputation for excellence in veterinary medicine.
            </p>
        </div>

        <!-- Contact Information -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div class="flex items-center gap-3 mb-4">
                <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-phone text-green-600"></i>
                </div>
                <h3 class="text-lg font-bold text-gray-800">Contact Information</h3>
            </div>
            <div class="space-y-3">
                <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-house-chimney-medical text-blue-600 text-sm"></i>
                    </div>
                    <span class="text-gray-700">@_vetUser.ClinicName</span>
                </div>
                <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-map-marker-alt text-red-600 text-sm"></i>
                    </div>
                    <span class="text-gray-700">@_vetUser.Address</span>
                </div>
                <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                    <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-phone text-green-600 text-sm"></i>
                    </div>
                    <span class="text-gray-700">@_vetUser.Phone</span>
                </div>
                <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                    <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-envelope text-purple-600 text-sm"></i>
                    </div>
                    <span class="text-gray-700">@_vetUser.Email</span>
                </div>
            </div>
        </div>

        <!-- Education & Certifications -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div class="flex items-center gap-3 mb-4">
                <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-graduation-cap text-yellow-600"></i>
                </div>
                <h3 class="text-lg font-bold text-gray-800">Education & Certifications</h3>
            </div>
            <div class="space-y-3">
                <div class="flex items-center gap-3 p-3 bg-gray-50 rounded-xl">
                    <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-book-open text-blue-600 text-sm"></i>
                    </div>
                    <span class="text-gray-700">@_vetUser.EducationName</span>
                </div>

                @if (!string.IsNullOrEmpty(_vetUser.CertificationsUrl))
                {
                    <a href="data:application/pdf;base64,@_vetUser.CertificationsUrl"
                       download="Certificate.pdf"
                       class="flex items-center gap-3 p-3 bg-teal-50 hover:bg-teal-100 rounded-xl transition-all group">
                        <div class="w-8 h-8 bg-teal-100 group-hover:bg-teal-200 rounded-full flex items-center justify-center">
                            <i class="fas fa-file-alt text-teal-600 text-sm"></i>
                        </div>
                        <span class="text-teal-700 font-medium">View Certificate</span>
                        <i class="fas fa-external-link-alt text-teal-500 text-xs ml-auto"></i>
                    </a>
                }

                @if (!string.IsNullOrEmpty(_vetUser.LicenceDocumentUrl))
                {
                    <a href="data:application/pdf;base64,@_vetUser.LicenceDocumentUrl"
                       download="LicenseDocument.pdf"
                       class="flex items-center gap-3 p-3 bg-blue-50 hover:bg-blue-100 rounded-xl transition-all group">
                        <div class="w-8 h-8 bg-blue-100 group-hover:bg-blue-200 rounded-full flex items-center justify-center">
                            <i class="fas fa-file-medical text-blue-600 text-sm"></i>
                        </div>
                        <span class="text-blue-700 font-medium">View License</span>
                        <i class="fas fa-external-link-alt text-blue-500 text-xs ml-auto"></i>
                    </a>
                }
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="grid grid-cols-2 gap-4">
            <button @onclick="OpenAppointmentModal"
                    class="bg-gradient-to-r from-teal-500 to-blue-600 text-white py-4 px-6 rounded-2xl font-medium hover:from-teal-600 hover:to-blue-700 transition-all transform hover:scale-105 shadow-lg flex items-center justify-center gap-2">
                <i class="fas fa-calendar-plus"></i>
                <span>Book Appointment</span>
            </button>

            <button @onclick="StartChat"
                    class="bg-gradient-to-r from-purple-500 to-pink-600 text-white py-4 px-6 rounded-2xl font-medium hover:from-purple-600 hover:to-pink-700 transition-all transform hover:scale-105 shadow-lg flex items-center justify-center gap-2">
                <i class="fas fa-comment"></i>
                <span>Send Message</span>
            </button>
        </div>
    }
    else
    {
        <!-- Loading State -->
        <div class="flex items-center justify-center py-20">
            <div class="text-center">
                <div class="w-16 h-16 border-4 border-teal-200 border-t-teal-600 rounded-full animate-spin mx-auto mb-4"></div>
                <p class="text-gray-600">Loading doctor profile...</p>
            </div>
        </div>
    }

</div>

<!-- Modern Appointment Booking Modal -->
@if (_isModalOpen)
{
    <Modal Title="@GetModalTitle()"
           ActionButtonText="Confirm Booking"
           OnActionButtonClick="ConfirmAppointment"
           OnCancelButtonClick="CloseAppointmentModal"
           IsVisible="_isModalOpen"
           Size="ModalSize.Large">

        <div class="space-y-6">
            <!-- Date Selection -->
            <div>
                <label for="appointment-date" class="block text-sm font-medium text-gray-700 mb-2">Select Date</label>
                <input type="date"
                       id="appointment-date"
                       @bind-value="_selectedDate"
                       @bind-value:event="oninput"
                       min="@DateTime.Today.ToString("yyyy-MM-dd")"
                       class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all" />
            </div>

            <!-- Time Slots -->
            @if (_selectedDate != default)
            {
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-3">Available Time Slots for @_selectedDate.ToString("dddd, MMMM dd")</label>
                    @if (!_vetTimeSlots.Any(ts => ts.Day == _selectedDate.ToString("dddd")))
                    {
                        <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-4 text-center">
                            <i class="fas fa-calendar-times text-yellow-600 text-2xl mb-2"></i>
                            <p class="text-yellow-700 font-medium">No time slots available for this day</p>
                            <p class="text-yellow-600 text-sm">Please select another date</p>
                        </div>
                    }
                    else
                    {
                        <div class="grid grid-cols-3 gap-3">
                            @foreach (var slot in _vetTimeSlots.Where(ts => ts.Day == _selectedDate.ToString("dddd")))
                            {
                                <button class="p-3 rounded-xl border-2 transition-all text-sm font-medium @(slot.IsBooked || slot.IsExpired ? "border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed" : slot.Id == _selectedTimeSlotId ? "border-teal-500 bg-teal-50 text-teal-700" : "border-gray-200 hover:border-teal-300 hover:bg-teal-50 text-gray-700")"
                                        disabled="@(slot.IsBooked || slot.IsExpired)"
                                        @onclick="() => SelectTimeSlot(slot.Id, slot.Time)">
                                    <div class="text-center">
                                        <div>@slot.Time</div>
                                        @if (slot.IsBooked)
                                        {
                                            <div class="text-xs text-red-500 mt-1">Booked</div>
                                        }
                                        @if (slot.IsExpired)
                                        {
                                            <div class="text-xs text-gray-400 mt-1">Expired</div>
                                        }
                                    </div>
                                </button>
                            }
                        </div>
                    }
                </div>
            }

            <!-- Confirmation and Notes -->
            @if (_selectedTimeSlot is not null)
            {
                <div class="bg-teal-50 border border-teal-200 rounded-xl p-4">
                    <div class="flex items-center gap-2 mb-2">
                        <i class="fas fa-check-circle text-teal-600"></i>
                        <span class="font-medium text-teal-800">Appointment Summary</span>
                    </div>
                    <p class="text-teal-700 text-sm">
                        Booking appointment with <strong>Dr. @_vetUser.Name</strong> at <strong>@_selectedTimeSlot</strong> on <strong>@_selectedDate.ToString("dddd, MMMM dd, yyyy")</strong>
                    </p>
                </div>

                <div>
                    <label for="appointment-notes" class="block text-sm font-medium text-gray-700 mb-2">Additional Notes (Optional)</label>
                    <textarea id="appointment-notes"
                              @bind="_notes"
                              placeholder="Any special notes, concerns, or symptoms you'd like to mention..."
                              rows="3"
                              class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all resize-none"></textarea>
                </div>
            }
        </div>
    </Modal>
}

@code {
    [Parameter] public int Id { get; set; }
    private UserDto _vetUser = new();
    private bool isProcessing { get; set; } = true;
    private int _currentUserId;

    // Appointment Modal State
    private bool _isModalOpen = false;
    private string? _selectedTimeSlot;
    private int _selectedTimeSlotId;
    private string _notes = "";
    private List<VetTimeSlotDto> _vetTimeSlots = new();

    private DateTime _selectedDateField = DateTime.Today;
    private DateTime _selectedDate
    {
        get => _selectedDateField;
        set
        {
            if (_selectedDateField != value)
            {
                _selectedDateField = value;
                LoadAvailableTimeSlots().ConfigureAwait(false);
            }
        }
    }

    protected override async Task OnInitializedAsync()
    {
        _currentUserId = _PetVetAuthStateProvider.User.Id;

        await LoadVetUserAsync();
    }

    private async Task LoadVetUserAsync()
    {
        if (Id > 0)
        {
            isProcessing = true;
            _AppState.ShowLoader("Fetching Vet Data");
            _vetUser = await _UserApi.GetVetUserAsync(Id);
            _AppState.HideLoader();
            isProcessing = false;
        }
    }

    private async Task OpenAppointmentModal()
    {
        _selectedDate = DateTime.Today;
        _selectedTimeSlot = null;
        _selectedTimeSlotId = 0;
        _notes = "";
        _isModalOpen = true;

        // Load vet's time slots
        await LoadVetTimeSlots();
    }

    private async Task LoadVetTimeSlots()
    {
        try
        {
            _AppState.ShowLoader("Loading Time Slots");
            _vetTimeSlots = await _AppointmentApi.GetVetTimeSlotsAsync(Id);
        }
        catch (Exception ex)
        {
            _vetTimeSlots = new List<VetTimeSlotDto>();
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private async Task LoadAvailableTimeSlots()
    {
        _selectedTimeSlot = null;
        _selectedTimeSlotId = 0;
        await LoadVetTimeSlots();
    }

    private void CloseAppointmentModal()
    {
        _isModalOpen = false;
        _selectedTimeSlot = null;
        _selectedTimeSlotId = 0;
        _notes = "";
        _vetTimeSlots.Clear();
    }

    private void SelectTimeSlot(int timeSlotId, string time)
    {
        _selectedTimeSlotId = timeSlotId;
        _selectedTimeSlot = time;
    }

    private async Task ConfirmAppointment()
    {
        if (_selectedTimeSlot is null || _selectedTimeSlotId == 0)
        {
            return;
        }

        try
        {
            _AppState.ShowLoader("Booking Appointment");

            var appointmentDto = new AppointmentDto
                {
                    PetOwnerId = _currentUserId,
                    VetId = Id,
                    TimeSlotId = _selectedTimeSlotId,
                    AppointmentDate = _selectedDate.ToString("yyyy-MM-dd"),
                    Status = nameof(AppointmentStatus.Confirmed),
                    Notes = _notes
                };

            var response = await _AppointmentApi.CreateAppointmentAsync(appointmentDto);

            if (response.IsSuccess)
            {
                await _JS.ToastrSuccess("Appointment booked successfully! You will receive a confirmation message shortly.");
                CloseAppointmentModal();
            }
            else
            {
                await _JS.ToastrError($"Error booking appointment: {response.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error booking appointment: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private void StartChat()
    {
        // Navigate to the chat page with the vet's ID as a parameter
        // This will open a chat with the currently viewed vet
        _NavigationManager.NavigateTo($"/petOwner/chat?userId={Id}");
    }

    private void NavigateBack()
    {
        _NavigationManager.NavigateTo("/petOwner/vetList");
    }

    private string GetModalTitle()
    {
        return $"Book Appointment with Dr. {_vetUser.Name}";
    }

    private string GetVetImageSrc(string? imageUrl)
    {
        if (string.IsNullOrEmpty(imageUrl))
            return "images/dummy-profile.png";

        // If it's already a data URL, return as is
        if (imageUrl.StartsWith("data:"))
            return imageUrl;

        // If it's a regular URL (http/https), return as is
        if (imageUrl.StartsWith("http"))
            return imageUrl;

        // Otherwise, treat it as base64 and create data URL
        return $"data:image/jpeg;base64,{imageUrl}";
    }
}
