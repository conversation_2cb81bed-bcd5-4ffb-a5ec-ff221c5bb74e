﻿<!-- Modern Tailwind Modal -->
<div class="fixed inset-0 z-50 hidden items-center justify-center bg-black bg-opacity-50 p-4" id="bsConfirmationModal">
    <div class="bg-white rounded-3xl shadow-2xl max-w-md w-full mx-4 overflow-hidden transform transition-all">
        <!-- Modern Header -->
        <div class="bg-gradient-to-r from-red-500 to-red-600 text-white p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-white"></i>
                    </div>
                    <h5 class="text-lg font-bold">@Title</h5>
                </div>
                <button type="button" 
                        @onclick="() => ConfirmationClicked(false)"
                        class="text-white hover:text-gray-200 transition-colors"
                        aria-label="Close">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
        </div>

        <!-- Modern Body -->
        <div class="p-6 text-center">
            <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-bold text-gray-800 mb-2">Are you sure?</h3>
            <p class="text-gray-600 mb-6">This action cannot be undone.</p>

            <!-- Action Buttons -->
            <div class="flex gap-3 justify-center">
                <button type="button"
                        @onclick="() => ConfirmationClicked(false)"
                        class="px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-xl font-medium transition-all flex items-center gap-2">
                    <i class="fas fa-times text-sm"></i>
                    <span>Cancel</span>
                </button>
                <button type="button"
                        @onclick="() => ConfirmationClicked(true)"
                        class="px-6 py-3 bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white rounded-xl font-medium transition-all flex items-center gap-2 shadow-lg">
                    <i class="fas fa-trash text-sm"></i>
                    <span>@ButtonText</span>
                </button>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public EventCallback<bool> OnModalConfirmation { get; set; }

    [Parameter]
    public string Title { get; set; } = "Please Confirm";

    [Parameter]
    public string ButtonBootstrapStyle { get; set; } = "btn-secondary";

    [Parameter]
    public string ButtonText { get; set; } = "Confirm";

    private async Task ConfirmationClicked(bool value)
    {
        await OnModalConfirmation.InvokeAsync(value);
    }
}
