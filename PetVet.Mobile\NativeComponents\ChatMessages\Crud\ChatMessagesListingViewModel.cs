﻿using PetVet.Framework.Core;
using PetVet.ServiceContracts.Enums;
using Microsoft.Maui.Controls.Shapes;
namespace Platform.Client.Common.Features.Conversation;

public interface IChatItem : IEquatable<IChatItem>
{
    string Id { get; set; }
    string ContentHash { get; }

}

//public interface IChatItem
//{
//    public string Id { get; set; }

//    public string ContentHash =>  GetHashCode().ToString();
//}

public class ChatMessagesListingViewModel : ObservableBase, IChatItem
{
    public string Id { get; set; } = null!;

    public string? Content { get; set; }

    private DateTime? _timestamp;
    public DateTime? Timestamp { get { return _timestamp; } set { SetField(ref _timestamp, value); } }

    public bool IsIncoming { get; set; }

    private DeliveryStatus _deliveryStatus;
    public DeliveryStatus DeliveryStatus
    {
        get { return _deliveryStatus; }
        set { SetField(ref _deliveryStatus, value);
            NotifyPropertyChanged(nameof(DeliverySymbol));
        }
    }

    public string? DeliverySymbol
    {
        get
        {
            if (IsIncoming)
                return null;

            switch (DeliveryStatus)
            {
                case DeliveryStatus.Pending:
                    return "";

                case DeliveryStatus.SentToMessageServer:
                case DeliveryStatus.SentToEndUserViaSignalR:
                case DeliveryStatus.SentToEndUserViaPushNotification:
                    return "sent.svg";

                case DeliveryStatus.DeliveredToEndUser:
                    return "delivered.svg";

                case DeliveryStatus.ReadByEndUser:
                    return "read.svg";
                default:
                    return null;
            }
        }
    }

    public IShape BubbleShape => IsIncoming ?
        new RoundRectangle()
        {
            BackgroundColor = Color.FromArgb("#FFFFFF"),
            CornerRadius = new CornerRadius(0, 8, 8, 8)

        } :
        new RoundRectangle()
        {
            BackgroundColor = Color.FromArgb("#33333"),
            CornerRadius = new CornerRadius(8, 0, 8, 8)
        };

    public Color BackgroundColor => IsIncoming ? Colors.White : Color.FromArgb("#E4E4E7");

    public LayoutOptions HorizontalOptions => IsIncoming ? LayoutOptions.Start : LayoutOptions.End;

    public Color MessageColor => IsIncoming ? Color.FromArgb("#333333") : Color.FromArgb("#111111");

    public Color TimestampColor => IsIncoming ? Color.FromArgb("#777777") : Color.FromArgb("#666666");

    public string TimeOfTheDay => Timestamp?.ToString("hh:mm tt") ?? string.Empty;

    public string ContentHash => Timestamp?.ToString("yyMMdd-HH:mm:tt.ss.fff") + Id;

    public bool Equals(IChatItem? other)
    {
        if (other == null) return false;
        return Id == other.Id && ContentHash == other.ContentHash;
    }

    public override bool Equals(object? obj)
    {
        return Equals(obj as IChatItem);
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(Id, ContentHash);
    }
}

public class DateSeparatorItem : IChatItem
{
    public string Id { get; set; } = string.Empty;

    public DateTime Date { get; }
    public DateSeparatorItem(DateTime d) => Date = d;

    public string ContentHash => Date.ToString("yyyy-MM-dd") + Id;

    public bool Equals(IChatItem? other)
    {
        if (other == null) return false;
        return Id == other.Id && ContentHash == other.ContentHash;
    }

    // Overriding Equals and GetHashCode for consistency
    public override bool Equals(object? obj)
    {
        return Equals(obj as IChatItem);
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(Id, ContentHash);
    }
}

public class ChatMessageStatus
{
    public string Id { get; set; } = null!;

    public DateTime? Timestamp { get; set; }

    public DeliveryStatus DeliveryStatus { get; set; }

    public string ContentHash => Timestamp?.ToString("yyMMdd-HH:mm:tt.ss.fff") + Id;
}