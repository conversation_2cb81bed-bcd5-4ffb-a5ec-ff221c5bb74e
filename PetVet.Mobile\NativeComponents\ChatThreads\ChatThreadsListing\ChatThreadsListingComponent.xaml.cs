﻿using CommunityToolkit.Mvvm.Messaging;
using PetVet.Client.Common.Data;
using PetVet.MauiApp.Services;
using PetVet.MauiShared;
using PetVet.ServiceContracts.Features.Conversation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Maui.Platform;
using System.Windows.Input;
using PetVet.Shared.Components.Framework;
using Firebase.Messaging;
using DeepMessage.ServiceContracts.Features.Configurations;
using Plugin.Firebase.CloudMessaging;
using PetVet.Shared.Models.AzureCommunication;
using PetVet.Shared.Services;
using static Android.Provider.Telephony.Sms;
namespace Platform.Client.Common.Features.Conversation;
public class ChatThreadsListingViewBase : ListingBaseMaui<ChatThreadsListingViewModel, ChatThreadsListingBusinessObject,
                                            ChatThreadsFilterViewModel, ChatThreadsFilterBusinessObject, IChatThreadsListingDataService>
{
    public ChatThreadsListingViewBase(IServiceScopeFactory scopeFactory) : base(scopeFactory)
    {
    }
}


public partial class ChatThreadsListingView : ChatThreadsListingViewBase
{
    private readonly IAzureCommunicationService _communicationService;
    private readonly string? conversationId;
    public ICommand BackCommand => new Command(async () => await Navigation.PopAsync());
    public ChatThreadsListingView(IServiceScopeFactory scopeFactory, string? conversationId) : base(scopeFactory)
    {
        // Get communication service from DI
        _communicationService = scopeFactory.CreateScope().ServiceProvider.GetRequiredService<IAzureCommunicationService>();

        InitializeComponent();

        MessageTappedCommand = new Command<ChatThreadsListingViewModel>(async (p) =>
        {
            //if (chats.ContainsKey(p.Id))
            //{
            //    var chat = chats[p.Id];
            //    if (Navigation.NavigationStack.Contains(chat))
            //    {
            //        // pop until it’s the top page
            //        while (Navigation.NavigationStack.Last() != chat)
            //            await Navigation.PopAsync(false);
            //    }
            //    else
            //    {
            //        // otherwise push it
            //        await Navigation.PushAsync(chat, false);
            //    }


            //}
            //else
            //{
            //    var chat = new ChatMessagesListingView(scopeFactory, p.Id, p.Name, p.Avatar);
            //    chats.Add(p.Id, chat);
            //    await Navigation.PushAsync(chat);
            //}
            var chat = new ChatMessagesListingView(scopeFactory, p.Id, p.Name, p.FriendId, p.ProfileImage);
            await Navigation.PushAsync(chat, false);
        });

        WeakReferenceMessenger.Default.Register<ChatSyncItem>(this, (r, m) =>
        {
            _ = LoadItems(false);
        });

        WeakReferenceMessenger.Default.Register<ChatMessageStatus>(this, (r, m) =>
        {
            _ = LoadItems(false);
        });
        BindingContext = this;
        this.conversationId = conversationId;
    }

    protected override void OnAppearing()
    {
        SetStatusBarColor(Color.FromArgb("#157BAB"));

        Task.Factory.StartNew(async () =>
        {
            var scope = ScopeFactory.CreateScope();
            var localStorage = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
            var deviceId = await localStorage.GetValue("device_id");
            if (string.IsNullOrEmpty(deviceId))
            {
                deviceId = Guid.NewGuid().ToString();
                await localStorage.SetValue(deviceId, "device_id");
            }

            var deviceToken = await localStorage.GetValue("device_token");
            //if (string.IsNullOrEmpty(deviceToken))
            {
                try
                {
                    var fcm = scope.ServiceProvider.GetRequiredService<IFirebaseCloudMessaging>();
                    deviceToken = await fcm.GetTokenAsync();

                    await localStorage.SetValue(deviceToken, "device_token");
                }
                catch (Exception ex)
                {
                    Console.Write(ex.ToString());
                }
            }

            var deviceTokenRegistration = await localStorage.GetValue("device_token_registration");
            // if (string.IsNullOrEmpty(deviceTokenRegistration))
            {
                var deviceTokenFormDataService = scope.ServiceProvider.GetRequiredService<IDeviceTokenFormDataService>();
                await deviceTokenFormDataService.SaveAsync(new DeviceTokenFormBusinessObject()
                {
                    Id = deviceId,
                    DeviceToken = deviceToken,
                    DeviceName = $"{DeviceInfo.Manufacturer}-{DeviceInfo.Model}-{DeviceInfo.Platform}-{DeviceInfo.VersionString}",
                    Platform = DeviceInfo.Platform.ToString(),
                });

                await localStorage.SetValue("true", "device_token_registration");
            }
        });
        base.OnAppearing();
    }
    void SetStatusBarColor(Color color)
    {
#if ANDROID
        var window = Microsoft.Maui.ApplicationModel.Platform.CurrentActivity?.Window; // Correct namespace for CurrentActivity

        if (window != null)
        {
            window.ClearFlags(Android.Views.WindowManagerFlags.TranslucentStatus);
            window.AddFlags(Android.Views.WindowManagerFlags.DrawsSystemBarBackgrounds);
            window.SetStatusBarColor(color.ToPlatform());
        }
#endif
    }

    public ICommand MessageTappedCommand { get; set; }

    private ICommand? _syncDownItemsCommand;
    public ICommand? SyncDownItemsCommand
    {
        get
        {
            return _syncDownItemsCommand = _syncDownItemsCommand ?? new Command(async () =>
            {
                MainThread.BeginInvokeOnMainThread(() =>
                {
                    IsBusy = true;
                });

                using (var scope = ScopeFactory.CreateScope())
                {
                    Error = string.Empty;
                    try
                    {
                        var listingService = scope.ServiceProvider.GetRequiredService<IChatThreadSyncFormDataService>();
                        var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                        var items = await listingService.GetItemsAsync() ?? throw new Exception("Paginaned items object is null");
                        foreach (var conversationItem in items)
                        {
                            var conversation = await context.Conversations.FirstOrDefaultAsync(x => x.Id == conversationItem.Id);

                            if (conversation == null)
                            {
                                conversation = new PetVet.Client.Common.Data.Conversation()
                                {
                                    Id = conversationItem.Id,
                                    CreatedAt = conversationItem.CreatedAt,
                                    IsDeleted = conversationItem.IsDeleted,
                                    Title = conversationItem.Title

                                };
                                context.Conversations.Add(conversation);
                                await context.SaveChangesAsync();
                            }

                            ArgumentNullException.ThrowIfNull(conversationItem.ChatParticipents);

                            foreach (var participentItem in conversationItem.ChatParticipents)
                            {
                                var participent = await context.ConversationParticipants.FirstOrDefaultAsync(x => x.Id == participentItem.Id);
                                if (participent == null)
                                {
                                    participent = new ConversationParticipant()
                                    {
                                        Id = participentItem.Id,
                                        ConversationId = participentItem.ConversationId,
                                        IsAdmin = participentItem.IsAdmin,
                                        JoinedAt = participentItem.JoinedAt,
                                        UserId = participentItem.UserId,
                                    };
                                    context.ConversationParticipants.Add(participent);
                                    await context.SaveChangesAsync();
                                }
                            }
                        }

                        await LoadItems(true);
                    }
                    catch (UnauthorizedAccessException)
                    {
                        _ = Shell.Current.GoToAsync("//signin");
                    }

                    catch (Exception ex)
                    {
                        //Crashes.TrackError(ex);
                        if (ex.Message.Contains("invalid_token"))
                        {
                            _ = Shell.Current.GoToAsync("//signin");
                        }
                        Error = ex.Message;
                    }
                }


                MainThread.BeginInvokeOnMainThread(() =>
                {
                    IsBusy = false;
                });
            });
        }
    }

    protected override Task ItemsLoaded(IChatThreadsListingDataService service)
    {
        if (!string.IsNullOrEmpty(conversationId))
        {
            var item = Items.FirstOrDefault(x => x.Id == conversationId);
            var chat = new ChatMessagesListingView(ScopeFactory, conversationId, item.Name, item.FriendId, item.ProfileImage);
            _ = Navigation.PushAsync(chat, false);
        }
        return base.ItemsLoaded(service);
    }

    #region Call Functionality

    private async void OnAudioCallClicked(object sender, EventArgs e)
    {
        try
        {
            if (sender is Button button && button.CommandParameter is ChatThreadsListingViewModel chatThread)
            {
                await StartCall(chatThread, CallType.Audio);
            }
        }
        catch (Exception ex)
        {
            await DisplayAlert("Error", $"Failed to start audio call: {ex.Message}", "OK");
        }
    }

    private async void OnVideoCallClicked(object sender, EventArgs e)
    {
        try
        {
            if (sender is Button button && button.CommandParameter is ChatThreadsListingViewModel chatThread)
            {
                await StartCall(chatThread, CallType.AudioVideo);
            }
        }
        catch (Exception ex)
        {
            await DisplayAlert("Error", $"Failed to start video call: {ex.Message}", "OK");
        }
    }

    private async Task StartCall(ChatThreadsListingViewModel chatThread, CallType callType)
    {
        try
        {
            var result = await DisplayAlert("Start Call",
                $"Start {callType} call with {chatThread.Name}?",
                "Yes", "Cancel");

            if (!result) return;

            var startRequest = new StartCallRequest
            {
                ThreadId = chatThread.Id,
                InitiatorId = "current-user-id", // Replace with actual user ID
                CallType = callType,
                ParticipantIds = new List<string> { "friend-user-id" }, // Replace with actual friend ID
                Subject = $"{callType} call with {chatThread.Name}"
            };

            var response = await _communicationService.StartCallAsync(startRequest);

            if (response.Success && !string.IsNullOrEmpty(response.CallId))
            {
                // Navigate to the chat page with call active
                var chat = new ChatMessagesListingView(ScopeFactory, chatThread.Id, chatThread.Name, chatThread.FriendId, chatThread.ProfileImage);
                await Navigation.PushAsync(chat, false);

                await DisplayAlert("Call Started",
                    $"Call started successfully. Call ID: {response.CallId}",
                    "OK");
            }
            else
            {
                await DisplayAlert("Error", response.Message ?? "Failed to start call", "OK");
            }
        }
        catch (Exception ex)
        {
            await DisplayAlert("Error", $"Failed to start call: {ex.Message}", "OK");
        }
    }

    #endregion
}




