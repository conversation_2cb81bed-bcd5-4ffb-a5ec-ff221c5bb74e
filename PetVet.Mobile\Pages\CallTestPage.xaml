<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="PetVet.Mobile.Pages.CallTestPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    Title="Call Test"
    BackgroundColor="#F8F9FA"
    NavigationPage.IconColor="White">

    <ScrollView>
        <StackLayout Padding="20" Spacing="20">

            <!--  Header  -->
            <Label
                FontAttributes="Bold"
                FontSize="24"
                HorizontalOptions="Center"
                IsVisible="Hidden"
                Text="Real-Time Calling Test"
                TextColor="#1F2937" />

            <!--  Connection Status  -->
            <Frame
                Padding="15"
                BackgroundColor="White"
                CornerRadius="10"
                HasShadow="True">
                <StackLayout>
                    <Label
                        FontAttributes="Bold"
                        FontSize="16"
                        Text="Connection Status"
                        TextColor="#374151" />
                    <StackLayout Orientation="Horizontal" Spacing="10">
                        <Ellipse
                            x:Name="ConnectionStatusIndicator"
                            Fill="Red"
                            HeightRequest="12"
                            VerticalOptions="Center"
                            WidthRequest="12" />
                        <Label
                            x:Name="ConnectionStatusLabel"
                            FontSize="14"
                            Text="Disconnected"
                            TextColor="#6B7280"
                            VerticalOptions="Center" />
                    </StackLayout>
                </StackLayout>
            </Frame>

            <!--  User Info  -->
            <Frame
                Padding="15"
                BackgroundColor="White"
                CornerRadius="10"
                HasShadow="True"
                IsVisible="Collapse">
                <StackLayout>
                    <Label
                        FontAttributes="Bold"
                        FontSize="16"
                        Text="User Information"
                        TextColor="#374151" />
                    <Entry
                        x:Name="UserIdEntry"
                        Placeholder="Enter User ID"
                        Text="user123" />
                    <Entry
                        x:Name="UserNameEntry"
                        Placeholder="Enter User Name"
                        Text="Test User" />
                    <Grid ColumnDefinitions="*,*,*" ColumnSpacing="10">
                        <Button
                            x:Name="InitializeButton"
                            Grid.Column="0"
                            BackgroundColor="#8FBFA8"
                            Clicked="OnInitializeClicked"
                            CornerRadius="8"
                            FontSize="12"
                            Text="Initialize"
                            TextColor="White" />
                        <Button
                            x:Name="DiagnoseButton"
                            Grid.Column="1"
                            BackgroundColor="#FEA195"
                            Clicked="OnDiagnoseClicked"
                            CornerRadius="8"
                            FontSize="12"
                            Text="Diagnose"
                            TextColor="White" />
                        <Button
                            x:Name="AudioFlowButton"
                            Grid.Column="2"
                            BackgroundColor="#6B7280"
                            Clicked="OnAudioFlowClicked"
                            CornerRadius="8"
                            FontSize="12"
                            Text="Audio Flow"
                            TextColor="White" />
                    </Grid>
                </StackLayout>
            </Frame>

            <!--  Call Controls  -->
            <Frame
                Padding="15"
                BackgroundColor="White"
                CornerRadius="10"
                HasShadow="True">
                <StackLayout>
                    <Label
                        FontAttributes="Bold"
                        FontSize="16"
                        IsVisible="Hidden"
                        Text="Call Controls"
                        TextColor="#374151" />

                    <Entry
                        x:Name="ThreadIdEntry"
                        IsVisible="Hidden"
                        Placeholder="Thread ID"
                        Text="thread123" />
                    <Entry
                        x:Name="FriendIdEntry"
                        IsVisible="Hidden"
                        Placeholder="Friend User ID"
                        Text="friend456" />

                    <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
                        <Button
                            x:Name="AudioCallButton"
                            Grid.ColumnSpan="2"
                            Margin="8"
                            BackgroundColor="#FEA195"
                            Clicked="OnAudioCallClicked"
                            CornerRadius="8"
                            Text="Audio Call"
                            TextColor="White" />

                        <Button
                            x:Name="VideoCallButton"
                            Grid.Column="1"
                            Margin="8"
                            BackgroundColor="#FEA195"
                            Clicked="OnVideoCallClicked"
                            CornerRadius="8"
                            IsVisible="Hidden"
                            Text="Video Call"
                            TextColor="White" />
                    </Grid>

                    <Button
                        x:Name="EndCallButton"
                        BackgroundColor="#EF4444"
                        Clicked="OnEndCallClicked"
                        CornerRadius="8"
                        IsEnabled="False"
                        Text="End Call"
                        TextColor="White" />
                </StackLayout>
            </Frame>

            <!--  Active Calls  -->
            <Frame
                Padding="15"
                BackgroundColor="White"
                CornerRadius="10"
                HasShadow="True"
                IsVisible="Hidden">
                <StackLayout>
                    <Label
                        FontAttributes="Bold"
                        FontSize="16"
                        Text="Active Calls"
                        TextColor="#374151" />
                    <CollectionView x:Name="ActiveCallsCollectionView" ItemsSource="{Binding ActiveCalls}">
                        <CollectionView.ItemTemplate>
                            <DataTemplate>
                                <Grid Padding="10" ColumnDefinitions="*,Auto">
                                    <StackLayout Grid.Column="0">
                                        <Label
                                            FontSize="12"
                                            Text="{Binding CallId}"
                                            TextColor="#6B7280" />
                                        <Label
                                            FontAttributes="Bold"
                                            FontSize="14"
                                            Text="{Binding Status}"
                                            TextColor="#1F2937" />
                                    </StackLayout>
                                    <Label
                                        Grid.Column="1"
                                        FontSize="12"
                                        Text="{Binding CallType}"
                                        TextColor="#8FBFA8"
                                        VerticalOptions="Center" />
                                </Grid>
                            </DataTemplate>
                        </CollectionView.ItemTemplate>
                    </CollectionView>
                </StackLayout>
            </Frame>

            <!--  Enhanced Audio Controls  -->
            <Frame
                Padding="15"
                BackgroundColor="White"
                CornerRadius="10"
                HasShadow="True">
                <StackLayout>
                    <Label
                        FontAttributes="Bold"
                        FontSize="16"
                        Text="Enhanced Audio Controls"
                        TextColor="#374151" />

                    <!--  Include the enhanced audio controls component  -->
                    <ContentView x:Name="AudioControlsContainer" Margin="0,10,0,0">
                        <!--  AudioControlsComponent will be added programmatically  -->
                    </ContentView>

                    <Grid
                        Margin="0,10,0,0"
                        ColumnDefinitions="*,*"
                        ColumnSpacing="10">
                        <Button
                            x:Name="ToggleAudioButton"
                            Grid.Column="0"
                            BackgroundColor="#8FBFA8"
                            Clicked="OnToggleAudioClicked"
                            CornerRadius="8"
                            Text="Start Audio"
                            TextColor="White" />
                        <Button
                            x:Name="ToggleVideoButton"
                            Grid.Column="1"
                            BackgroundColor="#8FBFA8"
                            Clicked="OnToggleVideoClicked"
                            CornerRadius="8"
                            Text="Start Video"
                            TextColor="White" />
                    </Grid>
                </StackLayout>
            </Frame>

            <!--  Media Status  -->
            <Frame
                Padding="15"
                BackgroundColor="White"
                CornerRadius="10"
                HasShadow="True"
                IsVisible="Hidden">
                <StackLayout>
                    <Label
                        FontAttributes="Bold"
                        FontSize="16"
                        Text="Media Status"
                        TextColor="#374151" />

                    <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
                        <StackLayout Grid.Column="0">
                            <Label
                                FontAttributes="Bold"
                                FontSize="14"
                                Text="Audio"
                                TextColor="#374151" />
                            <Label
                                x:Name="AudioStatusLabel"
                                FontSize="12"
                                Text="Stopped"
                                TextColor="#6B7280" />
                        </StackLayout>
                        <StackLayout Grid.Column="1">
                            <Label
                                FontAttributes="Bold"
                                FontSize="14"
                                Text="Video"
                                TextColor="#374151" />
                            <Label
                                x:Name="VideoStatusLabel"
                                FontSize="12"
                                Text="Stopped"
                                TextColor="#6B7280" />
                        </StackLayout>
                    </Grid>
                </StackLayout>
            </Frame>

            <!--  Log  -->
            <Frame
                Padding="15"
                BackgroundColor="White"
                CornerRadius="10"
                HasShadow="True"
                IsVisible="Hidden">
                <StackLayout>
                    <Label
                        FontAttributes="Bold"
                        FontSize="16"
                        Text="Event Log"
                        TextColor="#374151" />
                    <ScrollView HeightRequest="200">
                        <Label
                            x:Name="LogLabel"
                            FontSize="12"
                            LineBreakMode="WordWrap"
                            Text="Ready..."
                            TextColor="#6B7280" />
                    </ScrollView>
                    <Button
                        BackgroundColor="#6B7280"
                        Clicked="OnClearLogClicked"
                        CornerRadius="8"
                        Text="Clear Log"
                        TextColor="White" />
                </StackLayout>
            </Frame>

        </StackLayout>
    </ScrollView>
</ContentPage>
