﻿
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PetVet.Client.Common.Data
{
    public class ApplicationUser
    {
        [Key, DatabaseGenerated(DatabaseGeneratedOption.None)]
        public int Id { get; set; }

        [StringLength(450)]
        public string NickName { get; set; } = string.Empty;

        [StringLength(450)]
        public string Hash { get; set; } = string.Empty;
    }
}
