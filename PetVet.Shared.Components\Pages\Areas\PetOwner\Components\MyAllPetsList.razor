﻿@inject IPetApi _PetApi
@inject IJSRuntime _JS
@inject IAppState _AppState
@inject PetVetAuthStateProvider _PetVetAuthStateProvider

@if (!isProcessing)
{
    <!-- Modern Pet List with Theme Colors -->
    <div class="space-y-4">
        <MyPetsCategories OnCategoryChanged="HandleCategoryChanged" OnFilterChanged="HandleFilterChanged" />

        <!-- Header with soft styling -->
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-bold text-gray-800">All Pets</h2>
            <span class="text-sm text-gray-500">@_filteredPets.Length pets</span>
        </div>

        <!-- Pet Cards Grid -->
        <div class="space-y-3">
            @if (_filteredPets.Any())
            {
                @foreach (var pet in _filteredPets)
                {
                    var cardIndex = Array.IndexOf(_filteredPets, pet) % PetCardStyles.Count;
                    var cardStyle = PetCardStyles[cardIndex];

                    <div class="@cardStyle.BackgroundClass rounded-3xl p-4 shadow-sm border border-white/20 hover:shadow-md transition-all">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <!-- Pet Image -->
                                <div class="relative">
                                    @if (!string.IsNullOrEmpty(pet.ImageUrl))
                                    {
                                        <img src="@pet.ImageUrl"
                                             alt="@pet.Name"
                                             class="w-16 h-16 rounded-2xl object-cover border-2 border-white/30" />
                                    }
                                    else
                                    {
                                        <div class="w-16 h-16 rounded-2xl bg-white/20 flex items-center justify-center border-2 border-white/30">
                                            <i class="fas fa-paw text-white/70 text-xl"></i>
                                        </div>
                                    }

                                    <!-- Status Indicator -->
                                    <div class="absolute -top-1 -right-1 w-5 h-5 bg-white rounded-full flex items-center justify-center">
                                        <div class="w-3 h-3 @GetStatusColor(pet.VaccinationStatus) rounded-full"></div>
                                    </div>
                                </div>

                                <!-- Pet Info -->
                                <div class="flex-1">
                                    <div class="flex items-center gap-2 mb-1">
                                        <h3 class="font-bold text-white text-lg">@pet.Name</h3>
                                        @if (pet.Count > 1)
                                        {
                                            <span class="px-2 py-1 bg-white/20 text-white text-xs rounded-full font-medium">
                                                @pet.Count pets
                                            </span>
                                            <span class="px-2 py-1 bg-orange-400 text-white text-xs rounded-full font-medium">
                                                Group
                                            </span>
                                        }
                                    </div>

                                    <!-- Pet Details -->
                                    <div class="flex items-center gap-4 text-white/80 text-sm">
                                        <span class="flex items-center gap-1">
                                            <i class="fas fa-birthday-cake text-xs"></i>
                                            @GetPetAge(pet.DateOfBirth)
                                        </span>
                                        <span class="flex items-center gap-1">
                                            <i class="fas fa-venus-mars text-xs"></i>
                                            @pet.Gender
                                        </span>
                                    </div>

                                    <!-- Action Button -->
                                    <a href="@($"petOwner/myPets/all/petprofile/{pet.Id}")"
                                       class="inline-flex items-center gap-2 mt-2 px-3 py-1.5 bg-white/20 hover:bg-white/30 text-white text-sm rounded-xl transition-all">
                                        <i class="fas fa-eye text-xs"></i>
                                        <span>View Profile</span>
                                    </a>
                                </div>
                            </div>

                            <!-- Heart Icon -->
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                                    <i class="fas fa-heart text-red-300 text-lg"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            }
            else
            {
                <!-- Empty State -->
                <div class="bg-gradient-to-br from-gray-50 to-gray-100 rounded-3xl p-8 text-center">
                    <div class="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-paw text-gray-400 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-bold text-gray-800 mb-2">No Pets Found</h3>
                    <p class="text-gray-500">No pets match your current filters</p>
                </div>
            }
        </div>
    </div>
}

@code {
    private PetDto[] _pets = [];
    private PetDto[] _filteredPets = [];
    private bool isProcessing { get; set; } = true;
    private int? selectedCategoryId = null;
    private int _currentUserId => _PetVetAuthStateProvider?.User.Id ?? 0;
    private MyPetsFilters.PetFilterCriteria _currentFilters = new();
    private string _searchTerm = "";

    protected override async Task OnInitializedAsync()
    {
        await LoadPetsAsync();
    }

    private async Task LoadPetsAsync()
    {
        isProcessing = true;
        _AppState.ShowLoader("Fetching Pets Data");

        try
        {
            // Get pets for the current user only
            _pets = await _PetApi.GetPetsByUserAsync(_currentUserId);

            _filteredPets = _pets.OrderByDescending(p => p.Id).ToArray();
        }
        catch (Exception ex)
        {
            await _JS.ToastrError("Error loading pets");
        }
        finally
        {
            _AppState.HideLoader();
            isProcessing = false;
        }
    }

    // Modern card styles matching the theme
    private List<CardStyle> PetCardStyles = new List<CardStyle>
    {
        new CardStyle { BackgroundClass = "bg-gradient-to-br from-primary-300 to-primary-400" },
        new CardStyle { BackgroundClass = "bg-gradient-to-br from-secondary-300 to-secondary-400" },
        new CardStyle { BackgroundClass = "bg-gradient-to-br from-primary-400 to-secondary-300" },
        new CardStyle { BackgroundClass = "bg-gradient-to-br from-secondary-400 to-primary-300" },
        new CardStyle { BackgroundClass = "bg-gradient-to-br from-primary-200 to-primary-300" },
        new CardStyle { BackgroundClass = "bg-gradient-to-br from-secondary-200 to-secondary-300" }
    };

    public class CardStyle
    {
        public string BackgroundClass { get; set; } = "";
    }

    private string GetStatusColor(string vaccinationStatus)
    {
        return vaccinationStatus?.ToLower() switch
        {
            "vaccinated" => "bg-green-400",
            "partially vaccinated" => "bg-yellow-400",
            "not vaccinated" => "bg-red-400",
            _ => "bg-gray-400"
        };
    }

    private string GetPetAge(DateTime? dateOfBirth)
    {
        if (!dateOfBirth.HasValue) return "Unknown";

        var age = DateTime.Now - dateOfBirth.Value;
        var years = (int)(age.TotalDays / 365.25);
        var months = (int)((age.TotalDays % 365.25) / 30.44);

        if (years > 0)
            return $"{years}y {months}m";
        else if (months > 0)
            return $"{months}m";
        else
            return "< 1m";
    }

    private async Task HandleCategoryChanged(int? categoryId)
    {
        selectedCategoryId = categoryId;
        await ApplyFilters();
    }

    private async Task HandleFilterChanged(MyPetsFilters.PetFilterCriteria criteria)
    {
        _currentFilters = criteria;
        await ApplyFilters();
    }

    public async Task HandleSearch(string searchTerm)
    {
        _searchTerm = searchTerm?.Trim() ?? "";
        await ApplyFilters();
        StateHasChanged();
    }

    private Task ApplyFilters()
    {
        // Start with all pets for the current user
        var query = _pets.AsQueryable();

        // Apply search filter
        if (!string.IsNullOrWhiteSpace(_searchTerm))
        {
            query = query.Where(p => p.Name != null &&
                                    p.Name.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase));
        }

        // Apply category filter from dropdown
        if (selectedCategoryId.HasValue)
        {
            query = query.Where(p => p.CategoryId == selectedCategoryId.Value);
        }

        // Apply category filters from checkboxes
        if (_currentFilters.CategoryIds.Any())
        {
            query = query.Where(p => _currentFilters.CategoryIds.Contains(p.CategoryId));
        }

        // Apply vaccination status filters
        if (_currentFilters.VaccinationStatuses.Any())
        {
            query = query.Where(p => _currentFilters.VaccinationStatuses.Contains(p.VaccinationStatus));
        }

        // Get all filtered results, sorted by newest first
        _filteredPets = query.OrderByDescending(p => p.Id).ToArray();

        return Task.CompletedTask;
    }
}