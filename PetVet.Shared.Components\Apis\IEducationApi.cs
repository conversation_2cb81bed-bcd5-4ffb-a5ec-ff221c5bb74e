﻿using PetVet.Shared.DTOs;
using Refit;

namespace PetVet.Shared.Components.Apis
{
    [Headers("Authorization: Bearer ")]
    public interface IEducationApi
    {
        [Post("/api/education")]
        Task<PetVetApiResponse> SaveEducationAsync(EducationDto educationDto);

        [Get("/api/education")]
        Task<EducationDto[]> GetEducationListAsync();

        [Get("/api/education/{id}")]
        Task<EducationDto> GetEducationAsync(int id);

        [Delete("/api/education/{id}")]
        Task<PetVetApiResponse> DeleteEducationAsync(int id);
    }
}
