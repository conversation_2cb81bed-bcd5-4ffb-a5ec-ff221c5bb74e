﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace PetVet.Api.Data.Entities
{
    public class VetTimeSlot
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int VetId { get; set; }

        [ForeignKey(nameof(VetId))]
        public virtual User? Vet { get; set; }

        [Required]
        public string Day { get; set; } = "";

        [Required]
        public string Time { get; set; } = "";

        public bool IsBooked { get; set; }
    }
}
