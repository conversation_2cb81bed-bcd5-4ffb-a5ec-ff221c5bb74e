﻿using Android.Media;
using PetVet.Mobile.Services;

[assembly: Dependency(typeof(AudioCaptureService))]
public class AudioCaptureService : IAudioCaptureService
{
    private Android.Media.AudioRecord? _recorder;
    private int _sampleRate = 16000; 
    private Android.Media.Encoding _encoding = Android.Media.Encoding.Pcm16bit;

    public AudioCaptureService()
    {
        int bufferSize = Android.Media.AudioRecord.GetMinBufferSize(_sampleRate, ChannelIn.Mono, _encoding);
        _recorder = new Android.Media.AudioRecord(
            AudioSource.Mic, _sampleRate, ChannelIn.Mono, _encoding, bufferSize);
       
    }

    bool permissionChecked = false;
    public  byte[]? CaptureFrameAsync(int frameSizeInBytes)
    {
        if (!permissionChecked)
        {
            _recorder.StartRecording();
            permissionChecked = true;
        }

        if (_recorder == null) return null;

        var buffer = new byte[frameSizeInBytes];
        int read = _recorder.Read(buffer, 0, frameSizeInBytes);
        return read > 0 ? buffer : null;
    }
}