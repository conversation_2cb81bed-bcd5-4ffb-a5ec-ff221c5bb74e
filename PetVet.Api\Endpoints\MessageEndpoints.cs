﻿using PetVet.Api.Services;
using PetVet.Shared.DTOs;

namespace PetVet.Api.Endpoints
{
    public static class MessageEndpoints
    {
        public static IEndpointRouteBuilder MapMessageEndpoints(this IEndpointRouteBuilder app)
        {
            var MessageGroup = app.MapGroup("/api/messages")
                .RequireAuthorization();

            MessageGroup.MapPost("", async (MessageSendDto dto, MessageService messageService) =>
                Results.Ok(await messageService.SendMessageAsync(dto)));

            MessageGroup.MapGet("/{otherUserId}/{currentUserId}", async (int otherUserId, int currentUserId, MessageService messageService) =>
            {
                var messages = await messageService.GetMessagesAsync(otherUserId, currentUserId);
                return Results.Ok(messages);
            });

            return app;
        }
    }
}
