﻿using PetVet.ServiceContracts.Features.Conversation;
using System.Security.Claims;
using PetVet.Client.Common.Data;
using PetVet.MauiApp.Services;
using Microsoft.EntityFrameworkCore;
using PetVet.Shared.Components.Framework;
using PetVet.Platform.Razor;
namespace Platform.Client.Common.Features.Conversation;
public class ChatMessageClientSideFormDataService : IChatMessageFormDataService
{

    private readonly BaseHttpClient _httpClient;
    private readonly AppDbContext context;
    private readonly ILocalStorageService localStorageService;
    private readonly ChatSyncUpService chatSyncUpService;

    public ChatMessageClientSideFormDataService(BaseHttpClient httpClient, AppDbContext context,
        ILocalStorageService localStorageService, ChatSyncUpService chatThreadsSyncService)
    {
        _httpClient = httpClient;
        this.context = context;
        this.localStorageService = localStorageService;
        this.chatSyncUpService = chatThreadsSyncService;
    }

    public async Task<string> SaveAsync(ChatMessageFormBusinessObject formBusinessObject)
    {
        //return await _httpClient.PostAsJsonAsync<string>($"api/ChatMessageForm/Save", formBusinessObject);

        var userIdString = await localStorageService.GetValue(ClaimTypes.NameIdentifier);
        var userId = Convert.ToInt32(userIdString);
        ArgumentException.ThrowIfNullOrEmpty(formBusinessObject.ConversationId);
     
        var message = new Message()
        {
            Id = Guid.CreateVersion7().ToString(),
            ConversationId = formBusinessObject.ConversationId,
            CreatedAt = DateTime.UtcNow,
            PlainContent = formBusinessObject.Content,
            DeliveryStatus =  PetVet.ServiceContracts.Enums.DeliveryStatus.QueuedToUpSync,
            SenderId = userId
        };

        context.Messages.Add(message);
        await context.SaveChangesAsync();

        //todo: cache
        var conversationParticipants = await context.ConversationParticipants.Where(x => x.ConversationId == message.ConversationId).ToListAsync();

        foreach (var participant in conversationParticipants)
        {
            var messageRecipient = await context.MessageRecipients.FirstOrDefaultAsync(x => x.MessageId == message.Id && x.RecipientId == participant.UserId);
            if (messageRecipient == null)
            {
                messageRecipient = new MessageRecipient()
                {
                    Id = Guid.CreateVersion7().ToString().ToLower(),
                    MessageId = message.Id,
                    //UserDeviceId = participant.UserId,
                    RecipientId = participant.UserId,
                    EncryptedContent = message.PlainContent ?? string.Empty,
                };
            }
            context.MessageRecipients.Add(messageRecipient);
            await context.SaveChangesAsync();

        }

        chatSyncUpService.Sync(new ChatSyncItem() { Id = message.Id, SyncType = 1 });
        return message.Id;
    } 

    public async Task<ChatMessageFormBusinessObject?> GetItemByIdAsync(string id)
    {
        return await _httpClient.GetFromJsonAsync<ChatMessageFormBusinessObject>($"api/ChatMessageForm/GetItemById?id=" + id);

    }
}
