<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="PetVet.Mobile.Components.CallInvitationPopup"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    BackgroundColor="Transparent"
    NavigationPage.HasNavigationBar="False">

    <!--  Semi-transparent overlay  -->
    <Grid BackgroundColor="#80000000">

        <!--  Call invitation card  -->
        <Frame
            Padding="30"
            BackgroundColor="White"
            CornerRadius="20"
            HasShadow="True"
            HeightRequest="460"
            HorizontalOptions="Center"
            VerticalOptions="Center"
            WidthRequest="320">

            <StackLayout Spacing="20">

                <!--  Caller profile image  -->
                <Border
                    BackgroundColor="#F0F0F0"
                    HeightRequest="100"
                    HorizontalOptions="Center"
                    WidthRequest="100">
                    <Border.StrokeShape>
                        <RoundRectangle CornerRadius="50" />
                    </Border.StrokeShape>
                    <Image
                        x:Name="CallerImage"
                        Aspect="AspectFill"
                        Source="default_avatar.png" />
                </Border>

                <!--  Incoming call text  -->
                <Label
                    FontAttributes="Bold"
                    FontSize="18"
                    HorizontalOptions="Center"
                    Text="Incoming Call"
                    TextColor="#333333" />

                <!--  Caller name  -->
                <Label
                    x:Name="CallerNameLabel"
                    FontAttributes="Bold"
                    FontSize="24"
                    HorizontalOptions="Center"
                    Text="Dr. Smith"
                    TextColor="#1F2937" />

                <!--  Call type  -->
                <Label
                    x:Name="CallTypeLabel"
                    FontSize="16"
                    HorizontalOptions="Center"
                    Text="Video Call"
                    TextColor="#6B7280" />

                <!--  Animated ringing indicator  -->
                <StackLayout
                    x:Name="RingingIndicator"
                    HorizontalOptions="Center"
                    Orientation="Horizontal"
                    Spacing="5">
                    <Ellipse
                        x:Name="Dot1"
                        Fill="#FEA195"
                        HeightRequest="8"
                        WidthRequest="8" />
                    <Ellipse
                        x:Name="Dot2"
                        Fill="#FEA195"
                        HeightRequest="8"
                        WidthRequest="8" />
                    <Ellipse
                        x:Name="Dot3"
                        Fill="#FEA195"
                        HeightRequest="8"
                        WidthRequest="8" />
                </StackLayout>

                <!--  Action buttons  -->
                <Grid
                    Margin="0,20,0,0"
                    ColumnDefinitions="*,*"
                    ColumnSpacing="20">

                    <!--  Decline button  -->
                    <Button
                        x:Name="DeclineButton"
                        Grid.Column="0"
                        BackgroundColor="#EF4444"
                        Clicked="OnDeclineClicked"
                        CornerRadius="25"
                        FontAttributes="Bold"
                        FontSize="16"
                        HeightRequest="50"
                        Text="Decline"
                        TextColor="White" />

                    <!--  Accept button  -->
                    <Button
                        x:Name="AcceptButton"
                        Grid.Column="1"
                        BackgroundColor="#8FBFA8"
                        Clicked="OnAcceptClicked"
                        CornerRadius="25"
                        FontAttributes="Bold"
                        FontSize="16"
                        HeightRequest="50"
                        Text="Accept"
                        TextColor="White" />
                </Grid>

            </StackLayout>
        </Frame>
    </Grid>
</ContentPage>
