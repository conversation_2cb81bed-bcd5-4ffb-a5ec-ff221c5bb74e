{"Twilio": {"AccountSid": "**********************************", "AuthToken": "ac949af1a7f7165e10f2c1f8307c0140  ", "FromPhoneNumber": "+***********", "EnableNotifications": true, "EnableRetryLogic": true, "DefaultRetryCount": 3, "RetryIntervalMinutes": 5, "MaxMessageLength": 160, "EnableDeliveryReports": true, "WebhookUrl": "https://your-domain.com/api/twilio/webhook", "WebhookVerifyToken": "YOUR_WEBHOOK_VERIFY_TOKEN"}, "SmsNotifications": {"EnableVetNotifications": true, "EnablePetOwnerNotifications": true, "EnableStatusUpdates": true, "EnableReminderNotifications": false, "DefaultCountryCode": "+92", "MessageTemplates": {"VetNewAppointment": "PetVet: New appointment with {PetOwnerName} on {Date} at {Time}. {Notes}", "AppointmentConfirmed": "PetVet: Your appointment with Dr. {<PERSON>et<PERSON><PERSON>} is confirmed for {Date} at {Time}. Clinic: {ClinicName}", "AppointmentCancelled": "PetVet: Your appointment on {Date} at {Time} has been cancelled.", "AppointmentRescheduled": "PetVet: Your appointment has been rescheduled to {Date} at {Time}.", "AppointmentCompleted": "PetVet: Your appointment on {Date} at {Time} is completed. Thank you!"}}}