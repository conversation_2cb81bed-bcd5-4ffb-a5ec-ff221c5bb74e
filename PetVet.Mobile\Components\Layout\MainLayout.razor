﻿@inherits LayoutComponentBase
@inject PetVetAuthStateProvider _PetVetAuthStateProvider
@inject NavigationManager _NavigationManager
@inject IAppState _AppState
@inject IUserApi _UserApi
@inject IRecentActivityApi _RecentActivityApi
@inject IAppointmentApi _AppointmentApi

@using PetVet.Shared
@using PetVet.Shared.DTOs
@using PetVet.Shared.Components.Apis
@using PetVet.Mobile.Components.Shared

<!-- Modern Mobile-First Layout -->
<div class="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-blue-50 flex flex-col">

    <!-- Top Header -->
    <header class="bg-white/80 backdrop-blur-md border-b border-white/20 px-4 py-3 flex items-center justify-between sticky top-0 z-40">
        <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-gradient-to-r from-teal-400 to-blue-500 rounded-full flex items-center justify-center">
                <i class="fas fa-paw text-white text-lg"></i>
            </div>
            <div>
                <h1 class="text-lg font-bold text-gray-800">PetVet</h1>
                <p class="text-xs text-gray-500">Care System</p>
            </div>
        </div>

        <div class="flex items-center gap-3">
            <!-- Notifications - Show for both PetOwner and Vet -->
            @if (ShouldShowNotifications())
            {
                <button @onclick="ToggleNotifications" class="relative p-2 rounded-full bg-white shadow-sm border border-gray-100 hover:shadow-md transition-all">
                    <i class="fas fa-bell text-gray-600"></i>
                    @if (GetNotificationCount() > 0)
                    {
                        <span class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full text-xs text-white flex items-center justify-center">@GetNotificationCount()</span>
                    }
                </button>
            }

            <!-- Profile Menu -->
            <button @onclick="ToggleProfileMenu" class="flex items-center gap-2 p-1 rounded-full bg-white shadow-sm border border-gray-100 hover:shadow-md transition-all">
                @if (HasValidProfileImage())
                {
                    <img src="@GetProfileImageUrl()" alt="Profile" class="w-8 h-8 rounded-full object-cover"
                         onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                    <div class="w-8 h-8 rounded-full bg-gradient-to-r from-primary-400 to-secondary-500 flex items-center justify-center" style="display: none;">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                }
                else
                {
                    <div class="w-8 h-8 rounded-full bg-gradient-to-r from-primary-400 to-secondary-500 flex items-center justify-center">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                }
                <i class="fas fa-chevron-down text-gray-400 text-xs mr-2"></i>
            </button>
        </div>
    </header>

    <!-- Notifications Dropdown -->
    @if(_notificationsOpened)
    {
        <div class="fixed inset-0 z-50" @onclick="ToggleNotifications">
            <div class="absolute top-16 right-16 bg-white rounded-2xl shadow-xl border border-gray-100 py-2 w-80 max-h-96 overflow-y-auto" @onclick:stopPropagation="true">
                
                @if (currentUserProfile?.Role == nameof(UserRole.PetOwner))
                {
                    <!-- PetOwner Notification Center -->
                    <div class="px-4 py-3 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-800">Notification Center</h3>
                            @if (recentActivities?.Count > 0)
                            {
                                <span class="text-xs text-gray-500">@recentActivities.Count notification(s)</span>
                            }
                        </div>
                    </div>
                    
                    <div class="max-h-80 overflow-y-auto">
                        @if (recentActivities?.Any() == true)
                        {
                            @foreach (var activity in recentActivities.Take(10))
                            {
                                <div class="px-4 py-3 hover:bg-gray-50 transition-colors border-b border-gray-50 last:border-b-0">
                                    <div class="flex items-start gap-3">
                                        <div class="w-8 h-8 @activity.BackgroundColor rounded-full flex items-center justify-center flex-shrink-0">
                                            <i class="@activity.Icon @activity.IconColor text-xs"></i>
                                        </div>
                                        <div class="flex-1 min-w-0">
                                            <p class="font-medium text-gray-800 text-sm">@activity.Title</p>
                                            <p class="text-gray-500 text-xs leading-relaxed">@activity.Description</p>
                                            <p class="text-gray-400 text-xs mt-1">@activity.TimeAgo</p>
                                        </div>
                                    </div>
                                </div>
                            }
                        }
                        else
                        {
                            <!-- Empty State -->
                            <div class="px-4 py-8 text-center">
                                <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-bell text-gray-400 text-lg"></i>
                                </div>
                                <p class="text-gray-600 text-sm font-medium mb-1">No recent activity</p>
                                <p class="text-gray-500 text-xs">Your recent activities will appear here</p>
                            </div>
                        }
                    </div>
                    
                    @if (recentActivities?.Count > 10)
                    {
                        <div class="px-4 py-3 border-t border-gray-100">
                            <button @onclick="NavigateToAllActivities" class="w-full text-primary-600 text-sm font-medium hover:text-primary-700 transition-colors">
                                View All Activities
                            </button>
                        </div>
                    }
                }
                else if (currentUserProfile?.Role == nameof(UserRole.Vet))
                {
                    <!-- Vet Notification Center -->
                    <div class="px-4 py-3 border-b border-gray-100">
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-800">Notification Center</h3>
                            @{
                                var todayCount = GetTodayAppointments().Count;
                                var upcomingCount = GetUpcomingAppointments().Count;
                                var totalCount = todayCount + upcomingCount;
                            }
                            @if (totalCount > 0)
                            {
                                <span class="text-xs text-gray-500">@totalCount appointment(s)</span>
                            }
                        </div>
                    </div>
                    
                    <div class="max-h-80 overflow-y-auto">
                        @{
                            var todayAppointments = GetTodayAppointments();
                            var upcomingAppointments = GetUpcomingAppointments();
                        }
                        
                        @if (todayAppointments.Any() || upcomingAppointments.Any())
                        {
                            @if (todayAppointments.Any())
                            {
                                <div class="px-4 py-2 bg-green-50">
                                    <p class="text-xs font-semibold text-green-700 uppercase tracking-wide">Today (@todayAppointments.Count)</p>
                                </div>
                                @foreach (var appointment in todayAppointments.Take(3))
                                {
                                    <div class="px-4 py-3 hover:bg-gray-50 transition-colors border-b border-gray-50">
                                        <div class="flex items-start gap-3">
                                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                                                <i class="fas fa-calendar-check text-green-600 text-xs"></i>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <p class="font-medium text-gray-800 text-sm">@appointment.PetOwnerName</p>
                                                <p class="text-gray-500 text-xs">@appointment.Time - @appointment.Status</p>
                                                @if (!string.IsNullOrEmpty(appointment.Notes))
                                                {
                                                    <p class="text-gray-400 text-xs mt-1 truncate">@appointment.Notes</p>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                }
                            }
                            
                            @if (upcomingAppointments.Any())
                            {
                                <div class="px-4 py-2 bg-blue-50">
                                    <p class="text-xs font-semibold text-blue-700 uppercase tracking-wide">Upcoming (@upcomingAppointments.Count)</p>
                                </div>
                                @foreach (var appointment in upcomingAppointments.Take(3))
                                {
                                    <div class="px-4 py-3 hover:bg-gray-50 transition-colors border-b border-gray-50">
                                        <div class="flex items-start gap-3">
                                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                                                <i class="fas fa-clock text-blue-600 text-xs"></i>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <p class="font-medium text-gray-800 text-sm">@appointment.PetOwnerName</p>
                                                <p class="text-gray-500 text-xs">@FormatAppointmentDate(appointment.AppointmentDate) - @appointment.Time</p>
                                                @if (!string.IsNullOrEmpty(appointment.Notes))
                                                {
                                                    <p class="text-gray-400 text-xs mt-1 truncate">@appointment.Notes</p>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                }
                            }
                        }
                        else
                        {
                            <!-- Empty State -->
                            <div class="px-4 py-8 text-center">
                                <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-calendar-times text-gray-400 text-lg"></i>
                                </div>
                                <p class="text-gray-600 text-sm font-medium mb-1">No appointments today</p>
                                <p class="text-gray-500 text-xs">Your appointments will appear here</p>
                            </div>
                        }
                    </div>
                    
                    <div class="px-4 py-3 border-t border-gray-100">
                        <button @onclick="NavigateToVetAppointments" class="w-full text-primary-600 text-sm font-medium hover:text-primary-700 transition-colors">
                            View All Appointments
                        </button>
                    </div>
                }
            </div>
        </div>
    }

    <!-- Profile Dropdown -->
    @if(_profileMenuOpened)
    {
        <div class="fixed inset-0 z-50" @onclick="ToggleProfileMenu">
            <div class="absolute top-16 right-4 bg-white rounded-2xl shadow-xl border border-gray-100 py-2 min-w-48">
                <a href="/@currentUserProfile?.Role/account" class="flex items-center gap-3 px-4 py-3 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-user text-gray-500"></i>
                    <span class="text-gray-700">@currentUserProfile?.Name - @currentUserProfile?.Role</span>
                </a>
                <button @onclick="NavigateToSettings" class="w-full flex items-center gap-3 px-4 py-3 hover:bg-gray-50 transition-colors text-left">
                    <i class="fas fa-cog text-gray-500"></i>
                    <span class="text-gray-700">Settings</span>
                </button>
                <hr class="my-2 border-gray-100">
                <button @onclick="LogoutAsync" class="w-full flex items-center gap-3 px-4 py-3 hover:bg-red-50 transition-colors text-left">
                    <i class="fas fa-sign-out-alt text-red-500"></i>
                    <span class="text-red-600">Logout</span>
                </button>
            </div>
        </div>
    }

    <!-- Main Content Area -->
    <main class="flex-1 pb-20 overflow-y-auto">
        <div class="max-w-md mx-auto">
            @Body
        </div>
    </main>

    <!-- Bottom Navigation -->
    <NavMenu />

    <!-- AI Chatbot - Only show on PetOwner home page -->
    @if (ShouldShowChatbot())
    {
        <Chatbot @ref="chatbotComponent"
                 OnUserMessage="@HandleUserMessage"
                 OnAIResponse="@HandleAIResponse" />
    }

    <!-- Floating Action Button - Only show on home pages -->
    @if (ShouldShowPlusButton())
    {
        <button @onclick="ShowQuickActions" class="fixed bottom-24 left-6 w-14 h-14 bg-gradient-to-r from-primary-400 to-secondary-500 rounded-full shadow-lg flex items-center justify-center z-30 hover:shadow-xl transition-all transform hover:scale-105">
            <i class="fas fa-plus text-white text-xl"></i>
        </button>
    }

    <!-- Quick Actions Modal -->
    @if(_quickActionsOpened)
    {
        <div class="fixed inset-0 bg-black/50 z-50 flex items-end" @onclick="ShowQuickActions">
            <div class="bg-white rounded-t-3xl w-full p-6 transform transition-transform" @onclick:stopPropagation="true">
                <div class="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-6"></div>
                <h3 class="text-lg font-bold text-gray-800 mb-4">Quick Actions</h3>

                <div class="grid grid-cols-2 gap-4">
                    <AuthorizeView Roles="@nameof(UserRole.PetOwner)">
                        <button @onclick="NavigateToAddPet"
                                class="flex flex-col items-center gap-2 p-4 bg-primary-50 rounded-2xl hover:bg-primary-100 transition-colors">
                            <i class="fas fa-plus-circle text-primary-500 text-2xl"></i>
                            <span class="text-sm font-medium text-primary-700">Add Pet</span>
                        </button>

                        <button @onclick="NavigateToBookAppointment"
                                class="flex flex-col items-center gap-2 p-4 bg-secondary-50 rounded-2xl hover:bg-secondary-100 transition-colors">
                            <i class="fas fa-calendar-plus text-secondary-500 text-2xl"></i>
                            <span class="text-sm font-medium text-secondary-700">Book Appointment</span>
                        </button>

                        <button @onclick="NavigateToBreedRecognition"
                                class="flex flex-col items-center gap-2 p-4 bg-primary-50 rounded-2xl hover:bg-primary-100 transition-colors">
                            <i class="fas fa-camera text-primary-500 text-2xl"></i>
                            <span class="text-sm font-medium text-primary-700">Breed Recognition</span>
                        </button>

                        <button @onclick="NavigateToEmergency"
                                class="flex flex-col items-center gap-2 p-4 bg-purple-50 rounded-2xl hover:bg-purple-100 transition-colors">
                            <i class="fas fa-lightbulb text-purple-500 text-2xl"></i>
                            <span class="text-sm font-medium text-purple-700">Vet Tips</span>
                        </button>
                    </AuthorizeView>

                    <AuthorizeView Roles="@nameof(UserRole.Vet)">
                        <button @onclick="NavigateToAddSchedule"
                                class="flex flex-col items-center gap-2 p-4 bg-primary-50 rounded-2xl hover:bg-primary-100 transition-colors">
                            <i class="fas fa-clock text-primary-500 text-2xl"></i>
                            <span class="text-sm font-medium text-primary-700">Add Schedule</span>
                        </button>

                        <button @onclick="NavigateToViewAppointments"
                                class="flex flex-col items-center gap-2 p-4 bg-secondary-50 rounded-2xl hover:bg-secondary-100 transition-colors">
                            <i class="fas fa-calendar-check text-secondary-500 text-2xl"></i>
                            <span class="text-sm font-medium text-secondary-700">View Appointments</span>
                        </button>
                    </AuthorizeView>
                </div>

                <button @onclick="ShowQuickActions" class="w-full mt-6 py-3 bg-gray-100 rounded-2xl text-gray-600 font-medium">
                    Cancel
                </button>
            </div>
        </div>
    }
</div>

@code {
    private bool _profileMenuOpened = false;
    private bool _quickActionsOpened = false;
    private bool _notificationsOpened = false;
    private UserDto? currentUserProfile;
    private Chatbot? chatbotComponent;
    private List<RecentActivityDto> recentActivities = new();
    private List<AppointmentDto> vetAppointments = new();

    protected override async Task OnInitializedAsync()
    {
        if (_PetVetAuthStateProvider.IsLoggedIn && _PetVetAuthStateProvider.User != null)
        {
            await LoadCurrentUserProfile();
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && _PetVetAuthStateProvider.IsLoggedIn && currentUserProfile == null)
        {
            // Try loading again after first render if not loaded
            await LoadCurrentUserProfile();
        }
    }

    private async Task LoadCurrentUserProfile()
    {
        try
        {
            var userId = _PetVetAuthStateProvider.User.Id;
            var userRole = _PetVetAuthStateProvider.User.Role;

            if (userRole == nameof(UserRole.PetOwner))
            {
                currentUserProfile = await _UserApi.GetCurrentPetOwnerUserAsync(userId);
                
                // Load recent activities for PetOwner
                await LoadRecentActivities();
            }
            else if (userRole == nameof(UserRole.Vet))
            {
                currentUserProfile = await _UserApi.GetCurrentVetUserAsync(userId);
                
                // Load appointments for Vet
                await LoadVetAppointments();
            }

            // Force UI update after loading profile
            StateHasChanged();
        }
        catch (Exception)
        {
            // Handle error silently, use default profile
        }
    }

    private async Task LoadRecentActivities()
    {
        try
        {
            if (_PetVetAuthStateProvider.IsLoggedIn && 
                _PetVetAuthStateProvider.User?.Role == nameof(UserRole.PetOwner))
            {
                var userId = _PetVetAuthStateProvider.User.Id;
                recentActivities = await _RecentActivityApi.GetPetOwnerRecentActivitiesAsync(userId);
                StateHasChanged();
            }
        }
        catch (Exception)
        {
            // Handle error silently, keep empty list
            recentActivities = new List<RecentActivityDto>();
        }
    }

    private async Task LoadVetAppointments()
    {
        try
        {
            if (_PetVetAuthStateProvider.IsLoggedIn && 
                _PetVetAuthStateProvider.User?.Role == nameof(UserRole.Vet))
            {
                var userId = _PetVetAuthStateProvider.User.Id;
                vetAppointments = await _AppointmentApi.GetVetAppointmentsAsync(userId);
                StateHasChanged();
            }
        }
        catch (Exception)
        {
            // Handle error silently, keep empty list
            vetAppointments = new List<AppointmentDto>();
        }
    }

    private bool HasValidProfileImage()
    {
        return !string.IsNullOrEmpty(currentUserProfile?.ImageUrl) &&
               !string.IsNullOrWhiteSpace(currentUserProfile.ImageUrl);
    }

    private string GetProfileImageUrl()
    {
        if (currentUserProfile?.ImageUrl == null) return "";

        var imageUrl = currentUserProfile.ImageUrl.Trim();

        // Use the same logic as PetOwnerAccount.razor
        return imageUrl.StartsWith("data:") ? imageUrl : $"data:image/jpeg;base64,{imageUrl}";
    }

    private void ToggleProfileMenu()
    {
        _profileMenuOpened = !_profileMenuOpened;
        // Close other dropdowns
        _notificationsOpened = false;
    }

    private void ToggleNotifications()
    {
        _notificationsOpened = !_notificationsOpened;
        // Close other dropdowns
        _profileMenuOpened = false;
    }

    private void ShowQuickActions()
    {
        _quickActionsOpened = !_quickActionsOpened;
        // Close other dropdowns
        _profileMenuOpened = false;
        _notificationsOpened = false;
    }

    private void NavigateToQuickAction(string url)
    {
        _quickActionsOpened = false;
        _NavigationManager.NavigateTo(url);
    }

    // Pet Owner Quick Actions
    private void NavigateToAddPet() => NavigateToQuickAction("/petOwner/myPets/profile/create");
    private void NavigateToBookAppointment() => NavigateToQuickAction("/petOwner/vetList");
    private void NavigateToBreedRecognition() => NavigateToQuickAction("/petOwner/breedRecognition");
    private void NavigateToEmergency() => NavigateToQuickAction("/petOwner/vetList/vetTips");

    // Vet Quick Actions
    private void NavigateToAddSchedule() => NavigateToQuickAction("/vet/addSchedule");
    private void NavigateToViewAppointments() => NavigateToQuickAction("/vet/viewAppointments");

    private async Task LogoutAsync()
    {
        _profileMenuOpened = false;
        _AppState.ShowLoader("Logging out");
        await _PetVetAuthStateProvider.SetLogoutAsync();
        _NavigationManager.NavigateTo("/auth/login", replace: true);
        _AppState.HideLoader();
    }

    // Chatbot Event Handlers
    private async Task HandleUserMessage(string message)
    {
        // Log user message for analytics (optional)
        Console.WriteLine($"User message: {message}");

        // Here you can add additional logic like:
        // - Logging to analytics
        // - Preprocessing the message
        // - Adding context from current user/page

        // The actual AI response will be handled in the Chatbot component
        // where you'll integrate with Azure OpenAI API
    }

    private async Task HandleAIResponse(string response)
    {
        // Log AI response for analytics (optional)
        Console.WriteLine($"AI response: {response}");

        // Here you can add additional logic like:
        // - Logging to analytics
        // - Post-processing the response
        // - Triggering notifications
    }

    // Public method to open chatbot from other components
    public async Task OpenChatbot()
    {
        if (chatbotComponent != null)
        {
            await chatbotComponent.OpenChatbot();
        }
    }

    // Public method to send a message to chatbot programmatically
    public async Task SendChatbotMessage(string message)
    {
        if (chatbotComponent != null)
        {
            await chatbotComponent.SendMessage(message);
        }
    }

    // Helper method to determine if chatbot should be shown
    // Only show on PetOwner home page
    private bool ShouldShowChatbot()
    {
        if (!_PetVetAuthStateProvider.IsLoggedIn) return false;
        
        var currentUrl = _NavigationManager.ToBaseRelativePath(_NavigationManager.Uri);
        var userRole = _PetVetAuthStateProvider.User?.Role;
        
        // Only show for PetOwner role and only on home page
        return userRole == nameof(UserRole.PetOwner) && 
               (currentUrl == "petOwner/home" || currentUrl == "/petOwner/home");
    }

    // Helper method to determine if plus button should be shown
    // Only show on home pages for both PetOwner and Vet
    private bool ShouldShowPlusButton()
    {
        if (!_PetVetAuthStateProvider.IsLoggedIn) return false;
        
        var currentUrl = _NavigationManager.ToBaseRelativePath(_NavigationManager.Uri);
        var userRole = _PetVetAuthStateProvider.User?.Role;
        
        // Show on home pages for both roles
        return (userRole == nameof(UserRole.PetOwner) && 
                (currentUrl == "petOwner/home" || currentUrl == "/petOwner/home")) ||
               (userRole == nameof(UserRole.Vet) && 
                (currentUrl == "vet/home" || currentUrl == "/vet/home"));
    }

    // Helper method to determine if notifications should be shown
    // Show for both PetOwner and Vet roles
    private bool ShouldShowNotifications()
    {
        if (!_PetVetAuthStateProvider.IsLoggedIn) return false;
        
        var userRole = _PetVetAuthStateProvider.User?.Role;
        return userRole == nameof(UserRole.PetOwner) || userRole == nameof(UserRole.Vet);
    }

    // Get notification count based on user role
    private int GetNotificationCount()
    {
        var userRole = _PetVetAuthStateProvider.User?.Role;
        if (userRole == nameof(UserRole.PetOwner))
        {
            return recentActivities?.Count ?? 0;
        }
        else if (userRole == nameof(UserRole.Vet))
        {
            var todayCount = GetTodayAppointments().Count;
            var upcomingCount = GetUpcomingAppointments().Count;
            return todayCount + upcomingCount;
        }
        return 0;
    }

    // Get today's appointments for vet
    private List<AppointmentDto> GetTodayAppointments()
    {
        if (vetAppointments == null) return new List<AppointmentDto>();
        
        var today = DateTime.Today;
        return vetAppointments
            .Where(a => DateTime.TryParse(a.AppointmentDate, out var date) && date.Date == today)
            .Where(a => a.Status != nameof(AppointmentStatus.Cancelled))
            .OrderBy(a => a.Time)
            .ToList();
    }

    // Get upcoming appointments for vet (next 7 days, excluding today)
    private List<AppointmentDto> GetUpcomingAppointments()
    {
        if (vetAppointments == null) return new List<AppointmentDto>();
        
        var today = DateTime.Today;
        var nextWeek = today.AddDays(7);
        
        return vetAppointments
            .Where(a => DateTime.TryParse(a.AppointmentDate, out var date) && 
                       date.Date > today && date.Date <= nextWeek)
            .Where(a => a.Status != nameof(AppointmentStatus.Cancelled))
            .OrderBy(a => DateTime.Parse(a.AppointmentDate))
            .ThenBy(a => a.Time)
            .ToList();
    }

    // Format appointment date for display
    private string FormatAppointmentDate(string dateString)
    {
        if (DateTime.TryParse(dateString, out var date))
        {
            if (date.Date == DateTime.Today)
                return "Today";
            else if (date.Date == DateTime.Today.AddDays(1))
                return "Tomorrow";
            else
                return date.ToString("MMM d");
        }
        return dateString;
    }

    // Navigate to vet appointments page
    private void NavigateToVetAppointments()
    {
        _notificationsOpened = false;
        _NavigationManager.NavigateTo("/vet/appointments");
    }

    // Navigate to settings page based on user role
    private void NavigateToSettings()
    {
        _profileMenuOpened = false;
        
        var userRole = _PetVetAuthStateProvider.User?.Role;
        string settingsUrl = "";
        
        if (userRole == nameof(UserRole.PetOwner))
        {
            settingsUrl = "/petOwner/account?tab=security";
        }
        else if (userRole == nameof(UserRole.Vet))
        {
            settingsUrl = "/vet/account?tab=security";
        }
        
        _NavigationManager.NavigateTo(settingsUrl);
    }

    // Navigate to all activities page
    private void NavigateToAllActivities()
    {
        _notificationsOpened = false;
        _NavigationManager.NavigateTo("/petOwner/home"); // Navigate to home where all activities are shown
    }
}