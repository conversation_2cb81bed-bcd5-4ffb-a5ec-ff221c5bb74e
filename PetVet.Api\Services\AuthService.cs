﻿using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using PetVet.Api.Data;
using PetVet.Api.Data.Entities;
using PetVet.Shared;
using PetVet.Shared.DTOs;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace PetVet.Api.Services
{
    public class AuthService
    {
        private readonly PetVetContext _context;
        private readonly IPasswordHasher<User> _passwordHasher;
        private readonly IConfiguration _configuration;

        public AuthService(PetVetContext context, IPasswordHasher<User> passwordHasher, IConfiguration configuration)
        {
            _context = context;
            _passwordHasher = passwordHasher;
            _configuration = configuration;
        }

        public static Dictionary<string, User> Users { get; set; } = new Dictionary<string, User>();

        public async Task<AuthResponseDto> LoginAsync(LoginDto dto)
        {
            User? user = null;
            if (Users.ContainsKey(dto.Username))
            {
                user = Users[dto.Username];
            }
            else
            {
                user = await _context.Users.Where(u => u.Email == dto.Username).FirstOrDefaultAsync();
                Users.Add(dto.Username, user);
                
            }
            if (user == null)
            {
                //Invalid Username
                return new AuthResponseDto(default, "Invalid Credentials.");
            }

            if (!user.IsApproved)
            {
                return new AuthResponseDto(default, "Your account is not approved.");
            }

            var passwordResult = _passwordHasher.VerifyHashedPassword(user, user.PasswordHash, dto.Password);

            if (passwordResult == PasswordVerificationResult.Failed)
            {
                //Incorrect Password
                return new AuthResponseDto(default, "Invalid Credentials.");
            }

            var jwt = GenerateJwtToken(user);
            var loggedInUser = new LoggedInUser(user.Id, user.Name, user.Role, jwt);
            return new AuthResponseDto(loggedInUser);
        }


        public async Task<PetVetApiResponse> RegisterPetOwnerAsync(PetOwnerDto dto)
        {
            if (await _context.Users.AnyAsync(u => u.Email == dto.Email))
            {
                return PetVetApiResponse.Fail("Email address alreasy exists. Please try logging in.");
            }

            var petOwnerUser = new User
            {
                Email = dto.Email,
                Name = dto.Name,
                Phone = dto.Phone,
                Address = dto.Address,
                Role = nameof(UserRole.PetOwner),
                IsApproved = true,
                ImageUrl = dto.ImageUrl,
            };
            petOwnerUser.PasswordHash = _passwordHasher.HashPassword(petOwnerUser, dto.Password);
            _context.Users.Add(petOwnerUser);

            try
            {
                await _context.SaveChangesAsync();
                return PetVetApiResponse.Success();
            }
            catch (Exception ex)
            {
                return PetVetApiResponse.Fail(ex.Message);
            }
        }


        public async Task<PetVetApiResponse> RegisterVetAsync(VetDto dto)
        {
            if (await _context.Users.AnyAsync(u => u.Email == dto.Email))
            {
                return PetVetApiResponse.Fail("Email address alreasy exists. Please try logging in.");
            }

            var vetUser = new User
            {
                Email = dto.Email,
                Name = dto.Name,
                Phone = dto.Phone,
                Address = dto.Address,
                Role = nameof(UserRole.Vet),
                IsApproved = false,
                ClinicName = dto.ClinicName,
                YearsOfExperience = dto.YearsOfExperience,
                Specialization = dto.Specialization,
                LicenceDocumentUrl = dto.LicenceDocumentUrl,
                CertificationsUrl = dto.CertificationsUrl,
                ImageUrl = dto.ImageUrl,
                IsOnline = dto.IsOnline,
                Rating = dto.Rating,
                EducationId = dto.EducationId,
            };
            vetUser.PasswordHash = _passwordHasher.HashPassword(vetUser, dto.Password);
            _context.Users.Add(vetUser);

            try
            {
                await _context.SaveChangesAsync();
                return PetVetApiResponse.Success();
            }
            catch (Exception ex)
            {
                return PetVetApiResponse.Fail(ex.Message);
            }
        }

        private string GenerateJwtToken(User user)
        {
            Claim[] claims = [
            new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new Claim(ClaimTypes.Name, user.Name),
            new Claim(ClaimTypes.Role, user.Role),
            ];

            var secretKey = _configuration.GetValue<string>("Jwt:Secret");
            var symmetricKey = new SymmetricSecurityKey(System.Text.Encoding.UTF8.GetBytes(secretKey));
            var signinCred = new SigningCredentials(symmetricKey, SecurityAlgorithms.HmacSha256);

            var jwtSecurityToken = new JwtSecurityToken(
                issuer: _configuration.GetValue<string>("Jwt:Issuer"),
                audience: _configuration.GetValue<string>("Jwt:Audience"),
                claims:
                claims,
                expires: DateTime.UtcNow.AddDays(_configuration.GetValue<int>("Jwt:ExpiresInDays")),
                signingCredentials: signinCred
            );

            var token = new JwtSecurityTokenHandler().WriteToken(jwtSecurityToken);

            return token;
        }

        public async Task<PetVetApiResponse> ChangePasswordAsync(ChangePasswordDto dto)
        {
            var user = await _context.Users.FirstOrDefaultAsync(u => u.Id == dto.UserId);

            if (user == null)
            {
                return PetVetApiResponse.Fail("User not found.");
            }

            // Verify current password
            var passwordResult = _passwordHasher.VerifyHashedPassword(user, user.PasswordHash, dto.CurrentPassword);
            if (passwordResult == PasswordVerificationResult.Failed)
            {
                return PetVetApiResponse.Fail("Current password is incorrect.");
            }

            // Update password
            user.PasswordHash = _passwordHasher.HashPassword(user, dto.NewPassword);

            try
            {
                await _context.SaveChangesAsync();
                return PetVetApiResponse.Success();
            }
            catch (Exception ex)
            {
                return PetVetApiResponse.Fail($"Error changing password: {ex.Message}");
            }
        }
    }
}
