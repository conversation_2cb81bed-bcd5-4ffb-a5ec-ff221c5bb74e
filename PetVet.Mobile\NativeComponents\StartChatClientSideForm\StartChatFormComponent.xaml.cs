﻿using Platform.Client.Common.Features.Conversation.ChatThreads.Form;
using PetVet.MauiShared;
using PetVet.ServiceContracts.Features.Conversation;
namespace Platform.Client.Common.Features.Conversation;
public class StartChatFormViewBase : FormBaseMaui<StartChatFormBusinessObject, StartChatFormViewModel, string, IStartChatFormDataService>
{
    public StartChatFormViewBase(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
    }
}

public partial class StartChatFormView : StartChatFormViewBase
{
    public StartChatFormView(IServiceScopeFactory scopeFactory, string key) : base(scopeFactory, key)
    {
        InitializeComponent();
        BindingContext = this;
    }
}
