﻿.Schedule-container {
    background: #f2f6f9;
    padding: 2rem;
    min-height: 100vh;
    width: 100%;
    max-width: 72rem;
    margin: 0 auto;
    font-family: 'Inter', sans-serif;
}

.Schedule-HEADER {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #157BAB;
    border-radius: 1.25rem;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.Schedule-CARD {
    border-radius: 1.25rem;
    width: 95%;
}

.Schedule-HEADING {
    font-size: 1.5rem;
    font-family: 'Acme', sans-serif;
    font-weight: bold;
    cursor: default;
    margin: 0;
}

.Schedule-CARD-BODY {
    padding: 2rem;
}

.working-days,
.time-slots {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1rem;
}

h4 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    font-family: 'Acme', sans-serif;
}

.day-item,
.time-slot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 1rem;
    margin-bottom: 0.5rem;
    background-color: white;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

    .day-item:hover,
    .time-slot:hover {
        background-color: #e9ecef;
        transform: translateX(5px);
    }

    .day-item.selected,
    .time-slot.selected {
        background-color: #157BAB;
        color: white;
    }

        .day-item.selected i,
        .time-slot.selected i {
            color: #fff;
        }

.time-slots-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.5rem;
}

.schedule-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.schedule-day {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1rem;
}

    .schedule-day h5 {
        color: #2c3e50;
        margin-bottom: 1rem;
        font-family: 'Acme', sans-serif;
    }

.day-slots {
    display: grid;
    gap: 0.5rem;
}

.slot-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: white;
    border-radius: 0.25rem;
    border: 1px solid #e9ecef;
}

    .slot-item.booked {
        background-color: #fef2f2;
        border-color: #fee2e2;
        opacity: 0.7;
    }

.booked-badge {
    background-color: #ef4444;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
}

.Save-BTN {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    background: #157BAB;
    color: white;
    border: none;
    border-radius: 1.25rem;
    
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 180px;
}

    .Save-BTN:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.5);
    }

@media (max-width: 768px) {
    .Schedule-container {
        padding: 1rem;
    }

    .Schedule-CARD-BODY {
        padding: 1rem;
    }

    .Schedule-HEADING {
        font-size: 1.25rem;
    }

    .time-slots-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }

    .schedule-grid {
        grid-template-columns: 1fr;
    }

    .day-item,
    .time-slot {
        padding: 0.5rem 0.75rem;
    }

    .Save-BTN {
        width: 100%;
    }
}

.schedule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: background-color 0.3s ease;
    margin-bottom: 1rem;
}

    .schedule-header:hover {
        background-color: #e9ecef;
    }

    .schedule-header h4 {
        margin: 0;
        color: #2c3e50;
        font-family: 'Acme', sans-serif;
    }

    .schedule-header i {
        font-size: 1.25rem;
        color: #4A90E2;
        transition: transform 0.3s ease;
    }

.schedule-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
    transition: all 0.3s ease;
}


/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.Schedule-container {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
