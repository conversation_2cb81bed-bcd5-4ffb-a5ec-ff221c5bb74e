﻿@inherits LayoutComponentBase
@inject PetVetAuthStateProvider _PetVetAuthStateProvider

<div class="page">
    <div class="sidebar">
        <NavMenu />
    </div>

    <main>
        <article class="content" style="padding: 0 !important;">
            @if(_PetVetAuthStateProvider.IsInitializing)
            {
                <div class="alert alert-warning text-center">
                    <p class="m-0">Initializing State...</p>
                </div>
            }
            else
            {
                @Body
            }
        </article>
    </main>
</div>


@code{
    protected override async Task OnInitializedAsync()
    {
        while(_PetVetAuthStateProvider.IsInitializing)
        {
            await Task.Delay(500);
        }
    }
}