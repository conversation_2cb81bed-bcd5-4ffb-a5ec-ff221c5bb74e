﻿using System.ComponentModel.DataAnnotations;

namespace PetVet.Shared.DTOs
{
    public class PetOwnerDto
    {
        public int Id { get; set; }

        [Required, MaxLength(20)]
        public string Name { get; set; } = string.Empty;

        [Required, EmailAddress, DataType(DataType.EmailAddress)]
        public string Email { get; set; } = string.Empty;

        [Required, StringLength(15, MinimumLength = 10)]
        public string Phone { get; set; } = string.Empty;

        [Required, MaxLength(250)]
        public string Password { get; set; } = string.Empty;

        [MaxLength(15)]
        public string Role { get; set; } = string.Empty;

        [Required, MaxLength(250)]
        public string Address { get; set; } = string.Empty;

        public string? ImageUrl { get; set; }
    }
}
