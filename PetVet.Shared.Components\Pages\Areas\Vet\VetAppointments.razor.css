.Appointments-CONTAINER {
    background: #f2f6f9;
    padding: 2rem;
    min-height: 100vh;
    width: 100%;
    max-width: 72rem;
    margin: 0 auto;
    font-family: 'Inter', sans-serif;
}

/* Header Section */
.header-section {
    background: #157BAB;
    border-radius: 1.25rem;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    text-align: center;
}

.header-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    font-family: 'Acme', sans-serif;
}

.header-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    
}

/* Stats Section */
.stats-section {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    background-color: white;
    border-radius: 1.25rem;
    padding: 1.5rem;
    margin: 2rem 0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.stats-icon {
    width: 2.5rem;
    height: 2.5rem;
    margin-bottom: 0.75rem;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.stats-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #3B82F6;
    font-size: 0.875rem;
    font-weight: 600;
    text-align: center;
    transition: transform 0.3s ease;
    font-family: 'Acme', sans-serif;
}

    .stats-item:hover {
        transform: scale(1.05);
    }

.stats-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.stats-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1F2937;
    font-family: 'Acme', sans-serif;
}

.stats-label {
    font-size: 1rem;
    color: #6B7280;
    
}

/* Filter Section */
.filter-section {
    background: white;
    border-radius: 1.25rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.status-filters {
    display: flex;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 0.75rem 1.25rem;
    border: none;
    border-radius: 1.25rem;
    background: #F3F4F6;
    color: #4B5563;
    
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

    .filter-btn:hover,
    .filter-btn.active {
        background: #157BAB;
        color: white;
        transform: translateY(-2px);
    }

.date-filter {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: flex-end;
}

.date-inputs {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    flex: 1;
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    flex: 1;
}

.input-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4B5563;
    font-family: 'Acme', sans-serif;
}

.date-input {
    padding: 0.75rem;
    border: 1px solid #E5E7EB;
    border-radius: 0.75rem;
    
}

.filter-actions {
    display: flex;
    gap: 0.75rem;
}

/* Appointments List */
.appointments-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    background: white;
    border-radius: 1.25rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.empty-icon {
    font-size: 3rem;
    color: #3B82F6;
    margin-bottom: 1rem;
}

.appointment-card {
    display: flex;
    align-items: stretch;
    background: white;
    border-radius: 1.25rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

    .appointment-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.15);
    }

.appointment-date {
    background: #157BAB;
    color: white;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 150px;
}

.date-badge {
    text-align: center;
    margin-bottom: 0.5rem;
}

.month {
    font-size: 0.875rem;
    text-transform: uppercase;
    font-weight: 600;
    opacity: 0.9;
    font-family: 'Acme', sans-serif;
}

.day {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
    font-family: 'Acme', sans-serif;
}

.time {
    font-size: 0.875rem;
    opacity: 0.9;
}

.status-badge {
    margin-top: 0.5rem;
    padding: 0.25rem 0.75rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
}

.appointment-info {
    flex: 1;
    padding: 1.5rem;
}

.patient-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 0.5rem;
    font-family: 'Acme', sans-serif;
}

.notes {
    color: #6B7280;
    font-size: 0.875rem;
    
}

.appointment-actions {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    padding: 1.5rem;
    border-left: 1px solid #E5E7EB;
}

/* Action Buttons */
.action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: 1.25rem;
    
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    cursor: pointer;
}

.apply-btn,
.reschedule-btn {
    background: #FFC107;
    color: #1F2937;
}

.reset-btn,
.message-btn {
    background: #157BAB;
    color: white;
}

.cancel-btn {
    background: #EF4444;
    color: white;
}

.calendar-link {
    text-align: right;
    margin-top: 2rem;
}

.calendar-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    background: #157BAB;
    color: white;
    border-radius: 1.25rem;
    text-decoration: none;
    
    font-weight: 600;
    transition: all 0.3s ease;
}

    .calendar-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.5);
    }

/* Status Colors */
.status-confirmed {
    border-left: 4px solid #3B82F6;
}

.status-completed {
    border-left: 4px solid #10B981;
}

.status-cancelled {
    border-left: 4px solid #EF4444;
}

.status-expired {
    border-left: 4px solid #6B7280;
}

/* Responsive Design */
@media (max-width: 768px) {
    .Appointments-CONTAINER {
        padding: 1rem;
    }

    .header-section {
        padding: 1.5rem;
    }

    .header-title {
        font-size: 1.5rem;
    }

    .stats-section {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
        padding: 1.25rem;
        margin: 1.5rem 0;
    }

    .stats-icon {
        width: 2rem;
        height: 2rem;
        margin-bottom: 0.5rem;
    }

    .appointment-card {
        flex-direction: column;
    }

    .appointment-date {
        flex-direction: row;
        justify-content: space-between;
        min-width: auto;
    }

    .date-badge {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin: 0;
    }

    .appointment-actions {
        flex-wrap: wrap;
        border-left: none;
        border-top: 1px solid #E5E7EB;
    }

    .action-btn {
        flex: 1;
        min-width: 0;
    }

    .calendar-btn {
        width: 100%;
    }

    .filter-section {
        padding: 1rem;
    }

    .status-filters {
        overflow-x: auto;
        padding-bottom: 0.5rem;
        justify-content: center;
    }

    .date-inputs {
        flex-direction: column;
    }

    .filter-actions {
        width: 100%;
    }

    .action-btn {
        flex: 1;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.Appointments-CONTAINER {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
