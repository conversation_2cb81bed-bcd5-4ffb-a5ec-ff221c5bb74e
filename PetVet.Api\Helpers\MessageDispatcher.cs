﻿using FirebaseAdmin.Messaging;
using Microsoft.EntityFrameworkCore; 
using Microsoft.Extensions.Caching.Memory;
using PetVet.Api.Data;
using PetVet.Api.Hubs;
using PetVet.ServiceContracts.Enums;
using PetVet.ServiceContracts.Features.Conversation;
using PetVet.Shared.Enums;
using System.Text.Json;
using System.Threading.Channels;

namespace PetVet.Server.DataServices.Helpers;

public class MessageDispatcher
{
    private Channel<ChatMessagesSyncFormBusinessObject> ChatMessages { get; set; }

    private Channel<ChatMessageUpdate> ChatMessageUpdates { get; set; }

    private readonly Task? _dispatcherTask;
    //private readonly Task? _monitoringTask;
    private readonly IServiceScopeFactory scopeFactory;
    private readonly ChatHub chatHub;

    private ILogger<MessageDispatcher> _logger;

    public MessageDispatcher(IServiceScopeFactory scopeFactory, ChatHub chatHub, ILogger<MessageDispatcher> logger)
    {
        ChatMessages = Channel.CreateUnbounded<ChatMessagesSyncFormBusinessObject>();

        ChatMessageUpdates = Channel.CreateUnbounded<ChatMessageUpdate>();

        _dispatcherTask = Task.Factory.StartNew(async () =>
        {
            var fiveMinutesAgo = DateTime.UtcNow.AddMinutes(-5);
            while (true)
            {
                try
                {
                    var message = await ChatMessages.Reader.ReadAsync();
                    try
                    {
                        var scope = scopeFactory.CreateScope(); 
                        var context = scope.ServiceProvider.GetRequiredService<PetVetContext>();
                        var memoryCache = scope.ServiceProvider.GetRequiredService<IMemoryCache>();

                        var messageRecipients = context.MessageRecipients.Where(x => x.MessageId == message.Id
                                && x.DeliveryStatus < DeliveryStatus.DeliveredToEndUser
                                && x.RecipientId != message.SenderId).ToList();

                        foreach (var recipient in messageRecipients)
                        {
                            var userName = await memoryCache.GetOrCreateAsync($"username_{recipient.RecipientId}", async entry =>
                            {
                                var user = await context.Users.FirstOrDefaultAsync(x => x.Id == recipient.RecipientId);
                                return user?.Name;
                            });  

                            var utcNow = DateTime.UtcNow;
                            var result = await chatHub.SendMessageAsync(recipient.RecipientId, userName,
                            message.Id, message.PlainContent, JsonSerializer.Serialize(message), SignalRMethod.OnNewMessage);
                            if (result)
                            {
                                recipient.DeliveryStatus = DeliveryStatus.SentToEndUserViaSignalR;
                                await context.SaveChangesAsync();


                                await context.ConversationMessages.Where(x => x.Id == message.Id && x.DeliveryStatus < DeliveryStatus.SentToEndUserViaSignalR)
                                 .ExecuteUpdateAsync(x => x
                                 .SetProperty(p => p.DeliveryStatus, DeliveryStatus.SentToEndUserViaSignalR)
                                 .SetProperty(p => p.DeliveryStatusTime, utcNow));

                                DispatchMessageUpdate(new ChatMessageUpdate()
                                {
                                    Id = message.Id,
                                    SenderId = message.SenderId,
                                    DeliveryStatusTime = utcNow,
                                    DeliveryStatus = DeliveryStatus.SentToEndUserViaSignalR,
                                });
                            }
                            else if (message.EnableFallBackChannel)
                            {
                                var deviceToken = await memoryCache.GetOrCreateAsync($"devicetoken_{recipient.RecipientId}", async entry =>
                                {
                                    var temp = await context.UserDevices.Where(x => x.UserId == recipient.RecipientId)
                                    .Select(x => x.DeviceToken).FirstOrDefaultAsync();
                                    return temp;
                                });

                                if (!string.IsNullOrEmpty(deviceToken))
                                {
                                    var fcmMessage = new FirebaseAdmin.Messaging.Message()
                                    {
                                        Token = deviceToken,
                                        Notification = new Notification()
                                        {
                                            Title = "PetVet Message",
                                            Body = message.PlainContent,
                                        },
                                        //Data = new Dictionary<string, string>
                                        //{
                                        //    ["type"] = "Briefly News Updates",
                                        //    ["message"] = newsService.News[Random.Shared.Next(newsService.News.Length)].Title,
                                        //},
                                        Android = new AndroidConfig
                                        {
                                            Priority = Priority.High,            // REQUEST_IMMEDIATE delivery 
                                            CollapseKey = "PetVet Message",                 // optional—only latest in this group
                                        }
                                    };

                                    // 3) Send it
                                    string response = await FirebaseMessaging.DefaultInstance.SendAsync(fcmMessage);
                                    Console.WriteLine($"Successfully sent message: {response}");

                                    recipient.DeliveryStatus = DeliveryStatus.SentToEndUserViaPushNotification;
                                    await context.SaveChangesAsync();

                                    await context.ConversationMessages.Where(x => x.Id == message.Id && x.DeliveryStatus < DeliveryStatus.SentToEndUserViaPushNotification)
                                   .ExecuteUpdateAsync(x => x
                                   .SetProperty(p => p.DeliveryStatus, DeliveryStatus.SentToEndUserViaPushNotification)
                                   .SetProperty(p => p.DeliveryStatusTime, utcNow));

                                    DispatchMessageUpdate(new ChatMessageUpdate()
                                    {
                                        Id = message.Id,
                                        SenderId = message.SenderId,
                                        DeliveryStatusTime = utcNow,
                                        DeliveryStatus = DeliveryStatus.SentToEndUserViaPushNotification,
                                    });
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        //DispatchMessage(message);
                        logger.LogWarning(ex.Message);
                    }
                }
                catch (Exception ex)
                {
                    logger.LogWarning(ex.Message);
                }
            }
        });

        Task.Factory.StartNew(async () =>
        {
            while (true)
            {
                try
                {
                    var message_ = await ChatMessageUpdates.Reader.ReadAsync();
                    var scope = scopeFactory.CreateScope();
                    var memoryCache = scope.ServiceProvider.GetRequiredService<IMemoryCache>();
                    var context = scope.ServiceProvider.GetRequiredService<PetVetContext>(); 
                    var message = await context.ConversationMessages.Where(x => x.Id == message_.Id).FirstAsync();
                    var userName = await memoryCache.GetOrCreateAsync($"username_{message.SenderId}", async entry =>
                    {
                        var user = await context.Users.FirstOrDefaultAsync(x => x.Id == message.SenderId);
                        return user?.Name;
                    });
                    var result = await chatHub.SendMessageAsync(message.SenderId, userName,
                        message.Id, message.DeliveryStatus.ToString(), JsonSerializer.Serialize(message_), SignalRMethod.OnFeedUpdate);
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex);
                }
            }
        });


        //_monitoringTask = Task.Factory.StartNew(async () =>
        // {
        //     while (true)
        //     {
        //         await Task.Delay(10000);
        //         var fiveMinutesAgo = DateTime.UtcNow.AddMinutes(-1);
        //         var scope = scopeFactory.CreateScope();
        //         var context = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        //         var pendingMessages = (from m in context.Messages
        //                                from recipients in context.MessageRecipients.Where(x => x.MessageId == m.Id)
        //                                from user in context.Users.Where(x => x.Id == recipients.RecipientId)
        //                                where
        //                                //m.Id == "0195ffba-4ae1-7a10-8dc2-84f438f42825" &&
        //                                recipients.DeliveryStatus == DeliveryStatus.Pending ||
        //                                (recipients.DeliveryStatus == DeliveryStatus.QueuedToUpSync && recipients.DeliveryStatusTime < fiveMinutesAgo) ||
        //                                 (recipients.DeliveryStatus == DeliveryStatus.SentToEndUserViaSignalR && recipients.DeliveryStatusTime < fiveMinutesAgo)
        //                                orderby m.Id
        //                                select new ChatMessagesSyncFormBusinessObject
        //                                {
        //                                    Id = m.Id,
        //                                    MessageRecepientId = recipients.Id,
        //                                    MessageRecepientUserName = user.UserName,
        //                                    ConversationId = m.ConversationId,
        //                                    SenderId = m.SenderId,
        //                                    CreatedAt = m.CreatedAt,
        //                                    DeletedAt = m.DeletedAt,
        //                                    DisappearAfter = m.DisappearAfter,
        //                                    DisappearAt = m.DisappearAt,
        //                                    EditedAt = m.EditedAt,
        //                                    IsDeleted = m.IsDeleted,
        //                                    IsEdited = m.IsEdited,
        //                                    IsEphemeral = m.IsEphemeral,
        //                                    PlainContent = m.PlainContent,
        //                                }).ToList();
        //         pendingMessages = pendingMessages.DistinctBy(x => x.Id).ToList();
        //         foreach (var item in pendingMessages)
        //         {
        //             DispatchMessage(item);
        //         }

        //         var recipientIds = pendingMessages.Select(x => x.MessageRecepientId).ToList();
        //         await context.MessageRecipients
        //               .Where(x => recipientIds.Contains(x.Id))
        //               .ExecuteUpdateAsync(x => x.SetProperty(p => p.DeliveryStatus, DeliveryStatus.QueuedToUpSync)
        //               .SetProperty(p => p.DeliveryStatusTime, DateTime.UtcNow));
        //     }
        // });

        this.scopeFactory = scopeFactory;
        this.chatHub = chatHub;
        _logger = logger;
    }


    public bool DispatchMessage(ChatMessagesSyncFormBusinessObject chatMessage)
    {
        return ChatMessages.Writer.TryWrite(chatMessage);
    }

    public bool DispatchMessageUpdate(ChatMessageUpdate chatMessageUpdate)
    {
        return ChatMessageUpdates.Writer.TryWrite(chatMessageUpdate);
    }
}