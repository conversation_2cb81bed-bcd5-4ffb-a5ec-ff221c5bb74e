﻿@page "/petOwner/myPets/all"
@using PetVet.Shared.Components.Pages.Areas.PetOwner.Components
@inject NavigationManager _NavigationManager

<PageTitle>All My Pets</PageTitle>

<!-- Modern All Pets Page -->
<div class="px-4 py-6 space-y-6">

    <!-- Header with Back Button -->
    <div class="flex items-center gap-4 mb-6">
        <button @onclick="NavigateBack" class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors">
            <i class="fas fa-arrow-left text-gray-600"></i>
        </button>
        <div class="flex-1">
            <h1 class="text-2xl font-bold text-gray-800">All My Pets</h1>
            <p class="text-gray-500 text-sm">Complete list of your furry friends</p>
        </div>
        <button class="w-10 h-10 bg-teal-500 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all transform hover:scale-105"
                @onclick="NavigateToAddPet">
            <i class="fas fa-plus text-white"></i>
        </button>
    </div>

    <!-- Modern Search Bar -->
    <div class="relative mb-6">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <i class="fas fa-search text-gray-400"></i>
        </div>
        <input type="text"
               class="w-full pl-10 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all"
               placeholder="Search your pets..."
               @bind="searchTerm"
               @bind:event="oninput"
               @onkeyup="HandleSearch" />
    </div>

    <!-- Filter Chips -->
    <div class="flex gap-2 overflow-x-auto pb-2 mb-6">
        <button class="px-4 py-2 bg-teal-500 text-white rounded-full text-sm font-medium whitespace-nowrap">
            All Pets
        </button>
        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm font-medium whitespace-nowrap hover:bg-gray-200 transition-colors">
            Dogs
        </button>
        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm font-medium whitespace-nowrap hover:bg-gray-200 transition-colors">
            Cats
        </button>
        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm font-medium whitespace-nowrap hover:bg-gray-200 transition-colors">
            Birds
        </button>
        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm font-medium whitespace-nowrap hover:bg-gray-200 transition-colors">
            Others
        </button>
    </div>

    <!-- All Pets List -->
    <MyAllPetsList @ref="allPetsList" />

</div>



@code {
    private MyAllPetsList allPetsList;
    private string searchTerm = "";

    protected override void OnInitialized()
    {
        base.OnInitialized();
    }

    private async Task HandleSearch(KeyboardEventArgs e)
    {
        if (allPetsList != null)
        {
            await allPetsList.HandleSearch(searchTerm);
        }
    }

    private void NavigateBack()
    {
        _NavigationManager.NavigateTo("/petOwner/myPets");
    }

    private void NavigateToAddPet()
    {
        _NavigationManager.NavigateTo("/petOwner/addPet");
    }
}


