﻿using PetVet.ServiceContracts.Features.Conversation;
using System.Security.Claims;
using PetVet.Api.Data;
using PetVet.Server.DataServices.Features;
namespace PetVet.Api.Services.ChatMessages.Listing;
public class ChatMessagesServerSideListingDataService : ServerSideListingDataService<ChatMessagesListingBusinessObject, ChatMessagesFilterBusinessObject>, IChatMessagesListingDataService
{

    private readonly PetVetContext _context;
    private readonly IHttpContextAccessor contextAccessor;

    public ChatMessagesServerSideListingDataService(PetVetContext context, IHttpContextAccessor contextAccessor)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
    }

    public override IQueryable<ChatMessagesListingBusinessObject> GetQuery(ChatMessagesFilterBusinessObject filterBusinessObject)
    {
        var userId =Convert.ToInt32(contextAccessor.HttpContext?.User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
        var query = from m in _context.ConversationMessages
                     where m.ConversationId == filterBusinessObject.ConversationId
                     select new ChatMessagesListingBusinessObject
                     {
                         Id = m.Id,
                         Content = m.PlainContent,
                         IsIncoming = m.SenderId != userId,
                         Timestamp = m.CreatedAt,
                     };
        return query;
    }
}
