﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PetVet.Client.Common.Data;

var builder = new ConfigurationBuilder();

var configuration = builder.Build();
var services = new ServiceCollection();
var dbPath = Path.Combine(Path.GetTempPath(), "deep.db");
services.AddDbContext<AppDbContext>(options =>
               options.UseSqlite($"Filename={dbPath}"));

var app = services.BuildServiceProvider();


public class AppDbContextFactory : IDesignTimeDbContextFactory<AppDbContext>
{
    public AppDbContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<AppDbContext>();
        optionsBuilder.UseSqlite("Data Source=mydatabase.db");
        return new AppDbContext(optionsBuilder.Options);
    }
}
