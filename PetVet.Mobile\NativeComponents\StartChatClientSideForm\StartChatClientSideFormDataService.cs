﻿using PetVet.ServiceContracts.Features.Conversation;
using PetVet.Client.Common.Data;
using System.Security.Claims;
using Microsoft.EntityFrameworkCore;
using PetVet.MauiApp.Services;
using PetVet.Shared.Components.Framework;
namespace Platform.Client.Common.Features.Conversation.ChatThreads.Form;
public class StartChatClientSideFormDataService : IStartChatFormDataService
{

    private readonly AppDbContext context;
    private readonly ILocalStorageService localStorageService;
    private readonly ChatSyncUpService chatSyncUpService;

    public StartChatClientSideFormDataService(AppDbContext context, ILocalStorageService localStorageService, ChatSyncUpService chatThreadsSyncService)
    {
        this.context = context;
        this.localStorageService = localStorageService;
        this.chatSyncUpService = chatThreadsSyncService;
    }

    public async Task<string> SaveAsync(StartChatFormBusinessObject formBusinessObject)
    {
        var userIdString = await localStorageService.GetValue(ClaimTypes.NameIdentifier);
        var userId = Convert.ToInt32(userIdString);

        var participant = new int[]
       {
             userId,
            formBusinessObject.FriendId
       };
        var conversationId = $"{participant.OrderBy(x => x).FirstOrDefault()}-{participant.OrderBy(x => x).LastOrDefault()}";
        var conversation = await context.Conversations
                .Where(x => x.Id == conversationId)
                .FirstOrDefaultAsync();

        if (conversation == null)
        {
            conversation = new PetVet.Client.Common.Data.Conversation
            {
                Id = conversationId,
                CreatedAt = DateTime.UtcNow,
                IsDeleted = false,
                Title = "Direct Chat"

            };
            context.Conversations.Add(conversation);

            var participants = new List<ConversationParticipant>
                        {
                            new ConversationParticipant
                            {
                                Id = Guid.NewGuid().ToString(),
                                UserId = userId!,
                                 ConversationId = conversation.Id,
                                IsAdmin = true,
                                JoinedAt = DateTime.UtcNow
                            },
                            new ConversationParticipant
                            {
                                Id = Guid.NewGuid().ToString(),
                                UserId = formBusinessObject.FriendId,
                                 UserName = formBusinessObject.FriendName,
                                  DisplayPictureUrl = formBusinessObject.DisplayPictureUrl,
                                ConversationId = conversation.Id,
                                IsAdmin = false,
                                JoinedAt = DateTime.UtcNow
                            }
                        };
            context.ConversationParticipants.AddRange(participants);
            await context.SaveChangesAsync();

        }
        chatSyncUpService.Sync(new ChatSyncItem() { Id = conversation.Id, SyncType = 0 });
        return conversation.Id;

    }

    public Task<StartChatFormBusinessObject?> GetItemByIdAsync(string id)
    {
        return Task.FromResult((StartChatFormBusinessObject?)null);
    }
}
