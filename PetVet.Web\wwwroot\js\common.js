﻿function ShowConfirmationModal() {
    const modal = document.getElementById("bsConfirmationModal");
    if (modal) {
        modal.classList.remove("hidden");
        modal.classList.add("flex");
        document.body.style.overflow = "hidden";
    }
}

function HideConfirmationModal() {
    const modal = document.getElementById("bsConfirmationModal");
    if (modal) {
        modal.classList.add("hidden");
        modal.classList.remove("flex");
        document.body.style.overflow = "auto";
    }
}

// Close modal when clicking outside
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById("bsConfirmationModal");
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                HideConfirmationModal();
            }
        });
    }
});

// Admin Dashboard Animation Functions
window.initializeCounters = function() {
    const counters = document.querySelectorAll('.counter-animation');
    
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        const increment = target / 60; // Animation duration roughly 1 second at 60fps
        let current = 0;
        
        const updateCounter = () => {
            if (current < target) {
                current += increment;
                counter.textContent = Math.ceil(current);
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target;
            }
        };
        
        // Start animation after a short delay
        setTimeout(() => {
            updateCounter();
        }, 500);
    });
};

window.initializeProgressBars = function() {
    const progressBars = document.querySelectorAll('.loading-bar, .loading-bar-delay-1, .loading-bar-delay-2, .loading-bar-delay-3, .loading-bar-delay-4');
    
    progressBars.forEach((bar, index) => {
        const targetWidth = bar.style.width;
        bar.style.width = '0%';
        
        setTimeout(() => {
            bar.style.transition = 'width 2s ease-out';
            bar.style.width = targetWidth;
        }, index * 300);
    });
};

// Initialize animations when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // These will be called by Blazor after render
});