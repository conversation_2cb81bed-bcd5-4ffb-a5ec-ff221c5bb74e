@using Microsoft.AspNetCore.Components.Web
@inject IJSRuntime JSRuntime

<!-- Chatbot Interface Modal -->
<div class="chatbot-modal @(IsOpen ? "chatbot-modal--open" : "")" @onclick="HandleBackdropClick">
    <div class="chatbot-container" @onclick:stopPropagation="true">
        
        <!-- Header -->
        <div class="chatbot-header">
            <div class="chatbot-header__info">
                <div class="chatbot-header__avatar">
                    <svg width="24" height="24" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                    </svg>
                </div>
                <div class="chatbot-header__text">
                    <h3>PetVet AI Assistant</h3>
                    <p class="chatbot-status @(IsTyping ? "chatbot-status--typing" : "")">
                        @if (IsTyping)
                        {
                            <span>AI is typing</span>
                            <div class="typing-indicator">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        }
                        else
                        {
                            <span>Online • Ready to help</span>
                        }
                    </p>
                </div>
            </div>
            <button @onclick="CloseChatbot" class="chatbot-header__close" aria-label="Close chat">
                <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>

        <!-- Messages Area -->
        <div class="chatbot-messages" @ref="messagesContainer">
            @if (Messages.Count == 0)
            {
                <!-- Welcome Message -->
                <div class="chatbot-welcome">
                    <div class="chatbot-welcome__icon">
                        <svg width="48" height="48" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" 
                                  d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                        </svg>
                    </div>
                    <h4>Welcome to PetVet AI!</h4>
                    <p>I'm here to help you with pet care questions, health advice, and general information about your furry friends.</p>
                    
                    <!-- Quick Actions -->
                    <div class="chatbot-quick-actions">
                        <button @onclick='() => SendQuickMessage("Tell me about pet nutrition")' class="quick-action-btn">
                            Pet Nutrition
                        </button>
                        <button @onclick='() => SendQuickMessage("What are common pet health symptoms?")' class="quick-action-btn">
                            Health Symptoms
                        </button>
                        <button @onclick='() => SendQuickMessage("How often should I groom my pet?")' class="quick-action-btn">
                            Grooming Tips
                        </button>
                        <button @onclick='() => SendQuickMessage("Pet training advice")' class="quick-action-btn">
                            Training Tips
                        </button>
                    </div>
                </div>
            }
            else
            {
                @foreach (var message in Messages)
                {
                    <div class="chatbot-message @(message.IsUser ? "chatbot-message--user" : "chatbot-message--ai")">
                        @if (!message.IsUser)
                        {
                            <div class="chatbot-message__avatar">
                                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                          d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                                </svg>
                            </div>
                        }
                        <div class="chatbot-message__content">
                            <div class="chatbot-message__bubble">
                                @((MarkupString)message.Content)
                            </div>
                            <div class="chatbot-message__time">
                                @message.Timestamp.ToString("HH:mm")
                            </div>
                        </div>
                    </div>
                }
            }

            @if (IsTyping)
            {
                <div class="chatbot-message chatbot-message--ai chatbot-message--typing">
                    <div class="chatbot-message__avatar">
                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                  d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                    </div>
                    <div class="chatbot-message__content">
                        <div class="chatbot-message__bubble">
                            <div class="typing-indicator">
                                <span></span>
                                <span></span>
                                <span></span>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Input Area -->
        <div class="chatbot-input">
            <div class="chatbot-input__container">
                <textarea @bind="CurrentMessage" 
                         @onkeypress="HandleKeyPress"
                         @ref="messageInput"
                         placeholder="Ask me anything about pet care..."
                         class="chatbot-input__field"
                         rows="1"></textarea>
                <button @onclick="SendMessage" 
                        class="chatbot-input__send"
                        aria-label="Send message">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                    </svg>
                </button>
            </div> 
        </div>
    </div>
</div>

@code {
    [Parameter] public bool IsOpen { get; set; } = false;
    [Parameter] public EventCallback<bool> IsOpenChanged { get; set; }
    [Parameter] public EventCallback<string> OnMessageSent { get; set; }

    private ElementReference messagesContainer;
    private ElementReference messageInput;
    private string CurrentMessage = "";
    private bool IsTyping = false;

    public List<ChatMessage> Messages { get; set; } = new();

    public class ChatMessage
    {
        public string Content { get; set; } = "";
        public bool IsUser { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.Now;
    }

    private async Task CloseChatbot()
    {
        IsOpen = false;
        await IsOpenChanged.InvokeAsync(IsOpen);
    }

    private async Task HandleBackdropClick()
    {
        await CloseChatbot();
    }

    private async Task SendMessage()
    {
        if (string.IsNullOrWhiteSpace(CurrentMessage) || IsTyping)
            return;

        var userMessage = CurrentMessage.Trim();
        CurrentMessage = "";

        // Add user message
        Messages.Add(new ChatMessage
        {
            Content = userMessage,
            IsUser = true,
            Timestamp = DateTime.Now
        });

        StateHasChanged();
        await ScrollToBottom();

        // Notify parent component
        await OnMessageSent.InvokeAsync(userMessage);

        // Show typing indicator
        //IsTyping = true;
        StateHasChanged();
        await ScrollToBottom();
    }

    private async Task SendQuickMessage(string message)
    {
        CurrentMessage = message;
        await SendMessage();
    }

    public async Task AddAIResponse(string response)
    {
        //IsTyping = false;
        
        Messages.Add(new ChatMessage
        {
            Content = response,
            IsUser = false,
            Timestamp = DateTime.Now
        });

        StateHasChanged();
        await ScrollToBottom();
    }

    private async Task HandleKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter" && !e.ShiftKey)
        {
            await SendMessage();
        }
    }

    private async Task ScrollToBottom()
    {
        try
        {
            await Task.Delay(50); // Small delay to ensure DOM is updated
            await JSRuntime.InvokeVoidAsync("scrollToBottom", messagesContainer);
        }
        catch
        {
            // Ignore JS errors
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await JSRuntime.InvokeVoidAsync("initializeChatbot");
        }

        if (IsOpen)
        {
            try
            {
                await messageInput.FocusAsync();
            }
            catch
            {
                // Ignore focus errors
            }
        }
    }
}
