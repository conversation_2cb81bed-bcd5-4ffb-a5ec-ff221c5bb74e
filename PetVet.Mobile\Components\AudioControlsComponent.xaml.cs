using Microsoft.Extensions.Logging;
using PetVet.Mobile.Models;
using PetVet.Mobile.Services;
using System.Timers;

namespace PetVet.Mobile.Components;

public partial class AudioControlsComponent : ContentView
{
    private readonly IMediaCaptureService _mediaCaptureService;
    private readonly IAudioStreamingService _audioStreamingService;
    private readonly ILogger<AudioControlsComponent> _logger;
    
    private System.Timers.Timer? _metricsUpdateTimer;
    private bool _isMuted = false;
    private AudioQuality _currentQuality = AudioQuality.Standard;
    private string _currentCallId = "";
    
    // Events
    public event EventHandler<bool>? MuteToggled;
    public event EventHandler<AudioQuality>? QualityChanged;
    public event EventHandler<double>? VolumeChanged;

    public AudioControlsComponent()
    {
        InitializeComponent();
        
        // Get services from DI (this would normally be injected)
        var serviceProvider = Application.Current?.Handler?.MauiContext?.Services;
        if (serviceProvider != null)
        {
            _mediaCaptureService = serviceProvider.GetRequiredService<IMediaCaptureService>();
            _audioStreamingService = serviceProvider.GetRequiredService<IAudioStreamingService>();
            _logger = serviceProvider.GetRequiredService<ILogger<AudioControlsComponent>>();
            
            SubscribeToEvents();
            InitializeControls();
            StartMetricsUpdates();
        }
        else
        {
            _logger = Microsoft.Extensions.Logging.Abstractions.NullLogger<AudioControlsComponent>.Instance;
        }
    }

    public void SetCallId(string callId)
    {
        _currentCallId = callId;
        _logger.LogInformation("Audio controls set for call {CallId}", callId);
    }

    private void SubscribeToEvents()
    {
        if (_mediaCaptureService != null)
        {
            _mediaCaptureService.AudioQualityChanged += OnAudioQualityChanged;
        }
        
        if (_audioStreamingService != null)
        {
            _audioStreamingService.QualityMetricsUpdated += OnStreamingQualityUpdated;
        }
    }

    private void InitializeControls()
    {
        try
        {
            // Set initial volume
            VolumeSlider.Value = _mediaCaptureService?.Volume ?? 1.0;
            UpdateVolumeLabel(VolumeSlider.Value);
            
            // Set initial quality
            UpdateQualityButton(_currentQuality);
            
            // Set initial mute state
            UpdateMuteButton(_isMuted);
            
            _logger.LogDebug("Audio controls initialized");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing audio controls");
        }
    }

    private void StartMetricsUpdates()
    {
        try
        {
            _metricsUpdateTimer = new System.Timers.Timer(500); // Update every 500ms
            _metricsUpdateTimer.Elapsed += OnMetricsUpdateTimer;
            _metricsUpdateTimer.Start();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting metrics updates");
        }
    }

    private void OnMetricsUpdateTimer(object? sender, ElapsedEventArgs e)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            try
            {
                UpdateAudioLevelMeter();
                UpdateBufferHealth();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating metrics display");
            }
        });
    }

    private void OnAudioQualityChanged(object? sender, AudioQualityMetrics metrics)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            try
            {
                UpdateQualityIndicators(metrics);
                UpdateNetworkStatus(metrics);
                UpdateLatencyDisplay(metrics.Latency);
                UpdateAudioLevel(metrics.AudioLevel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling audio quality change");
            }
        });
    }

    private void OnStreamingQualityUpdated(object? sender, AudioQualityMetrics metrics)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            try
            {
                if (metrics.CallId == _currentCallId)
                {
                    UpdateQualityIndicators(metrics);
                    UpdateNetworkStatus(metrics);
                    UpdatePacketLossDisplay(metrics.PacketLossRate);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling streaming quality update");
            }
        });
    }

    private void OnVolumeChanged(object sender, ValueChangedEventArgs e)
    {
        try
        {
            var volume = e.NewValue;
            _mediaCaptureService?.SetVolume(volume);
            UpdateVolumeLabel(volume);
            VolumeChanged?.Invoke(this, volume);
            
            _logger.LogDebug("Volume changed to {Volume}", volume);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing volume");
        }
    }

    private void OnMuteClicked(object sender, EventArgs e)
    {
        try
        {
            _isMuted = !_isMuted;
            _mediaCaptureService?.SetMuted(_isMuted);
            _audioStreamingService?.SetMuted(_isMuted);
            
            UpdateMuteButton(_isMuted);
            MuteToggled?.Invoke(this, _isMuted);
            
            _logger.LogInformation("Audio {Status}", _isMuted ? "muted" : "unmuted");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling mute");
        }
    }

    private async void OnQualityClicked(object sender, EventArgs e)
    {
        try
        {
            // Cycle through quality levels
            var qualities = Enum.GetValues<AudioQuality>();
            var currentIndex = Array.IndexOf(qualities, _currentQuality);
            var nextIndex = (currentIndex + 1) % qualities.Length;
            _currentQuality = qualities[nextIndex];
            
            _audioStreamingService?.SetAudioQuality(_currentQuality);
            _mediaCaptureService?.SetAudioQuality(_currentQuality);
            
            UpdateQualityButton(_currentQuality);
            QualityChanged?.Invoke(this, _currentQuality);
            
            _logger.LogInformation("Audio quality changed to {Quality}", _currentQuality);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing audio quality");
        }
    }

    private async void OnSettingsClicked(object sender, EventArgs e)
    {
        try
        {
            // Toggle advanced settings visibility
            BufferHealthGrid.IsVisible = !BufferHealthGrid.IsVisible;
            
            var options = new string[] { "Show Buffer Health", "Audio Diagnostics", "Reset Settings", "Cancel" };
            var result = await Application.Current.MainPage.DisplayActionSheet(
                "Audio Settings", "Cancel", null, options);
            
            switch (result)
            {
                case "Show Buffer Health":
                    BufferHealthGrid.IsVisible = true;
                    break;
                case "Audio Diagnostics":
                    await ShowAudioDiagnostics();
                    break;
                case "Reset Settings":
                    ResetToDefaults();
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling settings click");
        }
    }

    private void UpdateQualityIndicators(AudioQualityMetrics metrics)
    {
        try
        {
            // Update quality indicators based on network quality
            var qualityLevel = (int)(metrics.NetworkQuality * 5);
            
            var indicators = new[] { QualityIndicator1, QualityIndicator2, QualityIndicator3, QualityIndicator4, QualityIndicator5 };
            
            for (int i = 0; i < indicators.Length; i++)
            {
                indicators[i].Fill = i < qualityLevel ? Colors.Green : Colors.LightGray;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating quality indicators");
        }
    }

    private void UpdateNetworkStatus(AudioQualityMetrics metrics)
    {
        try
        {
            var (status, color) = metrics.NetworkQuality switch
            {
                >= 0.8 => ("Excellent connection", Colors.Green),
                >= 0.6 => ("Good connection", Colors.Orange),
                >= 0.4 => ("Fair connection", Colors.Orange),
                _ => ("Poor connection", Colors.Red)
            };
            
            NetworkStatusIndicator.Fill = color;
            NetworkStatusLabel.Text = status;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating network status");
        }
    }

    private void UpdateLatencyDisplay(double latency)
    {
        try
        {
            LatencyLabel.Text = $"{latency:F0}ms";
            LatencyLabel.TextColor = latency switch
            {
                <= 50 => Colors.Green,
                <= 100 => Colors.Orange,
                _ => Colors.Red
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating latency display");
        }
    }

    private void UpdatePacketLossDisplay(double packetLossRate)
    {
        try
        {
            PacketLossLabel.Text = $"{packetLossRate * 100:F1}% loss";
            PacketLossLabel.TextColor = packetLossRate switch
            {
                <= 0.01 => Colors.Green,
                <= 0.05 => Colors.Orange,
                _ => Colors.Red
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating packet loss display");
        }
    }

    private void UpdateAudioLevel(double audioLevel)
    {
        try
        {
            AudioLevelMeter.Progress = audioLevel;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating audio level");
        }
    }

    private void UpdateAudioLevelMeter()
    {
        try
        {
            var metrics = _mediaCaptureService?.GetAudioQualityMetrics();
            if (metrics != null)
            {
                AudioLevelMeter.Progress = metrics.AudioLevel;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating audio level meter");
        }
    }

    private void UpdateBufferHealth()
    {
        try
        {
            var bufferHealth = _audioStreamingService?.GetBufferHealth();
            if (bufferHealth.HasValue)
            {
                var healthRatio = Math.Min(1.0, bufferHealth.Value.TotalMilliseconds / 200.0); // 200ms target
                BufferHealthMeter.Progress = healthRatio;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating buffer health");
        }
    }

    private void UpdateVolumeLabel(double volume)
    {
        try
        {
            VolumeLabel.Text = $"{volume * 100:F0}%";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating volume label");
        }
    }

    private void UpdateMuteButton(bool isMuted)
    {
        try
        {
            MuteButton.Text = isMuted ? "🔇" : "🎤";
            MuteButton.BackgroundColor = isMuted ? Colors.Red : Color.FromArgb("#8FBFA8");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating mute button");
        }
    }

    private void UpdateQualityButton(AudioQuality quality)
    {
        try
        {
            QualityButton.Text = quality switch
            {
                AudioQuality.Low => "LQ",
                AudioQuality.Standard => "SD",
                AudioQuality.High => "HD",
                AudioQuality.Premium => "4K",
                _ => "SD"
            };
            
            QualityButton.BackgroundColor = quality switch
            {
                AudioQuality.Low => Colors.Gray,
                AudioQuality.Standard => Color.FromArgb("#6B7280"),
                AudioQuality.High => Color.FromArgb("#8FBFA8"),
                AudioQuality.Premium => Color.FromArgb("#FEA195"),
                _ => Color.FromArgb("#6B7280")
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating quality button");
        }
    }

    private async Task ShowAudioDiagnostics()
    {
        try
        {
            var metrics = _mediaCaptureService?.GetAudioQualityMetrics();
            var streamingMetrics = _audioStreamingService?.GetQualityMetrics(_currentCallId);
            
            var diagnostics = $"Audio Diagnostics:\n\n" +
                             $"Latency: {metrics?.Latency:F1}ms\n" +
                             $"Audio Level: {metrics?.AudioLevel * 100:F1}%\n" +
                             $"Quality: {metrics?.CurrentQuality}\n" +
                             $"Packet Loss: {streamingMetrics?.PacketLossRate * 100:F2}%\n" +
                             $"Network Quality: {streamingMetrics?.NetworkQuality * 100:F1}%\n" +
                             $"Buffer Health: {streamingMetrics?.BufferHealth.TotalMilliseconds:F0}ms";
            
            await Application.Current.MainPage.DisplayAlert("Audio Diagnostics", diagnostics, "OK");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error showing audio diagnostics");
        }
    }

    private void ResetToDefaults()
    {
        try
        {
            VolumeSlider.Value = 1.0;
            _currentQuality = AudioQuality.Standard;
            _isMuted = false;
            
            _mediaCaptureService?.SetVolume(1.0);
            _mediaCaptureService?.SetMuted(false);
            _mediaCaptureService?.SetAudioQuality(AudioQuality.Standard);
            _audioStreamingService?.SetVolume(1.0);
            _audioStreamingService?.SetMuted(false);
            _audioStreamingService?.SetAudioQuality(AudioQuality.Standard);
            
            UpdateMuteButton(false);
            UpdateQualityButton(AudioQuality.Standard);
            UpdateVolumeLabel(1.0);
            
            _logger.LogInformation("Audio controls reset to defaults");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting to defaults");
        }
    }

    protected override void OnHandlerChanged()
    {
        base.OnHandlerChanged();
        
        if (Handler == null)
        {
            // Cleanup when component is removed
            _metricsUpdateTimer?.Stop();
            _metricsUpdateTimer?.Dispose();
            
            if (_mediaCaptureService != null)
            {
                _mediaCaptureService.AudioQualityChanged -= OnAudioQualityChanged;
            }
            
            if (_audioStreamingService != null)
            {
                _audioStreamingService.QualityMetricsUpdated -= OnStreamingQualityUpdated;
            }
        }
    }
}
