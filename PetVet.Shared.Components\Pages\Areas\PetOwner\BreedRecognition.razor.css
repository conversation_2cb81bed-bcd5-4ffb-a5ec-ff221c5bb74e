﻿/* Scanning animation for breed recognition */
.scanning-line {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #ffffff, transparent);
    animation: scan 2s linear infinite;
}

@keyframes scan {
    0% {
        transform: translateY(0);
    }
    100% {
        transform: translateY(320px);
    }
}

/* Additional scanning effects */
.scanning-overlay {
    position: absolute;
    inset: 0;
    background: rgba(34, 197, 94, 0.1);
    border-radius: 0.5rem;
}

/* Pulse animation for loading */
.pulse-green {
    animation: pulse-green 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse-green {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Header Section */
.header-section {
    background: #FEA195;
    border-radius: 1.25rem;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.header-content {
    text-align: center;
}

.header-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    font-family: 'Acme', sans-serif;
}

.header-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    
}

/* Content Section */
.content-section {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    margin-bottom: 2rem;
}

/* Upload Section */
.upload-section {
    display: flex;
    flex-direction: column;
}

.upload-card {
    background: white;
    border-radius: 1.25rem;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    height: 100%;
    transition: transform 0.3s ease;
}

    .upload-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.15);
    }

.upload-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1rem;
}

.upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
}

.upload-icon {
    font-size: 3rem;
    color: #FEA195;
    margin-bottom: 1rem;
}

.upload-title,
.preview-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 0.5rem;
    font-family: 'Acme', sans-serif;
}

.upload-text {
    color: #6B7280;
    font-size: 1rem;
    margin-bottom: 1.5rem;
    
}

.upload-tip {
    background: #F3F4F6;
    padding: 1rem;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    color: #4B5563;
    margin-top: 1rem;
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    
}

/* Preview Section */
.preview-section {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.image-preview-container {
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.image-preview {
    width: 100%;
    height: auto;
    object-fit: cover;
}

/* Result Section */
.result-section {
    display: flex;
    flex-direction: column;
}

.result-card {
    background: white;
    border-radius: 1.25rem;
    padding: 2rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    transition: transform 0.3s ease;
}

    .result-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.15);
    }

.result-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 0.5rem;
    font-family: 'Acme', sans-serif;
}

.result-text {
    color: #6B7280;
    font-size: 1rem;
    
}

/* States */
.loading-state,
.error-state,
.success-state,
.placeholder-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.loading-text {
    color: #6B7280;
    font-size: 1rem;
    
}

.error-icon {
    font-size: 3rem;
    color: #EF4444;
}

.error-message {
    color: #EF4444;
    font-size: 1.25rem;
    font-family: 'Acme', sans-serif;
}

.success-icon {
    font-size: 3rem;
    color: #10B981;
}

.placeholder-icon {
    font-size: 3rem;
    color: #6B7280;
}

.breed-icon {
    width: 2.5rem;
    height: 2.5rem;
    margin-bottom: 0.75rem;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* Confidence Badge */
.confidence-badge {
    background: #DBEAFE;
    color: #1E40AF;
    padding: 0.5rem 1rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 600;
    margin: 1rem 0;
    
}

/* Action Buttons */
.action-button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: 1.25rem;
    
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

.remove-button {
    background: #EF4444;
    color: white;
}

.retry-button {
    background: #FEA195;
    color: white;
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* File Input Styling */
::deep input[type="file"] {
    padding: 0.75rem;
    border: 2px dashed #E5E7EB;
    border-radius: 0.75rem;
    background: #F9FAFB;
    width: 100%;
    transition: all 0.3s ease;
    
}

    ::deep input[type="file"]:hover {
        border-color: #FEA195;
        background: #F3F8FF;
    }

/* Spinner */
.spinner {
    width: 3rem;
    height: 3rem;
    border: 3px solid rgba(41, 104, 237, 0.1);
    border-radius: 50%;
    border-top-color: #FEA195;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Mobile Layout */
@media (max-width: 768px) {
    .BreedRecognition-CONTAINER {
        padding: 1rem;
    }

    .header-section {
        padding: 1.5rem;
    }

    .header-title {
        font-size: 1.5rem;
    }

    .content-section {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .upload-card,
    .result-card {
        padding: 1.5rem;
    }

    .image-preview-container {
        max-width: 250px;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.BreedRecognition-CONTAINER {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
