using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PetVet.Api.Data.Entities
{
    /// <summary>
    /// SMS notification log entity for audit and debugging
    /// </summary>
    [Table("SmsNotificationLogs")]
    public class SmsNotificationLog
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int UserId { get; set; }

        [Required]
        [MaxLength(15)]
        public string PhoneNumber { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string NotificationType { get; set; } = string.Empty;

        [Required]
        [Column(TypeName = "nvarchar(max)")]
        public string MessageContent { get; set; } = string.Empty;

        [Required]
        [MaxLength(20)]
        public string Status { get; set; } = "Pending"; // Sent, Failed, Delivered, Read

        [Column(TypeName = "nvarchar(max)")]
        public string? ErrorMessage { get; set; }

        public int? AppointmentId { get; set; }

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? DeliveredAt { get; set; }

        [MaxLength(100)]
        public string? TwilioMessageSid { get; set; }

        [MaxLength(20)]
        public string? TwilioStatus { get; set; }

        public bool IsRetry { get; set; } = false;

        public int RetryCount { get; set; } = 0;

        public int MaxRetries { get; set; } = 3;

        public DateTime? NextRetryAt { get; set; }

        [Column(TypeName = "decimal(10,4)")]
        public decimal? Cost { get; set; }

        [MaxLength(10)]
        public string? CostUnit { get; set; }

        // Navigation properties
        [ForeignKey("UserId")]
        public virtual User User { get; set; } = null!;

        [ForeignKey("AppointmentId")]
        public virtual Appointment? Appointment { get; set; }
    }
}
