@page "/petOwner/calendar"
@inject IAppointmentApi _AppointmentApi
@inject IAppState _AppState
@inject IJSRuntime _JS
@inject NavigationManager _NavigationManager
@inject PetVetAuthStateProvider _PetVetAuthStateProvider

<PageTitle>Appointment Calendar</PageTitle>

<!-- Modern Pet Owner Calendar Page -->
<div class="px-4 py-6 space-y-6">

    <!-- Header Section -->
    <div class="text-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-2">Appointment Calendar</h1>
        <p class="text-gray-600">View and manage your appointments in calendar view</p>
    </div>

    <!-- Calendar Controls -->
    <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 mb-6">
        <div class="flex items-center justify-between">
            <button class="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-xl flex items-center justify-center transition-all"
                    @onclick="PreviousMonth">
                <i class="fas fa-chevron-left text-gray-600"></i>
            </button>
            <h2 class="text-xl font-bold text-gray-800">@currentDate.ToString("MMMM yyyy")</h2>
            <button class="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-xl flex items-center justify-center transition-all"
                    @onclick="NextMonth">
                <i class="fas fa-chevron-right text-gray-600"></i>
            </button>
        </div>
    </div>

    <!-- Calendar Grid -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
        <!-- Calendar Header -->
        <div class="grid grid-cols-7 bg-gray-50">
            <div class="p-3 text-center text-sm font-medium text-gray-600">Sun</div>
            <div class="p-3 text-center text-sm font-medium text-gray-600">Mon</div>
            <div class="p-3 text-center text-sm font-medium text-gray-600">Tue</div>
            <div class="p-3 text-center text-sm font-medium text-gray-600">Wed</div>
            <div class="p-3 text-center text-sm font-medium text-gray-600">Thu</div>
            <div class="p-3 text-center text-sm font-medium text-gray-600">Fri</div>
            <div class="p-3 text-center text-sm font-medium text-gray-600">Sat</div>
        </div>

        <!-- Calendar Body -->
        <div class="divide-y divide-gray-100">
            @foreach (var week in calendarDays)
            {
                <div class="grid grid-cols-7 divide-x divide-gray-100">
                    @foreach (var day in week)
                    {
                        <div class="min-h-[100px] p-2 @(day.IsCurrentMonth ? "bg-white" : "bg-gray-50") @(day.IsToday ? "bg-blue-50" : "")">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm font-medium @(day.IsCurrentMonth ? "text-gray-800" : "text-gray-400") @(day.IsToday ? "text-blue-600" : "")">
                                    @day.Date.Day
                                </span>
                                @if (day.IsToday)
                                {
                                    <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                                }
                            </div>

                            <div class="space-y-1">
                                @if (day.Appointments.Any())
                                {
                                    var firstAppointment = day.Appointments.First();
                                    <div class="p-1 rounded-lg text-xs cursor-pointer hover:shadow-sm transition-all @GetCalendarStatusClass(firstAppointment.Status)"
                                         @onclick="()=>ShowAppointmentDetails(firstAppointment)">
                                        <div class="font-medium truncate">@firstAppointment.Time</div>
                                        <div class="truncate opacity-80">Dr. @firstAppointment.VetName</div>
                                    </div>

                                    @if (day.Appointments.Count > 1)
                                    {
                                        <div class="text-xs text-teal-600 font-medium cursor-pointer hover:text-teal-700 transition-colors"
                                             @onclick="() => ShowDayAppointments(day)">
                                            +@(day.Appointments.Count - 1) more
                                        </div>
                                    }
                                }
                            </div>
                        </div>
                    }
                </div>
            }
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-6">
        <a href="/petOwner/appointments"
           class="w-full bg-gradient-to-r from-teal-500 to-blue-600 text-white py-4 px-6 rounded-2xl font-medium hover:from-teal-600 hover:to-blue-700 transition-all transform hover:scale-105 shadow-lg flex items-center justify-center gap-2">
            <i class="fas fa-list"></i>
            <span>View Appointments</span>
        </a>
    </div>

</div>

<!-- Modal for Appointment Cancellation -->
<BsModal OnModalConfirmation="ConfirmCancel_Click"
         ButtonText="Cancel Appointment"
         Title="Are you sure you want to cancel this appointment?">
</BsModal>

@if (isAppointmentModalOpen && selectedAppointment != null)
{
    <Modal Title="Appointment Details"
           ActionButtonText="Close"
           OnActionButtonClick="CloseAppointmentModal"
           OnCancelButtonClick="CloseAppointmentModal"
           IsVisible="isAppointmentModalOpen"
           Size="ModalSize.Large">
        <div class="appointment-details">
            <div class="appointment-card @GetStatusClass(selectedAppointment.Status)">
                <div class="appointment-date">
                    <div class="date-badge">
                        <span class="month">@GetMonthFromDate(selectedAppointment.AppointmentDate)</span>
                        <span class="day">@GetDayFromDate(selectedAppointment.AppointmentDate)</span>
                    </div>
                    <span class="time">@selectedAppointment.Time</span>
                    <span class="status-badge">@selectedAppointment.Status</span>
                </div>
                <div class="appointment-info">
                    <h3 class="vet-name">Dr. @selectedAppointment.VetName</h3>
                    @if (!string.IsNullOrEmpty(selectedAppointment.Notes))
                    {
                        <p class="notes">
                            <i class="fas fa-clipboard-list"></i> @selectedAppointment.Notes
                        </p>
                    }
                </div>
                <div class="appointment-actions">
                    @if (selectedAppointment.Status == nameof(AppointmentStatus.Confirmed))
                    {
                        <button class="action-btn reschedule-btn" @onclick="() => RescheduleAppointment(selectedAppointment)">
                            <i class="fas fa-calendar-alt"></i> Reschedule
                        </button>
                        <button class="action-btn cancel-btn" @onclick="() => CancelAppointment(selectedAppointment.Id)">
                            <i class="fas fa-times-circle"></i> Cancel
                        </button>
                    }
                    <button class="action-btn message-btn" @onclick="() => Contact(selectedAppointment.VetId)">
                        <i class="fas fa-comments"></i> Message
                    </button>
                </div>
            </div>
        </div>
    </Modal>
}

@if (isDayModalOpen && selectedDay != null)
{
    <Modal Title="@GetDayModalTitle()"
           ActionButtonText="Close"
           OnActionButtonClick="CloseDayModal"
           OnCancelButtonClick="CloseDayModal"
           IsVisible="isDayModalOpen"
           Size="ModalSize.Large">
        <div class="day-appointments">
            @foreach (var appointment in selectedDay.Appointments.OrderBy(a => a.Time))
            {
                <div class="appointment-card @GetStatusClass(appointment.Status)">
                    <div class="appointment-date">
                        <div class="date-badge">
                            <span class="month">@GetMonthFromDate(appointment.AppointmentDate)</span>
                            <span class="day">@GetDayFromDate(appointment.AppointmentDate)</span>
                        </div>
                        <span class="time">@appointment.Time</span>
                        <span class="status-badge">@appointment.Status</span>
                    </div>
                    <div class="appointment-info">
                        <h3 class="vet-name">Dr. @appointment.VetName</h3>
                    </div>
                    <div class="appointment-actions">
                        <button class="action-btn details-btn" @onclick="() => ShowAppointmentDetails(appointment)">
                            <i class="fas fa-info-circle"></i> Details
                        </button>
                    </div>
                </div>
            }
        </div>
    </Modal>
}

@if (isRescheduleModalOpen && selectedAppointment != null)
{
    <Modal Title="Reschedule Appointment"
           ActionButtonText="Confirm"
           OnActionButtonClick="ConfirmReschedule"
           OnCancelButtonClick="CloseRescheduleModal"
           IsVisible="isRescheduleModalOpen"
           Size="ModalSize.Large">
        <div class="reschedule-form">
            <div class="form-group">
                <label>Select New Date</label>
                <input type="date"
                       class="date-input"
                       @bind-value="newAppointmentDate"
                       @bind-value:event="oninput"
                       min="@DateTime.Today.ToString("yyyy-MM-dd")" />
            </div>

            @if (newAppointmentDate != default)
            {
                <div class="time-slots">
                    <label>Available Time Slots for @newAppointmentDate.ToString("dddd")</label>
                    @if (!availableTimeSlots.Any(ts => ts.Day == newAppointmentDate.ToString("dddd") && !ts.IsBooked))
                    {
                        <div class="empty-slots">
                            <i class="fas fa-calendar-times"></i>
                            <p>No time slots available for this day. Please select another date.</p>
                        </div>
                    }
                    else
                    {
                        <div class="slot-grid">
                            @foreach (var slot in availableTimeSlots.Where(ts => ts.Day == newAppointmentDate.ToString("dddd")))
                            {
                                <button class="time-slot @(slot.IsBooked ? "booked" : "") @(slot.IsExpired ? "expired" : "") @(slot.Id == newTimeSlotId ? "selected" : "")"
                                        disabled="@(slot.IsBooked || slot.IsExpired)"
                                        @onclick="() => SelectTimeSlot(slot.Id)">
                                    @slot.Time
                                    @if (slot.IsBooked)
                                    {
                                        <span class="slot-status">Booked</span>
                                    }
                                    @if (slot.IsExpired)
                                    {
                                        <span class="slot-status">Expired</span>
                                    }
                                </button>
                            }
                        </div>
                    }
                </div>
            }
        </div>
    </Modal>
}

@code {
    private int _currentUserId;
    private List<AppointmentDto> appointments = new();
    private DateTime currentDate = DateTime.Today;
    private List<List<CalendarDay>> calendarDays = new();
    private int CancelAppointmentID { get; set; } = 0;

    // Modal state
    private bool isAppointmentModalOpen = false;
    private AppointmentDto selectedAppointment;
    private bool isDayModalOpen = false;
    private CalendarDay selectedDay;

    // Reschedule modal state
    private bool isRescheduleModalOpen = false;
    private DateTime newAppointmentDate = DateTime.Today;
    private int newTimeSlotId;
    private List<VetTimeSlotDto> availableTimeSlots = new();

    protected override async Task OnInitializedAsync()
    {
        _currentUserId = _PetVetAuthStateProvider.User.Id;

        await LoadAppointments();
    }

    private async Task LoadAppointments()
    {
        try
        {
            _AppState.ShowLoader("Loading Appointments");
            appointments = await _AppointmentApi.GetPetOwnerAppointmentsAsync(_currentUserId);
            GenerateCalendar();
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error loading appointments: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private void GenerateCalendar()
    {
        calendarDays = new List<List<CalendarDay>>();

        // Determine the first day of the month
        var firstDayOfMonth = new DateTime(currentDate.Year, currentDate.Month, 1);

        // Determine the last day of the month
        var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);

        // Determine the first day to display (the previous Sunday or the first day of the month)
        var firstDayToDisplay = firstDayOfMonth.DayOfWeek == DayOfWeek.Sunday
            ? firstDayOfMonth
            : firstDayOfMonth.AddDays(-(int)firstDayOfMonth.DayOfWeek);

        // Generate calendar for 6 weeks (which covers all possible month views)
        for (int week = 0; week < 6; week++)
        {
            var weekDays = new List<CalendarDay>();

            for (int day = 0; day < 7; day++)
            {
                var date = firstDayToDisplay.AddDays(week * 7 + day);
                var isCurrentMonth = date.Month == currentDate.Month;
                var isToday = date.Date == DateTime.Today;

                // Get appointments for this day
                var dayAppointments = appointments
                    .Where(a => DateTime.Parse(a.AppointmentDate).Date == date.Date)
                    .ToList();

                weekDays.Add(new CalendarDay
                    {
                        Date = date,
                        IsCurrentMonth = isCurrentMonth,
                        IsToday = isToday,
                        Appointments = dayAppointments
                    });
            }

            calendarDays.Add(weekDays);

            // If the last day of the last week is already past the end of the month, we're done
            if (weekDays[6].Date > lastDayOfMonth && week >= 3)
                break;
        }
    }

    private void PreviousMonth()
    {
        currentDate = currentDate.AddMonths(-1);
        GenerateCalendar();
    }

    private void NextMonth()
    {
        currentDate = currentDate.AddMonths(1);
        GenerateCalendar();
    }

    private string GetStatusClass(string status)
    {
        return status switch
        {
            nameof(AppointmentStatus.Confirmed) => "bg-blue-100 text-blue-700",
            nameof(AppointmentStatus.Completed) => "bg-green-100 text-green-700",
            nameof(AppointmentStatus.Cancelled) => "bg-red-100 text-red-700",
            nameof(AppointmentStatus.Expired) => "bg-gray-100 text-gray-700",
            _ => "bg-gray-100 text-gray-700"
        };
    }

    private string GetCalendarStatusClass(string status)
    {
        return status switch
        {
            nameof(AppointmentStatus.Confirmed) => "bg-blue-100 text-blue-800 border border-blue-200",
            nameof(AppointmentStatus.Completed) => "bg-green-100 text-green-800 border border-green-200",
            nameof(AppointmentStatus.Cancelled) => "bg-red-100 text-red-800 border border-red-200",
            nameof(AppointmentStatus.Expired) => "bg-gray-100 text-gray-800 border border-gray-200",
            _ => "bg-gray-100 text-gray-800 border border-gray-200"
        };
    }

    private string GetDayModalTitle()
    {
        if (selectedDay == null) return "Appointments";
        return $"Appointments on {selectedDay.Date.ToString("MMMM d, yyyy")}";
    }

    private void ShowAppointmentDetails(AppointmentDto appointment)
    {
        selectedAppointment = appointment;
        isAppointmentModalOpen = true;

        // If day modal is open, close it
        if (isDayModalOpen)
        {
            isDayModalOpen = false;
        }
    }

    private void ShowDayAppointments(CalendarDay day)
    {
        selectedDay = day;
        isDayModalOpen = true;
    }

    private void CloseAppointmentModal()
    {
        isAppointmentModalOpen = false;
        selectedAppointment = null;
    }

    private void CloseDayModal()
    {
        isDayModalOpen = false;
        selectedDay = null;
    }

    private async Task CancelAppointment(int appointmentId)
    {
        CancelAppointmentID = appointmentId;
        CloseAppointmentModal(); // Close the appointment details modal first
        await _JS.InvokeVoidAsync("ShowConfirmationModal");
    }

    [JSInvokable]
    public async Task ConfirmCancel_Click(bool isConfirmed)
    {
        await _JS.InvokeVoidAsync("HideConfirmationModal");

        _AppState.ShowLoader("Cancelling Appointment");

        if (isConfirmed && CancelAppointmentID != 0)
        {
            var response = await _AppointmentApi.UpdateAppointmentStatusAsync(CancelAppointmentID, nameof(AppointmentStatus.Cancelled));

            if (response.IsSuccess)
            {
                await _JS.ToastrSuccess("Appointment cancelled successfully.");
                await LoadAppointments();
            }
            else
            {
                await _JS.ToastrError($"Error cancelling appointment: {response.ErrorMessage}");
            }
        }
        CancelAppointmentID = 0;

        _AppState.HideLoader();

        StateHasChanged();
    }

    private async Task RescheduleAppointment(AppointmentDto appointment)
    {
        selectedAppointment = appointment;
        newAppointmentDate = DateTime.Today;
        newTimeSlotId = 0;
        isRescheduleModalOpen = true;
        isAppointmentModalOpen = false;

        // Load time slots for the selected vet
        await LoadAvailableTimeSlots(appointment.VetId);
    }

    private async Task LoadAvailableTimeSlots(int vetId)
    {
        try
        {
            _AppState.ShowLoader("Loading Time Slots");
            availableTimeSlots = await _AppointmentApi.GetVetTimeSlotsAsync(vetId);
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error loading time slots: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private void SelectTimeSlot(int timeSlotId)
    {
        newTimeSlotId = timeSlotId;
    }

    private void CloseRescheduleModal()
    {
        isRescheduleModalOpen = false;
        availableTimeSlots.Clear();
    }

    private async Task ConfirmReschedule()
    {
        if (selectedAppointment == null || newTimeSlotId == 0)
        {
            await _JS.ToastrError("Please select a new date and time slot.");
            return;
        }

        try
        {
            _AppState.ShowLoader("Rescheduling Appointment");

            var response = await _AppointmentApi.RescheduleAppointmentAsync(
                selectedAppointment.Id,
                newTimeSlotId,
                newAppointmentDate.ToString("yyyy-MM-dd"));

            if (response.IsSuccess)
            {
                await _JS.ToastrSuccess("Appointment rescheduled successfully.");
                CloseRescheduleModal();
                await LoadAppointments();
            }
            else
            {
                await _JS.ToastrError($"Error rescheduling appointment: {response.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error rescheduling appointment: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private string GetMonthFromDate(string dateString)
    {
        if (DateTime.TryParse(dateString, out DateTime date))
        {
            return date.ToString("MMM");
        }
        return "";
    }

    private string GetDayFromDate(string dateString)
    {
        if (DateTime.TryParse(dateString, out DateTime date))
        {
            return date.Day.ToString();
        }
        return "";
    }

    private void Contact(int vetId)
    {
        // Navigate to chat with the vet
        _NavigationManager.NavigateTo($"/petOwner/chat?userId={vetId}");
    }

    // Calendar day class to store day-specific data
    public class CalendarDay
    {
        public DateTime Date { get; set; }
        public bool IsCurrentMonth { get; set; }
        public bool IsToday { get; set; }
        public List<AppointmentDto> Appointments { get; set; } = new();
    }
} 