﻿@page "/admin/manage/users/profile/create"
@page "/admin/manage/users/profile/update/{Id:int}"
@inject IUserApi _UserApi
@inject IJSRuntime _JS
@inject IAppState _AppState
@inject NavigationManager _NavigationManager

<PageTitle>@(Id == 0 ? "Create User" : "Update User")</PageTitle>

<div class="bg-gray-50 p-8 min-h-screen w-full max-w-4xl mx-auto">
    <div class="bg-white rounded-3xl shadow-xl border border-gray-200 overflow-hidden">
        <!-- Header -->
        <div class="bg-primary-500 rounded-t-3xl p-8 text-center text-white">
            <h2 class="text-3xl font-bold py-2 font-acme">
                @(Id == 0 ? "Create New User" : "Update User") 
                <i class="fas fa-user-plus ml-2"></i>
            </h2>
        </div>
        
        <!-- Body -->
        <div class="p-8">
            <EditForm Model="_user" OnValidSubmit="UpsertUserAsync">
                <DataAnnotationsValidator />

                <!-- Profile Image Section -->
                <div class="flex flex-col items-center mb-8">
                    @if (!string.IsNullOrEmpty(_user.ImageUrl))
                    {
                        <img src="@(_user.ImageUrl.StartsWith("data:") ? _user.ImageUrl : $"data:image/jpeg;base64,{_user.ImageUrl}")"
                             alt="Profile Image" class="w-24 h-24 rounded-full object-cover shadow-lg mb-4" />
                    }
                    else
                    {
                        <div class="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center shadow-lg mb-4">
                            <i class="fas fa-user text-2xl text-gray-400"></i>
                        </div>
                    }
                </div>

                <!-- Form Fields -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="floating-label-group">
                        <InputText @bind-Value="_user.Name" class="floating-label-input" id="Name" placeholder=" " disabled />
                        <label for="Name" class="floating-label">Full Name</label>
                        <ValidationMessage For="() => _user.Name" />
                    </div>

                    <div class="floating-label-group">
                        <InputText @bind-Value="_user.Email" class="floating-label-input" id="Email" placeholder=" " disabled />
                        <label for="Email" class="floating-label">Email Address</label>
                        <ValidationMessage For="() => _user.Email" />
                    </div>

                    <div class="floating-label-group">
                        <InputText @bind-Value="_user.Phone" class="floating-label-input" id="Phone" placeholder=" " disabled />
                        <label for="Phone" class="floating-label">Phone Number</label>
                        <ValidationMessage For="() => _user.Phone" />
                    </div>

                    <div class="floating-label-group">
                        <InputSelect @bind-Value="_user.Role" class="floating-label-input" id="Role" disabled>
                            <option value="">Select Role</option>
                            <option value="@nameof(UserRole.Admin)">Admin</option>
                            <option value="@nameof(UserRole.Vet)">Vet</option>
                            <option value="@nameof(UserRole.PetOwner)">Pet Owner</option>
                        </InputSelect>
                        <label for="Role" class="floating-label">User Role</label>
                        <ValidationMessage For="() => _user.Role" />
                    </div>

                    @if (_user.Role == nameof(UserRole.Vet))
                    {
                        <div class="floating-label-group">
                            <InputText @bind-Value="_user.ClinicName" class="floating-label-input" id="ClinicName" placeholder=" " disabled />
                            <label for="ClinicName" class="floating-label">Clinic Name</label>
                            <ValidationMessage For="() => _user.ClinicName" />
                        </div>

                        <div class="floating-label-group">
                            <InputText @bind-Value="_user.Address" class="floating-label-input" id="Address" placeholder=" " disabled />
                            <label for="Address" class="floating-label">Address</label>
                            <ValidationMessage For="() => _user.Address" />
                        </div>

                        <div class="floating-label-group">
                            <InputText @bind-Value="_user.Specialization" class="floating-label-input" id="Specialization" placeholder=" " disabled />
                            <label for="Specialization" class="floating-label">Specialization</label>
                            <ValidationMessage For="() => _user.Specialization" />
                        </div>

                        <div class="floating-label-group">
                            <InputNumber @bind-Value="_user.YearsOfExperience" class="floating-label-input" id="YearsOfExperience" placeholder=" " disabled />
                            <label for="YearsOfExperience" class="floating-label">Years of Experience</label>
                            <ValidationMessage For="() => _user.YearsOfExperience" />
                        </div>
                    }
                </div>

                <!-- Error Alert -->
                @if (!string.IsNullOrEmpty(_error))
                {
                    <div class="bg-red-50 border border-red-200 text-red-800 p-4 rounded-xl mt-6 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle mr-3"></i>
                            <span>@_error</span>
                        </div>
                        <button type="button" class="text-red-600 hover:text-red-800 text-xl" @onclick="() => _error = null">
                            &times;
                        </button>
                    </div>
                }

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-between mt-8">
                    <div class="flex gap-4">
                        @if (Id != 0)
                        {
                            <button type="button" 
                                    class="inline-flex items-center space-x-2 bg-blue-500 hover:bg-blue-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 hover:transform hover:scale-105 shadow-lg"
                                    @onclick="() => DownloadPdf(Id, _user.Name)">
                                <i class="fas fa-download"></i>
                                <span>Download Profile</span>
                            </button>

                            <button type="button" 
                                    class="inline-flex items-center space-x-2 bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 hover:transform hover:scale-105 shadow-lg"
                                    @onclick="() => DownloadQrCode(Id, _user.Name)">
                                <i class="fas fa-qrcode"></i>
                                <span>Download QR Code</span>
                            </button>
                        }
                    </div>

                    <div class="flex gap-4">
                        <a href="/admin/manage/users/profile" 
                           class="inline-flex items-center space-x-2 bg-gray-500 hover:bg-gray-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 hover:transform hover:scale-105 shadow-lg">
                            <i class="fas fa-arrow-left"></i>
                            <span>Back To List</span>
                        </a>
                    </div>
                </div>
            </EditForm>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public int Id { get; set; } = 0;

    private UserDto _user = new();
    private string _error = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        if (Id != 0)
        {
            _AppState.ShowLoader("Fetching User Data");
            _user = await _UserApi.GetUserAsync(Id);
            _AppState.HideLoader();
        }
    }

    private async Task UpsertUserAsync()
    {
        try
        {
            _error = string.Empty;
            _AppState.ShowLoader(Id == 0 ? "Creating User" : "Updating User");

            var result = await _UserApi.SaveUserAsync(_user);

            if (result.IsSuccess)
            {
                await _JS.InvokeVoidAsync("toastr.success", Id == 0 ? "User created successfully." : "User updated successfully.");
                _NavigationManager.NavigateTo("/admin/manage/users/profile");
            }
            else
            {
                _error = result.ErrorMessage ?? "Operation failed.";
            }
        }
        catch (Exception ex)
        {
            _error = $"An error occurred: {ex.Message}";
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private async Task DownloadPdf(int userId, string userName)
    {
        try
        {
            _AppState.ShowLoader("Generating PDF");
            // Note: PDF download functionality needs to be implemented in the API
            await _JS.InvokeVoidAsync("toastr.info", "PDF download functionality is not yet implemented.");
        }
        catch (Exception ex)
        {
            await _JS.InvokeVoidAsync("toastr.error", $"Error downloading PDF: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private async Task DownloadQrCode(int userId, string userName)
    {
        try
        {
            _AppState.ShowLoader("Generating QR Code");
            // Note: QR Code download functionality needs to be implemented in the API
            await _JS.InvokeVoidAsync("toastr.info", "QR Code download functionality is not yet implemented.");
        }
        catch (Exception ex)
        {
            await _JS.InvokeVoidAsync("toastr.error", $"Error downloading QR Code: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }
}
