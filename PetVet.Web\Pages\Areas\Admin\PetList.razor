﻿@page "/admin/manage/pets/profile"
@inject IPetApi _PetApi
@inject IJSRuntime _JS
@inject IAppState _AppState

<PageTitle>Manage Pets</PageTitle>

<BsModal OnModalConfirmation="ConfirmDelete_Click"
ButtonText="Delete"
Title="Are you sure you want to delete this pet profile?">
</BsModal>

@if(!isProcessing)
{
    <div class="bg-gray-50 p-8 min-h-screen w-full max-w-7xl mx-auto">
        <div class="bg-white rounded-3xl shadow-xl border border-gray-200 overflow-hidden">
            <!-- Header -->
            <div class="bg-primary-500 rounded-t-3xl p-8 text-center text-white">
                <h2 class="text-3xl font-bold py-2 font-acme">Pet Profile List <i class="fas fa-paw ml-2"></i></h2>
            </div>
            
            <!-- Body -->
            <div class="p-8">
                <!-- Add New Pet Button -->
                <div class="flex justify-end mb-6">
                    <a href="/admin/manage/pets/profile/create" 
                       class="inline-flex items-center space-x-2 bg-primary-500 hover:bg-primary-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 hover:transform hover:scale-105 shadow-lg">
                        <i class="fas fa-plus-circle"></i>
                        <span>Add New Pet Profile</span>
                    </a>
                </div>

                @if (!_pets.Any())
                {
                    <div class="text-center bg-yellow-50 border border-yellow-200 text-yellow-800 p-6 rounded-xl">
                        <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                        <p class="font-medium">No Pet Profiles Available.</p>
                    </div>
                }
                else
                {
                    <!-- Responsive Table Container -->
                    <div class="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900">Name</th>
                                        <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900">Category</th>
                                        <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900">Breed</th>
                                        <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900">Gender</th>
                                        <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900">Age</th>
                                        <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900">Weight</th>
                                        <th class="px-6 py-4 text-center text-sm font-semibold text-gray-900">Image</th>
                                        <th class="px-6 py-4 text-center text-sm font-semibold text-gray-900">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    @foreach (var pet in _pets)
                                    {
                                        <tr class="hover:bg-gray-50 transition-colors duration-150">
                                            <td class="px-6 py-4 text-sm font-medium text-gray-900">@pet.Name</td>
                                            <td class="px-6 py-4 text-sm text-gray-700">@pet.CategoryName</td>
                                            <td class="px-6 py-4 text-sm text-gray-700">@pet.Breed</td>
                                            <td class="px-6 py-4">
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium @(pet.Gender.ToLower() == "male" ? "bg-blue-100 text-blue-800" : "bg-pink-100 text-pink-800")">
                                                    @pet.Gender
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 text-sm text-gray-700">@pet.Age</td>
                                            <td class="px-6 py-4 text-sm text-gray-700">@pet.Weight kg</td>
                                            <td class="px-6 py-4 text-center">
                                                @if (!string.IsNullOrEmpty(pet.ImageUrl))
                                                {
                                                    <img src="@pet.ImageUrl" alt="@pet.Name" class="w-12 h-12 rounded-full object-cover mx-auto shadow-sm" />
                                                }
                                                else
                                                {
                                                    <div class="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mx-auto">
                                                        <i class="fas fa-paw text-gray-400"></i>
                                                    </div>
                                                }
                                            </td>
                                            <td class="px-6 py-4 text-center">
                                                <div class="flex justify-center space-x-2">
                                                    <a href="@($"/admin/manage/pets/profile/update/{pet.Id}")" 
                                                       class="inline-flex items-center space-x-1 bg-yellow-500 hover:bg-yellow-600 text-white text-xs font-medium py-2 px-3 rounded-lg transition-colors duration-200">
                                                        <i class="fas fa-edit"></i>
                                                        <span>Edit</span>
                                                    </a>
                                                    
                                                    <button @onclick="() => HandleDelete(pet.Id)"
                                                            class="inline-flex items-center space-x-1 bg-red-500 hover:bg-red-600 text-white text-xs font-medium py-2 px-3 rounded-lg transition-colors duration-200">
                                                        <i class="fas fa-trash"></i>
                                                        <span>Delete</span>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
}

@code {
    private PetDto[] _pets = [];
    private int DeletePetID { get; set; } = 0;
    private bool isProcessing { get; set; } = true;

    protected override async Task OnInitializedAsync() => await LoadPetsAsync();


    private async Task LoadPetsAsync()
    {
        isProcessing = true;
        _AppState.ShowLoader("Fetching Pets Data");
        _pets = await _PetApi.GetPetsAsync();
        _AppState.HideLoader();
        isProcessing = false;
    }


    private async Task HandleDelete(int id)
    {
        DeletePetID = id;
        await _JS.InvokeVoidAsync("ShowConfirmationModal");
    }


    [JSInvokable]
    public async Task ConfirmDelete_Click(bool isConfirmed)
    {
        await _JS.InvokeVoidAsync("HideConfirmationModal");

        _AppState.ShowLoader("Deleting Pet Data");

        if (isConfirmed && DeletePetID != 0)
        {
            var result = await _PetApi.DeletePetAsync(DeletePetID);
            if (result.IsSuccess)
            {
                await _JS.InvokeVoidAsync("toastr.success", "Pet profile deleted successfully.");
            }
            else
            {
                await _JS.InvokeVoidAsync("toastr.error", "Failed to delete pet profile.");
            }
            await LoadPetsAsync();
        }
        DeletePetID = 0;

        _AppState.HideLoader();

        StateHasChanged();
    }
}
