{"scripts": {"ui:dev": "npx tailwindcss -c ./wwwroot/js/tailwind.config.js -i ./wwwroot/css/tailwind.css -o ./wwwroot/css/site.css --watch", "ui:build": "npx tailwindcss -c ./wwwroot/js/tailwind.config.js -i ./wwwroot/css/tailwind.css -o ./wwwroot/css/site.css --minify"}, "dependencies": {"@tailwindcss/container-queries": "^0.1.1", "@tailwindcss/forms": "^0.5.3", "autoprefixer": "^10.4.20", "flowbite": "^1.6.5", "postcss": "^8.4.42"}, "devDependencies": {"tailwindcss": "^3.4.17"}}