﻿using Microsoft.EntityFrameworkCore;
using PetVet.Api.Data;
using PetVet.Api.Data.Entities;
using PetVet.Shared.DTOs;

namespace PetVet.Api.Services
{
    public class EducationService
    {
        private readonly PetVetContext _context;

        public EducationService(PetVetContext context)
        {
            _context = context;
        }


        public async Task<PetVetApiResponse> SaveEducationAsync(EducationDto dto)
        {
            if (await _context.Educations
                .AsNoTracking()
                .AnyAsync(e => e.Name == dto.Name && e.Id != dto.Id))
            {
                // Category with same name already exists
                return PetVetApiResponse.Fail("Education with same name already exists.");
            }

            if (dto.Id == 0)
            {
                // Create new Education
                var education = new Education
                {
                    Name = dto.Name,
                };
                _context.Educations.Add(education);
            }
            else
            {
                // Update existing Education
                var dbEducation = await _context.Educations.FirstOrDefaultAsync(e => e.Id == dto.Id);

                if (dbEducation == null)
                {
                    // Education does not exist
                    return PetVetApiResponse.Fail("Education does not exist.");
                }
                dbEducation.Name = dto.Name;
                _context.Educations.Update(dbEducation);
            }
            await _context.SaveChangesAsync();
            return PetVetApiResponse.Success();
        }


        public async Task<EducationDto[]> GetEducationListAsync() => await _context.Educations
            .AsNoTracking()
            .Select(e => new EducationDto
            {
                Name = e.Name,
                Id = e.Id,
            })
            .ToArrayAsync();
         
        public async Task<EducationDto> GetEducationAsync(int id)
        {
            return await _context.Educations
                 .Where(e => e.Id == id)
                 .Select(x => new EducationDto()
                 {
                     Id = x.Id,
                     Name = x.Name
                 }).FirstAsync();

        }


        public async Task<PetVetApiResponse> DeleteEducationAsync(int id)
        {
            var education = await _context.Educations.FirstOrDefaultAsync(e => e.Id == id);

            if (education == null)
            {
                return PetVetApiResponse.Fail("Dducation not found.");
            }

            _context.Educations.Remove(education);
            await _context.SaveChangesAsync();

            return PetVetApiResponse.Success();
        }
    }
}
