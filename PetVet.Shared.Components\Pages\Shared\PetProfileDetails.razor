﻿@page "/petOwner/myPets/petprofile/{Id:int}"
@page "/petOwner/myPets/all/petprofile/{Id:int}"
@page "/shared/petprofile/{Id:int}"
@page "/vet/shared/petprofile/{Id:int}"
@inject IPetApi _PetApi
@inject ICategoryApi _CategoryApi
@inject IUserApi _UserApi
@inject IMessageApi _MessageApi
@inject IJSRuntime _JS
@inject IAppState _AppState
@inject PetVetAuthStateProvider _PetVetAuthStateProvider
@inject NavigationManager _NavigationManager

<Modal Title="Share Pet Profile"
       IsVisible="@showShareModal"
       ActionButtonText="Share"
       Size="ModalSize.Large"
       OnActionButtonClick="ShareProfileToSelectedUser"
       OnCancelButtonClick="() => showShareModal = false">
    <div class="mb-3">
        <h5>Select a user to share with:</h5>
        <div class="list-group">
            @if (isLoadingChatUsers)
            {
                <div class="d-flex justify-content-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            }
            else if (chatUsers.Count == 0)
            {
                <div class="alert alert-info">No users available to share with.</div>
            }
            else
            {
                @foreach (var user in chatUsers)
                {
                    <button type="button"
                            class="list-group-item list-group-item-action d-flex justify-content-between align-items-center @(selectedUserId == user.Id ? "active" : "")"
                            @onclick="() => SelectUser(user.Id)">
                        @user.Name
                        @if (selectedUserId == user.Id)
                        {
                            <i class="fa-solid fa-check"></i>
                        }
                    </button>
                }
            }
        </div>
    </div>
</Modal>
  
@if (!isProcessing)
{
    <!-- Modern Pet Profile with Theme Styling -->
    <div class="px-4 py-6 space-y-6">
        <PetProfileHeader PetId="@Id" OnShareProfile="ToggleShareModal" />

        <!-- Hero Section with Pet Image and Name -->
        <div class="relative">
            <!-- Background Gradient -->
            <div class="bg-gradient-to-br from-orange-300 to-orange-400 rounded-3xl p-8 text-center relative overflow-hidden">
                <!-- Decorative Elements -->
                <div class="absolute top-4 right-4 w-16 h-16 bg-white/10 rounded-full"></div>
                <div class="absolute bottom-4 left-4 w-12 h-12 bg-white/10 rounded-full"></div>

                <div class="relative z-10">
                    <!-- Pet Image -->
                    <div class="flex justify-center mb-6">
                        @if (!string.IsNullOrEmpty(_pet.ImageUrl))
                        {
                            <div class="w-32 h-32 rounded-full border-4 border-white/30 overflow-hidden shadow-xl">
                                <img src="@_pet.ImageUrl"
                                     alt="@_pet.Name"
                                     class="w-full h-full object-cover" />
                            </div>
                        }
                        else
                        {
                            <div class="w-32 h-32 rounded-full border-4 border-white/30 bg-white/20 flex items-center justify-center shadow-xl">
                                <i class="fas fa-paw text-white text-4xl"></i>
                            </div>
                        }
                    </div>

                    <!-- Pet Name and Badges -->
                    <div class="text-center">
                        <div class="flex items-center justify-center gap-3 mb-2">
                            <h1 class="text-3xl font-bold text-white">@_pet.Name</h1>
                            @if (_pet.Count > 1)
                            {
                                <span class="px-3 py-1 bg-white/20 text-white text-sm rounded-full font-medium">
                                    @_pet.Count pets
                                </span>
                                <span class="px-3 py-1 bg-red-400 text-white text-sm rounded-full font-medium">
                                    Group Profile
                                </span>
                            }
                        </div>

                        <!-- Heart Icon -->
                        <div class="flex justify-center">
                            <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                                <i class="fas fa-heart text-red-300 text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pet Details Cards -->
        <div class="space-y-4">
            @if (_pet != null)
            {
                <!-- Basic Info Cards -->
                <div class="grid grid-cols-2 gap-4">
                    <!-- Age -->
                    <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                        <div class="flex items-center gap-3">
                            <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                                <i class="fas fa-birthday-cake text-blue-600 text-lg"></i>
                            </div>
                            <div>
                                <p class="text-gray-500 text-sm">Age</p>
                                <p class="font-bold text-gray-800">@_pet.AgeDisplay</p>
                            </div>
                        </div>
                    </div>

                    <!-- Weight -->
                    <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                        <div class="flex items-center gap-3">
                            <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                                <i class="fas fa-weight-scale text-green-600 text-lg"></i>
                            </div>
                            <div>
                                <p class="text-gray-500 text-sm">@(_pet.Count > 1 ? "Avg Weight" : "Weight")</p>
                                <p class="font-bold text-gray-800">@_pet.Weight kg</p>
                            </div>
                        </div>
                    </div>

                    @if (_pet.Count > 1)
                    {
                        <!-- Count (only for group) -->
                        <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                            <div class="flex items-center gap-3">
                                <div class="w-12 h-12 bg-purple-100 rounded-xl flex items-center justify-center">
                                    <i class="fas fa-layer-group text-purple-600 text-lg"></i>
                                </div>
                                <div>
                                    <p class="text-gray-500 text-sm">Count</p>
                                    <p class="font-bold text-gray-800">@_pet.Count pets</p>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Gender -->
                    <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                        <div class="flex items-center gap-3">
                            <div class="w-12 h-12 bg-pink-100 rounded-xl flex items-center justify-center">
                                <i class="fas fa-venus-mars text-pink-600 text-lg"></i>
                            </div>
                            <div>
                                <p class="text-gray-500 text-sm">@(_pet.Count > 1 ? "Predominant Gender" : "Gender")</p>
                                <p class="font-bold text-gray-800">@_pet.Gender</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Extended Info Cards -->
                <div class="grid grid-cols-1 gap-4">
                    <!-- Breed -->
                    <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                        <div class="flex items-center gap-3">
                            <div class="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center">
                                <i class="fas fa-dog text-orange-600 text-lg"></i>
                            </div>
                            <div>
                                <p class="text-gray-500 text-sm">Breed</p>
                                <p class="font-bold text-gray-800">@_pet.Breed</p>
                            </div>
                        </div>
                    </div>

                    <!-- Date of Birth -->
                    <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                        <div class="flex items-center gap-3">
                            <div class="w-12 h-12 bg-teal-100 rounded-xl flex items-center justify-center">
                                <i class="fas fa-calendar-days text-teal-600 text-lg"></i>
                            </div>
                            <div>
                                <p class="text-gray-500 text-sm">@(_pet.Count > 1 ? "Group Created On" : "Date of Birth")</p>
                                <p class="font-bold text-gray-800">@_pet.DateOfBirth?.ToString("MMM dd, yyyy")</p>
                            </div>
                        </div>
                    </div>

                    <!-- Vaccination Status -->
                    <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                        <div class="flex items-center gap-3">
                            <div class="w-12 h-12 @GetVaccinationBgColor(_pet.VaccinationStatus) rounded-xl flex items-center justify-center">
                                <i class="fas fa-syringe @GetVaccinationTextColor(_pet.VaccinationStatus) text-lg"></i>
                            </div>
                            <div class="flex-1">
                                <p class="text-gray-500 text-sm">Vaccination Status</p>
                                <div class="flex items-center gap-2">
                                    <p class="font-bold text-gray-800">@_pet.VaccinationStatus</p>
                                    <div class="w-3 h-3 @GetVaccinationDotColor(_pet.VaccinationStatus) rounded-full"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Category -->
                    <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                        <div class="flex items-center gap-3">
                            <div class="w-12 h-12 bg-indigo-100 rounded-xl flex items-center justify-center">
                                <i class="fas fa-tags text-indigo-600 text-lg"></i>
                            </div>
                            <div>
                                <p class="text-gray-500 text-sm">Category</p>
                                <p class="font-bold text-gray-800">@_categories.FirstOrDefault(c => c.Id == _pet.CategoryId)?.Name</p>
                            </div>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="bg-white rounded-2xl p-8 text-center shadow-sm border border-gray-100">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-spinner fa-spin text-gray-400 text-2xl"></i>
                    </div>
                    <p class="text-gray-500">Loading pet details...</p>
                </div>
            }
        </div>

        <!-- Action Buttons -->
        <div class="grid grid-cols-2 gap-4">
            <button class="flex items-center justify-center gap-3 bg-white hover:bg-gray-50 border border-gray-200 rounded-2xl p-4 transition-all">
                <div class="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-notes-medical text-blue-600"></i>
                </div>
                <span class="font-medium text-gray-700">Medical Records</span>
            </button>

            <button class="flex items-center justify-center gap-3 bg-white hover:bg-gray-50 border border-gray-200 rounded-2xl p-4 transition-all">
                <div class="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
                    <i class="fas fa-photo-film text-purple-600"></i>
                </div>
                <span class="font-medium text-gray-700">Album</span>
            </button>
        </div>
    </div>
}


@code {
    [Parameter] public int Id { get; set; }
    private CategoryDto[] _categories = [];
    private PetDto _pet = new();
    private bool isProcessing { get; set; } = true;
    private bool showShareModal { get; set; } = false;
    private List<UserDto> chatUsers { get; set; } = new List<UserDto>();
    private bool isLoadingChatUsers { get; set; } = false;
    private int selectedUserId { get; set; } = 0;
    private int currentUserId => _PetVetAuthStateProvider?.User?.Id ?? 0;

    protected override async Task OnInitializedAsync() => await LoadPetAsync();

    private async Task LoadPetAsync()
    {
        if (Id > 0)
        {
            isProcessing = true;
            _AppState.ShowLoader("Fetching Pet Data");
            _pet = await _PetApi.GetPetAsync(Id);
        }

        _categories = await _CategoryApi.GetCategoriesAsync();
        _AppState.HideLoader();
        isProcessing = false;

        // Ensure the selected category is set correctly
        if (_pet.CategoryId == 0 && _categories.Length > 0)
        {
            _pet.CategoryId = _categories.FirstOrDefault()?.Id ?? 0;
        }
    }

    private async Task ToggleShareModal()
    {
        showShareModal = !showShareModal;

        if (showShareModal)
        {
            await LoadChatUsersAsync();
        }
    }

    private async Task LoadChatUsersAsync()
    {
        try
        {
            isLoadingChatUsers = true;
            chatUsers = (await _UserApi.GetChatUsersAsync(currentUserId)).ToList();
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Failed to load chat users: {ex.Message}");
        }
        finally
        {
            isLoadingChatUsers = false;
        }
    }

    private void SelectUser(int userId)
    {
        selectedUserId = userId;
    }

    private async Task ShareProfileToSelectedUser()
    {
        if (selectedUserId == 0)
        {
            await _JS.ToastrError("Please select a user to share with.");
            return;
        }

        _AppState.ShowLoader("Sharing Pet Profile");

        try
        {
            // Generate relative URL for the pet profile with vet access
            var profileUrl = $"/vet/shared/petprofile/{Id}";

            // Truncate image URL if too long (take last 100 chars)
            string imageUrl = _pet.ImageUrl ?? "";
            if (imageUrl.Length > 100)
            {
                imageUrl = imageUrl.Substring(Math.Max(0, imageUrl.Length - 100));
                // If we cut in the middle of the URL, just clear it
                if (!imageUrl.StartsWith("http") && !imageUrl.StartsWith("/"))
                {
                    imageUrl = "";
                }
            }

            // Ensure pet name is not too long (max 50 chars)
            string petName = _pet.Name ?? "";
            if (petName.Length > 50)
            {
                petName = petName.Substring(0, 47) + "...";
            }

            // Create message data with size limits in mind
            var messageData = new
            {
                type = "pet_profile",
                petId = Id,
                petName = petName,
                petImage = imageUrl,
                profileUrl = profileUrl,
                sharedBy = _PetVetAuthStateProvider.User.Name // Add sharer's name for context
            };

            string message = System.Text.Json.JsonSerializer.Serialize(messageData, new System.Text.Json.JsonSerializerOptions
                {
                    WriteIndented = false,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                });

            // If message is still too large, try without the image URL
            if (message.Length > 500)
            {
                messageData = new
                {
                    type = "pet_profile",
                    petId = Id,
                    petName = petName,
                    petImage = "",
                    profileUrl = profileUrl,
                    sharedBy = _PetVetAuthStateProvider.User.Name
                };

                message = System.Text.Json.JsonSerializer.Serialize(messageData, new System.Text.Json.JsonSerializerOptions
                    {
                        WriteIndented = false,
                        Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                    });
            }

            // Final size check
            if (message.Length > 500)
            {
                await _JS.ToastrError("Unable to share pet profile due to data size limitations.");
                return;
            }

            var messageSendDto = new MessageSendDto(selectedUserId, currentUserId, message);
            var response = await _MessageApi.SendMessageAsync(messageSendDto);

            if (response.IsSuccess)
            {
                var recipientName = chatUsers.FirstOrDefault(u => u.Id == selectedUserId)?.Name;
                await _JS.ToastrSuccess($"Pet profile shared successfully with {recipientName}.");
                showShareModal = false;

                // Determine the correct chat route based on user role
                string chatRoute = _PetVetAuthStateProvider.User.Role.ToLower().Contains("vet")
                    ? "/vet/chat"
                    : "/petOwner/chat";

                // Navigate to the chat page with userId parameter
                _NavigationManager.NavigateTo($"{chatRoute}?userId={selectedUserId}");

                // Reset the selected user ID
                selectedUserId = 0;
            }
            else
            {
                await _JS.ToastrError("Failed to share pet profile. Please try again.");
            }
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error sharing profile: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private string GetVaccinationBgColor(string vaccinationStatus)
    {
        return vaccinationStatus?.ToLower() switch
        {
            "vaccinated" => "bg-green-100",
            "partially vaccinated" => "bg-yellow-100",
            "not vaccinated" => "bg-red-100",
            _ => "bg-gray-100"
        };
    }

    private string GetVaccinationTextColor(string vaccinationStatus)
    {
        return vaccinationStatus?.ToLower() switch
        {
            "vaccinated" => "text-green-600",
            "partially vaccinated" => "text-yellow-600",
            "not vaccinated" => "text-red-600",
            _ => "text-gray-600"
        };
    }

    private string GetVaccinationDotColor(string vaccinationStatus)
    {
        return vaccinationStatus?.ToLower() switch
        {
            "vaccinated" => "bg-green-400",
            "partially vaccinated" => "bg-yellow-400",
            "not vaccinated" => "bg-red-400",
            _ => "bg-gray-400"
        };
    }

    // Legacy color arrays (keeping for compatibility)
    private List<string> PetDetailBGColors = new List<string>
    {
        "#FEA195",
        "#FEA195",
        "#FEA195",
        "#FEA195",
        "#FEA195",
    };

    private List<string> PetDetailIconColors = new List<string>
    {
        "#ffffff",
        "#ffffff",
        "#ffffff",
        "#ffffff",
        "#ffffff",
    };
}
