﻿using CommunityToolkit.Mvvm.Messaging;
using Microsoft.Maui.Platform;
using PetVet.Mobile.Components;
using PetVet.Mobile.Models;
using PetVet.Mobile.Services;
using PetVet.ServiceContracts.Features.Conversation;
using Platform.Client.Common.Features.Conversation;

namespace PetVet.Mobile
{
    public partial class MainPage : ContentPage
    {
        private IChatCallService _chatCallService;
        public MainPage(IServiceScopeFactory scopeFactory)
        {
            InitializeComponent();
            WeakReferenceMessenger.Default.Register<Tuple<string>>(this, async (recipient, message) =>
            {
                if (message.Item1 == "StartChat")
                {
                    await Navigation.PushAsync(new ChatThreadsListingView(scopeFactory, string.Empty));
                }
            });

            WeakReferenceMessenger.Default.Register<Tuple<string, int, string, string>>(this, async (recipient, message) =>
            {
                if (message.Item1 == "StartChat")
                {
                    var startChatFormDataService = scopeFactory.CreateScope().ServiceProvider.GetRequiredService<IStartChatFormDataService>();
                    var conversationId = await startChatFormDataService.SaveAsync(new StartChatFormBusinessObject()
                    {
                        FriendId = message.Item2,
                        FriendName = message.Item3,
                        DisplayPictureUrl = message.Item4
                    });
                    await Navigation.PushAsync(new ChatThreadsListingView(scopeFactory, conversationId));
                }
            });

            _chatCallService = scopeFactory.CreateScope().ServiceProvider.GetRequiredService<IChatCallService>();
            _chatCallService.CallInvitationReceived += OnCallInvitationReceived;
            _ = _chatCallService.InitializeAsync();
        }
         
        protected override void OnAppearing()
        {
            SetStatusBarColor(Color.FromArgb("#3E97DA"));

        }
        void SetStatusBarColor(Color color)
        {
#if ANDROID
            var window = Microsoft.Maui.ApplicationModel.Platform.CurrentActivity?.Window; // Correct namespace for CurrentActivity

            if (window != null)
            {
                window.ClearFlags(Android.Views.WindowManagerFlags.TranslucentStatus);
                window.AddFlags(Android.Views.WindowManagerFlags.DrawsSystemBarBackgrounds);
                window.SetStatusBarColor(color.ToPlatform());
            }
#endif
        }

        private void OnCallInvitationReceived(object? sender, CallInviteMessage e)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
               
                // Show call invitation popup
                var logger = Microsoft.Extensions.Logging.Abstractions.NullLogger<CallInvitationPopup>.Instance;
                var popup = new CallInvitationPopup(e, _chatCallService, logger);
                await Navigation.PushModalAsync(popup);

                var accepted = await popup.WaitForResponseAsync();
                
            });
        }
    }
}
