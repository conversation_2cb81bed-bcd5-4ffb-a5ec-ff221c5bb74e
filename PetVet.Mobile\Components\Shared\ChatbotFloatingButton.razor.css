.chatbot-fab-container {
    position: fixed;
    bottom: 6rem;
    right: 1rem;
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.chatbot-fab {
    position: relative;
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    border: none;
    background: linear-gradient(135deg, #3E97DA 0%, #2875c7 100%);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 25px rgba(62, 151, 218, 0.4);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    animation: chatbot-bounce 3s infinite;
    overflow: hidden;
}

.chatbot-fab:hover {
    transform: scale(1.1);
    box-shadow: 0 12px 35px rgba(62, 151, 218, 0.6);
    animation: none;
}

.chatbot-fab:active {
    transform: scale(0.95);
}

.chatbot-fab--open {
    background: linear-gradient(135deg, #45C1A5 0%, #15993e 100%);
    box-shadow: 0 8px 25px rgba(69, 193, 165, 0.4);
    animation: none;
}

.chatbot-fab--open:hover {
    box-shadow: 0 12px 35px rgba(69, 193, 165, 0.6);
}

.chatbot-fab__icon {
    position: absolute;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.chatbot-fab__icon--hidden {
    opacity: 0;
    transform: rotate(180deg) scale(0.5);
}

.chatbot-fab__badge {
    position: absolute;
    top: -0.25rem;
    right: -0.25rem;
    background: #EF4444;
    color: white;
    border-radius: 50%;
    width: 1.25rem;
    height: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    border: 2px solid white;
    animation: chatbot-badge-pulse 2s infinite;
}

.chatbot-fab__pulse {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: rgba(62, 151, 218, 0.3);
    animation: chatbot-pulse 2s infinite;
    pointer-events: none;
}

.chatbot-fab__pulse--hidden {
    display: none;
}

.chatbot-fab__label {
    background: linear-gradient(135deg, rgba(62, 151, 218, 0.95), rgba(40, 117, 199, 0.95));
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    white-space: nowrap;
    position: relative;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: chatbot-label-float 3s infinite;
    opacity: 0;
    animation-delay: 2s;
    animation-fill-mode: forwards;
}

.chatbot-fab__label-arrow {
    position: absolute;
    right: -0.25rem;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 0.25rem solid rgba(62, 151, 218, 0.95);
    border-top: 0.25rem solid transparent;
    border-bottom: 0.25rem solid transparent;
}

/* Animations */
@keyframes chatbot-bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes chatbot-pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(1.4);
        opacity: 0;
    }
}

@keyframes chatbot-badge-pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes chatbot-label-float {
    0% {
        opacity: 0;
        transform: translateX(10px);
    }
    20%, 80% {
        opacity: 1;
        transform: translateX(0);
    }
    100% {
        opacity: 0;
        transform: translateX(10px);
    }
}

/* Mobile optimizations */
@media (max-width: 640px) {
    .chatbot-fab-container {
        bottom: 5rem;
        right: 1rem;
    }

    .chatbot-fab {
        width: 3.5rem;
        height: 3.5rem;
    }

    .chatbot-fab__label {
        font-size: 0.8rem;
        padding: 0.4rem 0.6rem;
    }
}

/* Tablet and larger screens */
@media (min-width: 768px) {
    .chatbot-fab-container {
        bottom: 2rem;
        right: 2rem;
    }

    .chatbot-fab {
        width: 4rem;
        height: 4rem;
    }

    .chatbot-fab__label {
        font-size: 0.9rem;
        padding: 0.6rem 0.8rem;
    }
}

/* High contrast and accessibility */
@media (prefers-reduced-motion: reduce) {
    .chatbot-fab {
        animation: none;
    }
    
    .chatbot-fab__label {
        animation: none;
        opacity: 1;
    }
}

/* Focus states for accessibility */
.chatbot-fab:focus {
    outline: 3px solid rgba(62, 151, 218, 0.5);
    outline-offset: 2px;
}

/* Professional theme enhancements */
.chatbot-fab--professional {
    background: linear-gradient(135deg, #3E97DA 0%, #45C1A5 50%, #2875c7 100%);
    box-shadow: 0 8px 25px rgba(62, 151, 218, 0.3), 0 4px 12px rgba(69, 193, 165, 0.2);
}

.chatbot-fab--professional:hover {
    box-shadow: 0 12px 35px rgba(62, 151, 218, 0.4), 0 6px 16px rgba(69, 193, 165, 0.3);
}
