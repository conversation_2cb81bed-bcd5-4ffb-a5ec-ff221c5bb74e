using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PetVet.Api.Data.Entities
{
    /// <summary>
    /// SMS notification configuration entity
    /// </summary>
    [Table("SmsNotificationConfigs")]
    public class SmsNotificationConfig
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [MaxLength(50)]
        public string NotificationType { get; set; } = string.Empty;

        public bool IsEnabled { get; set; } = true;

        [Range(0, 1440)] // 0 to 24 hours in minutes
        public int DelayMinutes { get; set; } = 0;

        [Range(0, 5)]
        public int MaxRetries { get; set; } = 3;

        [Range(1, 60)]
        public int RetryIntervalMinutes { get; set; } = 5;

        [MaxLength(160)]
        public string? MessageTemplate { get; set; }

        public bool EnableForVets { get; set; } = true;

        public bool EnableForPetOwners { get; set; } = true;

        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? UpdatedAt { get; set; }
    }
}
