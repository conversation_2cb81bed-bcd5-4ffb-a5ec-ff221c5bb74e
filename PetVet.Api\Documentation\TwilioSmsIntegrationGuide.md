# Twilio SMS Integration Guide

## Overview

This document outlines the comprehensive Twilio SMS integration for the PetVet appointment system. The implementation provides professional SMS messaging for appointment notifications, confirmations, and status updates for both veterinarians and pet owners.

## 🎯 **Features Implemented**

### **1. Vet Notifications**
- **New Appointment Alerts**: Instant SMS messages when pet owners book appointments
- **Professional Templates**: Structured messages with appointment details and notes
- **Immediate Delivery**: Messages sent instantly upon appointment creation
- **Comprehensive Details**: Pet owner name, appointment date/time, and special notes

### **2. Pet Owner Notifications**
- **Confirmation Messages**: SMS confirmations when vets approve appointments
- **Status Updates**: Real-time notifications for appointment changes (confirmed, cancelled, rescheduled)
- **Professional Format**: Clear, concise messages suitable for SMS format

### **3. Technical Implementation**
- **Twilio REST API**: Integration with Twi<PERSON>'s official SMS API
- **EF Core Logging**: Complete audit trail with database logging
- **Fallback System**: Automatic fallback to legacy SMS service when <PERSON><PERSON><PERSON> fails
- **Error Handling**: Comprehensive error logging and retry mechanisms

## 🔧 **Technical Architecture**

### **Core Services**

#### **TwilioSmsService**
```csharp
public class TwilioSmsService
{
    // Core Twilio REST API integration
    // Phone number formatting for international standards
    // Message truncation for SMS limits (160 characters)
    // Configuration validation and status checking
}
```

#### **SmsNotificationService**
```csharp
public class SmsNotificationService
{
    // EF Core database integration for logging
    // Professional message templates for vets and pet owners
    // Status update notifications
    // Retry logic for failed messages
}
```

### **Database Schema**

#### **SmsNotificationLog Entity**
```csharp
public class SmsNotificationLog
{
    public int Id { get; set; }
    public int UserId { get; set; }
    public string PhoneNumber { get; set; }
    public string NotificationType { get; set; }
    public string MessageContent { get; set; }
    public string Status { get; set; } // Pending, Sent, Failed, Delivered
    public string? TwilioMessageSid { get; set; }
    public string? TwilioStatus { get; set; }
    public decimal? Cost { get; set; }
    public string? CostUnit { get; set; }
    public int RetryCount { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime? DeliveredAt { get; set; }
    // Navigation properties to User and Appointment
}
```

#### **SmsNotificationConfig Entity**
```csharp
public class SmsNotificationConfig
{
    public int Id { get; set; }
    public string NotificationType { get; set; }
    public bool IsEnabled { get; set; }
    public int MaxRetries { get; set; }
    public string? MessageTemplate { get; set; }
    public bool EnableForVets { get; set; }
    public bool EnableForPetOwners { get; set; }
}
```

## 📱 **Message Templates**

### **Vet Appointment Notification**
```
PetVet: New appointment with John Smith on March 15, 2024 at 2:00 PM. Notes: Annual checkup required
```

### **Pet Owner Confirmation**
```
PetVet: Your appointment with Dr. Smith is confirmed for March 15, 2024 at 2:00 PM. Clinic: PetVet Clinic
```

### **Status Update Messages**
```
PetVet: Your appointment with Dr. Smith on March 15, 2024 at 2:00 PM is confirmed.
PetVet: Your appointment on March 15, 2024 at 2:00 PM has been cancelled.
PetVet: Your appointment has been rescheduled to March 16, 2024 at 3:00 PM.
```

## ⚙️ **Configuration**

### **appsettings.Twilio.json**
```json
{
  "Twilio": {
    "AccountSid": "YOUR_TWILIO_ACCOUNT_SID",
    "AuthToken": "YOUR_TWILIO_AUTH_TOKEN",
    "FromPhoneNumber": "YOUR_TWILIO_PHONE_NUMBER",
    "EnableNotifications": true,
    "EnableRetryLogic": true,
    "DefaultRetryCount": 3,
    "RetryIntervalMinutes": 5,
    "MaxMessageLength": 160,
    "WebhookUrl": "https://your-domain.com/api/twilio/webhook"
  },
  "SmsNotifications": {
    "EnableVetNotifications": true,
    "EnablePetOwnerNotifications": true,
    "EnableStatusUpdates": true,
    "DefaultCountryCode": "+92"
  }
}
```

### **Dependency Injection**
```csharp
// Program.cs
builder.Services.AddScoped<TwilioSmsService>();
builder.Services.AddScoped<SmsNotificationService>();
```

## 🚀 **Usage Examples**

### **Sending Vet Notification**
```csharp
await _smsNotificationService.SendVetAppointmentNotificationAsync(appointmentId);
```

### **Sending Pet Owner Confirmation**
```csharp
await _smsNotificationService.SendPetOwnerConfirmationAsync(appointmentId);
```

### **Sending Status Update**
```csharp
await _smsNotificationService.SendAppointmentStatusUpdateAsync(appointmentId, "Confirmed");
```

## 🔒 **Security & Compliance**

### **Phone Number Formatting**
- **International Format**: Automatic conversion to +country_code format
- **Pakistani Numbers**: Special handling for local (+92) numbers
- **Validation**: Format validation before sending messages

### **Message Limits**
- **160 Character Limit**: Automatic truncation for single SMS
- **Professional Content**: Concise, professional message templates
- **Cost Optimization**: Efficient message formatting to minimize costs

### **Error Handling**
- **Retry Logic**: Configurable retry attempts with exponential backoff
- **Fallback SMS**: Automatic fallback to legacy SMS service when Twilio fails
- **Logging**: Comprehensive error logging for debugging

## 📊 **Monitoring & Analytics**

### **Delivery Reports**
- **Status Tracking**: Sent, Delivered, Failed status tracking via webhooks
- **Cost Tracking**: Message cost and pricing unit tracking
- **Performance Metrics**: Success rates and delivery times

### **Webhook Integration**
```csharp
[HttpPost("api/twilio/sms-status")]
public async Task<IActionResult> HandleSmsStatusCallback()
{
    // Process Twilio delivery reports
    // Update SMS notification logs with delivery status
    // Handle failed message notifications
}
```

## 🎨 **API Endpoints**

### **SMS Notification Management**
- `GET /api/smsnotification/logs` - Get user SMS notification logs
- `GET /api/smsnotification/stats` - Get SMS notification statistics
- `POST /api/smsnotification/test` - Send test SMS (Admin only)
- `POST /api/smsnotification/retry-failed` - Retry failed notifications (Admin only)
- `GET /api/smsnotification/configs` - Get notification configurations (Admin only)

### **Twilio Webhook Endpoints**
- `POST /api/twiliowebhook/sms-status` - Handle SMS delivery reports
- `GET /api/twiliowebhook/verify` - Webhook verification
- `GET /api/twiliowebhook/health` - Health check

## 🧪 **Testing**

### **Unit Tests**
- **Message Formatting**: Template generation and formatting tests
- **Phone Number Validation**: International format conversion tests
- **Error Handling**: Fallback mechanism tests

### **Integration Tests**
- **Twilio API**: End-to-end message delivery tests
- **Webhook Processing**: Delivery report handling tests
- **Database Operations**: Notification logging tests

## 🚀 **Deployment**

### **Prerequisites**
1. **Twilio Account**: Active Twilio account with SMS capabilities
2. **Phone Number**: Verified Twilio phone number for sending SMS
3. **Webhook URL**: Public HTTPS endpoint for delivery reports
4. **Database Migration**: Run EF Core migration to add SMS entities

### **Setup Steps**
1. **Configure Twilio Credentials** in appsettings.json
2. **Add Configuration** to appsettings.Twilio.json
3. **Run Database Migration** to add SMS notification entities
4. **Configure Webhook** for delivery reports in Twilio Console
5. **Test Integration** with sample messages

## 📈 **Benefits**

### **For Veterinarians**
- **Instant Notifications**: Immediate awareness of new appointments
- **Professional Communication**: Structured, professional message templates
- **Reduced Missed Appointments**: Better communication with pet owners
- **Cost Effective**: Competitive SMS pricing with Twilio

### **For Pet Owners**
- **Immediate Confirmations**: Instant appointment confirmations
- **Clear Communication**: Professional, easy-to-understand messages
- **Status Updates**: Real-time appointment status notifications
- **Reliable Delivery**: High delivery rates with Twilio's infrastructure

### **For the System**
- **High Reliability**: Twilio's robust SMS infrastructure
- **Comprehensive Logging**: Complete audit trail with EF Core
- **Fallback Support**: Graceful degradation to legacy SMS service
- **Cost Tracking**: Detailed cost analysis and optimization
- **Global Reach**: International SMS capabilities

## 🔧 **Integration Points**

### **AppointmentService Integration**
- **Automatic Notifications**: SMS messages triggered on appointment creation/status changes
- **Dual Notifications**: Both vet and pet owner receive appropriate messages
- **Status-Based Messaging**: Different messages for different appointment statuses
- **Fallback Logic**: Automatic fallback to legacy SMS when Twilio fails

### **Configuration Management**
- **Dynamic Configuration**: Runtime configuration changes via API
- **Per-Type Settings**: Different settings for different notification types
- **User Preferences**: Respect user notification preferences
- **Admin Controls**: Administrative control over notification settings

This comprehensive Twilio SMS integration provides a modern, professional, and reliable notification system that enhances the PetVet appointment experience for both veterinarians and pet owners while maintaining cost efficiency and comprehensive monitoring capabilities.
