@page "/vet/calendar"
@inject IAppointmentApi _AppointmentApi
@inject IAppState _AppState
@inject IJSRuntime _JS
@inject NavigationManager _NavigationManager
@inject PetVetAuthStateProvider _PetVetAuthStateProvider

<PageTitle>Appointment Calendar</PageTitle>

<!-- Modern Vet Calendar Page -->
<div class="px-4 py-6 space-y-6">

    <!-- Header Section -->
    <div class="text-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-2">Appointment Calendar</h1>
        <p class="text-gray-600">View and manage your appointments in calendar view</p>
    </div>

    <!-- Calendar Controls -->
    <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 mb-6">
        <div class="flex items-center justify-between">
            <button class="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-xl flex items-center justify-center transition-all"
                    @onclick="PreviousMonth">
                <i class="fas fa-chevron-left text-gray-600"></i>
            </button>

            <h2 class="text-xl font-bold text-gray-800">@currentDate.ToString("MMMM yyyy")</h2>

            <button class="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-xl flex items-center justify-center transition-all"
                    @onclick="NextMonth">
                <i class="fas fa-chevron-right text-gray-600"></i>
            </button>
        </div>
    </div>

    <!-- Modern Calendar Grid -->
    <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
        <!-- Calendar Header -->
        <div class="grid grid-cols-7 bg-gray-50">
            <div class="p-3 text-center text-sm font-medium text-gray-700 border-r border-gray-200">Sun</div>
            <div class="p-3 text-center text-sm font-medium text-gray-700 border-r border-gray-200">Mon</div>
            <div class="p-3 text-center text-sm font-medium text-gray-700 border-r border-gray-200">Tue</div>
            <div class="p-3 text-center text-sm font-medium text-gray-700 border-r border-gray-200">Wed</div>
            <div class="p-3 text-center text-sm font-medium text-gray-700 border-r border-gray-200">Thu</div>
            <div class="p-3 text-center text-sm font-medium text-gray-700 border-r border-gray-200">Fri</div>
            <div class="p-3 text-center text-sm font-medium text-gray-700">Sat</div>
        </div>

        <!-- Calendar Body -->
        <div class="grid grid-cols-7">
            @foreach (var week in calendarDays)
            {
                @foreach (var day in week)
                {
                    <div class="min-h-[120px] p-2 border-r border-b border-gray-100 @(day.IsCurrentMonth ? "bg-white" : "bg-gray-50") @(day.IsToday ? "bg-blue-50 border-blue-200" : "")">
                        <!-- Day Number -->
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-sm font-medium @(day.IsCurrentMonth ? "text-gray-800" : "text-gray-400") @(day.IsToday ? "text-blue-600" : "")">
                                @day.Date.Day
                            </span>
                            @if (day.Appointments.Any())
                            {
                                <span class="w-2 h-2 bg-blue-500 rounded-full"></span>
                            }
                        </div>

                        <!-- Appointments -->
                        <div class="space-y-1">
                            @if (day.Appointments.Any())
                            {
                                var firstAppointment = day.Appointments.First();
                                <div class="p-1 rounded-lg text-xs cursor-pointer hover:shadow-sm transition-all @GetCalendarStatusClass(firstAppointment.Status)"
                                     @onclick="() => ShowAppointmentDetails(firstAppointment)">
                                    <div class="font-medium truncate">@firstAppointment.Time</div>
                                    <div class="truncate opacity-90">@firstAppointment.PetOwnerName</div>
                                </div>

                                @if (day.Appointments.Count > 1)
                                {
                                    <div class="text-xs text-blue-600 cursor-pointer hover:text-blue-800 font-medium"
                                         @onclick="() => ShowDayAppointments(day)">
                                        +@(day.Appointments.Count - 1) more
                                    </div>
                                }
                            }
                        </div>
                    </div>
                }
            }
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="mt-6">
        <a href="/vet/appointments"
           class="w-full bg-gradient-to-r from-purple-500 to-indigo-600 text-white py-4 px-6 rounded-2xl font-medium hover:from-purple-600 hover:to-indigo-700 transition-all transform hover:scale-105 shadow-lg flex items-center justify-center gap-2">
            <i class="fas fa-list"></i>
            <span>View All Appointments</span>
        </a>
    </div>

</div>

<!-- Modern Appointment Details Modal -->
@if (isAppointmentModalOpen && selectedAppointment != null)
{
    <Modal Title="Appointment Details"
           ActionButtonText="Close"
           OnActionButtonClick="CloseAppointmentModal"
           OnCancelButtonClick="CloseAppointmentModal"
           IsVisible="isAppointmentModalOpen"
           Size="ModalSize.Large">

        <div class="space-y-4">
            <!-- Patient Info -->
            <div class="bg-gray-50 rounded-xl p-4">
                <div class="flex items-center gap-3 mb-3">
                    <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-blue-600"></i>
                    </div>
                    <div>
                        <h3 class="font-bold text-gray-800">@selectedAppointment.PetOwnerName</h3>
                        <p class="text-sm text-gray-600">Patient</p>
                    </div>
                </div>
            </div>

            <!-- Appointment Details -->
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-gray-50 rounded-xl p-4">
                    <div class="flex items-center gap-2 mb-2">
                        <i class="fas fa-calendar text-purple-600"></i>
                        <span class="text-sm font-medium text-gray-700">Date</span>
                    </div>
                    <p class="font-bold text-gray-800">@GetMonthFromDate(selectedAppointment.AppointmentDate) @GetDayFromDate(selectedAppointment.AppointmentDate)</p>
                </div>

                <div class="bg-gray-50 rounded-xl p-4">
                    <div class="flex items-center gap-2 mb-2">
                        <i class="fas fa-clock text-blue-600"></i>
                        <span class="text-sm font-medium text-gray-700">Time</span>
                    </div>
                    <p class="font-bold text-gray-800">@selectedAppointment.Time</p>
                </div>
            </div>

            <!-- Status -->
            <div class="bg-gray-50 rounded-xl p-4">
                <div class="flex items-center gap-2 mb-2">
                    <i class="fas fa-info-circle text-green-600"></i>
                    <span class="text-sm font-medium text-gray-700">Status</span>
                </div>
                <span class="px-3 py-1 rounded-full text-sm font-medium @GetStatusClass(selectedAppointment.Status)">
                    @selectedAppointment.Status
                </span>
            </div>

            <!-- Notes -->
            @if (!string.IsNullOrEmpty(selectedAppointment.Notes))
            {
                <div class="bg-gray-50 rounded-xl p-4">
                    <div class="flex items-center gap-2 mb-2">
                        <i class="fas fa-clipboard-list text-orange-600"></i>
                        <span class="text-sm font-medium text-gray-700">Notes</span>
                    </div>
                    <p class="text-gray-800">@selectedAppointment.Notes</p>
                </div>
            }

            <!-- Actions -->
            <div class="space-y-3 pt-4 border-t border-gray-200">
                @if (selectedAppointment.Status == nameof(AppointmentStatus.Confirmed))
                {
                    <div class="grid grid-cols-2 gap-3">
                        <button class="bg-green-500 text-white py-3 px-4 rounded-xl font-medium hover:bg-green-600 transition-all"
                                @onclick="() => MarkComplete(selectedAppointment.Id)">
                            <i class="fas fa-check-circle mr-2"></i>Mark Complete
                        </button>
                        <button class="bg-red-500 text-white py-3 px-4 rounded-xl font-medium hover:bg-red-600 transition-all"
                                @onclick="() => CancelAppointment(selectedAppointment.Id)">
                            <i class="fas fa-times-circle mr-2"></i>Cancel
                        </button>
                    </div>
                }
                <button class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-3 px-6 rounded-xl font-medium hover:from-blue-600 hover:to-purple-700 transition-all"
                        @onclick="() => Contact(selectedAppointment.PetOwnerId)">
                    <i class="fas fa-comments mr-2"></i>Message Patient
                </button>
            </div>
        </div>
    </Modal>
}

<!-- Modern Day Appointments Modal -->
@if (isDayModalOpen && selectedDay != null)
{
    <Modal Title="@GetDayModalTitle()"
           ActionButtonText="Close"
           OnActionButtonClick="CloseDayModal"
           OnCancelButtonClick="CloseDayModal"
           IsVisible="isDayModalOpen"
           Size="ModalSize.Large">

        <div class="space-y-4">
            @foreach (var appointment in selectedDay.Appointments.OrderBy(a => a.Time))
            {
                <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-all">
                    <div class="flex items-start gap-4">
                        <!-- Time Badge -->
                        <div class="flex-shrink-0">
                            <div class="w-16 h-16 @GetCalendarStatusBgClass(appointment.Status) rounded-2xl flex flex-col items-center justify-center text-white">
                                <span class="text-xs font-medium">@GetMonthFromDate(appointment.AppointmentDate)</span>
                                <span class="text-lg font-bold">@GetDayFromDate(appointment.AppointmentDate)</span>
                            </div>
                        </div>

                        <!-- Appointment Info -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-start justify-between mb-2">
                                <div>
                                    <h3 class="font-bold text-gray-800">@appointment.PetOwnerName</h3>
                                    <div class="flex items-center gap-2 text-sm text-gray-600">
                                        <i class="fas fa-clock"></i>
                                        <span>@appointment.Time</span>
                                    </div>
                                </div>
                                <span class="px-3 py-1 rounded-full text-xs font-medium @GetStatusClass(appointment.Status)">
                                    @appointment.Status
                                </span>
                            </div>

                            @if (!string.IsNullOrEmpty(appointment.Notes))
                            {
                                <div class="bg-gray-50 rounded-xl p-3 mb-3">
                                    <div class="flex items-start gap-2 text-sm text-gray-600">
                                        <i class="fas fa-clipboard-list mt-0.5"></i>
                                        <span>@appointment.Notes</span>
                                    </div>
                                </div>
                            }

                            <!-- Action Buttons -->
                            <div class="flex gap-2 flex-wrap">
                                @if (appointment.Status == nameof(AppointmentStatus.Confirmed))
                                {
                                    <button class="px-3 py-2 bg-green-100 text-green-700 rounded-xl text-xs font-medium hover:bg-green-200 transition-all"
                                            @onclick="() => MarkComplete(appointment.Id)">
                                        <i class="fas fa-check-circle mr-1"></i>Mark Complete
                                    </button>
                                    <button class="px-3 py-2 bg-red-100 text-red-700 rounded-xl text-xs font-medium hover:bg-red-200 transition-all"
                                            @onclick="() => CancelAppointment(appointment.Id)">
                                        <i class="fas fa-times-circle mr-1"></i>Cancel
                                    </button>
                                }
                                <button class="px-3 py-2 bg-blue-100 text-blue-700 rounded-xl text-xs font-medium hover:bg-blue-200 transition-all"
                                        @onclick="() => Contact(appointment.PetOwnerId)">
                                    <i class="fas fa-comments mr-1"></i>Message
                                </button>
                                <button class="px-3 py-2 bg-purple-100 text-purple-700 rounded-xl text-xs font-medium hover:bg-purple-200 transition-all"
                                        @onclick="() => ShowAppointmentDetails(appointment)">
                                    <i class="fas fa-eye mr-1"></i>Details
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </Modal>
}

<!-- Confirmation Modal -->
<BsModal OnModalConfirmation="ConfirmCancel_Click"
         ButtonText="Cancel Appointment"
         Title="Are you sure you want to cancel this appointment?">
</BsModal>

@code {
    private int _currentUserId;
    private List<AppointmentDto> appointments = new();
    private DateTime currentDate = DateTime.Today;
    private List<List<CalendarDay>> calendarDays = new();

    // Modal state
    private bool isAppointmentModalOpen = false;
    private AppointmentDto selectedAppointment;
    private bool isDayModalOpen = false;
    private CalendarDay selectedDay;

    protected override async Task OnInitializedAsync()
    {
        _currentUserId = _PetVetAuthStateProvider.User.Id;
        await LoadAppointments();
    }

    private async Task LoadAppointments()
    {
        try
        {
            _AppState.ShowLoader("Loading Appointments");
            appointments = await _AppointmentApi.GetVetAppointmentsAsync(_currentUserId);
            GenerateCalendar();
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error loading appointments: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private void GenerateCalendar()
    {
        calendarDays = new List<List<CalendarDay>>();

        // Determine the first day of the month
        var firstDayOfMonth = new DateTime(currentDate.Year, currentDate.Month, 1);

        // Determine the last day of the month
        var lastDayOfMonth = firstDayOfMonth.AddMonths(1).AddDays(-1);

        // Determine the first day to display (the previous Sunday or the first day of the month)
        var firstDayToDisplay = firstDayOfMonth.DayOfWeek == DayOfWeek.Sunday
            ? firstDayOfMonth
            : firstDayOfMonth.AddDays(-(int)firstDayOfMonth.DayOfWeek);

        // Generate calendar for 6 weeks (which covers all possible month views)
        for (int week = 0; week < 6; week++)
        {
            var weekDays = new List<CalendarDay>();

            for (int day = 0; day < 7; day++)
            {
                var date = firstDayToDisplay.AddDays(week * 7 + day);
                var isCurrentMonth = date.Month == currentDate.Month;
                var isToday = date.Date == DateTime.Today;

                // Get appointments for this day
                var dayAppointments = appointments
                    .Where(a => DateTime.Parse(a.AppointmentDate).Date == date.Date)
                    .ToList();

                weekDays.Add(new CalendarDay
                    {
                        Date = date,
                        IsCurrentMonth = isCurrentMonth,
                        IsToday = isToday,
                        Appointments = dayAppointments
                    });
            }

            calendarDays.Add(weekDays);

            // If the last day of the last week is already past the end of the month, we're done
            if (weekDays[6].Date > lastDayOfMonth && week >= 3)
                break;
        }
    }

    private void PreviousMonth()
    {
        currentDate = currentDate.AddMonths(-1);
        GenerateCalendar();
    }

    private void NextMonth()
    {
        currentDate = currentDate.AddMonths(1);
        GenerateCalendar();
    }

    private string GetStatusClass(string status)
    {
        return status switch
        {
            nameof(AppointmentStatus.Confirmed) => "bg-blue-100 text-blue-700",
            nameof(AppointmentStatus.Completed) => "bg-green-100 text-green-700",
            nameof(AppointmentStatus.Cancelled) => "bg-red-100 text-red-700",
            nameof(AppointmentStatus.Expired) => "bg-gray-100 text-gray-700",
            _ => "bg-gray-100 text-gray-700"
        };
    }

    private string GetCalendarStatusClass(string status)
    {
        return status switch
        {
            nameof(AppointmentStatus.Confirmed) => "bg-blue-100 text-blue-800 border border-blue-200",
            nameof(AppointmentStatus.Completed) => "bg-green-100 text-green-800 border border-green-200",
            nameof(AppointmentStatus.Cancelled) => "bg-red-100 text-red-800 border border-red-200",
            nameof(AppointmentStatus.Expired) => "bg-gray-100 text-gray-800 border border-gray-200",
            _ => "bg-gray-100 text-gray-800 border border-gray-200"
        };
    }

    private string GetCalendarStatusBgClass(string status)
    {
        return status switch
        {
            nameof(AppointmentStatus.Confirmed) => "bg-gradient-to-br from-blue-500 to-blue-600",
            nameof(AppointmentStatus.Completed) => "bg-gradient-to-br from-green-500 to-green-600",
            nameof(AppointmentStatus.Cancelled) => "bg-gradient-to-br from-red-500 to-red-600",
            nameof(AppointmentStatus.Expired) => "bg-gradient-to-br from-gray-500 to-gray-600",
            _ => "bg-gradient-to-br from-gray-500 to-gray-600"
        };
    }

    private string GetDayModalTitle()
    {
        if (selectedDay == null) return "Appointments";
        return $"Appointments on {selectedDay.Date.ToString("MMMM d, yyyy")}";
    }

    private void ShowAppointmentDetails(AppointmentDto appointment)
    {
        selectedAppointment = appointment;
        isAppointmentModalOpen = true;

        // If day modal is open, close it
        if (isDayModalOpen)
        {
            isDayModalOpen = false;
        }
    }

    private void ShowDayAppointments(CalendarDay day)
    {
        selectedDay = day;
        isDayModalOpen = true;
    }

    private void CloseAppointmentModal()
    {
        isAppointmentModalOpen = false;
        selectedAppointment = null;
    }

    private void CloseDayModal()
    {
        isDayModalOpen = false;
        selectedDay = null;
    }

    private async Task MarkComplete(int appointmentId)
    {
        try
        {
            _AppState.ShowLoader("Updating appointment");
            var response = await _AppointmentApi.UpdateAppointmentStatusAsync(appointmentId, nameof(AppointmentStatus.Completed));
            if (response.IsSuccess)
            {
                await _JS.ToastrSuccess("Appointment marked as completed");
                await LoadAppointments();
                CloseAppointmentModal();
                CloseDayModal();
            }
            else
            {
                await _JS.ToastrError($"Failed to update appointment: {response.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error updating appointment: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private async Task CancelAppointment(int appointmentId)
    {
        try
        {
            _AppState.ShowLoader("Cancelling appointment");
            var response = await _AppointmentApi.UpdateAppointmentStatusAsync(appointmentId, nameof(AppointmentStatus.Cancelled));
            if (response.IsSuccess)
            {
                await _JS.ToastrSuccess("Appointment cancelled successfully");
                await LoadAppointments();
                CloseAppointmentModal();
                CloseDayModal();
            }
            else
            {
                await _JS.ToastrError($"Failed to cancel appointment: {response.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error cancelling appointment: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private void Contact(int petOwnerId)
    {
        // Navigate to chat with the patient
        _NavigationManager.NavigateTo($"/vet/chat?userId={petOwnerId}");
    }

    private string GetMonthFromDate(string dateString)
    {
        if (DateTime.TryParse(dateString, out DateTime date))
        {
            return date.ToString("MMM");
        }
        return "";
    }

    private string GetDayFromDate(string dateString)
    {
        if (DateTime.TryParse(dateString, out DateTime date))
        {
            return date.Day.ToString();
        }
        return "";
    }

    private async Task ConfirmCancel_Click(bool isConfirmed)
    {
        if (isConfirmed && selectedAppointment != null)
        {
            await CancelAppointment(selectedAppointment.Id);
        }
    }

    // Calendar day class to store day-specific data
    public class CalendarDay
    {
        public DateTime Date { get; set; }
        public bool IsCurrentMonth { get; set; }
        public bool IsToday { get; set; }
        public List<AppointmentDto> Appointments { get; set; } = new();
    }
} 