using PetVet.Api.Services;
using PetVet.Shared;
using PetVet.Shared.DTOs;

namespace PetVet.Api.Endpoints
{
    public static class AppointmentEndpoints
    {
        public static IEndpointRouteBuilder MapAppointmentEndpoints(this IEndpointRouteBuilder app)
        {
            var appointmentGroup = app.MapGroup("/api/appointments")
                .RequireAuthorization();

            // Time slots management endpoints
            appointmentGroup.MapGet("/vets/{vetId:int}/timeslots", async (int vetId, AppointmentService appointmentService) =>
                Results.Ok(await appointmentService.GetVetTimeSlotsAsync(vetId)));

            appointmentGroup.MapPost("/vets/{vetId:int}/timeslots", async (int vetId, List<VetTimeSlotDto> timeSlots, AppointmentService appointmentService) =>
                Results.Ok(await appointmentService.SaveVetTimeSlotsAsync(vetId, timeSlots)));

            appointmentGroup.MapPost("/vets/timeslots/{timeSlotId:int}/book", async (int timeSlotId, AppointmentService appointmentService) =>
                Results.Ok(await appointmentService.BookTimeSlotAsync(timeSlotId)));

            // Create appointment
            appointmentGroup.MapPost("", async (AppointmentDto appointmentDto, AppointmentService appointmentService) =>
                Results.Ok(await appointmentService.CreateAppointmentAsync(appointmentDto)));

            // Get appointments for a pet owner
            appointmentGroup.MapGet("/petowner/{petOwnerId:int}", async (int petOwnerId, AppointmentService appointmentService) =>
                Results.Ok(await appointmentService.GetPetOwnerAppointmentsAsync(petOwnerId)));

            // Get appointments for a vet
            appointmentGroup.MapGet("/vet/{vetId:int}", async (int vetId, AppointmentService appointmentService) =>
                Results.Ok(await appointmentService.GetVetAppointmentsAsync(vetId)));

            // Get appointment details
            appointmentGroup.MapGet("/{appointmentId:int}", async (int appointmentId, AppointmentService appointmentService) =>
                Results.Ok(await appointmentService.GetAppointmentDetailsAsync(appointmentId)));

            // Update appointment status
            appointmentGroup.MapPatch("/{appointmentId:int}/status", async (int appointmentId, string status, AppointmentService appointmentService) =>
                Results.Ok(await appointmentService.UpdateAppointmentStatusAsync(appointmentId, status)));

            // Reschedule appointment
            appointmentGroup.MapPatch("/{appointmentId:int}/reschedule", async (int appointmentId, int newTimeSlotId, string newAppointmentDate, AppointmentService appointmentService) =>
                Results.Ok(await appointmentService.RescheduleAppointmentAsync(appointmentId, newTimeSlotId, newAppointmentDate)));

            // Get calendar data for a user
            appointmentGroup.MapGet("/calendar/{userId:int}", async (int userId, string userRole, AppointmentService appointmentService) =>
                Results.Ok(await appointmentService.GetCalendarDataAsync(userId, userRole)));

            return app;
        }
    }
} 