using System.Text.Json.Serialization;

namespace PetVet.Mobile.Models;

/// <summary>
/// Call-related message types for SignalR ChatHub
/// </summary>
public enum CallMessageType
{
    CallInvite,
    CallAccept,
    CallDecline,
    CallStart,
    CallEnd,
    ParticipantJoined,
    ParticipantLeft,
    AudioData,
    VideoData,
    CallStatus
}

public enum CallType
{
    Audio,
    Video,
    AudioVideo
}

public enum CallStatus
{
    Initiating,
    Ringing,
    Connected,
    OnHold,
    Ended,
    Failed
}

/// <summary>
/// Call invitation message sent through ChatHub
/// </summary>
public class CallInviteMessage
{
    public string CallId { get; set; } = Guid.NewGuid().ToString();
    public int FromUserId { get; set; }  
    public string FromUserName { get; set; } = "";
    public int ToUserId { get; set; }  
    public string ThreadId { get; set; } = "";
    public CallType CallType { get; set; } = CallType.AudioVideo;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public string Message { get; set; } = "";
}

/// <summary>
/// Call response message (accept/decline)
/// </summary>
public class CallResponseMessage
{
    public string CallId { get; set; } = "";
    public int UserId { get; set; }  
    public string UserName { get; set; } = "";
    public bool Accepted { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Call status update message
/// </summary>
public class CallStatusMessage
{
    public string CallId { get; set; } = "";
    public int UserId { get; set; } 
    public CallStatus Status { get; set; }
    public string Message { get; set; } = "";
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Audio data message for real-time streaming
/// </summary>
public class AudioDataMessage
{
    public string CallId { get; set; } = "";
    public int FromUserId { get; set; } 
    public byte[] AudioData { get; set; } = Array.Empty<byte>();
    public int SampleRate { get; set; } = 44100;
    public int Channels { get; set; } = 1;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Video data message for real-time streaming
/// </summary>
public class VideoDataMessage
{
    public string CallId { get; set; } = "";
    public int FromUserId { get; set; } 
    public byte[] VideoData { get; set; } = Array.Empty<byte>();
    public int Width { get; set; } = 320;
    public int Height { get; set; } = 240;
    public string Format { get; set; } = "RGB";
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Active call session information
/// </summary>
public class CallSession
{
    public string CallId { get; set; } = "";
    public string ThreadId { get; set; } = "";
    public CallType CallType { get; set; }
    public CallStatus Status { get; set; } = CallStatus.Initiating;
    public int InitiatorId { get; set; } 
    public string InitiatorName { get; set; } = "";
    public List<CallParticipant> Participants { get; set; } = new();
    public DateTime StartTime { get; set; } = DateTime.UtcNow;
    public DateTime? EndTime { get; set; }
    public Dictionary<string, object> Metadata { get; set; } = new();
}

/// <summary>
/// Call participant information
/// </summary>
public class CallParticipant
{
    public int UserId { get; set; }  
    public string UserName { get; set; } = "";
    public bool IsAudioEnabled { get; set; } = true;
    public bool IsVideoEnabled { get; set; } = false;
    public bool IsMuted { get; set; } = false;
    public DateTime JoinedAt { get; set; } = DateTime.UtcNow;
    public DateTime LastActivity { get; set; } = DateTime.UtcNow;
}

/// <summary>
/// Call quality metrics
/// </summary>
public class CallQualityMetrics
{
    public string CallId { get; set; } = "";
    public int AudioPacketsLost { get; set; }
    public int VideoFramesDropped { get; set; }
    public double AudioLatency { get; set; }
    public double VideoLatency { get; set; }
    public double NetworkQuality { get; set; } = 1.0; // 0.0 to 1.0
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}
