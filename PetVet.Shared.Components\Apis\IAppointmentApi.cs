using PetVet.Shared.DTOs;
using Refit;

namespace PetVet.Shared.Components.Apis
{
    [Headers("Authorization: Bearer ")]
    public interface IAppointmentApi
    {
        [Get("/api/appointments/vets/{vetId}/timeslots")]
        Task<List<VetTimeSlotDto>> GetVetTimeSlotsAsync(int vetId);

        [Post("/api/appointments/vets/{vetId}/timeslots")]
        Task<PetVetApiResponse> SaveVetTimeSlotsAsync(int vetId, List<VetTimeSlotDto> timeSlots);

        [Post("/api/appointments/vets/timeslots/{timeSlotId}/book")]
        Task<PetVetApiResponse> BookTimeSlotAsync(int timeSlotId);

        [Post("/api/appointments")]
        Task<PetVetApiResponse> CreateAppointmentAsync(AppointmentDto appointmentDto);

        [Get("/api/appointments/petowner/{petOwnerId}")]
        Task<List<AppointmentDto>> GetPetOwnerAppointmentsAsync(int petOwnerId);

        [Get("/api/appointments/vet/{vetId}")]
        Task<List<AppointmentDto>> GetVetAppointmentsAsync(int vetId);

        [Get("/api/appointments/{appointmentId}")]
        Task<AppointmentDto> GetAppointmentDetailsAsync(int appointmentId);

        [Patch("/api/appointments/{appointmentId}/status")]
        Task<PetVetApiResponse> UpdateAppointmentStatusAsync(int appointmentId, string status);

        [Patch("/api/appointments/{appointmentId}/reschedule")]
        Task<PetVetApiResponse> RescheduleAppointmentAsync(int appointmentId, int newTimeSlotId, string newAppointmentDate);

        [Get("/api/appointments/calendar/{userId}")]
        Task<List<AppointmentDto>> GetCalendarDataAsync(int userId, string userRole);
    }
} 