using Microsoft.EntityFrameworkCore;
using PetVet.Api.Data;
using PetVet.Shared;
using PetVet.Shared.DTOs;

namespace PetVet.Api.Services
{
    public class RecentActivityService
    {
        private readonly IDbContextFactory<PetVetContext> _contextFactory;

        public RecentActivityService(IDbContextFactory<PetVetContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<List<RecentActivityDto>> GetPetOwnerRecentActivitiesAsync(int petOwnerId, int limit = 10)
        {
            using var context = _contextFactory.CreateDbContext();
            var activities = new List<RecentActivityDto>();

            // Get recent appointments
            var recentAppointments = await context.Appointments
                .AsNoTracking()
                .Where(a => a.PetOwnerId == petOwnerId)
                .Include(a => a.Vet)
                .Include(a => a.TimeSlot)
                .OrderByDescending(a => a.CreatedAt)
                .Take(5)
                .ToListAsync();

            foreach (var appointment in recentAppointments)
            {
                var activityType = appointment.Status.ToLower() switch
                {
                    "completed" => "AppointmentCompleted",
                    "confirmed" => "AppointmentScheduled",
                    "cancelled" => "AppointmentCancelled",
                    _ => "AppointmentScheduled"
                };

                var title = appointment.Status.ToLower() switch
                {
                    "completed" => "Appointment completed",
                    "confirmed" => "Appointment scheduled",
                    "cancelled" => "Appointment cancelled",
                    _ => "Appointment updated"
                };

                var icon = appointment.Status.ToLower() switch
                {
                    "completed" => "fas fa-check",
                    "confirmed" => "fas fa-calendar",
                    "cancelled" => "fas fa-times",
                    _ => "fas fa-calendar"
                };

                var iconColor = appointment.Status.ToLower() switch
                {
                    "completed" => "text-green-600",
                    "confirmed" => "text-blue-600",
                    "cancelled" => "text-red-600",
                    _ => "text-blue-600"
                };

                var bgColor = appointment.Status.ToLower() switch
                {
                    "completed" => "bg-green-100",
                    "confirmed" => "bg-blue-100",
                    "cancelled" => "bg-red-100",
                    _ => "bg-blue-100"
                };

                activities.Add(new RecentActivityDto
                {
                    Id = appointment.Id,
                    ActivityType = activityType,
                    Title = title,
                    Description = $"Dr. {appointment.Vet?.Name ?? "Unknown"} • {FormatAppointmentDate(appointment.AppointmentDate)} {appointment.TimeSlot?.Time ?? ""}",
                    PetName = "", // We don't have direct pet association in appointments
                    Icon = icon,
                    IconColor = iconColor,
                    BackgroundColor = bgColor,
                    ActivityDate = appointment.CreatedAt,
                    TimeAgo = GetTimeAgo(appointment.CreatedAt),
                    RelatedId = appointment.Id
                });
            }

            // Get recently added pets
            var recentPets = await context.Pets
                .AsNoTracking()
                .Where(p => p.UserId == petOwnerId)
                .Include(p => p.Category)
                .OrderByDescending(p => p.Id)
                .Take(3)
                .ToListAsync();

            foreach (var pet in recentPets)
            {
                // Only show pets added in the last 30 days
                var petAge = DateTime.Now - (pet.DateOfBirth ?? DateTime.Now.AddYears(-1));
                if (petAge.TotalDays <= 30)
                {
                    activities.Add(new RecentActivityDto
                    {
                        Id = pet.Id,
                        ActivityType = "PetAdded",
                        Title = "New pet added",
                        Description = $"{pet.Name} • {pet.Breed}",
                        PetName = pet.Name,
                        Icon = "fas fa-paw",
                        IconColor = "text-purple-600",
                        BackgroundColor = "bg-purple-100",
                        ActivityDate = pet.DateOfBirth ?? DateTime.Now,
                        TimeAgo = GetTimeAgo(pet.DateOfBirth ?? DateTime.Now),
                        RelatedId = pet.Id
                    });
                }

                // Add vaccination status updates for recently vaccinated pets
                if (pet.VaccinationStatus.ToLower().Contains("vaccinated") && 
                    pet.VaccinationStatus.ToLower() != "not vaccinated")
                {
                    activities.Add(new RecentActivityDto
                    {
                        Id = pet.Id + 10000, // Unique ID for vaccination activity
                        ActivityType = "VaccinationCompleted",
                        Title = "Vaccination completed",
                        Description = $"{pet.Name} • {pet.VaccinationStatus}",
                        PetName = pet.Name,
                        Icon = "fas fa-syringe",
                        IconColor = "text-green-600",
                        BackgroundColor = "bg-green-100",
                        ActivityDate = pet.DateOfBirth?.AddDays(60) ?? DateTime.Now.AddDays(-7), // Approximate vaccination date
                        TimeAgo = GetTimeAgo(pet.DateOfBirth?.AddDays(60) ?? DateTime.Now.AddDays(-7)),
                        RelatedId = pet.Id
                    });
                }
            }

            // Sort all activities by date and take the most recent ones
            return activities
                .OrderByDescending(a => a.ActivityDate)
                .Take(limit)
                .ToList();
        }

        private string GetTimeAgo(DateTime dateTime)
        {
            var timeSpan = DateTime.Now - dateTime;

            if (timeSpan.TotalMinutes < 1)
                return "Just now";
            else if (timeSpan.TotalMinutes < 60)
                return $"{(int)timeSpan.TotalMinutes} minute(s) ago";
            else if (timeSpan.TotalHours < 24)
                return $"{(int)timeSpan.TotalHours} hour(s) ago";
            else if (timeSpan.TotalDays < 7)
                return $"{(int)timeSpan.TotalDays} day(s) ago";
            else if (timeSpan.TotalDays < 30)
                return $"{(int)(timeSpan.TotalDays / 7)} week(s) ago";
            else
                return $"{(int)(timeSpan.TotalDays / 30)} month(s) ago";
        }

        private string FormatAppointmentDate(string dateString)
        {
            if (DateTime.TryParse(dateString, out DateTime date))
            {
                var today = DateTime.Today;
                var tomorrow = today.AddDays(1);
                var yesterday = today.AddDays(-1);

                if (date.Date == today)
                    return "Today";
                else if (date.Date == tomorrow)
                    return "Tomorrow";
                else if (date.Date == yesterday)
                    return "Yesterday";
                else
                    return date.ToString("MMM d");
            }
            return dateString;
        }
    }
} 