@page "/call-demo"
@using PetVet.Shared.Models.AzureCommunication
@using PetVet.Shared.Services
@inject IAzureCommunicationService CommunicationService
@inject IJSRuntime JSRuntime

<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Azure Communication Services Demo</h1>
            <p class="text-gray-600">Test audio/video calling functionality with dummy implementation</p>
        </div>

        <!-- Service Status -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Service Status</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                        <span class="text-green-800 font-medium">Service Health</span>
                    </div>
                    <p class="text-green-600 text-sm mt-1">@(isServiceHealthy ? "Healthy" : "Unhealthy")</p>
                </div>
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                        <span class="text-blue-800 font-medium">Active Calls</span>
                    </div>
                    <p class="text-blue-600 text-sm mt-1">@activeCalls.Count</p>
                </div>
                <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                        <span class="text-purple-800 font-medium">Service Mode</span>
                    </div>
                    <p class="text-purple-600 text-sm mt-1">@serviceMode</p>
                </div>
            </div>
        </div>

        <!-- Call Controls -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Start New Call</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Participant ID</label>
                    <input @bind="participantId" 
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="Enter participant ID" />
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">Call Type</label>
                    <select @bind="selectedCallType" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="@CallType.Audio">Audio Only</option>
                        <option value="@CallType.AudioVideo">Audio + Video</option>
                        <option value="@CallType.Video">Video Only</option>
                    </select>
                </div>
            </div>
            
            <div class="flex flex-wrap gap-3 mt-6">
                <button @onclick="StartCall" 
                        disabled="@(isLoading || string.IsNullOrEmpty(participantId))"
                        class="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    @if (isLoading)
                    {
                        <span class="flex items-center">
                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Starting...
                        </span>
                    }
                    else
                    {
                        <span>Start Call</span>
                    }
                </button>
                
                <button @onclick="StartEmergencyCall" 
                        disabled="@isLoading"
                        class="bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    Emergency Call
                </button>
                
                <button @onclick="RefreshStatus" 
                        disabled="@isLoading"
                        class="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors">
                    Refresh Status
                </button>
            </div>
        </div>

        <!-- Active Calls -->
        @if (activeCalls.Any())
        {
            <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">Active Calls</h2>
                <div class="space-y-4">
                    @foreach (var call in activeCalls)
                    {
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex justify-between items-start mb-3">
                                <div>
                                    <h3 class="font-semibold text-gray-800">Call ID: @call.CallId[..8]...</h3>
                                    <p class="text-sm text-gray-600">Status: @call.Status</p>
                                    <p class="text-sm text-gray-600">Duration: @call.Duration.ToString(@"mm\:ss")</p>
                                    <p class="text-sm text-gray-600">Participants: @call.Participants.Count</p>
                                </div>
                                <div class="flex gap-2">
                                    <button @onclick="() => EndCall(call.CallId)" 
                                            class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 rounded text-sm">
                                        End Call
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Participants -->
                            <div class="mt-3">
                                <h4 class="text-sm font-medium text-gray-700 mb-2">Participants:</h4>
                                <div class="flex flex-wrap gap-2">
                                    @foreach (var participant in call.Participants)
                                    {
                                        <div class="bg-gray-100 rounded-full px-3 py-1 text-sm">
                                            <span class="@(participant.Status == ParticipantStatus.Connected ? "text-green-600" : "text-gray-600")">
                                                ●
                                            </span>
                                            @participant.DisplayName
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        }

        <!-- Call History/Logs -->
        <div class="bg-white rounded-xl shadow-lg p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Call Events</h2>
            <div class="max-h-64 overflow-y-auto space-y-2">
                @foreach (var log in callLogs.TakeLast(10).Reverse())
                {
                    <div class="text-sm p-2 bg-gray-50 rounded border-l-4 @GetLogBorderColor(log.Type)">
                        <span class="text-gray-500">[@log.Timestamp.ToString("HH:mm:ss")]</span>
                        <span class="font-medium">@log.Type:</span>
                        @log.Message
                    </div>
                }
                @if (!callLogs.Any())
                {
                    <p class="text-gray-500 text-center py-4">No call events yet</p>
                }
            </div>
        </div>
    </div>
</div>

@code {
    private bool isServiceHealthy = false;
    private string serviceMode = "Dummy";
    private List<CallSession> activeCalls = new();
    private List<CallLog> callLogs = new();
    private bool isLoading = false;
    private string participantId = "";
    private CallType selectedCallType = CallType.AudioVideo;

    protected override async Task OnInitializedAsync()
    {
        // Subscribe to call events
        CommunicationService.CallStatusChanged += OnCallStatusChanged;
        CommunicationService.CallInvitationReceived += OnCallInvitationReceived;
        
        await RefreshStatus();
    }

    private async Task RefreshStatus()
    {
        try
        {
            isServiceHealthy = await CommunicationService.IsServiceHealthyAsync();
            var stats = await CommunicationService.GetServiceStatsAsync();
            serviceMode = stats.ContainsKey("ServiceMode") ? stats["ServiceMode"].ToString() : "Unknown";
            
            activeCalls = await CommunicationService.GetActiveCallsAsync("demo-user");
            
            AddLog("Info", "Status refreshed successfully");
            StateHasChanged();
        }
        catch (Exception ex)
        {
            AddLog("Error", $"Failed to refresh status: {ex.Message}");
        }
    }

    private async Task StartCall()
    {
        if (string.IsNullOrEmpty(participantId)) return;
        
        isLoading = true;
        try
        {
            var request = new StartCallRequest
            {
                ThreadId = Guid.NewGuid().ToString(),
                InitiatorId = "demo-user",
                CallType = selectedCallType,
                ParticipantIds = new List<string> { participantId },
                Subject = $"Demo {selectedCallType} call"
            };

            var response = await CommunicationService.StartCallAsync(request);
            
            if (response.Success)
            {
                AddLog("Success", $"Call started successfully. Call ID: {response.CallId}");
                await RefreshStatus();
            }
            else
            {
                AddLog("Error", $"Failed to start call: {response.Message}");
            }
        }
        catch (Exception ex)
        {
            AddLog("Error", $"Exception starting call: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task StartEmergencyCall()
    {
        isLoading = true;
        try
        {
            var request = new EmergencyCallRequest
            {
                UserId = "demo-user",
                PetId = "demo-pet",
                EmergencyType = EmergencyType.Emergency,
                Description = "Demo emergency call",
                RequiresImmediate = true
            };

            var response = await CommunicationService.StartEmergencyCallAsync(request);
            
            if (response.Success)
            {
                AddLog("Emergency", $"Emergency call started. Vet: {response.VeterinarianName}");
                await RefreshStatus();
            }
            else
            {
                AddLog("Error", $"Failed to start emergency call: {response.Message}");
            }
        }
        catch (Exception ex)
        {
            AddLog("Error", $"Exception starting emergency call: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private async Task EndCall(string callId)
    {
        try
        {
            var request = new EndCallRequest
            {
                CallId = callId,
                UserId = "demo-user",
                Reason = "User ended call from demo"
            };

            var success = await CommunicationService.EndCallAsync(request);
            
            if (success)
            {
                AddLog("Info", $"Call {callId[..8]}... ended successfully");
                await RefreshStatus();
            }
            else
            {
                AddLog("Error", $"Failed to end call {callId[..8]}...");
            }
        }
        catch (Exception ex)
        {
            AddLog("Error", $"Exception ending call: {ex.Message}");
        }
    }

    private void OnCallStatusChanged(object? sender, CallStatusUpdate e)
    {
        InvokeAsync(() =>
        {
            AddLog("Status", $"Call {e.CallId?[..8]}... status changed to {e.Status}");
            StateHasChanged();
        });
    }

    private void OnCallInvitationReceived(object? sender, CallInvitation e)
    {
        InvokeAsync(() =>
        {
            AddLog("Invitation", $"Call invitation received from {e.FromDisplayName}");
            StateHasChanged();
        });
    }

    private void AddLog(string type, string message)
    {
        callLogs.Add(new CallLog
        {
            Timestamp = DateTime.Now,
            Type = type,
            Message = message
        });
        
        // Keep only last 50 logs
        if (callLogs.Count > 50)
        {
            callLogs.RemoveAt(0);
        }
    }

    private string GetLogBorderColor(string type) => type switch
    {
        "Success" => "border-green-500",
        "Error" => "border-red-500",
        "Emergency" => "border-red-600",
        "Warning" => "border-yellow-500",
        "Status" => "border-blue-500",
        "Invitation" => "border-purple-500",
        _ => "border-gray-400"
    };

    public void Dispose()
    {
        CommunicationService.CallStatusChanged -= OnCallStatusChanged;
        CommunicationService.CallInvitationReceived -= OnCallInvitationReceived;
    }

    private class CallLog
    {
        public DateTime Timestamp { get; set; }
        public string Type { get; set; } = "";
        public string Message { get; set; } = "";
    }
}
