﻿using PetVet.Server.DataServices.Data;
using PetVet.ServiceContracts.Features.Conversation;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using PetVet.Api.Data;
namespace PetVet.Api.Services.ChatThreads.Form;
public class ChatThreadSyncServerSideFormDataService : IChatThreadSyncFormDataService
{

    private readonly PetVetContext _context;
    private readonly IHttpContextAccessor contextAccessor;

    public ChatThreadSyncServerSideFormDataService(PetVetContext context, IHttpContextAccessor contextAccessor)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
    }
     
    public async Task<string> SaveAsync(ChatThreadSyncFormBusinessObject formBusinessObject)
    {
        var conversation = await _context.Conversations.FirstOrDefaultAsync(x => x.Id == formBusinessObject.Id);

        if (conversation == null)
        {
            conversation = new Conversation
            {
                Id = formBusinessObject.Id,
                CreatedAt = formBusinessObject.CreatedAt,
                IsDeleted = formBusinessObject.IsDeleted, 
                Title = formBusinessObject.Title

            };
            _context.Conversations.Add(conversation);
            await _context.SaveChangesAsync();
        }

        ArgumentNullException.ThrowIfNull(formBusinessObject.ChatParticipents);

        foreach (var item in formBusinessObject.ChatParticipents)
        {
            var participants = await _context.ConversationParticipants.FirstOrDefaultAsync(x => x.ConversationId == item.ConversationId
                    && x.UserId == item.UserId);
            if (participants == null)
            {
                participants = new ConversationParticipant()
                {
                    Id = item.Id,
                    ConversationId = item.ConversationId,
                    IsAdmin = item.IsAdmin,
                    JoinedAt = item.JoinedAt,
                    UserId = item.UserId,
                };
                _context.ConversationParticipants.Add(participants);
                await _context.SaveChangesAsync();
            }
        }

        return conversation.Id;
    }
     
    public async Task<ChatThreadSyncFormBusinessObject> GetItemByIdAsync(string id)
    {
        var conversation = await(from c in _context.Conversations
                                  where c.Id == id
                                  select new ChatThreadSyncFormBusinessObject()
                                  {
                                      Id = c.Id,
                                      CreatedAt = c.CreatedAt,
                                      IsDeleted = c.IsDeleted,
                                      Title = c.Title, 
                                      ChatParticipents = (from p in _context.ConversationParticipants
                                                          where p.ConversationId == c.Id
                                                          select new ChatParticipentsSyncFormBusinessObject()
                                                          {
                                                              Id = p.Id,
                                                              ConversationId = p.ConversationId,
                                                              IsAdmin = p.IsAdmin,
                                                              JoinedAt = p.JoinedAt,
                                                              UserId = p.UserId
                                                          }).ToList()
                                  }).FirstAsync();
        return conversation;
    }

    public async Task<ChatThreadSyncFormBusinessObject[]> GetItemsAsync()
    {
        var userId =Convert.ToInt32( contextAccessor.HttpContext?.User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
        var conversationIds = (from c in _context.Conversations
                             from p in _context.ConversationParticipants.Where(x => x.ConversationId == c.Id)
                             where p.UserId == userId
                             select c.Id).ToList();

        var conversations = await (from c in _context.Conversations
                                   where conversationIds.Contains(c.Id)
                                   select new ChatThreadSyncFormBusinessObject()
                                   {
                                       Id = c.Id,
                                       CreatedAt = c.CreatedAt,
                                       IsDeleted = c.IsDeleted,
                                       Title = c.Title, 
                                       ChatParticipents = (from p in _context.ConversationParticipants
                                                           where p.ConversationId == c.Id
                                                           select new ChatParticipentsSyncFormBusinessObject()
                                                           {
                                                               Id = p.Id,
                                                               ConversationId = p.ConversationId,
                                                               IsAdmin = p.IsAdmin,
                                                               JoinedAt = p.JoinedAt,
                                                               UserId = p.UserId
                                                           }).ToList()
                                   }).ToArrayAsync();
        return conversations;

    }
}
