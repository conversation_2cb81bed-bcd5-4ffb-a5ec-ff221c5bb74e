﻿.MyPetsList-CONTAINER {
    margin-top: 3rem 0;
}

.MyPetsList-HEADING {
    font-size: 1.125rem;
    font-weight: bold;
    font-family: 'Acme', sans-serif;
    cursor: default;
}

.MyPetsList-VIEWALL-BTN {
    font-size: 0.875rem;
    color: #FFC107;
    text-decoration: none;
    
    display: flex;
    align-items: center;
    transition: color 0.3s ease;
}

    .MyPetsList-VIEWALL-BTN:hover {
        color: #e0a406;
    }

.MyPetsList-VIEWALL-BTN-ICON {
    background-color: #f8c73d;
    border-radius: 0.3125rem;
    padding: 0.125rem;
    margin-left: 0.3125rem;
    font-size: 0.625rem;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 0.875rem;
    height: 0.875rem;
    color: white;
    transition: background-color 0.3s ease;
}

    .MyPetsList-VIEWALL-BTN-ICON:hover {
        background-color: #e0a406;
    }

.MyPetsList-PET-LIST {
    display: flex;
    justify-content: center;
    gap: 0.625rem;
    margin: 0.625rem 0;
}

.MyPetsList-CARD {
    border-radius: 1.25rem;
}

.MyPetsList-CARD-BODY {
    border-radius: 1.25rem;
}

.MyPetsList-IMAGE {
    height: 4.6875rem;
    width: 4.6875rem;
    object-fit: cover;
    border-radius: 50%;
}

.MyPetsList-Name {
    font-size: 1.125rem;
    font-weight: bold;
    font-family: 'Acme', sans-serif;
    cursor: default;
    color: #ffffff;
}

    .MyPetsList-Name .badge {
        font-size: 0.75rem;
        margin-left: 0.375rem;
        vertical-align: middle;
    }

.MyPetsList-VIEWPROFILE-BTN {
    display: inline-block;
    background-color: #FFC107;
    border-radius: 0.625rem;
    padding: 0.3125rem 0.625rem;
    color: #333;
    
    font-weight: bold;
    cursor: pointer;
    margin-top: 0.3125rem;
    text-decoration: none;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

    .MyPetsList-VIEWPROFILE-BTN:hover {
        color: #333;
        background-color: #e0a406;
        transform: scale(1.05);
    }

.MyPetsList-CARD-HEART-ICONS {
    background-color: white;
    border-radius: 50%;
    padding: 0.5rem;
    font-size: 1.5rem;
    color: red;
    display: inline-block;
    line-height: 1;
    box-shadow: 0 0.25rem 0.375rem rgba(0, 0, 0, 0.1);
}
