﻿@page "/auth/register"
@layout EmptyLayout
@inject IAuthApi _AuthApi
@inject NavigationManager _NavigationManager
@inject IJSRuntime _JS
@inject PetVetAuthStateProvider _PetVetAuthStateProvider
@inject IEducationApi _EducationApi
@inject IAppState _AppState

<PageTitle>Register</PageTitle>

<!-- Modern Register Page -->
<div class="min-h-screen bg-gradient-to-br from-primary-50 via-secondary-50 to-primary-100 flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="w-20 h-20 bg-gradient-to-r from-primary-500 to-secondary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-user-plus text-white text-2xl"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Create Account</h1>
            <p class="text-gray-600">Join our pet care community</p>
        </div>

        <!-- Main Card -->
        <div class="bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden">
            <!-- Role Selection -->
            <div class="p-6 border-b border-gray-100">
                <h3 class="text-lg font-bold text-gray-800 mb-4 text-center">Choose Your Role</h3>
                <div class="grid grid-cols-2 gap-3">
                    <button class="p-4 rounded-2xl border-2 transition-all @(SelectedRole == 1 ? "border-primary-500 bg-primary-50 text-primary-700" : "border-gray-200 hover:border-gray-300 text-gray-600")"
                            @onclick="() => SelectRole(1)">
                        <div class="text-center">
                            <i class="fas fa-paw text-2xl mb-2"></i>
                            <div class="font-medium">Pet Owner</div>
                        </div>
                    </button>
                    <button class="p-4 rounded-2xl border-2 transition-all @(SelectedRole == 2 ? "border-secondary-500 bg-secondary-50 text-secondary-700" : "border-gray-200 hover:border-gray-300 text-gray-600")"
                            @onclick="() => SelectRole(2)">
                        <div class="text-center">
                            <i class="fas fa-user-doctor text-2xl mb-2"></i>
                            <div class="font-medium">Veterinarian</div>
                        </div>
                    </button>
                </div>
            </div>

            <!-- Form Content -->
            <div class="p-6">
                <!-- Pet Owner Form -->
                @if (SelectedRole == 1)
                {
                    <EditForm Model="_petOwnerModel" OnValidSubmit="RegisterPetOwnerAsync">
                        <DataAnnotationsValidator />

                        <h4 class="text-lg font-bold text-gray-800 mb-6 text-center">Pet Owner Details</h4>

                        <div class="space-y-4">
                            <!-- Name Field -->
                            <div>
                                <label for="Name" class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                                <InputText id="Name"
                                           class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all"
                                           @bind-Value="_petOwnerModel.Name"
                                           placeholder="Enter your full name" />
                                <ValidationMessage For="() => _petOwnerModel.Name" class="text-red-500 text-sm mt-1" />
                            </div>

                            <!-- Email Field -->
                            <div>
                                <label for="Email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                                <InputText id="Email"
                                           class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all"
                                           @bind-Value="_petOwnerModel.Email"
                                           type="email"
                                           placeholder="Enter your email" />
                                <ValidationMessage For="() => _petOwnerModel.Email" class="text-red-500 text-sm mt-1" />
                            </div>

                            <!-- Phone Number Field -->
                            <div>
                                <label for="Phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                <InputText id="Phone"
                                           class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all"
                                           @bind-Value="_petOwnerModel.Phone"
                                           placeholder="Enter your phone number" />
                                <ValidationMessage For="() => _petOwnerModel.Phone" class="text-red-500 text-sm mt-1" />
                            </div>

                            <!-- Address Field -->
                            <div>
                                <label for="Address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                                <InputText id="Address"
                                           class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all"
                                           @bind-Value="_petOwnerModel.Address"
                                           placeholder="Enter your address" />
                                <ValidationMessage For="() => _petOwnerModel.Address" class="text-red-500 text-sm mt-1" />
                            </div>

                            <!-- Image Upload -->
                            <div>
                                <label for="ProfileImage" class="block text-sm font-medium text-gray-700 mb-2">Profile Image (Optional)</label>
                                <div class="border-2 border-dashed border-gray-300 rounded-xl p-4 text-center hover:border-teal-400 transition-all">
                                    <i class="fas fa-camera text-gray-400 text-2xl mb-2"></i>
                                    <p class="text-gray-500 mb-2">Upload your profile picture</p>
                                    <label class="inline-block px-4 py-2 bg-teal-500 text-white rounded-lg text-sm font-medium hover:bg-teal-600 transition-all cursor-pointer">
                                        Choose File
                                        <InputFile id="ProfileImage" class="hidden" accept="image/*" OnChange="HandleImageUpload" />
                                    </label>
                                </div>
                                <ValidationMessage For="() => _petOwnerModel.ImageUrl" class="text-red-500 text-sm mt-1" />
                            </div>

                            <!-- Password Field -->
                            <div>
                                <label for="Password" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                                <InputText id="Password"
                                           class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all"
                                           @bind-Value="_petOwnerModel.Password"
                                           type="password"
                                           placeholder="Create a secure password" />
                                <ValidationMessage For="() => _petOwnerModel.Password" class="text-red-500 text-sm mt-1" />
                            </div>
                        </div>

                        <!-- Error Message -->
                        @if (_error != null)
                        {
                            <div class="bg-red-50 border border-red-200 rounded-xl p-4 mt-4">
                                <div class="flex items-start justify-between">
                                    <div class="flex items-center gap-2 text-red-700">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <span class="text-sm">@_error</span>
                                    </div>
                                    <button type="button"
                                            class="text-red-400 hover:text-red-600 transition-colors"
                                            @onclick="() => _error = null">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        }

                        <!-- Register Button -->
                        <div class="mt-6">
                            @if (!_isBusy)
                            {
                                <button type="submit"
                                        class="w-full bg-gradient-to-r from-teal-500 to-blue-600 text-white py-4 px-6 rounded-2xl font-medium hover:from-teal-600 hover:to-blue-700 transition-all transform hover:scale-105 shadow-lg flex items-center justify-center gap-2">
                                    <i class="fas fa-user-plus"></i>
                                    <span>Create Pet Owner Account</span>
                                </button>
                            }
                            else
                            {
                                <button disabled type="button"
                                        class="w-full bg-gray-400 text-white py-4 px-6 rounded-2xl font-medium cursor-not-allowed flex items-center justify-center gap-2">
                                    <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                    <span>Creating Account...</span>
                                </button>
                            }
                        </div>
                    </EditForm>
                }

                <!-- Vet Form -->
                @if (SelectedRole == 2)
                {
                    <EditForm Model="_vetModel" OnValidSubmit="RegisterVetAsync">
                        <DataAnnotationsValidator />

                        <h4 class="text-lg font-bold text-gray-800 mb-6 text-center">Veterinarian Details</h4>

                        <div class="space-y-4">
                            <!-- Name Field -->
                            <div>
                                <label for="VetName" class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                                <InputText id="VetName"
                                           class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                           @bind-Value="_vetModel.Name"
                                           placeholder="Enter your full name" />
                                <ValidationMessage For="() => _vetModel.Name" class="text-red-500 text-sm mt-1" />
                            </div>

                            <!-- Email Field -->
                            <div>
                                <label for="VetEmail" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                                <InputText id="VetEmail"
                                           class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                           @bind-Value="_vetModel.Email"
                                           type="email"
                                           placeholder="Enter your email" />
                                <ValidationMessage For="() => _vetModel.Email" class="text-red-500 text-sm mt-1" />
                            </div>

                            <!-- Phone Number Field -->
                            <div>
                                <label for="VetPhone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                <InputText id="VetPhone"
                                           class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                           @bind-Value="_vetModel.Phone"
                                           placeholder="Enter your phone number" />
                                <ValidationMessage For="() => _vetModel.Phone" class="text-red-500 text-sm mt-1" />
                            </div>

                            <!-- Address Field -->
                            <div>
                                <label for="VetAddress" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                                <InputText id="VetAddress"
                                           class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                           @bind-Value="_vetModel.Address"
                                           placeholder="Enter your address" />
                                <ValidationMessage For="() => _vetModel.Address" class="text-red-500 text-sm mt-1" />
                            </div>

                            <!-- Clinic Name Field -->
                            <div>
                                <label for="ClinicName" class="block text-sm font-medium text-gray-700 mb-2">Clinic Name</label>
                                <InputText id="ClinicName"
                                           class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                           @bind-Value="_vetModel.ClinicName"
                                           placeholder="Enter your clinic name" />
                                <ValidationMessage For="() => _vetModel.ClinicName" class="text-red-500 text-sm mt-1" />
                            </div>

                            <!-- Specialization Field -->
                            <div>
                                <label for="Specialization" class="block text-sm font-medium text-gray-700 mb-2">Specialization</label>
                                <InputText id="Specialization"
                                           class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                           @bind-Value="_vetModel.Specialization"
                                           placeholder="Enter your specialization" />
                                <ValidationMessage For="() => _vetModel.Specialization" class="text-red-500 text-sm mt-1" />
                            </div>

                            <!-- Years Of Experience -->
                            <div>
                                <label for="YearsOfExperience" class="block text-sm font-medium text-gray-700 mb-2">Years of Experience</label>
                                <InputNumber id="YearsOfExperience"
                                             class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                             @bind-Value="_vetModel.YearsOfExperience"
                                             placeholder="Enter years of experience" />
                                <ValidationMessage For="() => _vetModel.YearsOfExperience" class="text-red-500 text-sm mt-1" />
                            </div>

                            <!-- Education -->
                            <div>
                                <label for="Education" class="block text-sm font-medium text-gray-700 mb-2">Education</label>
                                <InputSelect @bind-Value="_vetModel.EducationId"
                                             class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                             id="Education">
                                    <option value="0" disabled selected>Select Education</option>
                                    @foreach (var education in _educationlist)
                                    {
                                        <option value="@education.Id" selected="@(education.Id == _vetModel.EducationId)">
                                            @education.Name
                                        </option>
                                    }
                                </InputSelect>
                                <ValidationMessage For="@(() => _vetModel.EducationId)" class="text-red-500 text-sm mt-1" />
                            </div>

                            <!-- Profile Image Upload -->
                            <div>
                                <label for="VetProfileImage" class="block text-sm font-medium text-gray-700 mb-2">Profile Image (Optional)</label>
                                <div class="border-2 border-dashed border-gray-300 rounded-xl p-4 text-center hover:border-blue-400 transition-all">
                                    <i class="fas fa-camera text-gray-400 text-2xl mb-2"></i>
                                    <p class="text-gray-500 mb-2">Upload your profile picture</p>
                                    <label class="inline-block px-4 py-2 bg-blue-500 text-white rounded-lg text-sm font-medium hover:bg-blue-600 transition-all cursor-pointer">
                                        Choose File
                                        <InputFile id="VetProfileImage" class="hidden" accept="image/*" OnChange="HandleImageUpload" />
                                    </label>
                                </div>
                                <ValidationMessage For="() => _vetModel.ImageUrl" class="text-red-500 text-sm mt-1" />
                            </div>

                            <!-- Certificates Upload -->
                            <div>
                                <label for="Certifications" class="block text-sm font-medium text-gray-700 mb-2">Certificates (PDF)</label>
                                <div class="border-2 border-dashed border-gray-300 rounded-xl p-4 text-center hover:border-blue-400 transition-all">
                                    <i class="fas fa-file-pdf text-gray-400 text-2xl mb-2"></i>
                                    <p class="text-gray-500 mb-2">Upload your certificates</p>
                                    <label class="inline-block px-4 py-2 bg-blue-500 text-white rounded-lg text-sm font-medium hover:bg-blue-600 transition-all cursor-pointer">
                                        Choose Files
                                        <InputFile id="Certifications" class="hidden" OnChange="HandleCertificatesUpload" multiple accept=".pdf" />
                                    </label>
                                </div>
                                <ValidationMessage For="() => _vetModel.CertificationsUrl" class="text-red-500 text-sm mt-1" />
                            </div>

                            <!-- License Document Upload -->
                            <div>
                                <label for="DocumentUrl" class="block text-sm font-medium text-gray-700 mb-2">License Document (PDF)</label>
                                <div class="border-2 border-dashed border-gray-300 rounded-xl p-4 text-center hover:border-blue-400 transition-all">
                                    <i class="fas fa-file-medical text-gray-400 text-2xl mb-2"></i>
                                    <p class="text-gray-500 mb-2">Upload your license document</p>
                                    <label class="inline-block px-4 py-2 bg-blue-500 text-white rounded-lg text-sm font-medium hover:bg-blue-600 transition-all cursor-pointer">
                                        Choose File
                                        <InputFile id="DocumentUrl" class="hidden" OnChange="HandleDocumentUpload" accept=".pdf" />
                                    </label>
                                </div>
                                <ValidationMessage For="() => _vetModel.LicenceDocumentUrl" class="text-red-500 text-sm mt-1" />
                            </div>

                            <!-- Password Field -->
                            <div>
                                <label for="VetPassword" class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                                <InputText id="VetPassword"
                                           class="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                                           @bind-Value="_vetModel.Password"
                                           type="password"
                                           placeholder="Create a secure password" />
                                <ValidationMessage For="() => _vetModel.Password" class="text-red-500 text-sm mt-1" />
                            </div>
                        </div>

                        <!-- Error Message -->
                        @if (_error != null)
                        {
                            <div class="bg-red-50 border border-red-200 rounded-xl p-4 mt-4">
                                <div class="flex items-start justify-between">
                                    <div class="flex items-center gap-2 text-red-700">
                                        <i class="fas fa-exclamation-triangle"></i>
                                        <span class="text-sm">@_error</span>
                                    </div>
                                    <button type="button"
                                            class="text-red-400 hover:text-red-600 transition-colors"
                                            @onclick="() => _error = null">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        }

                        <!-- Register Button -->
                        <div class="mt-6">
                            @if (!_isBusy)
                            {
                                <button type="submit"
                                        class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-4 px-6 rounded-2xl font-medium hover:from-blue-600 hover:to-purple-700 transition-all transform hover:scale-105 shadow-lg flex items-center justify-center gap-2">
                                    <i class="fas fa-user-md"></i>
                                    <span>Create Veterinarian Account</span>
                                </button>
                            }
                            else
                            {
                                <button disabled type="button"
                                        class="w-full bg-gray-400 text-white py-4 px-6 rounded-2xl font-medium cursor-not-allowed flex items-center justify-center gap-2">
                                    <div class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                    <span>Creating Account...</span>
                                </button>
                            }
                        </div>
                    </EditForm>
                }
            </div>

            <!-- Footer -->
            <div class="p-6 bg-gray-50 text-center">
                <p class="text-gray-600 text-sm">
                    Already have an account?
                    <a href="/auth/login" class="text-teal-600 hover:text-teal-700 font-medium">Sign in here</a>
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
@if (_showSuccessAlert)
{
    <Modal Title="Success"
           ActionButtonText="Ok"
           IsVisible="@_showSuccessAlert"
           OnActionButtonClick="OnModalClose"
           OnCancelButtonClick="OnModalClose"
           Size="ModalSize.Large">
        <div class="text-center p-4">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-green-600 text-2xl"></i>
            </div>
            <h3 class="text-lg font-bold text-gray-800 mb-2">Account Created Successfully!</h3>
            <p class="text-gray-600">
                Your account has been created successfully.<br />
                You will be able to login once Admin approves your account.
            </p>
        </div>
    </Modal>
}

@code {
    [Parameter] public int Id { get; set; }
    private EducationDto[] _educationlist = [];
    private int SelectedRole { get; set; } = 1;
    private PetOwnerDto _petOwnerModel = new();
    private VetDto _vetModel = new();
    private bool _isBusy;
    private string? _error;

    private bool _showSuccessAlert;



    protected override void OnInitialized()
    {
        if (_PetVetAuthStateProvider.IsLoggedIn)
        {
            _NavigationManager.NavigateTo("/", replace: true);
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await LoadEducationList();

            StateHasChanged();
        }
    }

    private async Task LoadEducationList()
    {
        _educationlist = await _EducationApi.GetEducationListAsync();

        // Ensure the selected category is set correctly
        if (_vetModel.EducationId == 0 && _educationlist.Length > 0)
        {
            _vetModel.EducationId = _educationlist.FirstOrDefault()?.Id ?? 0;
        }
    }


    private async Task RegisterPetOwnerAsync()
    {
        _error = null;
        _AppState.ShowLoader("Registering");
        _isBusy = true;

        try
        {
            var apiResponse = await _AuthApi.RegisterPetOwnerAsync(_petOwnerModel);

            if (!apiResponse.IsSuccess)
            {
                _error = apiResponse.ErrorMessage;
                await _JS.ToastrError(_error);
                return;
            }

            // Navigate to some protected page
            await _JS.ToastrSuccess("Register successfully.");
            _NavigationManager.NavigateTo("auth/login");

        }
        catch (Exception ex)
        {
            await _JS.ToastrError("An unknown error has occurred.");
            _error = ex.Message;
        }
        finally
        {
            _AppState.HideLoader();
            _isBusy = false;
        }
    }

    private async Task RegisterVetAsync()
    {
        _error = null;
        _AppState.ShowLoader("Registering");
        _isBusy = true;

        try
        {
            var apiResponse = await _AuthApi.RegisterVetAsync(_vetModel);

            if (!apiResponse.IsSuccess)
            {
                _error = apiResponse.ErrorMessage;
                await _JS.ToastrError(_error);
                return;
            }

            await _JS.ToastrSuccess("Register successfully.");
            _showSuccessAlert = true;
        }
        catch (Exception ex)
        {
            await _JS.ToastrError("An unknown error has occurred.");
            _error = ex.Message;
        }
        finally
        {
            _AppState.HideLoader();
            _isBusy = false;
        }
    }

    
    private void SelectRole(int role)
    {
        SelectedRole = role;
        _petOwnerModel = new(); 
        _vetModel = new(); 
    }

    private void OnModalClose()
    {
        _vetModel = new();
        _NavigationManager.NavigateTo("auth/login");
    }

    
    private async Task HandleImageUpload(InputFileChangeEventArgs e)
    {
        _AppState.ShowLoader("Uploading Image");

        var file = e.File;
        var allowedExtensions = new[] { ".png", ".jpg", ".jpeg" };

        var fileExtension = Path.GetExtension(file.Name).ToLower();

        if (!allowedExtensions.Contains(fileExtension))
        {
            _error = "The file format is invalid. Please upload an image in JPEG, PNG, or JPG format.";
            await _JS.ToastrError(_error);
            _AppState.HideLoader();
            return;
        }

        var stream = file.OpenReadStream(5 * 1024 * 1024); // 5MB limit
        var memoryStream = new MemoryStream();
        await stream.CopyToAsync(memoryStream);

        // Process the image file (Upload to server or store in a model)
        if(SelectedRole == 1)
        {
            _petOwnerModel.ImageUrl = Convert.ToBase64String(memoryStream.ToArray());
        }
        else
        {
            _vetModel.ImageUrl = Convert.ToBase64String(memoryStream.ToArray());
        }

        _AppState.HideLoader();
    }


    private async Task HandleCertificatesUpload(InputFileChangeEventArgs e)
    {
        _AppState.ShowLoader("Uploading Document");

        var files = e.GetMultipleFiles();
        var allowedExtensions = new[] { ".pdf" };

        foreach (var file in files)
        {
            var fileExtension = Path.GetExtension(file.Name).ToLower();

            if (!allowedExtensions.Contains(fileExtension))
            {
                _error = "The file format is invalid. Only PDF files are allowed for certificates.";
                await _JS.ToastrError(_error);
                _AppState.HideLoader();
                return;
            }

            var stream = file.OpenReadStream(5 * 1024 * 1024); // 5MB limit
            var memoryStream = new MemoryStream();
            await stream.CopyToAsync(memoryStream);

            // Process the certificate file (Upload or store in a model)
            _vetModel.CertificationsUrl = Convert.ToBase64String(memoryStream.ToArray());
        }

        _AppState.HideLoader();
    }


    private async Task HandleDocumentUpload(InputFileChangeEventArgs e)
    {
        _AppState.ShowLoader("Uploading Document");

        var file = e.File;
        var allowedExtensions = new[] { ".pdf" };

        var fileExtension = Path.GetExtension(file.Name).ToLower();

        if (!allowedExtensions.Contains(fileExtension))
        {
            _error = "The file format is invalid. Only PDF files are allowed for licence.";
            await _JS.ToastrError(_error);
            _AppState.HideLoader();
            return;
        }

        var stream = file.OpenReadStream(5 * 1024 * 1024); // 5MB limit
        var memoryStream = new MemoryStream();
        await stream.CopyToAsync(memoryStream);

        // Process the license document (Upload or store in a model)
        _vetModel.LicenceDocumentUrl = Convert.ToBase64String(memoryStream.ToArray());

        _AppState.HideLoader();
    }
}
