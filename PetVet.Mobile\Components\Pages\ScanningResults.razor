@page "/results"
@inject NavigationManager Navigation

<div class="scanner-container page-transition">
    <!-- Header -->
    <div class="header">
        <svg class="header-icon" onclick="history.back()" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
        </svg>
        <div class="header-title">Scanning Result</div>
        <svg class="header-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
    </div>

    <!-- Results Card -->
    <div class="modern-card">
        <!-- Circular Chart -->
        <div class="chart-container">
            <svg width="200" height="200" viewBox="0 0 200 200">
                <!-- Background circle -->
                <circle cx="100" cy="100" r="80" fill="none" stroke="#f3f4f6" stroke-width="12"/>
                
                <!-- Progress circles -->
                <circle cx="100" cy="100" r="80" fill="none" stroke="#4ade80" stroke-width="12"
                        stroke-dasharray="@GetDashArray(43.7)" stroke-dashoffset="0" 
                        transform="rotate(-90 100 100)" stroke-linecap="round"/>
                
                <circle cx="100" cy="100" r="80" fill="none" stroke="#60a5fa" stroke-width="12"
                        stroke-dasharray="@GetDashArray(30.5)" stroke-dashoffset="@GetDashOffset(43.7)"
                        transform="rotate(-90 100 100)" stroke-linecap="round"/>
                
                <circle cx="100" cy="100" r="80" fill="none" stroke="#a855f7" stroke-width="12"
                        stroke-dasharray="@GetDashArray(25.8)" stroke-dashoffset="@GetDashOffset(43.7 + 30.5)"
                        transform="rotate(-90 100 100)" stroke-linecap="round"/>
            </svg>
            
            <div class="chart-center">
                <div class="chart-percentage">43.7%</div>
                <div class="chart-label">Mixed Breed</div>
            </div>
        </div>

        <div class="breed-description" style="text-align: center; margin: 16px 0; color: var(--text-light);">
            The dog you scanned looks like a mix of multiple breeds.
        </div>

        <!-- Results List -->
        <div class="result-list">
            @foreach (var result in scanResults)
            {
                <div class="result-item">  @* @onclick="() => NavigateToDetails(result.BreedName)" *@
                    <img src="@result.ImageUrl" alt="@result.BreedName" class="result-image" />
                    <div class="result-info">
                        <div class="result-name">@result.BreedName</div>
                        <div class="result-percentage">@result.Percentage% Match</div>
                    </div>
                </div>
            }
        </div>

        <!-- Action Buttons -->
        <div class="btn-group">
            <button class="btn btn-secondary" @onclick="ShareResults">
                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                </svg>
                Share
            </button>
            <button class="btn btn-primary" @onclick="RescanImage">
                <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                Rescan
            </button>
        </div>
    </div>
</div>

@code {
    private List<ScanResult> scanResults = new()
    {
        new ScanResult { BreedName = "Shih Tzu", Percentage = 43.7, ImageUrl = "https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=100&h=100&fit=crop&crop=face" },
        new ScanResult { BreedName = "Lhasa Apso", Percentage = 30.5, ImageUrl = "https://images.unsplash.com/photo-1552053831-71594a27632d?w=100&h=100&fit=crop&crop=face" },
        new ScanResult { BreedName = "Yorkshire Terrier", Percentage = 25.8, ImageUrl = "https://images.unsplash.com/photo-1605568427561-40dd23c2acea?w=100&h=100&fit=crop&crop=face" }
    };

    private string GetDashArray(double percentage)
    {
        var circumference = 2 * Math.PI * 80; // radius = 80
        var dashLength = (percentage / 100) * circumference;
        return $"{dashLength} {circumference}";
    }

    private string GetDashOffset(double previousPercentages)
    {
        var circumference = 2 * Math.PI * 80;
        return $"{-(previousPercentages / 100) * circumference}";
    }

    // private void NavigateToDetails(string breedName)
    // {
    //     // Navigate to breed details page
    //     Navigation.NavigateTo($"/details/{breedName.Replace(" ", "-").ToLower()}");
    // }

    private void ShareResults()
    {
        // Implement share functionality
        // For now, just show an alert
    }

    private void RescanImage()
    {
        Navigation.NavigateTo("/");
    }

    public class ScanResult
    {
        public string BreedName { get; set; } = "";
        public double Percentage { get; set; }
        public string ImageUrl { get; set; } = "";
    }
}

<link href="/css/modern-scanner.css" rel="stylesheet" /> 