﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Internal;
using PetVet.Api.Data;
using PetVet.Api.Data.Entities;
using PetVet.Shared;
using PetVet.Shared.DTOs;

namespace PetVet.Api.Services
{
    public class UserService
    {
        private readonly IDbContextFactory<PetVetContext> _contextFactory;
        private readonly SmsService _smsService;

        public UserService(IDbContextFactory<PetVetContext> contextFactory, SmsService smsService)
        {
            _contextFactory = contextFactory;
            _smsService = smsService;
        }


        public async Task<AdminHomeDataDto> GetAdminHomeDataAsync()
        {
            ;
            var totalPetCategoriesTask = _contextFactory.CreateDbContext().Categories.CountAsync();
            var totalPetsTask = _contextFactory.CreateDbContext().Pets.CountAsync();
            var totalEducationCategoriesTask = _contextFactory.CreateDbContext().Educations.CountAsync();
            var totalUsersTask = _contextFactory.CreateDbContext().Users.CountAsync();
            var totalPetOwnersTask = _contextFactory.CreateDbContext().Users.Where(u => u.Role == nameof(UserRole.PetOwner)).CountAsync();
            var totalVetsTask = _contextFactory.CreateDbContext().Users.Where(u => u.Role == nameof(UserRole.Vet)).CountAsync();

            var totalPetCategories = await totalPetCategoriesTask;
            var totalPets = await totalPetsTask;
            var totalEducationCategories = await totalEducationCategoriesTask;
            var totalUsers = await totalUsersTask;
            var totalPetOwners = await totalPetOwnersTask;
            var totalVets = await totalVetsTask;

            return new AdminHomeDataDto(totalPetCategories, totalPets, totalEducationCategories, totalUsers, totalPetOwners, totalVets);
        }



        // Save or update a user profile
        public async Task<PetVetApiResponse> SaveUserAsync(UserDto dto)
        {
            using var context = _contextFactory.CreateDbContext();

            if (await context.Users
                .AsNoTracking()
                .AnyAsync(u => u.Email == dto.Email && u.Id != dto.Id))
            {
                // Category with same name already exists
                return PetVetApiResponse.Fail("A user with the same email already exists.");
            }


            if (dto.Id == 0)
            {
                // Create new user profile
                var user = new User
                {
                    Name = dto.Name,
                    Email = dto.Email,
                    Phone = dto.Phone,
                    Role = dto.Role,
                    IsApproved = dto.IsApproved,
                    ImageUrl = dto.ImageUrl,
                };

                await context.Users.AddAsync(user);
            }
            else
            {
                // Update existing user profile
                var dbUser = await context.Users.FirstOrDefaultAsync(u => u.Id == dto.Id);

                if (dbUser == null)
                {
                    return PetVetApiResponse.Fail("User profile does not exist.");
                }

                // Update properties of existing user
                dbUser.Name = dto.Name;
                dbUser.Email = dto.Email;
                dbUser.Phone = dto.Phone;
                dbUser.Role = dto.Role;
                dbUser.IsApproved = dto.IsApproved;
                dbUser.ImageUrl = dto.ImageUrl;

                context.Users.Update(dbUser);
            }

            await context.SaveChangesAsync();
            return PetVetApiResponse.Success();
        }

        // Retrieve a list of all users
        public async Task<UserDto[]> GetUsersAsync() =>
            await _contextFactory.CreateDbContext().Users
                .AsNoTracking()
                .Where(u => u.Role != nameof(UserRole.Admin))
                .Select(u => new UserDto
                {
                    Id = u.Id,
                    Name = u.Name,
                    Email = u.Email,
                    Phone = u.Phone,
                    Role = u.Role,
                    IsApproved = u.IsApproved
                })
                .ToArrayAsync();

        // Retrieve a specific user by ID
        public async Task<UserDto> GetUserAsync(int id)
        {
            using var context = _contextFactory.CreateDbContext();

            var user = await context.Users
                .AsNoTracking()
                .FirstOrDefaultAsync(u => u.Id == id);

            if (user == null)
            {
                return null;
            }

            return new UserDto
            {
                Id = user.Id,
                Name = user.Name,
                Email = user.Email,
                Phone = user.Phone,
                Role = user.Role,
                IsApproved = user.IsApproved,
                ClinicName = user.ClinicName,
                Address = user.Address,
                Specialization = user.Specialization,
                YearsOfExperience = user.YearsOfExperience,
                IsOnline = user.IsOnline,
                ImageUrl = user.ImageUrl,
                CertificationsUrl = user.CertificationsUrl,
                EducationName = user.Education?.Name,
                LicenceDocumentUrl = user.LicenceDocumentUrl,
                Rating = user.Rating,
            };
        }

        public async Task<UserDto[]> GetVetUsersAsync() =>
            await _contextFactory.CreateDbContext().Users
        .AsNoTracking()
        .Where(u => u.Role == "Vet" && u.IsApproved == true)
        .Select(u => new UserDto
        {
            Id = u.Id,
            Name = u.Name,
            ClinicName = u.ClinicName,
            Address = u.Address,
            Specialization = u.Specialization,
            YearsOfExperience = u.YearsOfExperience,
            IsOnline = u.IsOnline,
            ImageUrl = u.ImageUrl,
            EducationName = u.Education.Name,
            EducationId = u.Education.Id,
            Rating = u.Rating,
        })
        .ToArrayAsync();

        public async Task<UserDto> GetVetUserAsync(int id)
        {
            using var context = _contextFactory.CreateDbContext();

            var vetUser = await context.Users
                .AsNoTracking()
                .Where(u => u.Role == "Vet")
                .Include(u => u.Education)
                .FirstOrDefaultAsync(u => u.Id == id);

            if (vetUser == null)
            {
                return null;
            }

            return new UserDto
            {
                Id = vetUser.Id,
                Name = vetUser.Name,
                Email = vetUser.Email,
                Phone = vetUser.Phone,
                ClinicName = vetUser.ClinicName,
                Address = vetUser.Address,
                Specialization = vetUser.Specialization,
                YearsOfExperience = vetUser.YearsOfExperience,
                IsOnline = vetUser.IsOnline,
                ImageUrl = vetUser.ImageUrl,
                CertificationsUrl = vetUser.CertificationsUrl,
                EducationName = vetUser.Education.Name,
                EducationId = vetUser.Education.Id,
                LicenceDocumentUrl = vetUser.LicenceDocumentUrl,
                Rating = vetUser.Rating,
                Role = vetUser.Role,
                IsApproved = vetUser.IsApproved,
            };
        }

        public async Task<UserDto> GetCurrentVetUserAsync(int userId)
        {
            using var context = _contextFactory.CreateDbContext();

            var currentVetUser =  (from u in context.Users
                                        from education in context.Educations.Where(x => x.Id == u.EducationId).DefaultIfEmpty()
                                        where u.Role == nameof(UserRole.Vet) && u.Id == userId
                                        select new UserDto
                                        {
                                            Id = u.Id,
                                            Name = u.Name,
                                            Email = u.Email,
                                            Phone = u.Phone,
                                            ClinicName = u.ClinicName,
                                            Address = u.Address,
                                            Specialization = u.Specialization,
                                            YearsOfExperience = u.YearsOfExperience,
                                            IsOnline = u.IsOnline,
                                            ImageUrl = u.ImageUrl,
                                            CertificationsUrl = u.CertificationsUrl,
                                            EducationName = u.Education.Name,
                                            EducationId = u.Education.Id,
                                            LicenceDocumentUrl = u.LicenceDocumentUrl,
                                            Rating = u.Rating,
                                            Role = u.Role,
                                            IsApproved = u.IsApproved,
                                        });

            return await currentVetUser.FirstOrDefaultAsync();

        }

        public async Task<UserDto> GetCurrentPetOwnerUserAsync(int userId)
        {
            using var context = _contextFactory.CreateDbContext();

            var currentPetOwnerUser = await context.Users
                .AsNoTracking()
                .Where(u => u.Role == nameof(UserRole.PetOwner))
                .FirstOrDefaultAsync(u => u.Id == userId);

            if (currentPetOwnerUser == null)
            {
                return null;
            }

            return new UserDto
            {
                Id = currentPetOwnerUser.Id,
                Name = currentPetOwnerUser.Name,
                Email = currentPetOwnerUser.Email,
                Phone = currentPetOwnerUser.Phone,
                Address = currentPetOwnerUser.Address,
                ImageUrl = currentPetOwnerUser.ImageUrl,
                Role = currentPetOwnerUser.Role,
                IsApproved = currentPetOwnerUser.IsApproved,
            };
        }

        public async Task<PetVetApiResponse> UpdateVetProfileAsync(UserDto dto)
        {
            using var context = _contextFactory.CreateDbContext();

            var vetUser = await context.Users
                .Include(u => u.Education)
                .FirstOrDefaultAsync(u => u.Id == dto.Id && u.Role == nameof(UserRole.Vet));

            if (vetUser == null)
            {
                return PetVetApiResponse.Fail("Vet profile not found");
            }

            // Update fields
            vetUser.Name = dto.Name;
            vetUser.Email = dto.Email;
            vetUser.Phone = dto.Phone;
            vetUser.Address = dto.Address;
            vetUser.ClinicName = dto.ClinicName;
            vetUser.Specialization = dto.Specialization;
            vetUser.YearsOfExperience = dto.YearsOfExperience;
            vetUser.EducationId = dto.EducationId;

            // Always update the image URL even if null
            vetUser.ImageUrl = dto.ImageUrl;

            // Always update certifications and license - to support document removal
            vetUser.CertificationsUrl = dto.CertificationsUrl;
            vetUser.LicenceDocumentUrl = dto.LicenceDocumentUrl;

            try
            {
                await context.SaveChangesAsync();
                return PetVetApiResponse.Success();
            }
            catch (Exception ex)
            {
                return PetVetApiResponse.Fail($"Error updating vet profile: {ex.Message}");
            }
        }

        public async Task<PetVetApiResponse> UpdatePetOwnerProfileAsync(UserDto dto)
        {
            using var context = _contextFactory.CreateDbContext();

            var petOwnerUser = await context.Users
                .FirstOrDefaultAsync(u => u.Id == dto.Id && u.Role == nameof(UserRole.PetOwner));

            if (petOwnerUser == null)
            {
                return PetVetApiResponse.Fail("Pet owner profile not found");
            }

            // Update fields
            petOwnerUser.Name = dto.Name;
            petOwnerUser.Email = dto.Email;
            petOwnerUser.Phone = dto.Phone;
            petOwnerUser.Address = dto.Address;

            // Always update the image URL even if null
            petOwnerUser.ImageUrl = dto.ImageUrl;

            try
            {
                await context.SaveChangesAsync();
                return PetVetApiResponse.Success();
            }
            catch (Exception ex)
            {
                return PetVetApiResponse.Fail($"Error updating pet owner profile: {ex.Message}");
            }
        }

        public async Task ToggleUserApprovedStatus(int userId)
        {

            using var context = _contextFactory.CreateDbContext();

            var dbUser = await context.Users.AsTracking().FirstOrDefaultAsync(u => u.Id == userId);

            if (dbUser != null)
            {
                dbUser.IsApproved = !dbUser.IsApproved;
                await context.SaveChangesAsync();
            }
        }



        // Delete a user profile by ID
        public async Task<PetVetApiResponse> DeleteUserAsync(int id)
        {
            using var context = _contextFactory.CreateDbContext();

            var user = await context.Users.FirstOrDefaultAsync(u => u.Id == id);

            if (user == null)
            {
                return PetVetApiResponse.Fail("User profile not found.");
            }

            // Check and delete related Pets
            var userPets = await context.Pets.Where(p => p.UserId == id).ToListAsync();
            if (userPets.Any())
            {
                context.Pets.RemoveRange(userPets);
            }

            // Check and delete related VetTimeSlots
            var timeSlots = await context.VetTimeSlots.Where(ts => ts.VetId == id).ToListAsync();
            if (timeSlots.Any())
            {
                context.VetTimeSlots.RemoveRange(timeSlots);
            }

            // Check and delete related Messages (both sent and received)
            var messages = await context.Messages
                .Where(m => m.FromId == id || m.ToId == id)
                .ToListAsync();
            if (messages.Any())
            {
                context.Messages.RemoveRange(messages);
            }

            // Check and delete related Appointments (both as pet owner and vet)
            var appointments = await context.Appointments
                .Where(a => a.PetOwnerId == id || a.VetId == id)
                .ToListAsync();
            if (appointments.Any())
            {
                context.Appointments.RemoveRange(appointments);
            }

            // Now delete the user
            context.Users.Remove(user);
            await context.SaveChangesAsync();

            return PetVetApiResponse.Success();
        }



        public async Task<IEnumerable<UserDto>> GetChatUsersAsync(int currentUserId)
        {
            using var context = _contextFactory.CreateDbContext();

            IEnumerable<UserDto> chatUsers = new List<UserDto>();

            var uniqueUsers = await context.Messages
                .AsNoTracking()
                .Where(m => m.FromId == currentUserId || m.ToId == currentUserId)
                .Select(m => new { From = m.FromId, To = m.ToId })
                .Distinct()
                .ToListAsync();

            var uniqueUserIds = new HashSet<int>();

            uniqueUsers.ForEach(u =>
            {
                if (u.From != currentUserId)
                    uniqueUserIds.Add(u.From);
                if (u.To != currentUserId)
                    uniqueUserIds.Add(u.To);
            });

            if (uniqueUserIds.Count > 0)
            {
                chatUsers = await context.Users
                    .AsNoTracking()
                    .Where(u => uniqueUserIds.Contains(u.Id))
                    .Select(u => new UserDto
                    {
                        Id = u.Id,
                        Name = u.Name
                    })
                    .ToListAsync();
            }

            return chatUsers;
        }
    }
}
