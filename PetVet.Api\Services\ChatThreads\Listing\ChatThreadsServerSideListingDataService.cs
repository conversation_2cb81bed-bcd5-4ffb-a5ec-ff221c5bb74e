﻿using PetVet.Server.DataServices.Features;
using PetVet.ServiceContracts.Features.Conversation;
using PetVet.Api.Data;
using PetVet.Shared.Interfaces;
using System.Security.Claims;

namespace PetVet.Api.Services.ChatThreads.Listing;
public class ChatThreadsServerSideListingDataService :
        ServerSideListingDataService<ChatThreadsListingBusinessObject, ChatThreadsFilterBusinessObject>, IChatThreadsListingDataService
{
    private readonly PetVetContext _context;
    private readonly IHttpContextAccessor contextAccessor;

    public ChatThreadsServerSideListingDataService(PetVetContext context, IHttpContextAccessor contextAccessor)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
    }

    public override IQueryable<ChatThreadsListingBusinessObject> GetQuery(ChatThreadsFilterBusinessObject filterBusinessObject)
    {
        var userId = Convert.ToInt32(contextAccessor.HttpContext?.User.FindFirst(ClaimTypes.NameIdentifier)?.Value);

        var conversations = (from c in _context.Conversations
                             from p in _context.ConversationParticipants.Where(x => x.ConversationId == c.Id)
                             where p.UserId == userId
                             select c.Id).ToList();

        var query = from c in _context.Conversations
                    from p in _context.ConversationParticipants.Where(x => x.ConversationId == c.Id)
                    from f in _context.Users.Where(x => x.Id == p.UserId)
                    where conversations.Contains(c.Id) && p.UserId != userId
                    select new ChatThreadsListingBusinessObject
                    {
                        Id = c.Id,
                        Avatar = f.ImageUrl,
                        Name = f.Name,
                        FriendId = p.UserId,
                        LastMessage = _context.ConversationMessages.Where(x => x.ConversationId == c.Id)
                            .OrderByDescending(x => x.CreatedAt)
                            .Select(x => x.PlainContent)
                            .FirstOrDefault()
                    };
        return query;
    }

    protected override PagedDataList<ChatThreadsListingBusinessObject> OnItemsLoaded(PagedDataList<ChatThreadsListingBusinessObject> items)
    {
        ArgumentNullException.ThrowIfNull(items?.Items);

        items.Items = items.Items.DistinctBy(x => x.Id).ToList();
        return items;
    }
}
