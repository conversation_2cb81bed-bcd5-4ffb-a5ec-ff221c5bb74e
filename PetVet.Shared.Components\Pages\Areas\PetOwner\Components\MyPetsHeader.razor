﻿@inject IJSRuntime _JS

<!-- Modern Search Header -->
<div class="mb-6">
    <!-- Search Section -->
    <div class="relative">
        <div class="relative">
            <!-- Search Icon -->
            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400"></i>
            </div>

            <!-- Search Input -->
            <input type="text"
                   class="w-full pl-12 pr-12 py-4 bg-white border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent shadow-sm text-gray-800 placeholder-gray-500"
                   placeholder="Search your pets..."
                   @bind="searchTerm"
                   @bind:event="oninput"
                   @onkeyup="HandleKeyPress" />

            <!-- Clear Button -->
            @if (!string.IsNullOrEmpty(searchTerm))
            {
                <button @onclick="ClearSearch"
                        class="absolute inset-y-0 right-0 pr-4 flex items-center">
                    <div class="w-6 h-6 bg-gray-200 hover:bg-gray-300 rounded-full flex items-center justify-center transition-all">
                        <i class="fas fa-times text-gray-600 text-xs"></i>
                    </div>
                </button>
            }
        </div>

        <!-- Search Results Indicator -->
        @if (!string.IsNullOrEmpty(searchTerm))
        {
            <div class="mt-2 text-sm text-gray-600">
                <i class="fas fa-search text-teal-500 mr-1"></i>
                Searching for: <span class="font-medium text-gray-800">"@searchTerm"</span>
            </div>
        }
    </div>
</div>

@code {
    [Parameter]
    public EventCallback<string> OnSearch { get; set; }

    private string searchTerm = "";
    
    private List<int> selectedCategories = new();
    private List<string> selectedVaccinationStatuses = new();
    private int activeFiltersCount => selectedCategories.Count + selectedVaccinationStatuses.Count;

    private List<CategoryDto> Categories = new()
    {
        new() { Id = 1, Name = "Dogs" },
        new() { Id = 2, Name = "Cats" },
        new() { Id = 3, Name = "Birds" },
        new() { Id = 4, Name = "Fish" },
        new() { Id = 5, Name = "Other" }
    };

    private List<string> VaccinationStatuses = new()
    {
        "Up to Date",
        "Due Soon",
        "Overdue",
        "Not Started"
    };

    private async Task HandleKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await OnSearch.InvokeAsync(searchTerm);
        }
    }

    private async Task ClearSearch()
    {
        searchTerm = "";
        await OnSearch.InvokeAsync(searchTerm);
    }

    private bool IsSelectedCategory(int categoryId)
    {
        return selectedCategories.Contains(categoryId);
    }

    private void ToggleCategory(int categoryId)
    {
        if (selectedCategories.Contains(categoryId))
        {
            selectedCategories.Remove(categoryId);
        }
        else
        {
            selectedCategories.Add(categoryId);
        }
    }

    private bool IsSelectedVaccinationStatus(string status)
    {
        return selectedVaccinationStatuses.Contains(status);
    }

    private void ToggleVaccinationStatus(string status)
    {
        if (selectedVaccinationStatuses.Contains(status))
        {
            selectedVaccinationStatuses.Remove(status);
        }
        else
        {
            selectedVaccinationStatuses.Add(status);
        }
    }
}