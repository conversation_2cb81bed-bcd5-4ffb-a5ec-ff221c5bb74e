﻿using System.ComponentModel.DataAnnotations;

namespace PetVet.Shared.DTOs
{
    public class UserDto : IValidatableObject
    {
        public int Id { get; set; }

        [Required, MaxLength(20)]
        public string Name { get; set; } = string.Empty;

        [Required, <PERSON><PERSON><PERSON>ddress, <PERSON><PERSON>ength(150)]
        public string Email { get; set; } = string.Empty;

        [Required, StringLength(15, MinimumLength = 10, ErrorMessage = "Phone number must be between 10 and 15 characters.")]
        public string Phone { get; set; } = string.Empty;
          
        [Required, MaxLength(15)]
        public string Role { get; set; } = string.Empty;
         
        public bool IsApproved { get; set; }

        [Required, MaxLength(250)]
        public string Address { get; set; } = string.Empty;

        // Vet-specific fields (nullable for PetOwner)
        [MaxLength(250)]
        public string? ClinicName { get; set; }

        [MaxLength(50)]
        public string? Specialization { get; set; }

        [Range(1, 100, ErrorMessage = "Years of experience must be between 1 and 100.")]
        public int YearsOfExperience { get; set; }
         
        public string? LicenceDocumentUrl { get; set; }

        public bool? IsOnline { get; set; }

        public bool IsSelected { get; set; }

        [Required]
        public string? ImageUrl { get; set; }

        [Range(0, 5, ErrorMessage = "Rating must be between 0 and 5.")]
        public double? Rating { get; set; }

        public string? CertificationsUrl { get; set; }
         

        [Range(1, int.MaxValue, ErrorMessage = "Please select a valid education.")]
        public int EducationId { get; set; }

        public string? EducationName { get; set; }

        // Custom validation: Ensures Vet fields are only required when Role is "Vet"
        public IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (!string.Equals(Role, "Vet", System.StringComparison.OrdinalIgnoreCase))
                yield break;

            if (string.IsNullOrWhiteSpace(ClinicName))
                yield return new ValidationResult("Clinic Name is required for Vets.", new[] { nameof(ClinicName) });

            if (string.IsNullOrWhiteSpace(Specialization))
                yield return new ValidationResult("Specialization is required for Vets.", new[] { nameof(Specialization) });

            if (YearsOfExperience < 1 || YearsOfExperience > 100)
                yield return new ValidationResult("Years of experience must be between 1 and 100.", new[] { nameof(YearsOfExperience) });

            if (EducationId < 1)
                yield return new ValidationResult("Please select a valid education.", new[] { nameof(EducationId) });

            // Note: LicenceDocumentUrl is not required for profile updates, only for registration
        }
    }
}
