<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="PetVet.Mobile.Pages.AudioTestPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    Title="Audio Test Page"
    BackgroundColor="#F8FAFC">

    <ScrollView>
        <StackLayout Padding="20" Spacing="20">

            <!--  Header  -->
            <Frame
                Padding="20"
                BackgroundColor="White"
                CornerRadius="10"
                HasShadow="True">
                <StackLayout>
                    <Label
                        FontAttributes="Bold"
                        FontSize="20"
                        HorizontalOptions="Center"
                        Text="Audio Permissions &amp; Playback Test"
                        TextColor="#374151" />
                    <Label
                        FontSize="14"
                        HorizontalOptions="Center"
                        Text="Test audio permissions and ringtone playback"
                        TextColor="#6B7280" />
                </StackLayout>
            </Frame>

            <!--  Permission Status  -->
            <Frame
                Padding="15"
                BackgroundColor="White"
                CornerRadius="10"
                HasShadow="True">
                <StackLayout>
                    <Label
                        FontAttributes="Bold"
                        FontSize="16"
                        Text="Permission Status"
                        TextColor="#374151" />

                    <Grid
                        ColumnDefinitions="Auto,*"
                        ColumnSpacing="10"
                        RowDefinitions="Auto,Auto"
                        RowSpacing="5">
                        <Ellipse
                            x:Name="MicrophoneStatusIndicator"
                            Grid.Row="0"
                            Grid.Column="0"
                            Fill="Red"
                            HeightRequest="12"
                            VerticalOptions="Center"
                            WidthRequest="12" />
                        <Label
                            x:Name="MicrophoneStatusLabel"
                            Grid.Row="0"
                            Grid.Column="1"
                            FontSize="14"
                            Text="Microphone: Not Checked"
                            TextColor="#374151"
                            VerticalOptions="Center" />

                        <Ellipse
                            x:Name="AudioStatusIndicator"
                            Grid.Row="1"
                            Grid.Column="0"
                            Fill="Red"
                            HeightRequest="12"
                            VerticalOptions="Center"
                            WidthRequest="12" />
                        <Label
                            x:Name="AudioStatusLabel"
                            Grid.Row="1"
                            Grid.Column="1"
                            FontSize="14"
                            Text="Audio Playback: Not Checked"
                            TextColor="#374151"
                            VerticalOptions="Center" />
                    </Grid>

                    <Button
                        x:Name="CheckPermissionsButton"
                        Margin="0,10,0,0"
                        BackgroundColor="#8FBFA8"
                        Clicked="OnCheckPermissionsClicked"
                        CornerRadius="8"
                        Text="Check Permissions"
                        TextColor="White" />
                </StackLayout>
            </Frame>

            <!--  Ringtone Tests  -->
            <Frame
                Padding="15"
                BackgroundColor="White"
                CornerRadius="10"
                HasShadow="True">
                <StackLayout>
                    <Label
                        FontAttributes="Bold"
                        FontSize="16"
                        Text="Ringtone Tests"
                        TextColor="#374151" />

                    <Grid
                        ColumnDefinitions="*,*"
                        ColumnSpacing="10"
                        RowSpacing="10">
                        <Button
                            x:Name="PlayIncomingButton"
                            Grid.Row="0"
                            Grid.Column="0"
                            BackgroundColor="#FEA195"
                            Clicked="OnPlayIncomingClicked"
                            CornerRadius="8"
                            Text="Play Incoming"
                            TextColor="White" />

                        <Button
                            x:Name="PlayOutgoingButton"
                            Grid.Row="0"
                            Grid.Column="1"
                            BackgroundColor="#8FBFA8"
                            Clicked="OnPlayOutgoingClicked"
                            CornerRadius="8"
                            Text="Play Outgoing"
                            TextColor="White" />

                        <Button
                            x:Name="PlayConnectedButton"
                            Grid.Row="1"
                            Grid.Column="0"
                            BackgroundColor="#10B981"
                            Clicked="OnPlayConnectedClicked"
                            CornerRadius="8"
                            Text="Play Connected"
                            TextColor="White" />

                        <Button
                            x:Name="PlayEndButton"
                            Grid.Row="1"
                            Grid.Column="1"
                            BackgroundColor="#EF4444"
                            Clicked="OnPlayEndClicked"
                            CornerRadius="8"
                            Text="Play End"
                            TextColor="White" />
                    </Grid>

                    <Button
                        x:Name="StopAllButton"
                        Margin="0,10,0,0"
                        BackgroundColor="#6B7280"
                        Clicked="OnStopAllClicked"
                        CornerRadius="8"
                        Text="Stop All Sounds"
                        TextColor="White" />
                </StackLayout>
            </Frame>

            <!--  Audio Capture Test  -->
            <Frame
                Padding="15"
                BackgroundColor="White"
                CornerRadius="10"
                HasShadow="True">
                <StackLayout>
                    <Label
                        FontAttributes="Bold"
                        FontSize="16"
                        Text="Audio Capture Test"
                        TextColor="#374151" />

                    <Label
                        x:Name="CaptureStatusLabel"
                        FontSize="14"
                        Text="Status: Not Started"
                        TextColor="#6B7280" />

                    <ProgressBar
                        x:Name="AudioLevelMeter"
                        Margin="0,5,0,5"
                        BackgroundColor="#E5E7EB"
                        HeightRequest="10"
                        Progress="0"
                        ProgressColor="#FEA195" />

                    <Grid ColumnDefinitions="*,*" ColumnSpacing="10">
                        <Button
                            x:Name="StartCaptureButton"
                            Grid.Column="0"
                            BackgroundColor="#8FBFA8"
                            Clicked="OnStartCaptureClicked"
                            CornerRadius="8"
                            Text="Start Capture"
                            TextColor="White" />

                        <Button
                            x:Name="StopCaptureButton"
                            Grid.Column="1"
                            BackgroundColor="#EF4444"
                            Clicked="OnStopCaptureClicked"
                            CornerRadius="8"
                            IsEnabled="False"
                            Text="Stop Capture"
                            TextColor="White" />
                    </Grid>
                </StackLayout>
            </Frame>

            <!--  Log Output  -->
            <Frame
                Padding="15"
                BackgroundColor="White"
                CornerRadius="10"
                HasShadow="True">
                <StackLayout>
                    <Label
                        FontAttributes="Bold"
                        FontSize="16"
                        Text="Test Log"
                        TextColor="#374151" />

                    <ScrollView HeightRequest="200">
                        <Label
                            x:Name="LogLabel"
                            Padding="10"
                            BackgroundColor="#F9FAFB"
                            FontFamily="Courier"
                            FontSize="12"
                            Text="Ready for testing..."
                            TextColor="#374151" />
                    </ScrollView>

                    <Button
                        x:Name="ClearLogButton"
                        Margin="0,10,0,0"
                        BackgroundColor="#6B7280"
                        Clicked="OnClearLogClicked"
                        CornerRadius="8"
                        Text="Clear Log"
                        TextColor="White" />
                </StackLayout>
            </Frame>

        </StackLayout>
    </ScrollView>
</ContentPage>
