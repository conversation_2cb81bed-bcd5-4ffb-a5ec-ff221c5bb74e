﻿.MyPets-CONTAINER {
    background: #f2f6f9;
    padding: 2rem;
    min-height: 100vh;
    width: 100%;
    max-width: 72rem;
    margin: 0 auto;
    font-family: 'Inter', sans-serif;
}

/* Header Section */
.header-section {
    background: #157BAB;
    border-radius: 1.25rem;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    text-align: center;
}

.header-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    font-family: 'Acme', sans-serif;
}

.header-subtitle {
    font-size: 1.125rem;
    opacity: 0.9;
    
}

/* Search Section */
.search-section {
    margin-bottom: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .MyPets-CONTAINER {
        padding: 1rem;
    }

    .header-section {
        padding: 1.5rem;
    }

    .header-title {
        font-size: 1.5rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.header-section {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
