using Microsoft.EntityFrameworkCore;
using PetVet.Api.Data;
using PetVet.Api.Data.Entities;
using PetVet.Shared;
using PetVet.Shared.DTOs;

namespace PetVet.Api.Services
{
    public class AppointmentService
    {
        private readonly IDbContextFactory<PetVetContext> _contextFactory;
        private readonly SmsService _smsService;
        private readonly SmsNotificationService _smsNotificationService;
        private readonly ILogger<AppointmentService> _logger;

        public AppointmentService(
            IDbContextFactory<PetVetContext> contextFactory,
            SmsService smsService,
            SmsNotificationService smsNotificationService,
            ILogger<AppointmentService> logger)
        {
            _contextFactory = contextFactory;
            _smsService = smsService;
            _smsNotificationService = smsNotificationService;
            _logger = logger;
        }

        public async Task<List<VetTimeSlotDto>> GetVetTimeSlotsAsync(int vetId)
        {
            using var context = _contextFactory.CreateDbContext();
            var currentTime = DateTime.Now;
            var currentDate = currentTime.Date;

            var timeSlots = await context.VetTimeSlots
                .AsNoTracking()
                .Where(ts => ts.VetId == vetId)
                .Select(ts => new VetTimeSlotDto
                {
                    Id = ts.Id,
                    VetId = ts.VetId,
                    Day = ts.Day,
                    Time = ts.Time,
                    IsBooked = ts.IsBooked,
                    IsExpired = false // Initialize IsExpired as false
                })
                .ToListAsync();

            // Mark time slots as expired if they're in the past
            foreach (var slot in timeSlots)
            {
                if (DateTime.TryParse(slot.Time, out DateTime slotTime))
                {
                    // Get the day of week from the slot's Day property
                    var dayOfWeek = slot.Day switch
                    {
                        "Monday" => DayOfWeek.Monday,
                        "Tuesday" => DayOfWeek.Tuesday,
                        "Wednesday" => DayOfWeek.Wednesday,
                        "Thursday" => DayOfWeek.Thursday,
                        "Friday" => DayOfWeek.Friday,
                        "Saturday" => DayOfWeek.Saturday,
                        "Sunday" => DayOfWeek.Sunday,
                        _ => DayOfWeek.Monday // Default to Monday if invalid
                    };

                    // Calculate the next occurrence of this day
                    var nextOccurrence = currentDate;
                    while (nextOccurrence.DayOfWeek != dayOfWeek)
                    {
                        nextOccurrence = nextOccurrence.AddDays(1);
                    }

                    // Combine the date and time
                    var combinedDateTime = nextOccurrence.Add(slotTime.TimeOfDay);

                    // Only mark as expired if the combined date and time is in the past
                    if (combinedDateTime < currentTime)
                    {
                        slot.IsExpired = true;
                    }
                }
            }

            return timeSlots;
        }

        public async Task<PetVetApiResponse> SaveVetTimeSlotsAsync(int vetId, List<VetTimeSlotDto> timeSlots)
        {
            using var context = _contextFactory.CreateDbContext();

            // Get existing time slots for the vet
            var existingSlots = await context.VetTimeSlots
                .Where(ts => ts.VetId == vetId)
                .ToListAsync();

            // Remove slots that are no longer in the updated list
            var slotsToRemove = existingSlots.Where(es =>
                !timeSlots.Any(ts => ts.Day == es.Day && ts.Time == es.Time && !es.IsBooked)).ToList();

            if (slotsToRemove.Any())
            {
                context.VetTimeSlots.RemoveRange(slotsToRemove);
            }

            // Add new slots
            foreach (var slot in timeSlots)
            {
                if (!existingSlots.Any(es => es.Day == slot.Day && es.Time == slot.Time))
                {
                    await context.VetTimeSlots.AddAsync(new VetTimeSlot
                    {
                        VetId = vetId,
                        Day = slot.Day,
                        Time = slot.Time,
                        IsBooked = false
                    });
                }
            }

            await context.SaveChangesAsync();
            return PetVetApiResponse.Success();
        }

        public async Task<PetVetApiResponse> BookTimeSlotAsync(int timeSlotId)
        {
            using var context = _contextFactory.CreateDbContext();

            var timeSlot = await context.VetTimeSlots.FindAsync(timeSlotId);

            if (timeSlot == null)
            {
                return PetVetApiResponse.Fail("Time slot not found.");
            }

            if (timeSlot.IsBooked)
            {
                return PetVetApiResponse.Fail("Time slot is already booked.");
            }

            timeSlot.IsBooked = true;
            await context.SaveChangesAsync();

            return PetVetApiResponse.Success();
        }

        // Create a new appointment
        public async Task<PetVetApiResponse> CreateAppointmentAsync(AppointmentDto appointmentDto)
        {
            using var context = _contextFactory.CreateDbContext();

            // Check if the time slot exists and is available
            var timeSlot = await context.VetTimeSlots.FindAsync(appointmentDto.TimeSlotId);

            if (timeSlot == null)
            {
                return PetVetApiResponse.Fail("Time slot not found.");
            }

            if (timeSlot.IsBooked)
            {
                return PetVetApiResponse.Fail("This time slot is already booked.");
            }

            // Create the appointment
            var appointment = new Appointment
            {
                PetOwnerId = appointmentDto.PetOwnerId,
                VetId = appointmentDto.VetId,
                TimeSlotId = appointmentDto.TimeSlotId,
                AppointmentDate = appointmentDto.AppointmentDate,
                Status = nameof(AppointmentStatus.Confirmed),
                Notes = appointmentDto.Notes,
                CreatedAt = DateTime.UtcNow
            };

            // Mark the time slot as booked
            timeSlot.IsBooked = true;

            await context.Appointments.AddAsync(appointment);
            await context.SaveChangesAsync();

            // Send notification to both pet owner and vet
            await SendAppointmentNotification(appointment.Id);

            return PetVetApiResponse.Success();
        }

        // Get appointments for a pet owner
        public async Task<List<AppointmentDto>> GetPetOwnerAppointmentsAsync(int petOwnerId)
        {
            using var context = _contextFactory.CreateDbContext();

            // First, update any expired appointments
            await UpdateExpiredAppointmentsAsync();

            var appointments = await context.Appointments
                .AsNoTracking()
                .Where(a => a.PetOwnerId == petOwnerId)
                .Include(a => a.PetOwner)
                .Include(a => a.Vet)
                .Include(a => a.TimeSlot)
                .Select(a => new AppointmentDto
                {
                    Id = a.Id,
                    PetOwnerId = a.PetOwnerId,
                    PetOwnerName = a.PetOwner.Name,
                     PetOwnerNameDisplayPicture = a.PetOwner.ImageUrl ,
                    VetId = a.VetId,
                    VetName = a.Vet.Name,
                    VetDisplayPicture = a.Vet.ImageUrl  ,
                    TimeSlotId = a.TimeSlotId,
                    Day = a.TimeSlot.Day,
                    Time = a.TimeSlot.Time,
                    AppointmentDate = a.AppointmentDate,
                    Status = a.Status,
                    Notes = a.Notes,
                    CreatedAt = a.CreatedAt,
                    UpdatedAt = a.UpdatedAt
                })
                .OrderByDescending(a => a.CreatedAt)
                .ThenByDescending(a => a.AppointmentDate)
                .ToListAsync();

            return appointments;
        }

        // Get appointments for a vet
        public async Task<List<AppointmentDto>> GetVetAppointmentsAsync(int vetId)
        {
            using var context = _contextFactory.CreateDbContext();

            // First, update any expired appointments
            await UpdateExpiredAppointmentsAsync();

            var appointments = await context.Appointments
                .AsNoTracking()
                .Where(a => a.VetId == vetId)
                .Include(a => a.PetOwner)
                .Include(a => a.Vet)
                .Include(a => a.TimeSlot)
                .Select(a => new AppointmentDto
                {
                    Id = a.Id,
                    PetOwnerId = a.PetOwnerId,
                    PetOwnerName = a.PetOwner.Name,
                    PetOwnerNameDisplayPicture = a.PetOwner.ImageUrl,
                    VetId = a.VetId,
                    VetName = a.Vet.Name,
                    VetDisplayPicture = a.Vet.ImageUrl,
                    TimeSlotId = a.TimeSlotId,
                    Day = a.TimeSlot.Day,
                    Time = a.TimeSlot.Time,
                    AppointmentDate = a.AppointmentDate,
                    Status = a.Status,
                    Notes = a.Notes,
                    CreatedAt = a.CreatedAt,
                    UpdatedAt = a.UpdatedAt
                })
                .OrderByDescending(a => a.CreatedAt)
                .ThenByDescending(a => a.AppointmentDate)
                .ToListAsync();

            return appointments;
        }

        // Update appointment status (Confirm, Cancel, Complete)
        public async Task<PetVetApiResponse> UpdateAppointmentStatusAsync(int appointmentId, string status)
        {
            using var context = _contextFactory.CreateDbContext();

            var appointment = await context.Appointments
                .Include(a => a.TimeSlot)
                .FirstOrDefaultAsync(a => a.Id == appointmentId);

            if (appointment == null)
            {
                return PetVetApiResponse.Fail("Appointment not found.");
            }

            // Validate status
            if (!Enum.TryParse<AppointmentStatus>(status, out var statusEnum))
            {
                return PetVetApiResponse.Fail("Invalid appointment status.");
            }

            // If cancelling, free up the time slot
            if (statusEnum == AppointmentStatus.Cancelled)
            {
                appointment.TimeSlot.IsBooked = false;
            }

            appointment.Status = status;
            appointment.UpdatedAt = DateTime.UtcNow;

            await context.SaveChangesAsync();

            // Send notification
            await SendStatusUpdateNotification(appointmentId, status);

            return PetVetApiResponse.Success();
        }

        // Reschedule appointment
        public async Task<PetVetApiResponse> RescheduleAppointmentAsync(int appointmentId, int newTimeSlotId, string newAppointmentDate)
        {
            using var context = _contextFactory.CreateDbContext();

            var appointment = await context.Appointments
                .Include(a => a.TimeSlot)
                .FirstOrDefaultAsync(a => a.Id == appointmentId);

            if (appointment == null)
            {
                return PetVetApiResponse.Fail("Appointment not found.");
            }

            // Check if new time slot exists and is available
            var newTimeSlot = await context.VetTimeSlots.FindAsync(newTimeSlotId);

            if (newTimeSlot == null)
            {
                return PetVetApiResponse.Fail("New time slot not found.");
            }

            if (newTimeSlot.IsBooked && newTimeSlot.Id != appointment.TimeSlotId)
            {
                return PetVetApiResponse.Fail("New time slot is already booked.");
            }

            // Free up old time slot
            appointment.TimeSlot.IsBooked = false;

            // Update appointment with new time slot
            appointment.TimeSlotId = newTimeSlotId;
            appointment.AppointmentDate = newAppointmentDate;
            appointment.UpdatedAt = DateTime.UtcNow;

            // Mark new time slot as booked
            newTimeSlot.IsBooked = true;

            await context.SaveChangesAsync();

            // Send notification
            await SendRescheduleNotification(appointmentId);

            return PetVetApiResponse.Success();
        }

        // Get calendar data for a user (pet owner or vet)
        public async Task<List<AppointmentDto>> GetCalendarDataAsync(int userId, string userRole)
        {
            using var context = _contextFactory.CreateDbContext();

            IQueryable<Appointment> query = context.Appointments.AsNoTracking();

            if (userRole == nameof(UserRole.PetOwner))
            {
                query = query.Where(a => a.PetOwnerId == userId);
            }
            else if (userRole == nameof(UserRole.Vet))
            {
                query = query.Where(a => a.VetId == userId);
            }
            else
            {
                return new List<AppointmentDto>();
            }

            var appointments = await query
                .Include(a => a.PetOwner)
                .Include(a => a.Vet)
                .Include(a => a.TimeSlot)
                .Select(a => new AppointmentDto
                {
                    Id = a.Id,
                    PetOwnerId = a.PetOwnerId,
                    PetOwnerName = a.PetOwner.Name,
                    VetId = a.VetId,
                    VetName = a.Vet.Name,
                    TimeSlotId = a.TimeSlotId,
                    Day = a.TimeSlot.Day,
                    Time = a.TimeSlot.Time,
                    AppointmentDate = a.AppointmentDate,
                    Status = a.Status,
                    Notes = a.Notes,
                    CreatedAt = a.CreatedAt,
                    UpdatedAt = a.UpdatedAt
                })
                .OrderBy(a => a.AppointmentDate)
                .ThenBy(a => a.Time)
                .ToListAsync();

            return appointments;
        }

        // Get detailed information about a specific appointment
        public async Task<AppointmentDto> GetAppointmentDetailsAsync(int appointmentId)
        {
            using var context = _contextFactory.CreateDbContext();

            var appointment = await context.Appointments
                .AsNoTracking()
                .Include(a => a.PetOwner)
                .Include(a => a.Vet)
                .Include(a => a.TimeSlot)
                .FirstOrDefaultAsync(a => a.Id == appointmentId);

            if (appointment == null)
            {
                return null;
            }

            return new AppointmentDto
            {
                Id = appointment.Id,
                PetOwnerId = appointment.PetOwnerId,
                PetOwnerName = appointment.PetOwner.Name,
                VetId = appointment.VetId,
                VetName = appointment.Vet.Name,
                TimeSlotId = appointment.TimeSlotId,
                Day = appointment.TimeSlot.Day,
                Time = appointment.TimeSlot.Time,
                AppointmentDate = appointment.AppointmentDate,
                Status = appointment.Status,
                Notes = appointment.Notes,
                CreatedAt = appointment.CreatedAt,
                UpdatedAt = appointment.UpdatedAt
            };
        }

        // Helper methods for notifications
        private async Task SendAppointmentNotification(int appointmentId)
        {
            try
            {
                _logger.LogInformation("Sending appointment notifications for appointment {AppointmentId}", appointmentId);

                // Send Twilio SMS notification to veterinarian about new appointment
                var vetResult = await _smsNotificationService.SendVetAppointmentNotificationAsync(appointmentId);
                if (!vetResult.IsSuccess)
                {
                    _logger.LogWarning("Failed to send SMS notification to vet for appointment {AppointmentId}: {Error}",
                        appointmentId, vetResult.ErrorMessage);
                }

                // Send Twilio SMS confirmation to pet owner (when appointment is confirmed)
                using var context = _contextFactory.CreateDbContext();
                var appointment = await context.Appointments
                    .Include(a => a.PetOwner)
                    .Include(a => a.Vet)
                    .FirstOrDefaultAsync(a => a.Id == appointmentId);

                if (appointment?.Status == nameof(AppointmentStatus.Confirmed))
                {
                    var petOwnerResult = await _smsNotificationService.SendPetOwnerConfirmationAsync(appointmentId);
                    if (!petOwnerResult.IsSuccess)
                    {
                        _logger.LogWarning("Failed to send SMS confirmation to pet owner for appointment {AppointmentId}: {Error}",
                            appointmentId, petOwnerResult.ErrorMessage);
                    }
                }

                // Fallback to legacy SMS service if Twilio fails
                if (!vetResult.IsSuccess && appointment != null)
                {
                    await SendLegacySmsNotifications(appointment);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending appointment notifications for appointment {AppointmentId}", appointmentId);
            }
        }

        private async Task SendStatusUpdateNotification(int appointmentId, string status)
        {
            try
            {
                _logger.LogInformation("Sending status update notifications for appointment {AppointmentId}, status: {Status}",
                    appointmentId, status);

                // Use the Twilio SMS notification service
                var result = await _smsNotificationService.SendAppointmentStatusUpdateAsync(appointmentId, status);

                if (!result.IsSuccess)
                {
                    _logger.LogWarning("Failed to send SMS status update notifications for appointment {AppointmentId}: {Error}",
                        appointmentId, result.ErrorMessage);

                    // Fallback to legacy SMS service
                    using var context = _contextFactory.CreateDbContext();
                    var appointment = await context.Appointments
                        .Include(a => a.PetOwner)
                        .Include(a => a.Vet)
                        .Include(a => a.TimeSlot)
                        .FirstOrDefaultAsync(a => a.Id == appointmentId);

                    if (appointment != null)
                    {
                        await SendLegacyStatusUpdateSms(appointment, status);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending status update notifications for appointment {AppointmentId}", appointmentId);
            }
        }

        private async Task SendRescheduleNotification(int appointmentId)
        {
            try
            {
                _logger.LogInformation("Sending reschedule notifications for appointment {AppointmentId}", appointmentId);

                // Use the Twilio SMS notification service for rescheduled status
                var result = await _smsNotificationService.SendAppointmentStatusUpdateAsync(appointmentId, "Rescheduled");

                if (!result.IsSuccess)
                {
                    _logger.LogWarning("Failed to send SMS reschedule notifications for appointment {AppointmentId}: {Error}",
                        appointmentId, result.ErrorMessage);

                    // Fallback to legacy SMS service
                    using var context = _contextFactory.CreateDbContext();
                    var appointment = await context.Appointments
                        .Include(a => a.PetOwner)
                        .Include(a => a.Vet)
                        .Include(a => a.TimeSlot)
                        .FirstOrDefaultAsync(a => a.Id == appointmentId);

                    if (appointment != null)
                    {
                        await SendLegacyRescheduleSms(appointment);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending reschedule notifications for appointment {AppointmentId}", appointmentId);
            }
        }

        // Legacy fallback methods for SMS notifications
        private async Task SendLegacySmsNotifications(Appointment appointment)
        {
            try
            {
                _logger.LogInformation("Using legacy SMS fallback for appointment {AppointmentId}", appointment.Id);
                var context = _contextFactory.CreateDbContext();
                var slot = await context.VetTimeSlots.FirstOrDefaultAsync(x=>x.Id == appointment.TimeSlotId);
                var petOwnerMessage = $"Dear {appointment.PetOwner.Name}, your appointment with Dr. {appointment.Vet.Name} is confirmed for {appointment.AppointmentDate} at {slot?.Time}.";
                var petOwnerResult = await SendSmsNotification(appointment.PetOwner.Phone, petOwnerMessage);
                if (!petOwnerResult.IsSuccess)
                {
                    _logger.LogError("Failed to send legacy SMS to pet owner: {Error}", petOwnerResult.ErrorMessage);
                }

                var vetMessage = $"New appointment scheduled with {appointment.PetOwner.Name} on {appointment.AppointmentDate} at {slot?.Time}.";
                var vetResult = await SendSmsNotification(appointment.Vet.Phone, vetMessage);
                if (!vetResult.IsSuccess)
                {
                    _logger.LogError("Failed to send legacy SMS to vet: {Error}", vetResult.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending legacy SMS notifications");
            }
        }

        private async Task SendLegacyStatusUpdateSms(Appointment appointment, string status)
        {
            try
            {
                string petOwnerMessage = string.Empty;
                string vetMessage = string.Empty;

                switch (status)
                {
                    case nameof(AppointmentStatus.Confirmed):
                        petOwnerMessage = $"Dear {appointment.PetOwner.Name}, your appointment with Dr. {appointment.Vet.Name} on {appointment.AppointmentDate} at {appointment.TimeSlot.Time} has been confirmed.";
                        vetMessage = $"Appointment with {appointment.PetOwner.Name} on {appointment.AppointmentDate} at {appointment.TimeSlot.Time} has been confirmed.";
                        break;
                    case nameof(AppointmentStatus.Cancelled):
                        petOwnerMessage = $"Dear {appointment.PetOwner.Name}, your appointment with Dr. {appointment.Vet.Name} on {appointment.AppointmentDate} at {appointment.TimeSlot.Time} has been cancelled.";
                        vetMessage = $"Appointment with {appointment.PetOwner.Name} on {appointment.AppointmentDate} at {appointment.TimeSlot.Time} has been cancelled.";
                        break;
                    case nameof(AppointmentStatus.Completed):
                        petOwnerMessage = $"Dear {appointment.PetOwner.Name}, your appointment with Dr. {appointment.Vet.Name} on {appointment.AppointmentDate} at {appointment.TimeSlot.Time} has been completed.";
                        vetMessage = $"Appointment with {appointment.PetOwner.Name} on {appointment.AppointmentDate} at {appointment.TimeSlot.Time} has been completed.";
                        break;
                }

                var petOwnerResult = await SendSmsNotification(appointment.PetOwner.Phone, petOwnerMessage);
                if (!petOwnerResult.IsSuccess)
                {
                    _logger.LogError("Failed to send legacy status update SMS to pet owner: {Error}", petOwnerResult.ErrorMessage);
                }

                var vetResult = await SendSmsNotification(appointment.Vet.Phone, vetMessage);
                if (!vetResult.IsSuccess)
                {
                    _logger.LogError("Failed to send legacy status update SMS to vet: {Error}", vetResult.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending legacy status update SMS");
            }
        }

        private async Task SendLegacyRescheduleSms(Appointment appointment)
        {
            try
            {
                var petOwnerMessage = $"Dear {appointment.PetOwner.Name}, your appointment with Dr. {appointment.Vet.Name} has been rescheduled to {appointment.AppointmentDate} at {appointment.TimeSlot.Time}.";
                var petOwnerResult = await SendSmsNotification(appointment.PetOwner.Phone, petOwnerMessage);
                if (!petOwnerResult.IsSuccess)
                {
                    _logger.LogError("Failed to send legacy reschedule SMS to pet owner: {Error}", petOwnerResult.ErrorMessage);
                }

                var vetMessage = $"Appointment with {appointment.PetOwner.Name} has been rescheduled to {appointment.AppointmentDate} at {appointment.TimeSlot.Time}.";
                var vetResult = await SendSmsNotification(appointment.Vet.Phone, vetMessage);
                if (!vetResult.IsSuccess)
                {
                    _logger.LogError("Failed to send legacy reschedule SMS to vet: {Error}", vetResult.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending legacy reschedule SMS");
            }
        }

        private async Task<PetVetApiResponse> SendSmsNotification(string phoneNumber, string message)
        {
            if (string.IsNullOrEmpty(phoneNumber))
            {
                return PetVetApiResponse.Fail("Cannot send SMS: Phone number is empty");
            }

            try
            {
                // Format phone number if needed (ensure it has country code)
                if (!phoneNumber.StartsWith("+"))
                {
                    // For Pakistani numbers (add +92 prefix)
                    if (phoneNumber.StartsWith("0"))
                    {
                        // Remove leading 0 and add +92
                        phoneNumber = "+92" + phoneNumber.TrimStart('0');
                    }
                    else if (!phoneNumber.StartsWith("92"))
                    {
                        // Add +92 if it doesn't already have the country code
                        phoneNumber = "+92" + phoneNumber;
                    }
                    else
                    {
                        // If it starts with 92 but no +, add the +
                        phoneNumber = "+" + phoneNumber;
                    }
                }

                var response = _smsService.SendSms(
                    to: phoneNumber,
                    from: "PetVet",
                    text: message
                );

                if (!response.IsSuccess)
                {
                    return PetVetApiResponse.Fail($"Failed to send SMS: {response.ErrorMessage}");
                }

                return PetVetApiResponse.Success();
            }
            catch (Exception ex)
            {
                return PetVetApiResponse.Fail($"Failed to send SMS: {ex.Message}");
            }
        }

        // Add new method to update expired appointments
        public async Task UpdateExpiredAppointmentsAsync()
        {
            using var context = _contextFactory.CreateDbContext();
            var currentTime = DateTime.Now;
            var currentDate = currentTime.Date;

            // Get all confirmed appointments that are in the past
            var expiredAppointments = await context.Appointments
                .Include(a => a.TimeSlot)
                .Where(a => a.Status == nameof(AppointmentStatus.Confirmed))
                .ToListAsync();

            foreach (var appointment in expiredAppointments)
            {
                if (DateTime.TryParse(appointment.AppointmentDate, out DateTime appointmentDate) &&
                    DateTime.TryParse(appointment.TimeSlot.Time, out DateTime appointmentTime))
                {
                    var combinedDateTime = appointmentDate.Add(appointmentTime.TimeOfDay);
                    if (combinedDateTime < currentTime)
                    {
                        appointment.Status = nameof(AppointmentStatus.Expired);
                        appointment.TimeSlot.IsBooked = false; // Free up the time slot
                    }
                }
            }

            await context.SaveChangesAsync();
        }
    }
} 