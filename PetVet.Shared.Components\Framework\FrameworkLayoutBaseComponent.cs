﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.JSInterop;
using PetVet.Shared.Components.Framework;
using System.Security.Claims;

namespace PetVet.Framework.Core
{
    public class FrameworkLayoutBaseComponent : LayoutComponentBase
    {
        [Inject]
        public KtDialogService? DialogService { get; set; }

       
        [Inject]
        public IJSRuntime? JsRuntime { get; set; }

        public string? Culture { get; set; } = "en-pk";

        [CascadingParameter]
        public ClaimsPrincipal? ClaimsPrinciple
        {
            get
            {
                return _user;
            }
            set
            {
                _user = value;
                 
            }
        }

        private ClaimsPrincipal? _user;

      
        [Inject]
        public ILocalStorageService? StorageService { get; set; }

        [Inject]
        public AuthenticationStateProvider? AuthStateProvider { get; set; }

        public int TimeZoneOffset { get; set; } = 0;

        protected override async Task OnInitializedAsync()
        {
            if (AuthStateProvider != null)
            {

                var state = await AuthStateProvider.GetAuthenticationStateAsync();
                if (state != null)
                {
                    if (state.User.Identity.IsAuthenticated)
                    {
                        ClaimsPrinciple = state.User;

                    }
                }

            }

           await base.OnInitializedAsync();
        }

        //protected override async Task OnAfterRenderAsync(bool firstRender)
        //{
        //    if (firstRender && JsRuntime is not null)
        //    {
        //        var timezoneOffset = await JsRuntime.InvokeAsync<string>("getCookie", "timezoneoffset");
        //        if (!string.IsNullOrEmpty(timezoneOffset) && double.TryParse(timezoneOffset, out double offset))
        //        {
        //            TimeZoneOffset = (int)offset;
        //        }
        //    }
        //}



        public virtual void DialogService_OnClose(dynamic obj)
        {
            Console.WriteLine("Dialog closed");
        }

        public async Task ClientLog(string Text)
        {
            await JsRuntime.InvokeVoidAsync("browserLog", Text);
        }
    }
}
