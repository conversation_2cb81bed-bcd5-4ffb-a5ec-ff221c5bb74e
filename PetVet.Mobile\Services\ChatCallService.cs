using Microsoft.Extensions.Logging;
using PetVet.MauiApp.Services;
using PetVet.Mobile.Models;
using PetVet.Mobile.Services;
using System.Collections.Concurrent;
using System.Text.Json;
using System.Security.Claims;
using PetVet.Shared.Interfaces;
using System.Diagnostics;
using PetVet.Shared.Components.Framework;

namespace PetVet.Mobile.Services;

/// <summary>
/// Call service that uses the existing SignalR ChatHub for real-time communication
/// </summary>
public interface IChatCallService
{
    // Events
    event EventHandler<CallInviteMessage>? CallInvitationReceived;
    event EventHandler<CallResponseMessage>? CallResponseReceived;
    event EventHandler<CallStatusMessage>? CallStatusChanged;
    event EventHandler<AudioDataMessage>? AudioDataReceived;
    event EventHandler<VideoDataMessage>? VideoDataReceived;

    // Initialization
    Task<bool> InitializeAsync();
    void Initialize(int userId, string userName);

    // Call management
    Task<bool> SendCallInvitationAsync(string threadId, int toUserId, CallType callType, string message = "");
    Task<bool> AcceptCallAsync(CallInviteMessage callInvite);
    Task<bool> AcceptCallAsync(string callId);
    Task<bool> DeclineCallAsync(string callId);
    Task<bool> EndCallAsync(string callId);

    // Media streaming
    bool SendAudioDataAsync(string callId, byte[] audioData);
    Task<bool> SendVideoDataAsync(string callId, byte[] videoData);

    // Call state
    CallSession? GetActiveCall(string threadId);
    List<CallSession> GetActiveCalls();
    bool IsInCall(string threadId);

    // Media control
    Task<bool> ToggleAudioAsync(string callId, bool enabled);
    Task<bool> ToggleVideoAsync(string callId, bool enabled);
}

public class ChatCallService : IChatCallService, IDisposable
{
    private readonly SignalRClientService _signalRService;
    private readonly IMediaCaptureService _mediaCaptureService;
    private readonly IAudioStreamingService _audioStreamingService;
    private readonly IAudioRingtoneService _ringtoneService;
    private readonly ILocalStorageService _localStorageService;
    private readonly ILogger<ChatCallService> _logger;

    private readonly ConcurrentDictionary<string, CallSession> _activeCalls = new();
    private readonly ConcurrentDictionary<string, CallSession> _callsByThread = new();

    private int _currentUserId;
    private string _currentUserName = "";
    private bool _isInitialized = false;

    // Audio streaming state
    private readonly ConcurrentDictionary<string, AudioQualityMetrics> _audioMetrics = new();
    private readonly Stopwatch _transmissionStopwatch = new();
    private long _audioFrameSequence = 0;

    // Events
    public event EventHandler<CallInviteMessage>? CallInvitationReceived;
    public event EventHandler<CallResponseMessage>? CallResponseReceived;
    public event EventHandler<CallStatusMessage>? CallStatusChanged;
    public event EventHandler<AudioDataMessage>? AudioDataReceived;
    public event EventHandler<VideoDataMessage>? VideoDataReceived;

    public ChatCallService(
        SignalRClientService signalRService,
        IMediaCaptureService mediaCaptureService,
        IAudioStreamingService audioStreamingService,
        IAudioRingtoneService ringtoneService,
        ILocalStorageService localStorageService,
        ILogger<ChatCallService> logger)
    {
        _signalRService = signalRService;
        _mediaCaptureService = mediaCaptureService;
        _audioStreamingService = audioStreamingService;
        _ringtoneService = ringtoneService;
        _localStorageService = localStorageService;
        _logger = logger;

        SubscribeToSignalREvents();
        SubscribeToMediaEvents();
        SubscribeToAudioStreamingEvents();
    }

    public async Task<bool> InitializeAsync()
    {
        try
        {
            // Get user credentials from localStorage using correct ClaimTypes
            var userId = await _localStorageService.GetValue(ClaimTypes.NameIdentifier);
            var userName = await _localStorageService.GetValue(ClaimTypes.Name);

            if (string.IsNullOrEmpty(userId)  )
            {
                _logger.LogWarning("Failed to initialize ChatCallService: Missing user credentials");
                return false;
            }

            _currentUserId = Convert.ToInt32(userId);
            _currentUserName = userName?? "Incoming Call";
            _isInitialized = true;

            _logger.LogInformation("ChatCallService initialized for user {UserName} ({UserId})", userName, userId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing ChatCallService");
            return false;
        }
    }

    public void Initialize(int userId, string userName)
    {
        _currentUserId = userId;
        _currentUserName = userName;
        _isInitialized = true;
        _logger.LogInformation("ChatCallService initialized for user {UserId}", userId);
    }

    private void SubscribeToSignalREvents()
    {
        // Subscribe to existing SignalR message events and filter for call messages
        _signalRService.OnMessageReceived += OnSignalRMessageReceived;
        //_signalRService._hubConnection.Reconnected += OnSignalRConnected;
        //_signalRService.Disconnected += OnSignalRDisconnected;

        // Subscribe to call-specific SignalR events
        _signalRService.CallActionReceived += OnCallActionReceived;
    }

    private void SubscribeToMediaEvents()
    {
        _mediaCaptureService.AudioDataCaptured += OnAudioDataCaptured;
        _mediaCaptureService.VideoDataCaptured += OnVideoDataCaptured;
        _mediaCaptureService.AudioQualityChanged += OnAudioQualityChanged;
    }

    private void SubscribeToAudioStreamingEvents()
    {
        _audioStreamingService.AudioFrameReady += OnAudioFrameReady;
        _audioStreamingService.QualityMetricsUpdated += OnStreamingQualityUpdated;
    }

    private async void OnSignalRMessageReceived(string message)
    {
        // Regular chat messages - not used for calls anymore
        _logger.LogDebug("Regular SignalR message received: {Message}", message);
    }

    private void OnCallActionReceived(int userId, string message)
    {
        try
        {
            _logger.LogInformation("Call action received from user {UserId}: {Message}", userId, message);

            // Parse the call message
            var messageData = JsonSerializer.Deserialize<Dictionary<string, object>>(message);

            if (messageData?.ContainsKey("MessageType") == true)
            {
                var messageType = messageData["MessageType"].ToString();

                switch (messageType)
                {
                    case "CallInvite":
                        HandleCallInviteMessage(message);
                        break;
                    case "CallAccept":
                    case "CallDecline":
                        HandleCallResponseMessage(message);
                        break;
                    case "CallStatus":
                        HandleCallStatusMessage(message);
                        break;
                    case "AudioData":

                        HandleAudioDataMessage(message);
                        break;
                    case "EnhancedAudioData":
                        HandleEnhancedAudioDataMessage(messageData);
                        break;
                    case "VideoData":
                        HandleVideoDataMessage(message);
                        break;
                    default:
                        _logger.LogWarning("Unknown call message type: {MessageType}", messageType);
                        break;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing call action message");
        }
    }

    public async Task<bool> SendCallInvitationAsync(string threadId, int toUserId, CallType callType, string message = "")
    {
        try
        {
            // Ensure service is initialized
            if (!_isInitialized)
            {
                var initialized = await InitializeAsync();
                if (!initialized)
                {
                    _logger.LogError("Cannot send call invitation: Service not initialized");
                    return false;
                }
            }

            // Validate inputs
            if (string.IsNullOrEmpty(threadId) || toUserId == 0)
            {
                _logger.LogError("Cannot send call invitation: Missing threadId or toUserId");
                return false;
            }

            // Stop incoming call ringtone
            await _ringtoneService.StopAllSoundsAsync();

            // Play call connected sound
            _ = Task.Run(async () => await _ringtoneService.PlayCallConnectedSoundAsync());

            var callInvite = new CallInviteMessage
            {
                CallId = Guid.NewGuid().ToString(),
                FromUserId = _currentUserId,
                FromUserName = _currentUserName,
                ToUserId = toUserId,
                ThreadId = threadId,
                CallType = callType,
                Message = string.IsNullOrEmpty(message) ? $"{callType} call invitation" : message
            };

            // Create call session
            var callSession = new CallSession
            {
                CallId = callInvite.CallId,
                ThreadId = threadId,
                CallType = callType,
                Status = CallStatus.Initiating,
                InitiatorId = _currentUserId,
                InitiatorName = _currentUserName
            };

            callSession.Participants.Add(new CallParticipant
            {
                UserId = _currentUserId,
                UserName = _currentUserName,
                IsAudioEnabled = true,
                IsVideoEnabled = callType == CallType.Video || callType == CallType.AudioVideo
            });

            _activeCalls[callInvite.CallId] = callSession;
            _callsByThread[threadId] = callSession;

            // Send via SignalR ChatHub using the correct format for call actions
            var signalRMessage = new
            {
                MessageType = "CallInvite",
                Data = callInvite,
                ToUserId = toUserId
            };

            await _signalRService.SendMessageAsync(JsonSerializer.Serialize(signalRMessage));

            // Play outgoing call ringtone
            _ = Task.Run(async () => await _ringtoneService.PlayOutgoingCallRingtoneAsync());

            _logger.LogInformation("Call invitation sent: {CallId} from {FromUserId} to {ToUserId}",
                callInvite.CallId, _currentUserId, toUserId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending call invitation");
            return false;
        }
    }

    public async Task<bool> AcceptCallAsync(CallInviteMessage callInvite)
    {
        try
        {
            CallSession? callSession = null;

            if (_activeCalls.ContainsKey(callInvite.CallId))
            {
                if (!_activeCalls.TryGetValue(callInvite.CallId, out callSession))
                {
                    _logger.LogWarning("Call {CallId} not found for accept", callInvite.CallId);
                    return false;
                }
            }
            else
            {
                callSession = new CallSession()
                {
                    CallId = callInvite.CallId,
                    CallType = callInvite.CallType,
                    InitiatorId = callInvite.FromUserId,
                    InitiatorName = callInvite.FromUserName,
                    ThreadId = callInvite.ThreadId,
                    StartTime = DateTime.UtcNow
                };

                // Add call initiator as participant
                callSession.Participants.Add(new CallParticipant
                {
                    UserId = callInvite.FromUserId,
                    UserName = callInvite.FromUserName,
                    IsAudioEnabled = true,
                    IsVideoEnabled = callSession.CallType == CallType.Video || callSession.CallType == CallType.AudioVideo
                });

                _activeCalls[callInvite.CallId] = callSession;
                _callsByThread[callInvite.ThreadId] = callSession; // Fix: Add thread mapping
            }

            var response = new CallResponseMessage
            {
                CallId = callSession.CallId,
                UserId = _currentUserId,
                UserName = _currentUserName,
                Accepted = true
            };

            // Add current user as participant (receiver)
            callSession.Participants.Add(new CallParticipant
            {
                UserId = _currentUserId,
                UserName = _currentUserName,
                IsAudioEnabled = true,
                IsVideoEnabled = callSession.CallType == CallType.Video || callSession.CallType == CallType.AudioVideo
            });

            callSession.Status = CallStatus.Connected;

          

            // Start media capture for receiver
            await StartMediaCaptureForCall(callSession);

            // Send response via SignalR to the call initiator
            var signalRMessage = new
            {
                MessageType = "CallAccept",
                Data = response,
                ToUserId = callSession.InitiatorId
            };

            await _signalRService.SendMessageAsync(JsonSerializer.Serialize(signalRMessage));

            _logger.LogInformation("Call accepted: {CallId} by {UserId}, participants: {ParticipantCount}",
                callSession.CallId, _currentUserId, callSession.Participants.Count);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error accepting call");
            return false;
        }
    }

    public async Task<bool> AcceptCallAsync(string callId)
    {
        try
        {
            if (!_activeCalls.TryGetValue(callId, out var callSession))
            {
                _logger.LogWarning("Call {CallId} not found for accept", callId);
                return false;
            }

            // Create a CallInviteMessage from the existing session
            var callInvite = new CallInviteMessage
            {
                CallId = callSession.CallId,
                CallType = callSession.CallType,
                FromUserId = callSession.InitiatorId,
                FromUserName = callSession.InitiatorName,
                ThreadId = callSession.ThreadId,
                ToUserId = _currentUserId,
                Timestamp = DateTime.UtcNow,
                Message = "Call accepted"
            };

            return await AcceptCallAsync(callInvite);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error accepting call by ID");
            return false;
        }
    }

    public async Task<bool> DeclineCallAsync(string callId)
    {
        try
        {
            if (!_activeCalls.TryGetValue(callId, out var callSession))
            {
                _logger.LogWarning("Call {CallId} not found for decline", callId);
                return false;
            }

            var response = new CallResponseMessage
            {
                CallId = callId,
                UserId = _currentUserId,
                UserName = _currentUserName,
                Accepted = false
            };

            callSession.Status = CallStatus.Ended;
            callSession.EndTime = DateTime.UtcNow;

            // Stop any ringtones
            await _ringtoneService.StopAllSoundsAsync();

            // Send response via SignalR to the call initiator
            var signalRMessage = new
            {
                MessageType = "CallDecline",
                Data = response,
                ToUserId = callSession.InitiatorId
            };

            await _signalRService.SendMessageAsync(JsonSerializer.Serialize(signalRMessage));

            // Clean up
            _activeCalls.TryRemove(callId, out _);
            _callsByThread.TryRemove(callSession.ThreadId, out _);

            _logger.LogInformation("Call declined: {CallId} by {UserId}", callId, _currentUserId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error declining call");
            return false;
        }
    }

    public async Task<bool> EndCallAsync(string callId)
    {
        try
        {
            if (!_activeCalls.TryGetValue(callId, out var callSession))
            {
                _logger.LogWarning("Call {CallId} not found for end", callId);
                return false;
            }

            callSession.Status = CallStatus.Ended;
            callSession.EndTime = DateTime.UtcNow;

            // Stop all sounds
            await _ringtoneService.StopAllSoundsAsync();

            // Play call end sound
            _ = Task.Run(async () => await _ringtoneService.PlayCallEndSoundAsync());

            // Stop media capture
            await StopMediaCaptureForCall(callSession);

            // Send end call message via SignalR
            var statusMessage = new CallStatusMessage
            {
                CallId = callId,
                UserId = _currentUserId,
                Status = CallStatus.Ended,
                Message = "Call ended"
            };

            var signalRMessage = new
            {
                MessageType = "CallStatus",
                Data = statusMessage,
                ThreadId = callSession.ThreadId
            };

            await _signalRService.SendMessageAsync(JsonSerializer.Serialize(signalRMessage));

            // Clean up
            _activeCalls.TryRemove(callId, out _);
            _callsByThread.TryRemove(callSession.ThreadId, out _);

            _logger.LogInformation("Call ended: {CallId} by {UserId}", callId, _currentUserId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ending call");
            return false;
        }
    }

    public bool SendAudioDataAsync(string callId, byte[] audioData)
    {
        try
        {
            if (!_activeCalls.ContainsKey(callId))
                return false;

            // Use streaming service for enhanced audio processing
            return _audioStreamingService.SendAudioFrameAsync(callId, audioData);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending audio data");
            return false;
        }
    }

    private async Task<bool> SendEnhancedAudioFrameAsync(AudioFrame frame)
    {
        try
        {
            if (!_activeCalls.ContainsKey(frame.CallId))
                return false;

            var callSession = _activeCalls[frame.CallId];

            // Send to all participants except sender
            foreach (var participant in callSession.Participants.Where(p => p.UserId != _currentUserId))
            {
                var audioMessage = new
                {
                    MessageType = "EnhancedAudioData",
                    Data = new
                    {
                        frame.CallId,
                        frame.FromUserId,
                        frame.AudioData,
                        frame.SampleRate,
                        frame.Channels,
                        frame.BitsPerSample,
                        frame.SequenceNumber,
                        frame.Timestamp,
                        frame.Duration,
                        frame.Quality
                    },
                    ToUserId = participant.UserId
                };

                await _signalRService.SendMessageAsync(JsonSerializer.Serialize(audioMessage));

                _logger.LogDebug("Sent enhanced audio frame {Sequence} to participant {UserId}, size: {Size} bytes",
                    frame.SequenceNumber, participant.UserId, frame.AudioData.Length);
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending enhanced audio frame");
            return false;
        }
    }

    public async Task<bool> SendVideoDataAsync(string callId, byte[] videoData)
    {
        try
        {
            if (!_activeCalls.ContainsKey(callId))
                return false;

            var videoMessage = new VideoDataMessage
            {
                CallId = callId,
                FromUserId = _currentUserId,
                VideoData = videoData,
                Width = 320,
                Height = 240,
                Format = "RGB"
            };

            var signalRMessage = new
            {
                MessageType = "VideoData",
                Data = videoMessage
            };

            await _signalRService.SendMessageAsync(JsonSerializer.Serialize(signalRMessage));
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending video data");
            return false;
        }
    }

    public CallSession? GetActiveCall(string threadId)
    {
        return _callsByThread.TryGetValue(threadId, out var call) ? call : null;
    }

    public List<CallSession> GetActiveCalls()
    {
        return _activeCalls.Values.ToList();
    }

    public bool IsInCall(string threadId)
    {
        return _callsByThread.ContainsKey(threadId) &&
               _callsByThread[threadId].Status == CallStatus.Connected;
    }

    public async Task<bool> ToggleAudioAsync(string callId, bool enabled)
    {
        try
        {
            if (_activeCalls.TryGetValue(callId, out var callSession))
            {
                var participant = callSession.Participants.FirstOrDefault(p => p.UserId == _currentUserId);
                if (participant != null)
                {
                    participant.IsAudioEnabled = enabled;
                    participant.IsMuted = !enabled;

                    // Use enhanced audio controls
                    _mediaCaptureService.SetMuted(!enabled);
                    _audioStreamingService.SetMuted(!enabled);

                    if (enabled && !_mediaCaptureService.IsAudioCapturing)
                    {
                        var audioConfig = AudioStreamingConfig.GetQualityConfig(AudioQuality.Standard);
                        await _mediaCaptureService.StartAudioCaptureAsync(audioConfig);
                    }
                    else if (!enabled && _mediaCaptureService.IsAudioCapturing)
                    {
                        // Don't stop capture completely, just mute
                        _logger.LogInformation("Audio muted for call {CallId}", callId);
                    }

                    _logger.LogInformation("Audio {Status} for call {CallId}", enabled ? "enabled" : "disabled", callId);
                    return true;
                }
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling audio");
            return false;
        }
    }

    public async Task<bool> ToggleVideoAsync(string callId, bool enabled)
    {
        try
        {
            if (_activeCalls.TryGetValue(callId, out var callSession))
            {
                var participant = callSession.Participants.FirstOrDefault(p => p.UserId == _currentUserId);
                if (participant != null)
                {
                    participant.IsVideoEnabled = enabled;

                    if (enabled)
                        await _mediaCaptureService.StartVideoCaptureAsync();
                    else
                        await _mediaCaptureService.StopVideoCaptureAsync();

                    return true;
                }
            }
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling video");
            return false;
        }
    }

    private async Task StartMediaCaptureForCall(CallSession callSession)
    {
        try
        {
            if (callSession.CallType == CallType.Audio || callSession.CallType == CallType.AudioVideo)
            {
                // Start enhanced audio streaming
                await _audioStreamingService.StartStreamingAsync(callSession.CallId);

                // Configure audio quality based on call type
                var audioQuality = callSession.CallType == CallType.Audio ? AudioQuality.High : AudioQuality.Standard;
                var audioConfig = AudioStreamingConfig.GetQualityConfig(audioQuality);

                await _mediaCaptureService.StartAudioCaptureAsync(audioConfig);

                _logger.LogInformation("Started enhanced audio streaming for call {CallId} with quality {Quality}",
                    callSession.CallId, audioQuality);
            }

            if (callSession.CallType == CallType.Video || callSession.CallType == CallType.AudioVideo)
            {
                await _mediaCaptureService.StartVideoCaptureAsync();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting media capture for call");
        }
    }

    private async Task StopMediaCaptureForCall(CallSession callSession)
    {
        try
        {
            await _mediaCaptureService.StopAudioCaptureAsync();
            await _mediaCaptureService.StopVideoCaptureAsync();
            await _audioStreamingService.StopStreamingAsync();

            _logger.LogInformation("Stopped media capture and audio streaming for call {CallId}", callSession.CallId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error stopping media capture for call");
        }
    }

    private void HandleCallInviteMessage(string message)
    {
        try
        {
            var messageData = JsonSerializer.Deserialize<Dictionary<string, object>>(message);
            var callInvite = JsonSerializer.Deserialize<CallInviteMessage>(messageData["Data"].ToString());

            if (callInvite != null && callInvite.ToUserId == _currentUserId)
            {
                // Play incoming call ringtone
                _ = Task.Run(async () => await _ringtoneService.PlayIncomingCallRingtoneAsync());

                CallInvitationReceived?.Invoke(this, callInvite);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling call invite message");
        }
    }

    private async void HandleCallResponseMessage(string message)
    {
        try
        {
            var messageData = JsonSerializer.Deserialize<Dictionary<string, object>>(message);
            var response = JsonSerializer.Deserialize<CallResponseMessage>(messageData["Data"].ToString());

            if (response != null)
            {
                // Handle call acceptance on initiator side
                if (response.Accepted && _activeCalls.TryGetValue(response.CallId, out var callSession))
                {
                    // Add the accepting user as participant
                    var existingParticipant = callSession.Participants.FirstOrDefault(p => p.UserId == response.UserId);
                    if (existingParticipant == null)
                    {
                        callSession.Participants.Add(new CallParticipant
                        {
                            UserId = response.UserId,
                            UserName = response.UserName,
                            IsAudioEnabled = true,
                            IsVideoEnabled = callSession.CallType == CallType.Video || callSession.CallType == CallType.AudioVideo
                        });
                    }

                    // Update call status to connected
                    callSession.Status = CallStatus.Connected;

                    // Stop outgoing ringtone and play connected sound
                    await _ringtoneService.StopAllSoundsAsync();
                    _ = Task.Run(async () => await _ringtoneService.PlayCallConnectedSoundAsync());

                    // Start media capture for initiator (if not already started)
                    if (!_mediaCaptureService.IsAudioCapturing && !_audioStreamingService.IsStreaming)
                    {
                        await StartMediaCaptureForCall(callSession);
                        _logger.LogInformation("Started media capture for call initiator after acceptance: {CallId}", callSession.CallId);
                    }

                    _logger.LogInformation("Call {CallId} accepted by {UserId}, participants: {ParticipantCount}",
                        callSession.CallId, response.UserId, callSession.Participants.Count);
                }

                CallResponseReceived?.Invoke(this, response);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling call response message");
        }
    }

    private void HandleCallStatusMessage(string message)
    {
        try
        {
            var messageData = JsonSerializer.Deserialize<Dictionary<string, object>>(message);
            var status = JsonSerializer.Deserialize<CallStatusMessage>(messageData["Data"].ToString());

            if (status != null)
            {
                CallStatusChanged?.Invoke(this, status);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling call status message");
        }
    }

    private void HandleAudioDataMessage(string message)
    {
        try
        {
            var messageData = JsonSerializer.Deserialize<Dictionary<string, object>>(message);
            var messageType = messageData["MessageType"].ToString();

            if (messageType != "EnhancedAudioData")
            {
                HandleEnhancedAudioDataMessage(messageData);
            }
            else
            {
                // Handle legacy audio data
                var audioData = JsonSerializer.Deserialize<AudioDataMessage>(messageData["Data"].ToString());

                if (audioData != null && audioData.FromUserId != _currentUserId)
                {
                    _mediaCaptureService.PlayAudioAsync(audioData.AudioData);
                    AudioDataReceived?.Invoke(this, audioData);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling audio data message");
        }
    }

    private void HandleEnhancedAudioDataMessage(Dictionary<string, object> messageData)
    {
        try
        {
            var data = messageData["Data"];
            var audioFrameJson = JsonSerializer.Serialize(data);
            var audioFrame = JsonSerializer.Deserialize<AudioFrame>(audioFrameJson);

            if (audioFrame != null && audioFrame.FromUserId != _currentUserId)
            {
                _logger.LogDebug("AUDIO_FLOW: Received enhanced audio frame {Sequence} from {UserId}, size: {Size} bytes",
                    audioFrame.SequenceNumber, audioFrame.FromUserId, audioFrame.AudioData.Length);

                // Process through audio streaming service for enhanced playback
                var processed = _audioStreamingService.ProcessIncomingAudioAsync(audioFrame);
                _logger.LogDebug("AUDIO_FLOW: Audio frame processed for playback: {Success}", processed);

                // Also trigger legacy event for compatibility
                var legacyAudioData = new AudioDataMessage
                {
                    CallId = audioFrame.CallId,
                    FromUserId = audioFrame.FromUserId,
                    AudioData = audioFrame.AudioData,
                    SampleRate = audioFrame.SampleRate,
                    Channels = audioFrame.Channels,
                    Timestamp = audioFrame.Timestamp
                };

                AudioDataReceived?.Invoke(this, legacyAudioData);
            }
            else if (audioFrame != null)
            {
                _logger.LogDebug("AUDIO_FLOW: Ignoring audio frame from self: {UserId}", audioFrame.FromUserId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling enhanced audio data message");
        }
    }

    private void HandleVideoDataMessage(string message)
    {
        try
        {
            var messageData = JsonSerializer.Deserialize<Dictionary<string, object>>(message);
            var videoData = JsonSerializer.Deserialize<VideoDataMessage>(messageData["Data"].ToString());

            if (videoData != null && videoData.FromUserId != _currentUserId)
            {
                // Display received video
                _mediaCaptureService.DisplayVideoAsync(videoData.VideoData);
                VideoDataReceived?.Invoke(this, videoData);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling video data message");
        }
    }

    private void OnAudioDataCaptured(object? sender, byte[] audioData)
    {
        try
        {
            _logger.LogDebug("AUDIO_FLOW: Audio data captured, size: {Size} bytes", audioData.Length);

            // Process audio through streaming service for all active calls
            var connectedCalls = _activeCalls.Values.Where(c => c.Status == CallStatus.Connected).ToList();
            _logger.LogDebug("AUDIO_FLOW: Sending to {CallCount} connected calls", connectedCalls.Count);

            foreach (var call in connectedCalls)
            {
                _audioStreamingService.SendAudioFrameAsync(call.CallId, audioData);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing captured audio data");
        }
    }

    private async void OnAudioFrameReady(object? sender, AudioFrame frame)
    {
        try
        {
            // Set the sender user ID
            frame.FromUserId = _currentUserId;
            frame.SequenceNumber = Interlocked.Increment(ref _audioFrameSequence);

            _logger.LogDebug("AUDIO_FLOW: Audio frame ready for transmission, sequence: {Sequence}, size: {Size} bytes",
                frame.SequenceNumber, frame.AudioData.Length);

            // Send enhanced audio frame via SignalR
            await SendEnhancedAudioFrameAsync(frame);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending audio frame");
        }
    }

    private async void OnAudioQualityChanged(object? sender, AudioQualityMetrics metrics)
    {
        try
        {
            _logger.LogDebug("Audio quality metrics updated: Latency={Latency}ms, Level={Level}, PacketLoss={PacketLoss}%",
                metrics.Latency, metrics.AudioLevel, metrics.PacketLossRate * 100);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling audio quality change");
        }
    }

    private async void OnStreamingQualityUpdated(object? sender, AudioQualityMetrics metrics)
    {
        try
        {
            _audioMetrics[metrics.CallId] = metrics;

            // Adaptive quality adjustment based on network conditions
            if (metrics.PacketLossRate > 0.05) // 5% packet loss
            {
                _logger.LogWarning("High packet loss detected ({PacketLoss}%), considering quality reduction",
                    metrics.PacketLossRate * 100);

                // Could implement automatic quality reduction here
                if (metrics.CurrentQuality > AudioQuality.Low)
                {
                    var lowerQuality = (AudioQuality)((int)metrics.CurrentQuality - 1);
                    _audioStreamingService.SetAudioQuality(lowerQuality);
                    _logger.LogInformation("Reduced audio quality to {Quality} due to network conditions", lowerQuality);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error handling streaming quality update");
        }
    }

    private async void OnVideoDataCaptured(object? sender, byte[] videoData)
    {
        // Send captured video to all active calls
        foreach (var call in _activeCalls.Values.Where(c => c.Status == CallStatus.Connected))
        {
            await SendVideoDataAsync(call.CallId, videoData);
        }
    }


    private void OnSignalRDisconnected(object? sender, EventArgs e)
    {
        _logger.LogWarning("SignalR disconnected - ending all active calls");

        // End all active calls on disconnect
        foreach (var call in _activeCalls.Values.ToList())
        {
            _ = Task.Run(() => EndCallAsync(call.CallId));
        }
    }

    public void Dispose()
    {
        // Unsubscribe from events
        _signalRService.OnMessageReceived -= OnSignalRMessageReceived;
        //_signalRService.Connected -= OnSignalRConnected;
        //_signalRService.Disconnected -= OnSignalRDisconnected;
        _signalRService.CallActionReceived -= OnCallActionReceived;

        _mediaCaptureService.AudioDataCaptured -= OnAudioDataCaptured;
        _mediaCaptureService.VideoDataCaptured -= OnVideoDataCaptured;
        _mediaCaptureService.AudioQualityChanged -= OnAudioQualityChanged;

        _audioStreamingService.AudioFrameReady -= OnAudioFrameReady;
        _audioStreamingService.QualityMetricsUpdated -= OnStreamingQualityUpdated;

        // End all active calls
        foreach (var call in _activeCalls.Values.ToList())
        {
            _ = Task.Run(() => EndCallAsync(call.CallId));
        }

        // Dispose audio streaming service
        //_audioStreamingService?.Dispose();
    }
}
