﻿.PetProfile-CONTAINER {
    background: #f2f6f9;
    padding: 2rem;
    min-height: 100vh;
    width: 100%;
    max-width: 72rem;
    margin: 0 auto;
    font-family: 'Inter', sans-serif;
    overflow-x: hidden;
}

.PetProfileDetails-BANNER {
    border-radius: 1.25rem;
    padding: 0.5rem;
    color: white;
    text-align: left;
    position: relative;
    margin: 0 auto;
    margin-top: 1rem;
    height: 60vh;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 94%;
    transition: background-color 0.5s ease;
}

.PetProfileDetails-BANNER-IMG {
    height: 100%;
    width: 100%;
    border-radius: 1.25rem; 
}

.PetProfileDetails-NAME-HEART-ICON {
    margin-top: 1rem; 
}

.PetProfileDetails-Name-Heading {
    font-size: 1.125rem;
    font-family: 'Acme', sans-serif;
    font-weight: bold;
    cursor: default;
}

.PetProfileDetails-HEART-ICONS {
    background-color: white;
    border-radius: 50%;
    padding: 0.5rem; 
    font-size: 1.5rem; 
    color: red;
    display: inline-block;
    line-height: 1;
    box-shadow: 0 0.25rem 0.375rem rgba(0, 0, 0, 0.1); 
}

.PetProfileDetails-DETAILS {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem; 
    margin: 1rem auto; 
}

.PetProfileDetails-DETAIL {
    background-color: #FFF3E0;
    border-radius: 0.625rem; 
    padding: 0.5rem; 
    margin: 0.25rem 0; 
    text-align: center;
    position: relative;
}

    .PetProfileDetails-DETAIL h2 {
        margin: 0;
        font-size: 1.125rem;
        
        color: #ffffff;
    }

    .PetProfileDetails-DETAIL p {
        margin: 0.3125rem 0 0;
        font-size: 0.875rem;
        font-family: 'Acme', sans-serif;
        color: #ffffff;
    }

.PetProfileDetails-DETAIL-ICON {
    position: absolute;
    top: 0.625rem; 
    right: 0.5rem; 
    font-size: 1rem; 
}

.PetProfileDetails-BUTTONS-CONTAINER {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.PetProfileDetails-BUTTONS {
    display: inline-block;
    background-color: #157BAB;
    border: none;
    border-radius: 0.625rem;
    padding: 0.625rem 1.25rem;
    color: #ffffff;
    
    font-weight: bold;
    cursor: pointer;
    width: 48%;
    margin-top: 0.625rem;
    text-decoration: none;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

    .PetProfileDetails-BUTTONS:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.5);
    }


@media (max-width: 768px) {

    .PetProfile-CONTAINER {
        padding: 1rem;
    }

    .PetProfileDetails-DETAILS {
        grid-template-columns: repeat(1, 1fr);
    }

    .PetProfileDetails-BANNER {
        border-radius: 1.25rem;
        padding: 0.5rem;
        color: white;
        text-align: left;
        position: relative;
        margin-top: 1rem;
        height: 35vh;
        display: flex;
        justify-content: center;
        align-items: center;
        transition: background-color 0.5s ease;
    }

    .PetProfileDetails-BANNER-IMG {
        height: 100%;
        width: 100%;
        border-radius: 1.25rem;
    }

    .PetProfileDetails-BUTTONS-CONTAINER {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .PetProfileDetails-BUTTONS {
        display: inline-block;
        background-color: #157BAB;
        border: none;
        border-radius: 0.625rem;
        padding: 0.625rem;
        color: #ffffff;
        
        font-size: 0.97rem;
        font-weight: bold;
        cursor: pointer;
        width: 49%;
        margin-top: 0.625rem;
        text-decoration: none;
        transition: background-color 0.3s ease, transform 0.2s ease;
    }
}
   

