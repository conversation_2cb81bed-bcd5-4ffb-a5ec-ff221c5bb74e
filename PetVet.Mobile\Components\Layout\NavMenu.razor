﻿@using CommunityToolkit.Mvvm.Messaging
@inject PetVetAuthStateProvider _PetVetAuthStateProvider
@inject NavigationManager _NavigationManager
@inject IAppState _AppState

<!-- Modern Bottom Tab Navigation -->
<nav class="fixed bottom-0 left-0 right-0 bg-white/90 backdrop-blur-md border-t border-gray-200/50 px-4 py-2 z-40">
    <div class="max-w-md mx-auto">
        <AuthorizeView>
            <Authorized>
                <!-- Pet Owner Navigation -->
                <AuthorizeView Roles="@(nameof(UserRole.PetOwner))" Context="petOwnerContext">
                    <div class="flex items-center justify-around">

                        <!-- Home Tab -->
                        <NavLink href="petOwner/home"
                        class="@($"nav-tab-item {GetActiveClass("petOwner/home")}")"
                        Match="NavLinkMatch.All">
                            <div class="nav-tab-content">
                                <div class="nav-tab-icon">
                                    <i class="fas fa-home"></i>
                                </div>
                                <span class="nav-tab-label">Home</span>
                            </div>
                        </NavLink>

                        <!-- My Pets Tab -->
                        <NavLink href="petOwner/myPets"
                        class="@($"nav-tab-item {GetActiveClass("petOwner/myPets")}")">
                            <div class="nav-tab-content">
                                <div class="nav-tab-icon">
                                    <i class="fas fa-paw"></i>
                                    @if (HasNewPetNotifications())
                                    {
                                        <span class="nav-badge"></span>
                                    }
                                </div>
                                <span class="nav-tab-label">My Pets</span>
                            </div>
                        </NavLink>

                        <!-- Vets Tab -->
                        <NavLink href="petOwner/vetList"
                        class="@($"nav-tab-item {GetActiveClass("petOwner/vetList")}")">
                            <div class="nav-tab-content">
                                <div class="nav-tab-icon">
                                    <i class="fas fa-stethoscope"></i>
                                </div>
                                <span class="nav-tab-label">Vets</span>
                            </div>
                        </NavLink>

                        <!-- Appointments Tab -->
                        <NavLink href="petOwner/appointments"
                        class="@($"nav-tab-item {GetActiveClass("petOwner/appointments")}")">
                            <div class="nav-tab-content">
                                <div class="nav-tab-icon">
                                    <i class="fas fa-calendar-check"></i>
                                    @if (HasUpcomingAppointments())
                                    {
                                        <span class="nav-badge"></span>
                                    }
                                </div>
                                <span class="nav-tab-label">Bookings</span>
                            </div>
                        </NavLink>

                        <!-- Chat Tab -->
                        <button @onclick="ShowChatThreads"
                        class="@($"nav-tab-item {GetActiveClass("chat")}")">
                            <div class="nav-tab-content">
                                <div class="nav-tab-icon">
                                    <i class="fas fa-comments"></i>
                                    @if (HasUnreadMessages())
                                    {
                                        <span class="nav-badge"></span>
                                    }
                                </div>
                                <span class="nav-tab-label">Chat</span>
                            </div>
                        </button>

                    </div>
                </AuthorizeView>

                <!-- Vet Navigation -->
                <AuthorizeView Roles="@(nameof(UserRole.Vet))" Context="vetContext">
                    <div class="flex items-center justify-around">

                        <!-- Home Tab -->
                        <NavLink href="vet/home"
                        class="@($"nav-tab-item {GetActiveClass("vet/home")}")"
                        Match="NavLinkMatch.All">
                            <div class="nav-tab-content">
                                <div class="nav-tab-icon">
                                    <i class="fas fa-home"></i>
                                </div>
                                <span class="nav-tab-label">Home</span>
                            </div>
                        </NavLink>

                        <!-- Schedule Tab -->
                        <NavLink href="vet/schedule"
                        class="@($"nav-tab-item {GetActiveClass("vet/schedule")}")">
                            <div class="nav-tab-content">
                                <div class="nav-tab-icon">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <span class="nav-tab-label">Schedule</span>
                            </div>
                        </NavLink>

                        <!-- Appointments Tab -->
                        <NavLink href="vet/appointments"
                        class="@($"nav-tab-item {GetActiveClass("vet/appointments")}")">
                            <div class="nav-tab-content">
                                <div class="nav-tab-icon">
                                    <i class="fas fa-calendar-check"></i>
                                    @if (HasUpcomingAppointments())
                                    {
                                        <span class="nav-badge"></span>
                                    }
                                </div>
                                <span class="nav-tab-label">Appointments</span>
                            </div>
                        </NavLink>

                        <!-- Calendar Tab -->
                        <NavLink href="vet/calendar"
                        class="@($"nav-tab-item {GetActiveClass("vet/calendar")}")">
                            <div class="nav-tab-content">
                                <div class="nav-tab-icon">
                                    <i class="fas fa-calendar-days"></i>
                                </div>
                                <span class="nav-tab-label">Calendar</span>
                            </div>
                        </NavLink>

                        <!-- Chat Tab -->
                        <button @onclick="ShowChatThreads"
                        class="@($"nav-tab-item {GetActiveClass("chat")}")">
                            <div class="nav-tab-content">
                                <div class="nav-tab-icon">
                                    <i class="fas fa-comments"></i>
                                    @if (HasUnreadMessages())
                                    {
                                        <span class="nav-badge"></span>
                                    }
                                </div>
                                <span class="nav-tab-label">Chat</span>
                            </div>
                        </button>

                    </div>
                </AuthorizeView>
            </Authorized>
        </AuthorizeView>
    </div>
</nav>




@code {
    private string currentPath = "";

    protected override void OnInitialized()
    {
        currentPath = _NavigationManager.Uri;
        _NavigationManager.LocationChanged += OnLocationChanged;
    }

    private void OnLocationChanged(object? sender, Microsoft.AspNetCore.Components.Routing.LocationChangedEventArgs e)
    {
        currentPath = e.Location;
        InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        _NavigationManager.LocationChanged -= OnLocationChanged;
    }

    private async Task ShowChatThreads()
    {
        _AppState.ShowLoader("Loading....");
        await Task.Delay(500); // Simulate loading delay
        WeakReferenceMessenger.Default.Send<Tuple<string>>(new Tuple<string>("StartChat"));
        _AppState.HideLoader();
    }

    private string GetActiveClass(string path)
    {
        if (path == "chat")
        {
            // Special handling for chat since it's not a direct navigation
            return "";
        }

        var currentUri = new Uri(_NavigationManager.Uri);
        var currentPathSegments = currentUri.AbsolutePath.TrimStart('/').Split('/');
        var targetPathSegments = path.Split('/');

        // Check if current path starts with the target path
        if (currentPathSegments.Length >= targetPathSegments.Length)
        {
            for (int i = 0; i < targetPathSegments.Length; i++)
            {
                if (currentPathSegments[i] != targetPathSegments[i])
                {
                    return "";
                }
            }
            return "active";
        }

        return "";
    }

    private bool HasNewPetNotifications()
    {
        // TODO: Implement logic to check for new pet notifications
        return false;
    }

    private bool HasUpcomingAppointments()
    {
        // TODO: Implement logic to check for upcoming appointments
        return true; // Temporary for demo
    }

    private bool HasUnreadMessages()
    {
        // TODO: Implement logic to check for unread messages
        return true; // Temporary for demo
    }
}