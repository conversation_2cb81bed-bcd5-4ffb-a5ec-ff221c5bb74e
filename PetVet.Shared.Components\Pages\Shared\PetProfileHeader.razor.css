﻿.PetProfileHeader-ROW1 {
    background-color: #157BAB;
    border-radius: 1rem;
    padding: 1rem 0.25rem;
    width: 98%;
    margin: 0 auto;
}

.PetProfileHeader-BACK-ICON {
    font-size: 1.5rem;
    color: #FFC107;
    cursor: pointer;
    transition: color 0.3s ease, transform 0.2s ease;
}

    .PetProfileHeader-BACK-ICON:hover {
        color: #e0a406;
        transform: scale(1.1);
    }

.PetProfileHeader-NOTIFICATION-THREE-DOTS-ICONS {
    display: flex; 
    align-items: center; 
    position: relative;
}

.PetProfileHeader-NOTIFICATION-a {
    display: flex;
    align-items: center;
    color: #333;
    text-decoration: none;
}

.PetProfileHeader-NOTIFICATION-ICON {
    font-size: 1.25rem;
    cursor: pointer;
    transition: color 0.3s ease, transform 0.3s ease;
}

    .PetProfileHeader-NOTIFICATION-ICON:hover {
        color: #FFC107;
        transform: scale(1.1);
    }

.PetProfileHeader-THREE-DOTS-ICON {
    font-size: 1.25rem;
    cursor: pointer;
    transition: color 0.3s ease, transform 0.3s ease;
}

    .PetProfileHeader-THREE-DOTS-ICON:hover {
        color: #FFC107;
        transform: scale(1.1);
    }

.dropdown-menu {
    position: absolute;
    top: 2.1875rem;
    right: -2%;
    display: block;
    width: 50%;
    background-color: #fff;
    border: 0.0625rem solid #ccc;
    border-radius: 0.3125rem;
    max-height: none;
    overflow: visible;
    cursor: pointer;
    z-index: 1000;
}

    .dropdown-menu.show {
        display: block;
    }
