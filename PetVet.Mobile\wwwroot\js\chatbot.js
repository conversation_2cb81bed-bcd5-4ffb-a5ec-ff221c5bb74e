// Chatbot JavaScript Helper Functions

window.initializeChatbot = () => {
    console.log('Chatbot initialized');
};

window.scrollToBottom = (element) => {
    if (element) {
        element.scrollTop = element.scrollHeight;
    }
};

// Auto-resize textarea
window.autoResizeTextarea = (element) => {
    if (element) {
        element.style.height = 'auto';
        element.style.height = Math.min(element.scrollHeight, 96) + 'px'; // Max height 96px (6rem)
    }
};

// Haptic feedback for mobile devices
window.triggerHapticFeedback = (type = 'light') => {
    try {
        if (navigator.vibrate) {
            switch (type) {
                case 'light':
                    navigator.vibrate(50);
                    break;
                case 'medium':
                    navigator.vibrate(100);
                    break;
                case 'heavy':
                    navigator.vibrate(200);
                    break;
                default:
                    navigator.vibrate(50);
            }
        }
    } catch (error) {
        console.log('Haptic feedback not supported');
    }
};

// Smooth scroll animation
window.smoothScrollToElement = (element, offset = 0) => {
    if (element) {
        const elementPosition = element.offsetTop - offset;
        window.scrollTo({
            top: elementPosition,
            behavior: 'smooth'
        });
    }
};

// Check if device supports touch
window.isTouchDevice = () => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
};

// Get device type
window.getDeviceType = () => {
    const width = window.innerWidth;
    if (width < 640) return 'mobile';
    if (width < 1024) return 'tablet';
    return 'desktop';
};

// Chatbot animation utilities
window.chatbotAnimations = {
    fadeIn: (element, duration = 300) => {
        if (!element) return;
        
        element.style.opacity = '0';
        element.style.display = 'block';
        
        const start = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - start;
            const progress = Math.min(elapsed / duration, 1);
            
            element.style.opacity = progress;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    },
    
    slideUp: (element, duration = 400) => {
        if (!element) return;
        
        element.style.transform = 'translateY(100%)';
        element.style.display = 'block';
        
        const start = performance.now();
        
        const animate = (currentTime) => {
            const elapsed = currentTime - start;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function (ease-out)
            const easeOut = 1 - Math.pow(1 - progress, 3);
            
            element.style.transform = `translateY(${100 - (easeOut * 100)}%)`;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        requestAnimationFrame(animate);
    },
    
    bounce: (element, intensity = 1) => {
        if (!element) return;
        
        element.style.animation = `chatbot-bounce-custom ${0.6 * intensity}s ease-out`;
        
        setTimeout(() => {
            element.style.animation = '';
        }, 600 * intensity);
    }
};

// Message formatting utilities
window.formatMessage = (message) => {
    if (!message) return '';
    
    // Convert line breaks to HTML
    message = message.replace(/\n/g, '<br>');
    
    // Convert URLs to links
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    message = message.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');
    
    // Convert email addresses to mailto links
    const emailRegex = /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/g;
    message = message.replace(emailRegex, '<a href="mailto:$1">$1</a>');
    
    return message;
};

// Local storage utilities for chatbot
window.chatbotStorage = {
    save: (key, data) => {
        try {
            localStorage.setItem(`chatbot_${key}`, JSON.stringify(data));
        } catch (error) {
            console.warn('Failed to save to localStorage:', error);
        }
    },
    
    load: (key) => {
        try {
            const data = localStorage.getItem(`chatbot_${key}`);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.warn('Failed to load from localStorage:', error);
            return null;
        }
    },
    
    remove: (key) => {
        try {
            localStorage.removeItem(`chatbot_${key}`);
        } catch (error) {
            console.warn('Failed to remove from localStorage:', error);
        }
    },
    
    clear: () => {
        try {
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith('chatbot_')) {
                    localStorage.removeItem(key);
                }
            });
        } catch (error) {
            console.warn('Failed to clear chatbot localStorage:', error);
        }
    }
};

// Performance monitoring
window.chatbotPerformance = {
    startTime: null,
    
    start: () => {
        window.chatbotPerformance.startTime = performance.now();
    },
    
    end: (label = 'Operation') => {
        if (window.chatbotPerformance.startTime) {
            const duration = performance.now() - window.chatbotPerformance.startTime;
            console.log(`${label} took ${duration.toFixed(2)}ms`);
            window.chatbotPerformance.startTime = null;
            return duration;
        }
        return 0;
    }
};

// Initialize chatbot when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    console.log('Chatbot JavaScript loaded');
    
    // Add global styles for chatbot animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes chatbot-bounce-custom {
            0%, 20%, 53%, 80%, 100% {
                transform: translate3d(0, 0, 0);
            }
            40%, 43% {
                transform: translate3d(0, -8px, 0);
            }
            70% {
                transform: translate3d(0, -4px, 0);
            }
            90% {
                transform: translate3d(0, -2px, 0);
            }
        }
    `;
    document.head.appendChild(style);
});
