<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>PetVet.Web</title>
    <base href="/" />
    <link href='https://fonts.googleapis.com/css2?family=Lora' rel="stylesheet">
    <link href='https://fonts.googleapis.com/css?family=Acme' rel='stylesheet'>
    <link href='https://fonts.googleapis.com/css?family=Afacad' rel='stylesheet'>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#2968ed',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    },
                    fontFamily: {
                        'acme': ['Acme', 'sans-serif'],
                        'lora': ['Lora', 'serif'],
                        'afacad': ['Afacad', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    <link rel="stylesheet" href="css/app.css" />
    <link rel="stylesheet" href="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <link href="PetVet.Web.styles.css" rel="stylesheet" />
</head>

<body>
    <div id="app">
        <div class="flex items-center justify-center min-h-screen">
            <div class="relative">
                <div class="w-32 h-32 border-4 border-gray-200 border-t-primary-500 rounded-full animate-spin"></div>
                <div class="absolute inset-0 flex items-center justify-center">
                    <div class="text-primary-500 font-semibold">Loading...</div>
                </div>
            </div>
        </div>
    </div>

    <div id="blazor-error-ui" class="hidden fixed bottom-0 left-0 right-0 bg-red-600 text-white px-6 py-4 shadow-lg z-50">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle mr-3"></i>
                <span>An unhandled error has occurred.</span>
            </div>
            <div class="flex space-x-4">
                <a href="" class="text-white hover:text-gray-200 underline">Reload</a>
                <button class="text-white hover:text-gray-200 text-xl" onclick="document.getElementById('blazor-error-ui').style.display='none'">&times;</button>
            </div>
        </div>
    </div>
    <script src="_framework/blazor.webassembly.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" integrity="sha512-v2CJ7UaYy4JwqLDIrZUI/4hqeoQieOmAZNXBeQyjo21dadnwR+8ZaIJVT8EE2iyI61OV8e6M8PP2/4hpQINQ/g==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
    <script src="/js/notification.js"></script>
    <script src="/js/common.js"></script>
    <script src="/js/scroll.js"></script>
</body>

</html>
