using PetVet.Mobile.Models;
using PetVet.Mobile.Services;
using Microsoft.Extensions.Logging;

namespace PetVet.Mobile.Components;

public partial class CallInvitationPopup : ContentPage
{
    private readonly CallInviteMessage _callInvite;
    private readonly IChatCallService _chatCallService;
    private readonly ILogger<CallInvitationPopup> _logger;
    private readonly TaskCompletionSource<bool> _responseTask;
    private System.Timers.Timer? _animationTimer;
    private int _animationStep = 0;

    public CallInvitationPopup(
        CallInviteMessage callInvite, 
        IChatCallService chatCallService,
        ILogger<CallInvitationPopup> logger)
    {
        InitializeComponent();
        
        _callInvite = callInvite;
        _chatCallService = chatCallService;
        _logger = logger;
        _responseTask = new TaskCompletionSource<bool>();
        
        InitializeUI();
        StartRingingAnimation();
    }

    private void InitializeUI()
    {
        try
        {
            // Set caller information
            CallerNameLabel.Text = _callInvite.FromUserName;
            CallTypeLabel.Text = _callInvite.CallType switch
            {
                CallType.Audio => "Audio Call",
                CallType.Video => "Video Call",
                CallType.AudioVideo => "Video Call",
                _ => "Call"
            };

            // Set caller image if available (placeholder for now)
            CallerImage.Source = "default_avatar.png";
            
            _logger.LogInformation("Call invitation UI initialized for {CallerName}", _callInvite.FromUserName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error initializing call invitation UI");
        }
    }

    private void StartRingingAnimation()
    {
        try
        {
            _animationTimer = new System.Timers.Timer(500); // 500ms intervals
            _animationTimer.Elapsed += OnAnimationTick;
            _animationTimer.Start();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error starting ringing animation");
        }
    }

    private void OnAnimationTick(object? sender, System.Timers.ElapsedEventArgs e)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            try
            {
                // Animate the dots to show ringing
                _animationStep = (_animationStep + 1) % 3;
                
                // Reset all dots to normal color
                Dot1.Fill = Colors.LightGray;
                Dot2.Fill = Colors.LightGray;
                Dot3.Fill = Colors.LightGray;
                
                // Highlight current dot
                switch (_animationStep)
                {
                    case 0:
                        Dot1.Fill = Color.FromArgb("#FEA195");
                        break;
                    case 1:
                        Dot2.Fill = Color.FromArgb("#FEA195");
                        break;
                    case 2:
                        Dot3.Fill = Color.FromArgb("#FEA195");
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in animation tick");
            }
        });
    }

    private async void OnAcceptClicked(object sender, EventArgs e)
    {
        try
        {
            _logger.LogInformation("User accepted call {CallId}", _callInvite.CallId);
            
            // Disable buttons to prevent double-tap
            AcceptButton.IsEnabled = false;
            DeclineButton.IsEnabled = false;
            
            // Update UI to show accepting
            AcceptButton.Text = "Accepting...";
            AcceptButton.BackgroundColor = Colors.Gray;
            
            // Accept the call
            var success = await _chatCallService.AcceptCallAsync(_callInvite);
            
            if (success)
            {
                _responseTask.SetResult(true);
                await ClosePopup();
            }
            else
            {
                await DisplayAlert("Error", "Failed to accept call", "OK");
                _responseTask.SetResult(false);
                await ClosePopup();
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error accepting call");
            await DisplayAlert("Error", $"Failed to accept call: {ex.Message}", "OK");
            _responseTask.SetResult(false);
            await ClosePopup();
        }
    }

    private async void OnDeclineClicked(object sender, EventArgs e)
    {
        try
        {
            _logger.LogInformation("User declined call {CallId}", _callInvite.CallId);
            
            // Disable buttons to prevent double-tap
            AcceptButton.IsEnabled = false;
            DeclineButton.IsEnabled = false;
            
            // Update UI to show declining
            DeclineButton.Text = "Declining...";
            DeclineButton.BackgroundColor = Colors.Gray;
            
            // Decline the call
            var success = await _chatCallService.DeclineCallAsync(_callInvite.CallId);
            
            if (success)
            {
                _responseTask.SetResult(false);
            }
            else
            {
                await DisplayAlert("Error", "Failed to decline call", "OK");
                _responseTask.SetResult(false);
            }
            
            await ClosePopup();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error declining call");
            await DisplayAlert("Error", $"Failed to decline call: {ex.Message}", "OK");
            _responseTask.SetResult(false);
            await ClosePopup();
        }
    }

    private async Task ClosePopup()
    {
        try
        {
            // Stop animation
            _animationTimer?.Stop();
            _animationTimer?.Dispose();
            
            // Close the popup
            await Navigation.PopModalAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error closing call invitation popup");
        }
    }

    public Task<bool> WaitForResponseAsync()
    {
        return _responseTask.Task;
    }

    protected override bool OnBackButtonPressed()
    {
        // Prevent back button from closing the popup
        // User must explicitly accept or decline
        return true;
    }

    protected override void OnDisappearing()
    {
        base.OnDisappearing();
        
        // Clean up animation timer
        _animationTimer?.Stop();
        _animationTimer?.Dispose();
        
        // If the task hasn't completed, set it to false (declined)
        if (!_responseTask.Task.IsCompleted)
        {
            _responseTask.SetResult(false);
        }
    }
}
