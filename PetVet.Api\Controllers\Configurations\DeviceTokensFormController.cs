﻿using Microsoft.AspNetCore.Mvc; 
using DeepMessage.ServiceContracts.Features.Configurations;
namespace DeepMessage.Server.WebApis.Controller.Configurations;
[ApiController, Route("api/[controller]/[action]")]
public class DeviceTokensFormController : ControllerBase, IDeviceTokenFormDataService
{

	private readonly IDeviceTokenFormDataService dataService;

	public DeviceTokensFormController(IDeviceTokenFormDataService dataService)
	{
		this.dataService = dataService;
	} 
	[HttpPost]
	public async Task<string> SaveAsync([FromBody] DeviceTokenFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	} 
	[HttpGet]
	public async Task<DeviceTokenFormBusinessObject?> GetItemByIdAsync(string id)
	{
		return await dataService.GetItemByIdAsync(id);
	}
}
