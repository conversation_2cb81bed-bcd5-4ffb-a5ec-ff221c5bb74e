﻿@inject IAppState AppState

@if (AppState.LoadingText != null)
{
    <!-- Modern Loading Overlay -->
    <div class="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
        <!-- Modern Loader Card -->
        <div class="bg-white rounded-3xl p-8 shadow-2xl border border-gray-100 max-w-sm mx-4">
            <div class="flex flex-col items-center text-center">
                <!-- Animated Pet Icon -->
                <div class="relative mb-6">
                    <div class="w-16 h-16 bg-gradient-to-br from-teal-400 to-blue-500 rounded-full flex items-center justify-center animate-pulse">
                        <i class="fas fa-paw text-white text-2xl"></i>
                    </div>
                    <!-- Spinning Ring -->
                    <div class="absolute inset-0 border-4 border-transparent border-t-teal-400 rounded-full animate-spin"></div>
                </div>

                <!-- Loading Text -->
                <h3 class="text-lg font-bold text-gray-800 mb-2">Please Wait</h3>
                <p class="text-gray-600 text-sm animate-pulse">@AppState.LoadingText</p>

                <!-- Progress Dots -->
                <div class="flex gap-1 mt-4">
                    <div class="w-2 h-2 bg-teal-400 rounded-full animate-bounce" style="animation-delay: 0ms;"></div>
                    <div class="w-2 h-2 bg-teal-400 rounded-full animate-bounce" style="animation-delay: 150ms;"></div>
                    <div class="w-2 h-2 bg-teal-400 rounded-full animate-bounce" style="animation-delay: 300ms;"></div>
                </div>
            </div>
        </div>
    </div>
}

@code {
    protected override void OnInitialized()
    {
        AppState.OnToggleLoader += StateHasChanged;
    }
}
