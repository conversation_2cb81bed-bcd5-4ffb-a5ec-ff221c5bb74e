﻿@page "/petOwner/home"
@inject NavigationManager _NavigationManager
@inject IAppState _AppState
@inject IUserApi _UserApi
@inject IPetApi _PetApi
@inject IAppointmentApi _AppointmentApi
@inject IRecentActivityApi _RecentActivityApi
@inject PetVetAuthStateProvider _PetVetAuthStateProvider

@using PetVet.Shared

<PageTitle>Home</PageTitle>

<!-- Modern Pet Owner Home -->
<div class="px-4 py-6 space-y-6">

    <!-- Welcome Header with User Info -->
    <div class="bg-gradient-to-r from-primary-400 via-primary-500 to-secondary-400 rounded-3xl p-6 text-white relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-4 right-4 w-32 h-32 bg-white rounded-full"></div>
            <div class="absolute bottom-4 left-4 w-20 h-20 bg-white rounded-full"></div>
            <div class="absolute top-1/2 right-1/3 w-16 h-16 bg-white rounded-full"></div>
        </div>

        <div class="relative z-10">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h1 class="text-2xl font-bold mb-1">Hello, @(currentUser?.Name ?? "User")! 👋</h1>
                    <p class="text-white/90 text-sm">How are your pets doing today?</p>
                </div>
                <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                    <i class="fas fa-paw text-white text-xl"></i>
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="flex gap-4 mt-6">
                <div class="bg-white/20 rounded-2xl p-3 flex-1 text-center">
                    <div class="text-2xl font-bold">@petCount</div>
                    <div class="text-xs text-white/80">My Pets</div>
                </div>
                <div class="bg-white/20 rounded-2xl p-3 flex-1 text-center">
                    <div class="text-2xl font-bold">@upcomingAppointments</div>
                    <div class="text-xs text-white/80">Upcoming</div>
                </div>
                <div class="bg-white/20 rounded-2xl p-3 flex-1 text-center">
                    <div class="text-2xl font-bold">@nearbyVets</div>
                    <div class="text-xs text-white/80">Vets Nearby</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Grid -->
    <div>
        <h2 class="text-xl font-bold text-gray-800 mb-4">Quick Actions</h2>
        <div class="grid grid-cols-2 gap-4">
            @foreach (var card in CardData)
            {
                <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-300 transform hover:scale-105 cursor-pointer"
                     @onclick="() => NavigateTo(card.Route)"
                     role="button"
                     tabindex="0">

                    <div class="w-12 h-12 @card.BgColor rounded-2xl flex items-center justify-center mb-3">
                        <img src="@card.Icon" alt="@card.Title" class="w-6 h-6" />
                    </div>

                    <h3 class="font-semibold text-gray-800 text-sm mb-1">@card.Title</h3>
                    <p class="text-gray-500 text-xs leading-relaxed">@card.Description</p>
                </div>
            }
        </div>
    </div>

    <!-- Recent Activity Section -->
    <div>
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-bold text-gray-800">Recent Activity</h2>
            <button class="text-primary-600 text-sm font-medium">View All</button>
        </div>

        <div class="space-y-3">
            @if (recentActivities?.Any() == true)
            {
                @foreach (var activity in recentActivities)
                {
                    <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 @activity.BackgroundColor rounded-full flex items-center justify-center">
                                <i class="@activity.Icon @activity.IconColor"></i>
                            </div>
                            <div class="flex-1">
                                <p class="font-medium text-gray-800 text-sm">@activity.Title</p>
                                <p class="text-gray-500 text-xs">@activity.Description • @activity.TimeAgo</p>
                            </div>
                        </div>
                    </div>
                }
            }
            else
            {
                <!-- Empty State -->
                <div class="bg-gray-50 rounded-2xl p-6 text-center">
                    <div class="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-3">
                        <i class="fas fa-clock text-gray-400 text-lg"></i>
                    </div>
                    <p class="text-gray-600 text-sm font-medium mb-1">No recent activity</p>
                    <p class="text-gray-500 text-xs">Your recent activities will appear here</p>
                </div>
            }
        </div>
    </div>

    <!-- Tips Section -->
    <div class="bg-gradient-to-r from-primary-100 to-secondary-100 rounded-2xl p-4">
        <div class="flex items-start gap-3">
            <div class="w-10 h-10 bg-primary-200 rounded-full flex items-center justify-center flex-shrink-0">
                <i class="fas fa-lightbulb text-primary-600"></i>
            </div>
            <div>
                <h3 class="font-semibold text-gray-800 mb-1">Pet Care Tip</h3>
                <p class="text-gray-600 text-sm leading-relaxed">
                    Regular grooming helps maintain your pet's health and strengthens your bond.
                    Brush your pet's coat daily to prevent matting and reduce shedding.
                </p>
            </div>
        </div>
    </div>

</div>

@code {
    private int currentIndex = 0;
    private string bannerImage = "images/pets/C1.jpg";
    private Timer? _bannerTimer;

    // Real user data
    private UserDto? currentUser;
    private int petCount = 0;
    private int upcomingAppointments = 0;
    private int nearbyVets = 5; // This could be fetched from API if needed
    private List<RecentActivityDto> recentActivities = new();

    private List<string> BannerImages = new List<string>
    {
        "images/pets/C1.jpg",
        "images/pets/C2.jpg",
        "images/pets/C3.jpg"
    };

    protected override async Task OnInitializedAsync()
    {
        _bannerTimer = new Timer(UpdateBanner, null, 0, 4000);
        await LoadUserDataAsync();
    }

    private async Task LoadUserDataAsync()
    {
        try
        {
            if (_PetVetAuthStateProvider.IsLoggedIn)
            {
                var userId = _PetVetAuthStateProvider.User.Id;

                // Load current user profile
                currentUser = await _UserApi.GetCurrentPetOwnerUserAsync(userId);

                // Load pet count
                var pets = await _PetApi.GetPetsByUserAsync(userId);
                petCount = pets?.Length ?? 0;

                // Load upcoming appointments count
                var appointments = await _AppointmentApi.GetPetOwnerAppointmentsAsync(userId);
                upcomingAppointments = appointments?.Count(a =>
                    a.Status == nameof(AppointmentStatus.Confirmed) &&
                    DateTime.Parse(a.AppointmentDate) >= DateTime.Today) ?? 0;

                // Load nearby vets count
                var vets = await _UserApi.GetVetUsersAsync();
                nearbyVets = vets?.Length ?? 0;

                // Load recent activities
                recentActivities = await _RecentActivityApi.GetPetOwnerRecentActivitiesAsync(userId);

                StateHasChanged();
            }
        }
        catch (Exception)
        {
            // Handle error silently, keep default values
        }
    }

    private void UpdateBanner(object? state)
    {
        currentIndex = (currentIndex + 1) % BannerImages.Count;
        bannerImage = BannerImages[currentIndex];
        InvokeAsync(StateHasChanged);
    }

    public void Dispose()
    {
        _bannerTimer?.Dispose();
    }

    private void NavigateTo(string route)
    {
        _NavigationManager.NavigateTo(route);
    }

    private List<CardModel> CardData = new()
    {
        new CardModel
        {
            Icon = "images/icons/pet-profile.png",
            Title = "Pet Profiles",
            Color = "#FEA195",
            BgColor = "bg-primary-100",
            Description = "Manage profiles & health records",
            Route = "/petOwner/myPets"
        },
        new CardModel
        {
            Icon = "images/icons/clinic.png",
            Title = "Appointments",
            Color = "#8FBFA8",
            BgColor = "bg-secondary-100",
            Description = "Schedule & manage appointments",
            Route = "/petOwner/appointments"
        },
        new CardModel
        {
            Icon = "images/icons/kitty.png",
            Title = "Breed Recognition",
            Color = "#FEA195",
            BgColor = "bg-primary-100",
            Description = "AI-powered breed identification",
            Route = "/petOwner/breedRecognition"
        },
        new CardModel
        {
            Icon = "images/icons/veterinarian.png",
            Title = "Find Vets",
            Color = "#8FBFA8",
            BgColor = "bg-secondary-100",
            Description = "Discover nearby veterinarians",
            Route = "/petOwner/vetList"
        }
    };

    private class CardModel
    {
        public string Icon { get; set; } = "";
        public string Title { get; set; } = "";
        public string Color { get; set; } = "";
        public string BgColor { get; set; } = "";
        public string Description { get; set; } = "";
        public string Route { get; set; } = "";
    }
}
