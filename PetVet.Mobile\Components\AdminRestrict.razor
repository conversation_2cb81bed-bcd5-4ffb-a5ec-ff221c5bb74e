﻿@inject NavigationManager _NavigationManager
@inject PetVetAuthStateProvider _PetVetAuthStateProvider

@code {
    protected override async Task OnInitializedAsync()
    {
        await App.Current.MainPage.DisplayAlert("Access Denied", "Admins are not allowed to use this mobile app", "Ok");
        await _PetVetAuthStateProvider.SetLogoutAsync();
        _NavigationManager.NavigateTo("auth/login");
    }
}