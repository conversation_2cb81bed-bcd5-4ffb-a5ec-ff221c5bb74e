﻿ 
using CommunityToolkit.Mvvm.Messaging; 
using Microsoft.Extensions.Logging;
using PetVet.MauiApp.Services;
using PetVet.Shared.Components.Framework;

namespace PetVet.Mobile
{
    public partial class App : Application
    {
        private readonly IServiceScopeFactory scopeFactory;
        private readonly ILogger<App> logger;
        public App(IServiceScopeFactory scopeFactory, ILogger<App> logger)
        {
            InitializeComponent();
            this.scopeFactory = scopeFactory;
            this.logger = logger;
        }

        protected override Microsoft.Maui.Controls.Window CreateWindow(IActivationState? activationState)
        {
            return new Microsoft.Maui.Controls.Window(new NavigationPage(new MainPage(scopeFactory)));
        }

        protected override void OnStart()
        {
            AppDomain.CurrentDomain.UnhandledException += (sender, args) =>
            {
                logger.LogError("Unhandled exception: {Exception}", args.ExceptionObject);
            };

            //WeakReferenceMessenger.Default.Register<string>(this, async (r, m) =>
            //{
            //    if (m == "Logout")
            //    {
            //        await Shell.Current.GoToAsync("//signin");
            //    }
            //});

            var localStorageService = scopeFactory.CreateScope().ServiceProvider.GetRequiredService<ILocalStorageService>();
            //localStorageService.GetValue("auth_token")
            //     .ContinueWith(x =>
            //     {
            //         if (!string.IsNullOrEmpty(x.Result))
            //         {
            //             Shell.Current.GoToAsync("//messages");
            //         }
            //     }, TaskScheduler.FromCurrentSynchronizationContext());

            StartSyncing();
            base.OnStart();

        }

        protected override void OnResume()
        {
            StartSyncing();
            base.OnResume();
        }

        private void StartSyncing()
        {
            try
            {
                var scope = scopeFactory.CreateScope();
                scope.ServiceProvider.GetRequiredService<ChatSyncUpService>().Start();
                scope.ServiceProvider.GetRequiredService<SignalRClientService>().Start($"{MauiProgram.ApiBaseUrl}/hubs/chathub");

                //DependencyService.Get<IForegroundServiceManager>()?.StartService();
            }
            catch (Exception ex)
            {
                logger.LogError(ex.ToString());
            }
        }

        protected override void OnSleep()
        {
            try
            {
                var scope = scopeFactory.CreateScope();
                scope.ServiceProvider.GetRequiredService<ChatSyncUpService>().Stop();
            }
            catch (Exception ex)
            {
                logger.LogError(ex.ToString());
            }
            base.OnSleep();
        }
    }
}
