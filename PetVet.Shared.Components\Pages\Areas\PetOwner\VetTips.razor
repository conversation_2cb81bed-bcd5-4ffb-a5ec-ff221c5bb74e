@page "/petOwner/vetList/vetTips"
@inject NavigationManager _NavigationManager

<PageTitle>Veterinary Tips & Articles</PageTitle>

<!-- Modern Vet Tips Page -->
<div class="px-4 py-6 space-y-6">

    <!-- Header -->
    <div class="text-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-2">Veterinary Tips & Articles</h1>
        <p class="text-gray-600">Expert advice and insights from veterinary professionals</p>
    </div>

    <!-- Search Section -->
    <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400"></i>
            </div>
            <input type="text"
                   placeholder="Search articles, topics, or veterinarians..."
                   @bind="searchQuery"
                   @oninput="FilterArticles"
                   class="w-full pl-12 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all" />
        </div>
    </div>

    <!-- Featured Articles -->
    <section>
        <div class="flex items-center gap-3 mb-4">
            <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <i class="fas fa-star text-yellow-600 text-sm"></i>
            </div>
            <h2 class="text-xl font-bold text-gray-800">Featured Articles</h2>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            @foreach (var article in _featuredArticles.Where(FilterArticle))
            {
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden cursor-pointer hover:shadow-md transition-all transform hover:scale-105"
                     @onclick="() => NavigateToArticle(article.Id)">
                    <!-- Article Image -->
                    <div class="aspect-video bg-gradient-to-r from-teal-100 to-blue-100 flex items-center justify-center">
                        <img src="@article.ImageUrl"
                             alt="@article.Title"
                             class="w-full h-full object-cover"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
                        <div class="hidden w-full h-full bg-gradient-to-r from-teal-100 to-blue-100 flex items-center justify-center">
                            <i class="fas fa-image text-teal-400 text-2xl"></i>
                        </div>
                    </div>

                    <!-- Article Content -->
                    <div class="p-4">
                        <div class="flex items-center gap-2 mb-2">
                            <div class="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-user-md text-blue-600 text-xs"></i>
                            </div>
                            <span class="text-sm text-blue-600 font-medium">Dr. @article.VetName</span>
                            <span class="text-xs text-gray-400">•</span>
                            <span class="text-xs text-gray-500">@article.PostedDate.ToString("MMM dd, yyyy")</span>
                        </div>
                        <h3 class="font-bold text-gray-800 mb-2 line-clamp-2">@article.Title</h3>
                        <p class="text-gray-600 text-sm line-clamp-2">@article.Summary</p>
                    </div>
                </div>
            }
        </div>
    </section>

    <!-- Latest Articles -->
    <section>
        <div class="flex items-center gap-3 mb-4">
            <div class="w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center">
                <i class="fas fa-newspaper text-teal-600 text-sm"></i>
            </div>
            <h2 class="text-xl font-bold text-gray-800">Latest Tips & Articles</h2>
        </div>

        <div class="space-y-4">
            @foreach (var article in _articles.Where(FilterArticle))
            {
                <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden cursor-pointer hover:shadow-md transition-all"
                     @onclick="() => NavigateToArticle(article.Id)">
                    <div class="flex">
                        <!-- Article Image -->
                        <div class="w-24 h-24 bg-gradient-to-r from-purple-100 to-pink-100 flex items-center justify-center flex-shrink-0">
                            <img src="@article.ImageUrl"
                                 alt="@article.Title"
                                 class="w-full h-full object-cover"
                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
                            <div class="hidden w-full h-full bg-gradient-to-r from-purple-100 to-pink-100 flex items-center justify-center">
                                <i class="fas fa-image text-purple-400"></i>
                            </div>
                        </div>

                        <!-- Article Content -->
                        <div class="flex-1 p-4">
                            <div class="flex items-center gap-2 mb-2">
                                <div class="w-5 h-5 bg-purple-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user-md text-purple-600 text-xs"></i>
                                </div>
                                <span class="text-sm text-purple-600 font-medium">Dr. @article.VetName</span>
                                <span class="text-xs text-gray-400">•</span>
                                <span class="text-xs text-gray-500">@article.PostedDate.ToString("MMM dd, yyyy")</span>
                            </div>

                            <h3 class="font-bold text-gray-800 mb-2 line-clamp-1">@article.Title</h3>
                            <p class="text-gray-600 text-sm line-clamp-2 mb-3">@article.Summary</p>

                            <!-- Tags -->
                            <div class="flex flex-wrap gap-1">
                                @foreach (var tag in article.Tags.Take(3))
                                {
                                    <span class="px-2 py-1 bg-gray-100 text-gray-600 rounded-lg text-xs">@tag</span>
                                }
                                @if (article.Tags.Length > 3)
                                {
                                    <span class="px-2 py-1 bg-gray-100 text-gray-600 rounded-lg text-xs">+@(article.Tags.Length - 3) more</span>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>

        @if (!_articles.Where(FilterArticle).Any() && !_featuredArticles.Where(FilterArticle).Any())
        {
            <div class="bg-white rounded-2xl p-8 text-center shadow-sm border border-gray-100">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-search text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-bold text-gray-800 mb-2">No articles found</h3>
                <p class="text-gray-500">Try adjusting your search terms or browse all articles</p>
            </div>
        }
    </section>

</div>

@code {
    private string searchQuery = "";
    private List<VetArticle> _articles = new();
    private List<VetArticle> _featuredArticles = new();

    protected override void OnInitialized()
    {
        // Dummy data for demonstration
        _featuredArticles = new List<VetArticle>
        {
            new()
            {
                Id = 1,
                Title = "Essential Vaccinations for Your Pet",
                VetName = "Sarah Johnson",
                PostedDate = DateTime.Now.AddDays(-5),
                ImageUrl = "images/articles/vaccinations.jpg",
                Summary = "Learn about the core vaccinations that every pet needs and when they should receive them.",
                Tags = new[] { "Vaccinations", "Preventive Care" }
            },
            new()
            {
                Id = 2,
                Title = "Nutrition Guidelines for Senior Dogs",
                VetName = "Michael Chen",
                PostedDate = DateTime.Now.AddDays(-7),
                ImageUrl = "images/articles/senior-dog-nutrition.jpg",
                Summary = "Proper nutrition is crucial for senior dogs. Find out what dietary changes your aging pet needs.",
                Tags = new[] { "Nutrition", "Senior Pets" }
            }
        };

        _articles = new List<VetArticle>
        {
            new()
            {
                Id = 3,
                Title = "Common Signs of Dental Disease in Cats",
                VetName = "Emily Wilson",
                PostedDate = DateTime.Now.AddDays(-10),
                ImageUrl = "images/articles/cat-dental.jpg",
                Summary = "Dental disease is common in cats. Learn to recognize the early warning signs.",
                Tags = new[] { "Dental Health", "Cats" }
            },
            new()
            {
                Id = 4,
                Title = "Understanding Pet Anxiety",
                VetName = "David Martinez",
                PostedDate = DateTime.Now.AddDays(-12),
                ImageUrl = "images/articles/pet-anxiety.jpg",
                Summary = "Discover the signs of anxiety in pets and learn effective management strategies.",
                Tags = new[] { "Behavior", "Mental Health" }
            },
            new()
            {
                Id = 5,
                Title = "First Aid Tips for Pet Owners",
                VetName = "Lisa Anderson",
                PostedDate = DateTime.Now.AddDays(-15),
                ImageUrl = "images/articles/pet-first-aid.jpg",
                Summary = "Essential first aid knowledge every pet owner should have for emergencies.",
                Tags = new[] { "Emergency Care", "First Aid" }
            }
        };
    }

    private bool FilterArticle(VetArticle article)
    {
        if (string.IsNullOrWhiteSpace(searchQuery))
            return true;

        var search = searchQuery.ToLower();
        return article.Title.ToLower().Contains(search) ||
               article.VetName.ToLower().Contains(search) ||
               article.Summary.ToLower().Contains(search) ||
               article.Tags.Any(t => t.ToLower().Contains(search));
    }

    private void FilterArticles()
    {
        StateHasChanged();
    }

    private void NavigateToArticle(int articleId)
    {
        _NavigationManager.NavigateTo($"/petOwner/vetTips/article/{articleId}");
    }

    public class VetArticle
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string VetName { get; set; } = "";
        public DateTime PostedDate { get; set; }
        public string ImageUrl { get; set; } = "";
        public string Summary { get; set; } = "";
        public string[] Tags { get; set; } = Array.Empty<string>();
        public string Content { get; set; } = "";
    }
} 