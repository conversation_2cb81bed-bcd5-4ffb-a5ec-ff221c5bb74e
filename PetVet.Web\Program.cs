using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using PetVet.Shared.AppState;
using PetVet.Shared.Components.Apis;
using PetVet.Shared.Components.Services.Auth;
using PetVet.Shared.IPlatform;
using PetVet.Shared.IStorage;
using PetVet.Web;
using PetVet.Web.Platform;
using PetVet.Web.Services.Storage;
using Refit;

var builder = WebAssemblyHostBuilder.CreateDefault(args);
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

builder.Services.AddCascadingAuthenticationState();
builder.Services.AddSingleton<PetVetAuthStateProvider>();
builder.Services.AddSingleton<AuthenticationStateProvider>(sp => sp.GetRequiredService<PetVetAuthStateProvider>());
builder.Services.AddAuthorizationCore();

builder.Services.AddSingleton<IAppState, AppState>()
    .AddSingleton<IStorageService, StorageService>()
    .AddSingleton<IPlatform, WebPlatform>();

// Add LocalStorageService for ILocalStorageService
builder.Services.AddScoped<PetVet.Shared.Components.Framework.ILocalStorageService, PetVet.Web.Services.Storage.LocalStorageService>();

ConfigureRefit(builder.Services);

await builder.Build().RunAsync();

static void ConfigureRefit(IServiceCollection services)
{
    const string ApiBaseUrl = "https://petvetapi.azurewebsites.net"; 

    services.AddRefitClient<IAuthApi>(GetReffitSettings)
        .ConfigureHttpClient(SetHttpClient);

    services.AddRefitClient<ICategoryApi>(GetReffitSettings)
        .ConfigureHttpClient(SetHttpClient);

    services.AddRefitClient<IEducationApi>(GetReffitSettings)
        .ConfigureHttpClient(SetHttpClient);

    services.AddRefitClient<IPetApi>(GetReffitSettings)
        .ConfigureHttpClient(SetHttpClient);

    services.AddRefitClient<IUserApi>(GetReffitSettings)
        .ConfigureHttpClient(SetHttpClient);

    services.AddRefitClient<IMessageApi>(GetReffitSettings)
        .ConfigureHttpClient(SetHttpClient);

    services.AddRefitClient<IAppointmentApi>(GetReffitSettings)
        .ConfigureHttpClient(SetHttpClient);

    services.AddRefitClient<IBreedApi>(GetReffitSettings)
        .ConfigureHttpClient(SetHttpClient);

    services.AddRefitClient<IRecentActivityApi>(GetReffitSettings)
        .ConfigureHttpClient(SetHttpClient);

    static void SetHttpClient(HttpClient httpClient) =>
        httpClient.BaseAddress = new Uri(ApiBaseUrl);

    static RefitSettings GetReffitSettings(IServiceProvider sp)
    {
        var authStateProvider = sp.GetRequiredService<PetVetAuthStateProvider>();

        return new RefitSettings
        {
            AuthorizationHeaderValueGetter = (_, __) => Task.FromResult(authStateProvider.User?.Token ?? "")
        };
    }
}