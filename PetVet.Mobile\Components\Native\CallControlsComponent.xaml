<?xml version="1.0" encoding="utf-8" ?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="PetVet.Mobile.Components.Native.CallControlsComponent">
    
    <ContentView.Resources>
        <ResourceDictionary>
            <!-- Call Control Button Style -->
            <Style x:Key="CallControlButtonStyle" TargetType="Button">
                <Setter Property="WidthRequest" Value="60" />
                <Setter Property="HeightRequest" Value="60" />
                <Setter Property="CornerRadius" Value="30" />
                <Setter Property="FontSize" Value="20" />
                <Setter Property="TextColor" Value="White" />
                <Setter Property="BorderWidth" Value="0" />
                <Setter Property="Shadow">
                    <Setter.Value>
                        <Shadow Brush="Black" Opacity="0.3" Radius="8" Offset="0,4" />
                    </Setter.Value>
                </Setter>
            </Style>

            <!-- Primary Call Button Style -->
            <Style x:Key="PrimaryCallButtonStyle" TargetType="Button" BasedOn="{StaticResource CallControlButtonStyle}">
                <Setter Property="BackgroundColor" Value="#FEA195" />
            </Style>

            <!-- Secondary Call Button Style -->
            <Style x:Key="SecondaryCallButtonStyle" TargetType="Button" BasedOn="{StaticResource CallControlButtonStyle}">
                <Setter Property="BackgroundColor" Value="#8FBFA8" />
            </Style>

            <!-- Danger Call Button Style -->
            <Style x:Key="DangerCallButtonStyle" TargetType="Button" BasedOn="{StaticResource CallControlButtonStyle}">
                <Setter Property="BackgroundColor" Value="#EF4444" />
            </Style>

            <!-- Disabled Call Button Style -->
            <Style x:Key="DisabledCallButtonStyle" TargetType="Button" BasedOn="{StaticResource CallControlButtonStyle}">
                <Setter Property="BackgroundColor" Value="#9CA3AF" />
                <Setter Property="IsEnabled" Value="False" />
            </Style>
        </ResourceDictionary>
    </ContentView.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!-- Call Status Display -->
        <Frame Grid.Row="0" 
               BackgroundColor="White" 
               CornerRadius="15" 
               Padding="20,15" 
               Margin="20,10"
               HasShadow="True">
            <StackLayout Orientation="Horizontal" HorizontalOptions="Center">
                <Ellipse x:Name="StatusIndicator"
                         Fill="#10B981" 
                         WidthRequest="12" 
                         HeightRequest="12" 
                         VerticalOptions="Center" />
                <Label x:Name="CallStatusLabel" 
                       Text="Connected" 
                       FontSize="16" 
                       FontAttributes="Bold" 
                       TextColor="#1F2937" 
                       VerticalOptions="Center" 
                       Margin="10,0,0,0" />
                <Label x:Name="CallDurationLabel" 
                       Text="00:00" 
                       FontSize="14" 
                       TextColor="#6B7280" 
                       VerticalOptions="Center" 
                       Margin="15,0,0,0" />
            </StackLayout>
        </Frame>

        <!-- Primary Call Controls -->
        <StackLayout Grid.Row="1" 
                     Orientation="Horizontal" 
                     HorizontalOptions="Center" 
                     Spacing="25" 
                     Margin="20,20">

            <!-- Mute/Unmute Button -->
            <Button x:Name="MuteButton"
                    Text="🎤"
                    Style="{StaticResource SecondaryCallButtonStyle}"
                    Clicked="OnMuteClicked" />

            <!-- Video On/Off Button -->
            <Button x:Name="VideoButton"
                    Text="📹"
                    Style="{StaticResource SecondaryCallButtonStyle}"
                    Clicked="OnVideoClicked" />

            <!-- End Call Button -->
            <Button x:Name="EndCallButton"
                    Text="📞"
                    Style="{StaticResource DangerCallButtonStyle}"
                    Clicked="OnEndCallClicked" />

            <!-- Speaker Button -->
            <Button x:Name="SpeakerButton"
                    Text="🔊"
                    Style="{StaticResource SecondaryCallButtonStyle}"
                    Clicked="OnSpeakerClicked" />

        </StackLayout>

        <!-- Secondary Call Controls -->
        <StackLayout Grid.Row="2" 
                     Orientation="Horizontal" 
                     HorizontalOptions="Center" 
                     Spacing="20" 
                     Margin="20,10,20,20">

            <!-- Hold Button -->
            <Button x:Name="HoldButton"
                    Text="⏸️"
                    Style="{StaticResource SecondaryCallButtonStyle}"
                    WidthRequest="50"
                    HeightRequest="50"
                    CornerRadius="25"
                    FontSize="16"
                    Clicked="OnHoldClicked" />

            <!-- Screen Share Button -->
            <Button x:Name="ScreenShareButton"
                    Text="📱"
                    Style="{StaticResource SecondaryCallButtonStyle}"
                    WidthRequest="50"
                    HeightRequest="50"
                    CornerRadius="25"
                    FontSize="16"
                    Clicked="OnScreenShareClicked" />

            <!-- Add Participant Button -->
            <Button x:Name="AddParticipantButton"
                    Text="👥"
                    Style="{StaticResource PrimaryCallButtonStyle}"
                    WidthRequest="50"
                    HeightRequest="50"
                    CornerRadius="25"
                    FontSize="16"
                    Clicked="OnAddParticipantClicked" />

            <!-- Settings Button -->
            <Button x:Name="SettingsButton"
                    Text="⚙️"
                    Style="{StaticResource SecondaryCallButtonStyle}"
                    WidthRequest="50"
                    HeightRequest="50"
                    CornerRadius="25"
                    FontSize="16"
                    Clicked="OnSettingsClicked" />

        </StackLayout>

        <!-- Call Quality Indicator (Hidden by default) -->
        <Frame x:Name="CallQualityFrame"
               Grid.Row="0"
               BackgroundColor="#FEF3C7"
               CornerRadius="10"
               Padding="10,5"
               Margin="20,10"
               HorizontalOptions="End"
               VerticalOptions="Start"
               IsVisible="False">
            <StackLayout Orientation="Horizontal" Spacing="5">
                <Label Text="📶" FontSize="12" />
                <Label x:Name="CallQualityLabel" 
                       Text="Good" 
                       FontSize="12" 
                       TextColor="#92400E" />
            </StackLayout>
        </Frame>

        <!-- Recording Indicator (Hidden by default) -->
        <Frame x:Name="RecordingFrame"
               Grid.Row="0"
               BackgroundColor="#FEE2E2"
               CornerRadius="10"
               Padding="10,5"
               Margin="20,10"
               HorizontalOptions="Start"
               VerticalOptions="Start"
               IsVisible="False">
            <StackLayout Orientation="Horizontal" Spacing="5">
                <Ellipse Fill="#EF4444" WidthRequest="8" HeightRequest="8" />
                <Label Text="Recording" FontSize="12" TextColor="#991B1B" />
            </StackLayout>
        </Frame>

    </Grid>
</ContentView>
