@page "/scanning"
@inject IJSRuntime JSRuntime

<div class="scanner-container page-transition">
    <div class="scanning-card modern-card">
        <div class="scan-title">
            Identifying Dog Breed
            <span class="loading-dots">
                <span class="loading-dot"></span>
                <span class="loading-dot"></span>
                <span class="loading-dot"></span>
            </span>
        </div>
        
        <div class="scan-image-container">
            <img src="@currentImageUrl" alt="Pet being scanned" class="scan-image" />
            <div class="scan-overlay"></div>
        </div>
        
        <div class="breed-name">@currentBreed</div>
        <div class="match-percentage">@currentPercentage% Match</div>
        <div class="breed-description">@currentDescription</div>
    </div>
</div>

@code {
    private string currentImageUrl = "https://images.unsplash.com/photo-1583337130417-3346a1be7dee?w=400&h=400&fit=crop&crop=face";
    private string currentBreed = "Shih Tzu";
    private double currentPercentage = 43.7;
    private string currentDescription = "It looks like you scanned a mixed breed between a Shih Tzu (43.7%) and Lhasa Apso (30.5%).";

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            // Simulate scanning process
            await Task.Delay(3000);
            await JSRuntime.InvokeVoidAsync("navigateToResults");
        }
    }
}

<link href="/css/modern-scanner.css" rel="stylesheet" />

<script>
    window.navigateToResults = () => {
        window.location.href = '/results';
    };
</script> 