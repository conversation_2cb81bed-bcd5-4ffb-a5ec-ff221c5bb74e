﻿using PetVet.Platform.Razor;
using PetVet.ServiceContracts.Features.Conversation;
namespace Platform.Client.Common.Features.Conversation;
public class ChatThreadSyncClientSideFormDataService : IChatThreadSyncFormDataService
{

	private readonly BaseHttpClient _httpClient;

	public ChatThreadSyncClientSideFormDataService (BaseHttpClient context)
	{
		_httpClient = context;
	}
	public async Task<string> SaveAsync(ChatThreadSyncFormBusinessObject formBusinessObject)
	{
		 return await _httpClient.PostAsJsonAsync<string>($"api/ChatThreadSyncsForm/Save", formBusinessObject);
	}
	public async Task<ChatThreadSyncFormBusinessObject> GetItemByIdAsync(string id)
	{
		return await _httpClient.GetFromJsonAsync<ChatThreadSyncFormBusinessObject>($"api/ChatThreadSyncsForm/GetItemById?id=" + id);
	}

    public async Task<ChatThreadSyncFormBusinessObject[]> GetItemsAsync()
    {
        return await _httpClient.GetFromJsonAsync<ChatThreadSyncFormBusinessObject[]>($"api/ChatThreadSyncsForm/GetItems");
    }
}
