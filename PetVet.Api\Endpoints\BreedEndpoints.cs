﻿using PetVet.Api.Services;
using PetVet.Shared.DTOs;

namespace PetVet.Api.Endpoints
{
    public static class BreedEndpoints
    {
        public static IEndpointRouteBuilder MapBreedEndpoints(this IEndpointRouteBuilder app)
        {
            var breedGroup = app.MapGroup("/api/breeds")
                .RequireAuthorization();

            // Endpoint to predict breed from image
            breedGroup.MapPost("/predict", async (HttpRequest request, BreedService breedService) =>
            {
                try
                {
                    if (!request.HasFormContentType)
                    {
                        return Results.BadRequest(PetVetApiResponse.Fail("Form content type required"));
                    }

                    var form = await request.ReadFormAsync();
                    var file = form.Files.GetFile("image");

                    if (file == null || file.Length == 0)
                    {
                        return Results.BadRequest(PetVetApiResponse.Fail("Image file is required"));
                    }

                    using var memoryStream = new MemoryStream();
                    await file.CopyToAsync(memoryStream);
                    var imageBytes = memoryStream.ToArray();

                    // First validate the image
                    var processResponse = await breedService.ProcessImageAsync(imageBytes);
                    if (!processResponse.IsSuccess)
                    {
                        return Results.BadRequest(processResponse);
                    }

                    // Then get the prediction
                    var result = await breedService.PredictBreedAsync(imageBytes);
                    return Results.Ok(result);
                }
                catch (Exception ex)
                {
                    return Results.BadRequest(PetVetApiResponse.Fail(ex.Message));
                }
            });

            return app;
        }
    }
}