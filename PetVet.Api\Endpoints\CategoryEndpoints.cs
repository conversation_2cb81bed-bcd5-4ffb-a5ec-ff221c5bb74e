﻿using PetVet.Api.Services;
using PetVet.Shared;
using PetVet.Shared.DTOs;

namespace PetVet.Api.Endpoints
{
    public static class CategoryEndpoints
    {
        public static IEndpointRouteBuilder MapCategoryEndpoints(this IEndpointRouteBuilder app)
        {
            var categoryGroup = app.MapGroup("/api/categories")
                .RequireAuthorization();

            categoryGroup.MapGet("/{id:int}", async (int id, CategoryService categoryService) =>
            Results.Ok(await categoryService.GetCategoryAsync(id)));

            categoryGroup.MapGet("", async (CategoryService categoryService) =>
                Results.Ok(await categoryService.GetCategoriesAsync()));

            categoryGroup.MapPost("", async (CategoryDto dto, CategoryService categoryService) =>
                Results.Ok(await categoryService.SaveCategoryAsync(dto)))
                .RequireAuthorization(p => p.RequireRole(nameof(UserRole.Admin)));

            categoryGroup.MapDelete("/{id:int}", async (int id, CategoryService categoryService) =>
                Results.Ok(await categoryService.DeleteCategoryAsync(id)))
                .RequireAuthorization(p => p.RequireRole(nameof(UserRole.Admin)));

            return app;
        }
    }
}
