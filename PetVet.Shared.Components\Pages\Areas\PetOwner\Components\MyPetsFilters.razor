﻿@inject ICategoryApi _CategoryApi

<!-- Modern Filter Panel -->
<div class="space-y-6">
    <!-- Pet Categories Section -->
    <div>
        <h4 class="text-sm font-bold text-gray-800 mb-4 flex items-center gap-2">
            <i class="fas fa-paw text-orange-400"></i>
            Pet Categories
        </h4>

        @if (_categories != null && _categories.Any())
        {
            <div class="space-y-3">
                @foreach (var category in _categories)
                {
                    <label class="flex items-center gap-3 p-3 rounded-2xl hover:bg-gray-50 transition-all cursor-pointer">
                        <div class="relative">
                            <input type="checkbox"
                                   id="@($"category_{category.Id}")"
                                   @onchange="@(e => CategoryCheckChanged(category.Id, (bool)e.Value))"
                                   checked="@IsCategorySelected(category.Id)"
                                   class="sr-only" />
                            <div class="w-5 h-5 rounded-lg border-2 @(IsCategorySelected(category.Id) ? "bg-teal-400 border-teal-400" : "border-gray-300") flex items-center justify-center transition-all">
                                @if (IsCategorySelected(category.Id))
                                {
                                    <i class="fas fa-check text-white text-xs"></i>
                                }
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <i class="@GetCategoryIcon(category.Name) text-gray-600 text-sm"></i>
                            <span class="text-gray-700 font-medium">@category.Name</span>
                        </div>
                    </label>
                }
            </div>
        }
        else
        {
            <div class="flex items-center gap-2 text-gray-500 p-3">
                <i class="fas fa-spinner fa-spin"></i>
                <span>Loading categories...</span>
            </div>
        }
    </div>

    <!-- Health Status Section -->
    <div>
        <h4 class="text-sm font-bold text-gray-800 mb-4 flex items-center gap-2">
            <i class="fas fa-heart text-red-400"></i>
            Health Status
        </h4>

        <div class="space-y-3">
            <!-- Vaccinated -->
            <label class="flex items-center gap-3 p-3 rounded-2xl hover:bg-gray-50 transition-all cursor-pointer">
                <div class="relative">
                    <input type="checkbox"
                           id="vaccinated"
                           @onchange="@(e => VaccinationStatusChanged("Vaccinated", (bool)e.Value))"
                           checked="@IsVaccinationStatusSelected("Vaccinated")"
                           class="sr-only" />
                    <div class="w-5 h-5 rounded-lg border-2 @(IsVaccinationStatusSelected("Vaccinated") ? "bg-green-400 border-green-400" : "border-gray-300") flex items-center justify-center transition-all">
                        @if (IsVaccinationStatusSelected("Vaccinated"))
                        {
                            <i class="fas fa-check text-white text-xs"></i>
                        }
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-green-400 rounded-full"></div>
                    <span class="text-gray-700 font-medium">Vaccinated</span>
                </div>
            </label>

            <!-- Not Vaccinated -->
            <label class="flex items-center gap-3 p-3 rounded-2xl hover:bg-gray-50 transition-all cursor-pointer">
                <div class="relative">
                    <input type="checkbox"
                           id="notVaccinated"
                           @onchange="@(e => VaccinationStatusChanged("Not Vaccinated", (bool)e.Value))"
                           checked="@IsVaccinationStatusSelected("Not Vaccinated")"
                           class="sr-only" />
                    <div class="w-5 h-5 rounded-lg border-2 @(IsVaccinationStatusSelected("Not Vaccinated") ? "bg-red-400 border-red-400" : "border-gray-300") flex items-center justify-center transition-all">
                        @if (IsVaccinationStatusSelected("Not Vaccinated"))
                        {
                            <i class="fas fa-check text-white text-xs"></i>
                        }
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <div class="w-3 h-3 bg-red-400 rounded-full"></div>
                    <span class="text-gray-700 font-medium">Not Vaccinated</span>
                </div>
            </label>
        </div>
    </div>

    <!-- Clear Filters Button -->
    <div class="pt-4 border-t border-gray-200">
        <button @onclick="ClearAllFilters"
                class="w-full py-3 px-4 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium rounded-2xl transition-all flex items-center justify-center gap-2">
            <i class="fas fa-times text-sm"></i>
            <span>Clear All Filters</span>
        </button>
    </div>
</div>

@code {
    private CategoryDto[] _categories = [];
    private List<int> _selectedCategoryIds = new();
    private List<string> _selectedVaccinationStatuses = new();

    [Parameter]
    public EventCallback<PetFilterCriteria> OnFilterChanged { get; set; }

    protected override async Task OnInitializedAsync()
    {
        await LoadCategories();
    }

    private async Task LoadCategories()
    {
        _categories = (await _CategoryApi.GetCategoriesAsync()).ToArray();
    }

    private void CategoryCheckChanged(int categoryId, bool isChecked)
    {
        if (isChecked)
        {
            if (!_selectedCategoryIds.Contains(categoryId))
            {
                _selectedCategoryIds.Add(categoryId);
            }
        }
        else
        {
            _selectedCategoryIds.Remove(categoryId);
        }

        NotifyFilterChanged();
    }

    private void VaccinationStatusChanged(string status, bool isChecked)
    {
        if (isChecked)
        {
            if (!_selectedVaccinationStatuses.Contains(status))
            {
                _selectedVaccinationStatuses.Add(status);
            }
        }
        else
        {
            _selectedVaccinationStatuses.Remove(status);
        }

        NotifyFilterChanged();
    }

    private bool IsCategorySelected(int categoryId)
    {
        return _selectedCategoryIds.Contains(categoryId);
    }

    private bool IsVaccinationStatusSelected(string status)
    {
        return _selectedVaccinationStatuses.Contains(status);
    }

    private void NotifyFilterChanged()
    {
        var criteria = new PetFilterCriteria
            {
                CategoryIds = _selectedCategoryIds,
                VaccinationStatuses = _selectedVaccinationStatuses
            };

        OnFilterChanged.InvokeAsync(criteria);
    }

    private string GetCategoryIcon(string categoryName)
    {
        return categoryName?.ToLower() switch
        {
            "cat" => "fas fa-cat",
            "dog" => "fas fa-dog",
            "bird" => "fas fa-dove",
            "fish" => "fas fa-fish",
            "rabbit" => "fas fa-rabbit",
            "hamster" => "fas fa-hamster",
            "reptile" => "fas fa-dragon",
            _ => "fas fa-paw"
        };
    }

    private void ClearAllFilters()
    {
        _selectedCategoryIds.Clear();
        _selectedVaccinationStatuses.Clear();
        NotifyFilterChanged();
        StateHasChanged();
    }

    public class PetFilterCriteria
    {
        public List<int> CategoryIds { get; set; } = new();
        public List<string> VaccinationStatuses { get; set; } = new();
    }
}
