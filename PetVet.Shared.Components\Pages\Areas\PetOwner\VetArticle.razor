@page "/petOwner/vetTips/article/{Id:int}"
@inject NavigationManager _NavigationManager

<PageTitle>@_article?.Title - Vet Article</PageTitle>

<!-- Modern Vet Article Page -->
<div class="px-4 py-6 space-y-6">

    <!-- Header with Back Button -->
    <div class="flex items-center gap-4 mb-6">
        <button @onclick="NavigateBack"
                class="w-10 h-10 bg-gray-100 hover:bg-gray-200 rounded-xl flex items-center justify-center transition-all">
            <i class="fas fa-arrow-left text-gray-600"></i>
        </button>
        <h1 class="text-xl font-bold text-gray-800">Article Details</h1>
    </div>

    @if (_article != null)
    {
        <!-- Article Header -->
        <div class="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
            <!-- Article Image -->
            <div class="aspect-video bg-gradient-to-r from-teal-100 to-blue-100 flex items-center justify-center">
                <img src="@_article.ImageUrl"
                     alt="@_article.Title"
                     class="w-full h-full object-cover"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';" />
                <div class="hidden w-full h-full bg-gradient-to-r from-teal-100 to-blue-100 flex items-center justify-center">
                    <i class="fas fa-image text-teal-400 text-4xl"></i>
                </div>
            </div>

            <!-- Article Meta -->
            <div class="p-6">
                <!-- Author Info -->
                <div class="flex items-center gap-3 mb-4">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-user-md text-white"></i>
                    </div>
                    <div>
                        <h3 class="font-bold text-gray-800">Dr. @_article.VetName</h3>
                        <p class="text-sm text-gray-500">@_article.PostedDate.ToString("MMMM dd, yyyy")</p>
                    </div>
                </div>

                <!-- Tags -->
                <div class="flex flex-wrap gap-2 mb-4">
                    @foreach (var tag in _article.Tags)
                    {
                        <span class="px-3 py-1 bg-teal-100 text-teal-700 rounded-full text-xs font-medium">@tag</span>
                    }
                </div>

                <!-- Title and Summary -->
                <h1 class="text-2xl font-bold text-gray-800 mb-3">@_article.Title</h1>
                <p class="text-gray-600 leading-relaxed">@_article.Summary</p>
            </div>
        </div>

        <!-- Article Content -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <div class="prose prose-gray max-w-none">
                @((MarkupString)_article.Content)
            </div>
        </div>

        <!-- Share Section -->
        <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
            <h3 class="text-lg font-bold text-gray-800 mb-4">Share this article</h3>
            <div class="flex gap-3">
                <button @onclick="ShareOnFacebook"
                        class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-3 px-4 rounded-xl font-medium transition-all flex items-center justify-center gap-2">
                    <i class="fab fa-facebook-f"></i>
                    <span>Facebook</span>
                </button>
                <button @onclick="ShareOnTwitter"
                        class="flex-1 bg-sky-500 hover:bg-sky-600 text-white py-3 px-4 rounded-xl font-medium transition-all flex items-center justify-center gap-2">
                    <i class="fab fa-twitter"></i>
                    <span>Twitter</span>
                </button>
                <button @onclick="ShareOnLinkedIn"
                        class="flex-1 bg-blue-700 hover:bg-blue-800 text-white py-3 px-4 rounded-xl font-medium transition-all flex items-center justify-center gap-2">
                    <i class="fab fa-linkedin-in"></i>
                    <span>LinkedIn</span>
                </button>
            </div>
        </div>
    }
    else
    {
        <!-- Loading State -->
        <div class="flex items-center justify-center py-20">
            <div class="text-center">
                <div class="w-16 h-16 border-4 border-teal-200 border-t-teal-600 rounded-full animate-spin mx-auto mb-4"></div>
                <p class="text-gray-600">Loading article...</p>
            </div>
        </div>
    }

</div>

@code {
    [Parameter] public int Id { get; set; }
    private VetArticleModel? _article;

    protected override void OnInitialized()
    {
        // Dummy data for demonstration
        var dummyContent = @"
            <h2 class=""text-xl font-bold text-gray-800 mb-4"">Introduction</h2>
            <p class=""text-gray-600 mb-6"">Pet healthcare is a crucial aspect of responsible pet ownership. Regular veterinary check-ups and preventive care can help ensure your pet lives a long, healthy life.</p>

            <h2 class=""text-xl font-bold text-gray-800 mb-4"">Key Points</h2>
            <ul class=""list-disc list-inside text-gray-600 mb-6 space-y-2"">
                <li>Schedule regular wellness exams</li>
                <li>Keep vaccinations up to date</li>
                <li>Maintain proper nutrition</li>
                <li>Exercise regularly</li>
                <li>Practice good dental hygiene</li>
            </ul>

            <h2 class=""text-xl font-bold text-gray-800 mb-4"">Preventive Care</h2>
            <p class=""text-gray-600 mb-6"">Preventive care is the foundation of pet health. Regular check-ups can help detect potential health issues before they become serious problems. Your veterinarian can provide guidance on vaccination schedules, parasite prevention, and nutrition.</p>

            <h2 class=""text-xl font-bold text-gray-800 mb-4"">Common Health Issues</h2>
            <p class=""text-gray-600 mb-4"">Understanding common health issues that affect pets can help you recognize when something might be wrong. Some common signs that warrant a veterinary visit include:</p>
            <ul class=""list-disc list-inside text-gray-600 mb-6 space-y-2"">
                <li>Changes in appetite or water consumption</li>
                <li>Changes in behavior or energy level</li>
                <li>Digestive issues</li>
                <li>Skin problems</li>
                <li>Difficulty breathing</li>
            </ul>

            <h2 class=""text-xl font-bold text-gray-800 mb-4"">Conclusion</h2>
            <p class=""text-gray-600"">By staying informed and working closely with your veterinarian, you can help ensure your pet stays healthy and happy for years to come. Remember that prevention is always better than cure when it comes to pet health.</p>";

        _article = new VetArticleModel
        {
            Id = Id,
            Title = "Essential Pet Healthcare Guidelines",
            VetName = "Sarah Johnson",
            PostedDate = DateTime.Now.AddDays(-5),
            ImageUrl = "images/articles/pet-health.jpg",
            Summary = "A comprehensive guide to maintaining your pet's health through preventive care and regular check-ups.",
            Tags = new[] { "Pet Health", "Preventive Care", "Wellness" },
            Content = dummyContent
        };
    }

    private void NavigateBack()
    {
        _NavigationManager.NavigateTo("/petOwner/vetTips");
    }

    private void ShareOnFacebook()
    {
        var url = _NavigationManager.Uri;
        var shareUrl = $"https://www.facebook.com/sharer/sharer.php?u={Uri.EscapeDataString(url)}";
        _NavigationManager.NavigateTo(shareUrl, true);
    }

    private void ShareOnTwitter()
    {
        var url = _NavigationManager.Uri;
        var text = Uri.EscapeDataString($"Check out this article: {_article?.Title}");
        var shareUrl = $"https://twitter.com/intent/tweet?text={text}&url={Uri.EscapeDataString(url)}";
        _NavigationManager.NavigateTo(shareUrl, true);
    }

    private void ShareOnLinkedIn()
    {
        var url = _NavigationManager.Uri;
        var shareUrl = $"https://www.linkedin.com/sharing/share-offsite/?url={Uri.EscapeDataString(url)}";
        _NavigationManager.NavigateTo(shareUrl, true);
    }

    public class VetArticleModel
    {
        public int Id { get; set; }
        public string Title { get; set; } = "";
        public string VetName { get; set; } = "";
        public DateTime PostedDate { get; set; }
        public string ImageUrl { get; set; } = "";
        public string Summary { get; set; } = "";
        public string[] Tags { get; set; } = Array.Empty<string>();
        public string Content { get; set; } = "";
    }
}