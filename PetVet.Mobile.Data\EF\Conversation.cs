﻿using PetVet.ServiceContracts.Enums;
using System.ComponentModel.DataAnnotations;

namespace PetVet.Client.Common.Data;

public class Conversation
{
    [Key, StringLength(450)]
    public string Id { get; set; } = null!;

    public string Title { get; set; } = null!; 

    public bool IsDeleted { get; set; } = false;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public byte SyncStatus { get; set; }

   
    public string? LastMessage { get; set; }

    public DateTime? LastMessageTime { get; set; }

    [StringLength(450)]
    public string? LastMessageSenderId { get; set; }

    // Navigation
    public ICollection<ConversationParticipant> Participants { get; set; } = new List<ConversationParticipant>();
    public ICollection<Message> Messages { get; set; } = new List<Message>();
}



public class ConversationParticipant
{
    [Key, StringLength(450)]
    public string Id { get; set; } = null!;

    [StringLength(450)]
    public string ConversationId { get; set; } = null!;

    public Conversation Conversation { get; set; } = null!;

    public int UserId { get; set; } = 0; 

    public bool IsAdmin { get; set; } = false;

    public DateTime JoinedAt { get; set; } = DateTime.UtcNow;

    public byte SyncStatus { get; set; }

    [StringLength(450)]
    public string? UserName { get; set; }

    public string? DisplayPictureUrl { get; set; }
}

/// <summary>
/// A single 'logical' message in a conversation. 
/// The actual encrypted copies are in the 'MessageRecipient' table (one per recipient).
/// </summary>
public class Message
{
    [Key, StringLength(450)] 
    public string Id { get; set; } = null!;

    [StringLength(450)]
    public string ConversationId { get; set; } = null!;
    public Conversation Conversation { get; set; } = null!;

    public int SenderId { get; set; }   

    /// <summary>
    /// (Optional) Plaintext content if you store it server-side. 
    /// If you truly want E2E encryption, you might store only ephemeral or no plaintext.
    /// For demonstration, we keep it. 
    /// </summary>
    public string? PlainContent { get; set; }
     

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    public byte SyncStatus { get; set; }

    // Soft-deletion
    public bool IsDeleted { get; set; } = false;
    public DateTime? DeletedAt { get; set; }

    // If you want to track edits:
    public bool IsEdited { get; set; } = false;
    public DateTime? EditedAt { get; set; }

    // Disappearing (ephemeral) message fields
    public bool IsEphemeral { get; set; } = false;
    public TimeSpan? DisappearAfter { get; set; }
    public DateTime? DisappearAt { get; set; }

    public DeliveryStatus DeliveryStatus { get; set; }

    public DateTime? DeliveryStatusTime { get; set; }

    // Attachments
    public ICollection<MessageAttachment> Attachments { get; set; } = new List<MessageAttachment>();

    /// <summary>
    /// Navigation to per-user encrypted copies (MessageRecipient).
    /// </summary>
    public ICollection<MessageRecipient> Recipients { get; set; } = new List<MessageRecipient>();
}

/// <summary>
/// One row per message per user. 
/// Holds the encryption payload, read status, and other user-specific states for the message.
/// </summary>
public class MessageRecipient
{
    [Key, StringLength(450)] 
    public string Id { get; set; } = null!;

    [StringLength(450)]
    public string MessageId { get; set; }  = null!;

    public Message? Message { get; set; }

    public int RecipientId { get; set; } 

    /// <summary>
    /// The encrypted message text, specifically for 'Recipient' (encrypted with their PublicKey).
    /// If there's an attachment, we might store an encrypted key, or chunk of data for that user.
    /// </summary>
    public string EncryptedContent { get; set; } = null!;

    // Is it read by the user?
    public bool IsRead { get; set; } = false;
    public DateTime? ReadAt { get; set; }

    public byte SyncStatus { get; set; }

    // If ephemeral messages vanish after the user reads them, 
    // you might store user-specific ephemeral timers here as well.
    // ...
}

/// <summary>
/// Supports pictures, documents, audio, or other file attachments. 
/// You can store the actual file data in a BLOB column or just keep a URL/path reference.
/// </summary>
public class MessageAttachment
{
    [Key, StringLength(450)]
    public string Id { get; set; } = null!;

    [StringLength(450)] 
    public string MessageId { get; set; } = null!;
    
    public Message Message { get; set; } = null!;

    // E.g. "Image", "Document", "Audio"...
    public string AttachmentType { get; set; } = string.Empty;

    // The filename or caption:
    public string FileName { get; set; } = null!;

    // For referencing the file location (could be local path, cloud storage, etc.).
    public string FileUrl { get; set; } = null!;

    // Optionally store the file size, mime type, metadata, etc.
    public long? FileSizeBytes { get; set; }
    public string? MimeType { get; set; }

    // Soft-deletion or ephemeral logic if needed
    public bool IsDeleted { get; set; } = false;
    public DateTime? DeletedAt { get; set; }

    public byte SyncStatus { get; set; }
}
