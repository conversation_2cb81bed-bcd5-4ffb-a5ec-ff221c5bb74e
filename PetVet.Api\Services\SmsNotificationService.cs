using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PetVet.Api.Data;
using PetVet.Api.Data.Entities;
using PetVet.Shared;
using PetVet.Shared.DTOs;

namespace PetVet.Api.Services
{
    /// <summary>
    /// Service for managing SMS notifications with EF Core data access and Twilio integration
    /// </summary>
    public class SmsNotificationService
    {
        private readonly IDbContextFactory<PetVetContext> _contextFactory;
        private readonly TwilioSmsService _twilioSmsService;
        private readonly ILogger<SmsNotificationService> _logger;

        public SmsNotificationService(
            IDbContextFactory<PetVetContext> contextFactory,
            TwilioSmsService twilioSmsService,
            ILogger<SmsNotificationService> logger)
        {
            _contextFactory = contextFactory;
            _twilioSmsService = twilioSmsService;
            _logger = logger;
        }

        /// <summary>
        /// Send SMS notification to veterinarian about new appointment
        /// </summary>
        public async Task<PetVetApiResponse> SendVetAppointmentNotificationAsync(int appointmentId)
        {
            using var context = _contextFactory.CreateDbContext();

            try
            {
                var appointment = await context.Appointments
                    .Include(a => a.PetOwner)
                    .Include(a => a.Vet)
                    .Include(a => a.TimeSlot)
                    .FirstOrDefaultAsync(a => a.Id == appointmentId);

                if (appointment?.Vet == null)
                {
                    _logger.LogWarning("Appointment {AppointmentId} or vet not found", appointmentId);
                    return PetVetApiResponse.Fail("Appointment or vet not found");
                }

                // Check if SMS notifications are enabled for this type
                var config = await GetNotificationConfigAsync(context, "VetNewAppointment");
                if (config == null || !config.IsEnabled || !config.EnableForVets)
                {
                    _logger.LogInformation("SMS notifications disabled for vet new appointments");
                    return PetVetApiResponse.Success();
                }

                // Check if vet has a phone number
                if (string.IsNullOrEmpty(appointment.Vet.Phone))
                {
                    _logger.LogInformation("Vet {VetId} does not have a phone number", appointment.VetId);
                    return PetVetApiResponse.Success();
                }

                // Create notification log entry
                var notificationLog = new SmsNotificationLog
                {
                    UserId = appointment.VetId,
                    PhoneNumber = appointment.Vet.Phone,
                    NotificationType = "VetNewAppointment",
                    AppointmentId = appointmentId,
                    Status = "Pending",
                    MaxRetries = config.MaxRetries,
                    CreatedAt = DateTime.UtcNow
                };

                // Generate message content
                var message = GenerateVetAppointmentMessage(appointment);
                notificationLog.MessageContent = message;

                // Send SMS via Twilio
                var result = await _twilioSmsService.SendSmsAsync(
                    appointment.Vet.Phone,
                    message,
                    "VetNewAppointment"
                );

                // Update notification log with result
                notificationLog.Status = result.IsSuccess ? "Sent" : "Failed";
                notificationLog.ErrorMessage = result.IsSuccess ? null : result.ErrorMessage;
                notificationLog.TwilioMessageSid = result.MessageSid;
                notificationLog.TwilioStatus = result.Status;
                notificationLog.Cost = result.Price;
                notificationLog.CostUnit = result.PriceUnit;

                // Save notification log
                context.SmsNotificationLogs.Add(notificationLog);
                await context.SaveChangesAsync();

                _logger.LogInformation("SMS notification {Status} for vet {VetId}, appointment {AppointmentId}", 
                    notificationLog.Status, appointment.VetId, appointmentId);

                return result.IsSuccess 
                    ? PetVetApiResponse.Success() 
                    : PetVetApiResponse.Fail(result.ErrorMessage ?? "Failed to send SMS");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending SMS notification to vet for appointment {AppointmentId}", appointmentId);
                return PetVetApiResponse.Fail($"Failed to send SMS notification: {ex.Message}");
            }
        }

        /// <summary>
        /// Send SMS confirmation to pet owner when appointment is confirmed
        /// </summary>
        public async Task<PetVetApiResponse> SendPetOwnerConfirmationAsync(int appointmentId)
        {
            using var context = _contextFactory.CreateDbContext();

            try
            {
                var appointment = await context.Appointments
                    .Include(a => a.PetOwner)
                    .Include(a => a.Vet)
                    .Include(a => a.TimeSlot)
                    .FirstOrDefaultAsync(a => a.Id == appointmentId);

                if (appointment?.PetOwner == null)
                {
                    _logger.LogWarning("Appointment {AppointmentId} or pet owner not found", appointmentId);
                    return PetVetApiResponse.Fail("Appointment or pet owner not found");
                }

                // Check if SMS notifications are enabled for this type
                var config = await GetNotificationConfigAsync(context, "AppointmentConfirmed");
                if (config == null || !config.IsEnabled || !config.EnableForPetOwners)
                {
                    _logger.LogInformation("SMS notifications disabled for pet owner confirmations");
                    return PetVetApiResponse.Success();
                }

                // Check if pet owner has a phone number
                if (string.IsNullOrEmpty(appointment.PetOwner.Phone))
                {
                    _logger.LogInformation("Pet owner {PetOwnerId} does not have a phone number", appointment.PetOwnerId);
                    return PetVetApiResponse.Success();
                }

                // Create notification log entry
                var notificationLog = new SmsNotificationLog
                {
                    UserId = appointment.PetOwnerId,
                    PhoneNumber = appointment.PetOwner.Phone,
                    NotificationType = "AppointmentConfirmed",
                    AppointmentId = appointmentId,
                    Status = "Pending",
                    MaxRetries = config.MaxRetries,
                    CreatedAt = DateTime.UtcNow
                };

                // Generate message content
                var message = GeneratePetOwnerConfirmationMessage(appointment);
                notificationLog.MessageContent = message;

                // Send SMS via Twilio
                var result = await _twilioSmsService.SendSmsAsync(
                    appointment.PetOwner.Phone,
                    message,
                    "AppointmentConfirmed"
                );

                // Update notification log with result
                notificationLog.Status = result.IsSuccess ? "Sent" : "Failed";
                notificationLog.ErrorMessage = result.IsSuccess ? null : result.ErrorMessage;
                notificationLog.TwilioMessageSid = result.MessageSid;
                notificationLog.TwilioStatus = result.Status;
                notificationLog.Cost = result.Price;
                notificationLog.CostUnit = result.PriceUnit;

                // Save notification log
                context.SmsNotificationLogs.Add(notificationLog);
                await context.SaveChangesAsync();

                _logger.LogInformation("SMS confirmation {Status} for pet owner {PetOwnerId}, appointment {AppointmentId}", 
                    notificationLog.Status, appointment.PetOwnerId, appointmentId);

                return result.IsSuccess 
                    ? PetVetApiResponse.Success() 
                    : PetVetApiResponse.Fail(result.ErrorMessage ?? "Failed to send SMS");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending SMS confirmation to pet owner for appointment {AppointmentId}", appointmentId);
                return PetVetApiResponse.Fail($"Failed to send SMS confirmation: {ex.Message}");
            }
        }

        /// <summary>
        /// Send SMS notification for appointment status updates
        /// </summary>
        public async Task<PetVetApiResponse> SendAppointmentStatusUpdateAsync(int appointmentId, string newStatus)
        {
            using var context = _contextFactory.CreateDbContext();

            try
            {
                var appointment = await context.Appointments
                    .Include(a => a.PetOwner)
                    .Include(a => a.Vet)
                    .Include(a => a.TimeSlot)
                    .FirstOrDefaultAsync(a => a.Id == appointmentId);

                if (appointment == null)
                {
                    _logger.LogWarning("Appointment {AppointmentId} not found", appointmentId);
                    return PetVetApiResponse.Fail("Appointment not found");
                }

                var results = new List<PetVetApiResponse>();

                // Send to pet owner
                if (!string.IsNullOrEmpty(appointment.PetOwner?.Phone))
                {
                    var petOwnerResult = await SendStatusUpdateToUser(
                        context, appointment, appointment.PetOwner, newStatus, false);
                    results.Add(petOwnerResult);
                }

                // Send to vet
                if (!string.IsNullOrEmpty(appointment.Vet?.Phone))
                {
                    var vetResult = await SendStatusUpdateToUser(
                        context, appointment, appointment.Vet, newStatus, true);
                    results.Add(vetResult);
                }

                await context.SaveChangesAsync();

                var successCount = results.Count(r => r.IsSuccess);
                var totalCount = results.Count;

                _logger.LogInformation("SMS status update notifications sent: {SuccessCount}/{TotalCount} for appointment {AppointmentId}", 
                    successCount, totalCount, appointmentId);

                return successCount > 0 
                    ? PetVetApiResponse.Success()
                    : PetVetApiResponse.Fail("No SMS notifications were sent successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending SMS status update notifications for appointment {AppointmentId}", appointmentId);
                return PetVetApiResponse.Fail($"Failed to send SMS status update notifications: {ex.Message}");
            }
        }

        private async Task<PetVetApiResponse> SendStatusUpdateToUser(
            PetVetContext context, 
            Appointment appointment, 
            User user, 
            string newStatus, 
            bool isForVet)
        {
            try
            {
                // Check if SMS notifications are enabled for this status
                var config = await GetNotificationConfigAsync(context, $"Appointment{newStatus}");
                if (config == null || !config.IsEnabled || 
                    (isForVet && !config.EnableForVets) || 
                    (!isForVet && !config.EnableForPetOwners))
                {
                    return PetVetApiResponse.Success();
                }

                // Create notification log entry
                var notificationLog = new SmsNotificationLog
                {
                    UserId = user.Id,
                    PhoneNumber = user.Phone!,
                    NotificationType = $"Appointment{newStatus}",
                    AppointmentId = appointment.Id,
                    Status = "Pending",
                    MaxRetries = config.MaxRetries,
                    CreatedAt = DateTime.UtcNow
                };

                // Generate message content
                var message = GenerateStatusUpdateMessage(appointment, newStatus, isForVet);
                notificationLog.MessageContent = message;

                // Send SMS via Twilio
                var result = await _twilioSmsService.SendSmsAsync(
                    user.Phone!,
                    message,
                    $"Appointment{newStatus}"
                );

                // Update notification log with result
                notificationLog.Status = result.IsSuccess ? "Sent" : "Failed";
                notificationLog.ErrorMessage = result.IsSuccess ? null : result.ErrorMessage;
                notificationLog.TwilioMessageSid = result.MessageSid;
                notificationLog.TwilioStatus = result.Status;
                notificationLog.Cost = result.Price;
                notificationLog.CostUnit = result.PriceUnit;

                // Save notification log
                context.SmsNotificationLogs.Add(notificationLog);

                return result.IsSuccess 
                    ? PetVetApiResponse.Success() 
                    : PetVetApiResponse.Fail(result.ErrorMessage ?? "Failed to send SMS");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending SMS status update to user {UserId}", user.Id);
                return PetVetApiResponse.Fail($"Failed to send SMS status update: {ex.Message}");
            }
        }

        private async Task<SmsNotificationConfig?> GetNotificationConfigAsync(PetVetContext context, string notificationType)
        {
            return await context.SmsNotificationConfigs
                .FirstOrDefaultAsync(c => c.NotificationType == notificationType);
        }

        private string GenerateVetAppointmentMessage(Appointment appointment)
        {
            return $"PetVet: New appointment with {appointment.PetOwner?.Name} on {appointment.AppointmentDate} at {appointment.TimeSlot?.Time}. " +
                   $"{(string.IsNullOrEmpty(appointment.Notes) ? "" : $"Notes: {appointment.Notes}")}";
        }

        private string GeneratePetOwnerConfirmationMessage(Appointment appointment)
        {
            return $"PetVet: Your appointment with Dr. {appointment.Vet?.Name} is confirmed for {appointment.AppointmentDate} at {appointment.TimeSlot?.Time}. " +
                   $"Clinic: {appointment.Vet?.ClinicName ?? "PetVet Clinic"}";
        }

        private string GenerateStatusUpdateMessage(Appointment appointment, string status, bool isForVet)
        {
            var baseMessage = isForVet 
                ? $"PetVet: Appointment with {appointment.PetOwner?.Name}"
                : $"PetVet: Your appointment with Dr. {appointment.Vet?.Name}";

            return status.ToLower() switch
            {
                "confirmed" => $"{baseMessage} on {appointment.AppointmentDate} at {appointment.TimeSlot?.Time} is confirmed.",
                "cancelled" => $"{baseMessage} on {appointment.AppointmentDate} at {appointment.TimeSlot?.Time} has been cancelled.",
                "completed" => $"{baseMessage} on {appointment.AppointmentDate} at {appointment.TimeSlot?.Time} is completed.",
                "rescheduled" => $"{baseMessage} has been rescheduled to {appointment.AppointmentDate} at {appointment.TimeSlot?.Time}.",
                _ => $"{baseMessage} status updated to {status}."
            };
        }

        /// <summary>
        /// Get SMS notification logs for a user
        /// </summary>
        public async Task<List<SmsNotificationLog>> GetUserNotificationLogsAsync(int userId, int take = 50)
        {
            using var context = _contextFactory.CreateDbContext();

            return await context.SmsNotificationLogs
                .Where(s => s.UserId == userId)
                .OrderByDescending(s => s.CreatedAt)
                .Take(take)
                .ToListAsync();
        }

        /// <summary>
        /// Retry failed SMS notifications
        /// </summary>
        public async Task<PetVetApiResponse> RetryFailedNotificationsAsync()
        {
            using var context = _contextFactory.CreateDbContext();

            try
            {
                var failedNotifications = await context.SmsNotificationLogs
                    .Where(s => s.Status == "Failed" && 
                               s.RetryCount < s.MaxRetries &&
                               (s.NextRetryAt == null || s.NextRetryAt <= DateTime.UtcNow))
                    .Take(10) // Limit to 10 retries per batch
                    .ToListAsync();

                var retryCount = 0;
                foreach (var notification in failedNotifications)
                {
                    var result = await _twilioSmsService.SendSmsAsync(
                        notification.PhoneNumber,
                        notification.MessageContent,
                        notification.NotificationType
                    );

                    notification.RetryCount++;
                    notification.IsRetry = true;
                    notification.Status = result.IsSuccess ? "Sent" : "Failed";
                    notification.ErrorMessage = result.IsSuccess ? null : result.ErrorMessage;
                    notification.TwilioMessageSid = result.MessageSid;
                    notification.TwilioStatus = result.Status;

                    if (!result.IsSuccess && notification.RetryCount < notification.MaxRetries)
                    {
                        // Schedule next retry
                        notification.NextRetryAt = DateTime.UtcNow.AddMinutes(5 * notification.RetryCount);
                    }

                    if (result.IsSuccess)
                    {
                        retryCount++;
                    }
                }

                await context.SaveChangesAsync();

                _logger.LogInformation("Retried {RetryCount} failed SMS notifications", retryCount);
                return PetVetApiResponse.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrying failed SMS notifications");
                return PetVetApiResponse.Fail($"Failed to retry notifications: {ex.Message}");
            }
        }
    }
}
