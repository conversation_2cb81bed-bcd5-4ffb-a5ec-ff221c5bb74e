﻿namespace PetVet.Framework.Core
{
    public class BaseFilterViewModel : ObservableBase
    {
        private string? _sortColumn;

        public string? SortColumn
        {
            get { return _sortColumn; }
            set
            {
                _sortColumn = value;
                NotifyPropertyChanged();
            }
        }

        public int CurrentIndex { get; set; } = 1;

        public int RowsPerPage { get; set; } = 10;

        public bool UsePagination { get; set; } = true;

        private string? _profileId;

        public string? ProfileId
        {
            get { return _profileId; }
            set { _profileId = value; }
        }

    }
}
