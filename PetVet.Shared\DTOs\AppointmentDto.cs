using System.ComponentModel.DataAnnotations;

namespace PetVet.Shared.DTOs
{
    public class AppointmentDto
    {
        public int Id { get; set; }

        [Required]
        public int PetOwnerId { get; set; }
        public string PetOwnerName { get; set; } = string.Empty;

        public string? PetOwnerNameDisplayPicture { get; set; }

        [Required]
        public int VetId { get; set; }
        public string VetName { get; set; } = string.Empty;

        public string? VetDisplayPicture { get; set; } = string.Empty;

        [Required]
        public int TimeSlotId { get; set; }
        public string Day { get; set; } = string.Empty;
        public string Time { get; set; } = string.Empty;

        [Required]
        public string AppointmentDate { get; set; } = string.Empty;

        [Required]
        public string Status { get; set; } = "Confirmed";

        public string? Notes { get; set; }

        public DateTime CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
    }
} 