﻿using PetVet.Server.DataServices.Data;
using PetVet.ServiceContracts.Features.Conversation;
using Microsoft.EntityFrameworkCore;
using PetVet.Api.Data;
using System.Security.Claims;
namespace PetVet.Api.Services.ChatThreads.Form;

public class StartChatServerSideFormDataService : IStartChatFormDataService
{

    private readonly PetVetContext _context;
    private readonly IHttpContextAccessor contextAccessor;

    public StartChatServerSideFormDataService(PetVetContext context, IHttpContextAccessor contextAccessor)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
    }

    public async Task<string> SaveAsync(StartChatFormBusinessObject formBusinessObject)
    {
        var userId = Convert.ToInt32(contextAccessor.HttpContext?.User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
        var participant = new int[]
        {
             userId,
            formBusinessObject.FriendId
        };

        var conversation = await _context.Conversations
                .Where(c => participant.All(p => c.Participants.Any(cp => cp.UserId == p))).FirstOrDefaultAsync();

        if (conversation == null)
        {
            conversation = new Conversation
            {
                Id = Guid.NewGuid().ToString().ToLower(),
                CreatedAt = DateTime.UtcNow,

                IsDeleted = false,
                Title = "Direct Chat"

            };
            _context.Conversations.Add(conversation);

            var participants = new List<ConversationParticipant>
                        {
                            new ConversationParticipant
                            {
                                Id = Guid.NewGuid().ToString(),
                                UserId = userId,
                                 ConversationId = conversation.Id,
                                IsAdmin = true,
                                JoinedAt = DateTime.UtcNow
                            },
                            new ConversationParticipant
                            {
                                Id = Guid.NewGuid().ToString(),
                                UserId = formBusinessObject.FriendId ,
                                ConversationId = conversation.Id,
                                IsAdmin = false,
                                JoinedAt = DateTime.UtcNow
                            }
                        };
            _context.ConversationParticipants.AddRange(participants);
            await _context.SaveChangesAsync();

        }
        return conversation.Id;
    }

    public Task<StartChatFormBusinessObject?> GetItemByIdAsync(string id)
    {
        throw new NotImplementedException();
    }
}
