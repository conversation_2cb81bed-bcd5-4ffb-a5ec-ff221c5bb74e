﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="Models\pet_breed_classifier.onnx" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Models\pet_breed_classifier.onnx" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Caching.Hybrid" Version="9.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.3" />
    <PackageReference Include="Vonage" Version="7.21.0" />
	  <PackageReference Include="FirebaseAdmin" Version="3.2.0" />
    <PackageReference Include="Microsoft.ML.OnnxRuntime" Version="1.19.2" />
    <PackageReference Include="Microsoft.ML.ImageAnalytics" Version="3.0.1" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.5" />
    <PackageReference Include="Twilio" Version="6.16.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PetVet.Shared\PetVet.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="Models\model_info.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Update="Models\pet_breed_classifier.onnx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

</Project>
