@page "/petOwner/vetList/allVets"
@inject IUserApi _UserApi
@inject IAppState _AppState
@inject NavigationManager _NavigationManager

<PageTitle>All Veterinarians</PageTitle>

@if (!isProcessing)
{
    <!-- Modern All Vets Page -->
    <div class="px-4 py-6 space-y-6">

        <!-- Header with Back Button -->
        <div class="flex items-center gap-4 mb-6">
            <button @onclick="NavigateBack" class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors">
                <i class="fas fa-arrow-left text-gray-600"></i>
            </button>
            <div class="flex-1">
                <h1 class="text-2xl font-bold text-gray-800">All Veterinarians</h1>
                <p class="text-gray-500 text-sm">@_filteredVets.Length vets available</p>
            </div>
        </div>

        <!-- Modern Search Bar -->
        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400"></i>
            </div>
            <input type="text"
                   class="w-full pl-10 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all"
                   placeholder="Search by name, specialization, or location"
                   @bind="searchQuery"
                   @oninput="FilterVets" />
        </div>

        <!-- Filter Chips -->
        <div class="flex gap-2 overflow-x-auto pb-2">
            <select @bind-value="selectedSpecialization" @bind-value:event="oninput" @onchange="FilterVets"
                    class="px-4 py-2 bg-gray-100 border border-gray-200 rounded-full text-sm font-medium text-gray-600 focus:outline-none focus:ring-2 focus:ring-teal-500">
                <option value="">All Specializations</option>
                @foreach (var spec in specializations)
                {
                    <option value="@spec">@spec</option>
                }
            </select>

            <select @bind-value="sortOption" @bind-value:event="oninput" @onchange="SortVets"
                    class="px-4 py-2 bg-gray-100 border border-gray-200 rounded-full text-sm font-medium text-gray-600 focus:outline-none focus:ring-2 focus:ring-teal-500">
                <option value="rating">Sort by Rating</option>
                <option value="experience">Sort by Experience</option>
                <option value="name">Sort by Name</option>
            </select>
        </div>

        <!-- Modern Vets Grid -->
        <div class="space-y-4">
            @foreach (var vet in _filteredVets)
            {
                <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-all cursor-pointer"
                     @onclick="() => NavigateToVetProfile(vet.Id)" role="button" tabindex="0">

                    <div class="flex items-center gap-4">
                        <!-- Vet Image -->
                        <img src="@GetVetImageSrc(vet.ImageUrl)"
                             alt="@vet.Name"
                             class="w-20 h-20 rounded-2xl object-cover border-2 border-gray-100" />

                        <!-- Vet Info -->
                        <div class="flex-1">
                            <div class="flex items-start justify-between mb-2">
                                <div>
                                    <h3 class="font-bold text-gray-800 text-lg">Dr. @vet.Name</h3>
                                    <p class="text-teal-600 text-sm font-medium">@vet.Specialization</p>
                                    @if (!string.IsNullOrEmpty(vet.ClinicName))
                                    {
                                        <p class="text-gray-500 text-sm">@vet.ClinicName</p>
                                    }
                                </div>
                                <div class="text-right">
                                    <div class="flex items-center gap-1 text-yellow-500 text-sm mb-1">
                                        @for (int i = 0; i < 5; i++)
                                        {
                                            if (i < Math.Floor(vet.Rating ?? 0))
                                            {
                                                <i class="fas fa-star"></i>
                                            }
                                            else if (i < Math.Ceiling(vet.Rating ?? 0))
                                            {
                                                <i class="fas fa-star-half-alt"></i>
                                            }
                                            else
                                            {
                                                <i class="far fa-star"></i>
                                            }
                                        }
                                    </div>
                                    <span class="text-gray-500 text-xs">@(vet.Rating?.ToString("0.0") ?? "0.0") rating</span>
                                </div>
                            </div>

                            <!-- Details -->
                            <div class="flex items-center gap-4 text-sm text-gray-500 mb-3">
                                <div class="flex items-center gap-1">
                                    <i class="fas fa-briefcase text-xs"></i>
                                    <span>@vet.YearsOfExperience years exp.</span>
                                </div>
                                <div class="flex items-center gap-1">
                                    <i class="fas fa-map-marker-alt text-xs"></i>
                                    <span>@vet.Address</span>
                                </div>
                            </div>

                            <!-- Action Buttons -->
                            <div class="flex gap-2">
                                <button class="flex-1 bg-teal-500 text-white py-2 px-4 rounded-xl text-sm font-medium hover:bg-teal-600 transition-colors">
                                    Book Appointment
                                </button>
                                <button class="bg-gray-100 text-gray-600 py-2 px-4 rounded-xl text-sm font-medium hover:bg-gray-200 transition-colors">
                                    View Profile
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
}

@code {
    private UserDto[] _allVets = [];
    private UserDto[] _filteredVets = [];
    private bool isProcessing = true;
    private string searchQuery = "";
    private string selectedSpecialization = "";
    private string sortOption = "rating";
    private List<string> specializations = new();

    protected override async Task OnInitializedAsync()
    {
        await LoadVetsAsync();
    }

    private async Task LoadVetsAsync()
    {
        try
        {
            isProcessing = true;
            _AppState.ShowLoader("Loading Veterinarians");
            _allVets = await _UserApi.GetVetUsersAsync();
            _filteredVets = _allVets;
            specializations = _allVets.Select(v => v.Specialization).Distinct().ToList();
            SortVets();
        }
        finally
        {
            _AppState.HideLoader();
            isProcessing = false;
        }
    }

    private void FilterVets()
    {
        _filteredVets = _allVets
            .Where(v =>
                (string.IsNullOrEmpty(searchQuery) ||
                v.Name.ToLower().Contains(searchQuery.ToLower()) ||
                v.Specialization.ToLower().Contains(searchQuery.ToLower()) ||
                v.Address.ToLower().Contains(searchQuery.ToLower()) ||
                (!string.IsNullOrEmpty(v.ClinicName) && v.ClinicName.ToLower().Contains(searchQuery.ToLower()))) &&
                (string.IsNullOrEmpty(selectedSpecialization) ||
                v.Specialization == selectedSpecialization))
            .ToArray();

        SortVets();
    }

    private void SortVets()
    {
        _filteredVets = sortOption switch
        {
            "rating" => _filteredVets.OrderByDescending(v => v.Rating).ToArray(),
            "experience" => _filteredVets.OrderByDescending(v => v.YearsOfExperience).ToArray(),
            "name" => _filteredVets.OrderBy(v => v.Name).ToArray(),
            _ => _filteredVets
        };
    }

    private void NavigateToVetProfile(int vetId)
    {
        _NavigationManager.NavigateTo($"/petOwner/vetList/vet/{vetId}");
    }

    private void NavigateBack()
    {
        _NavigationManager.NavigateTo("/petOwner/vetList");
    }

    private string GetVetImageSrc(string? imageUrl)
    {
        if (string.IsNullOrEmpty(imageUrl))
            return "images/dummy-profile.png";

        // If it's already a data URL, return as is
        if (imageUrl.StartsWith("data:"))
            return imageUrl;

        // If it's a regular URL (http/https), return as is
        if (imageUrl.StartsWith("http"))
            return imageUrl;

        // Otherwise, treat it as base64 and create data URL
        return $"data:image/jpeg;base64,{imageUrl}";
    }
}