﻿using CommunityToolkit.Mvvm.Messaging;
using PetVet.Client.Common.Data;
using PetVet.ServiceContracts.Enums;
using PetVet.ServiceContracts.Features.Conversation;
using Microsoft.AspNetCore.SignalR.Client;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using PetVet.Shared.Components.Framework;
using Platform.Client.Common.Features.Conversation;
using System.Text.Json;
using System.Threading.Channels;
using Message = PetVet.Client.Common.Data.Message;
using Microsoft.AspNetCore.Http.Connections;
using PetVet.Shared.IHub;
using Android.Content;

namespace PetVet.MauiApp.Services;
public class SignalRClientService : IAsyncDisposable
{
    public HubConnection? _hubConnection;
    private CancellationTokenSource _cts = new CancellationTokenSource();

    // These dependencies should be resolved via DI.
    private IServiceScopeFactory? _scopeFactory;
    private ILogger<SignalRClientService> _logger = null!;
    Task? _monitoringTask;
    Task? _monitoringProducerTask;
    public  bool  IsConnected { get { return _hubConnection.State == HubConnectionState.Connected; } }
    string _chatHubUrl = string.Empty;

    public SignalRClientService(ILogger<SignalRClientService> logger, IServiceScopeFactory scopeFactory)
    {
        _logger = logger;
        _scopeFactory = scopeFactory;
    }

    public void Start(string chatHubUrl)
    {
        _chatHubUrl = chatHubUrl;

        //Task.Factory.StartNew(StartDownSyncTask, TaskCreationOptions.RunContinuationsAsynchronously);

        if (_monitoringTask == null || _monitoringTask.Status != TaskStatus.Running)
        {
            _monitoringTask = Task.Factory.StartNew(async () =>
            {
                while (!_cts.IsCancellationRequested)
                {
                    var hint = await _monitoringChannel.Reader.ReadAsync();
                    await StartSignalRConnection(_cts.Token);
                    await Task.Delay(5000);
                }
            }, TaskCreationOptions.RunContinuationsAsynchronously);
        }
        if (_monitoringProducerTask == null || _monitoringProducerTask.Status != TaskStatus.Running)
        {
            _monitoringProducerTask = Task.Factory.StartNew(() =>
            {
                while (!_cts.IsCancellationRequested)
                {
                    try
                    {
                        _monitoringChannel.Writer.TryWrite(string.Empty);
                        _logger.LogDebug("Monitoring every few seconds if task is running...");
                        Thread.Sleep(15000);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError("Error in monitoring task: {Error}", ex.Message);
                    }
                }
            });
        }
        //_downSyncChannel.Writer.WriteAsync(new ChatSyncItem() { Id = string.Empty, SyncType = 1 }, _cts.Token);

    }


    Channel<string> _monitoringChannel = Channel.CreateBounded<string>(10);
    //Channel<ChatSyncItem> _downSyncChannel = Channel.CreateBounded<ChatSyncItem>(100);

    private bool IsAlive(object? obj)
    {
        try
        {
            if (obj != null && obj.ToString() != null)
            {
                _logger.LogDebug("IsAlive: true");
                return true;
            }
            _logger.LogDebug("IsAlive: false");
            return false;
        }
        catch (Exception ex)
        {

            _logger.LogError("IsAlive error: {Error}", ex.Message);
            return false;
        }
    }

    private async Task StartSignalRConnection(CancellationToken token)
    {
        try
        {
            _logger.LogDebug("SignalR Starting connection...");
            using var scope = _scopeFactory!.CreateScope();
            var localStorageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();

            var httpHandler = new HttpClientHandler
            {
                // WARNING: Do not use this in production.
                ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
            };

            if (_hubConnection != null && IsAlive(_hubConnection) && _hubConnection.State == HubConnectionState.Disconnected)
            {
                _logger.LogInformation("Stopping existing SignalR connection...");
                try
                {
                    await _hubConnection.StopAsync();
                    await _hubConnection.DisposeAsync();
                    _hubConnection = null;
                }
                catch { }

            }


            if (_hubConnection == null || !IsAlive(_hubConnection))
            {
                _logger.LogDebug("SignalR Building connection...");
                _hubConnection = new HubConnectionBuilder()
                     .ConfigureLogging(logging => logging.AddDebug().SetMinimumLevel(LogLevel.Debug))
                    .WithUrl(_chatHubUrl, options =>
                    {
                        options.HttpMessageHandlerFactory = _ => httpHandler;
                        options.AccessTokenProvider = async () => await localStorageService.GetValue("auth_token");
                    })
                    .WithAutomaticReconnect(new[]
                    {
                    TimeSpan.Zero,
                    TimeSpan.FromSeconds(2),
                    TimeSpan.FromSeconds(10),
                    TimeSpan.FromSeconds(30)
                    })
                    .Build();

                _hubConnection.On<int, string>(nameof(IChatHubClientProxy.OnNewMessage), ProcessIncomingMessage);

                _hubConnection.On(nameof(IChatHubClientProxy.Logout), async () =>
                {
                    _logger.LogInformation("SignalR Logout event received.");
                    var scope = _scopeFactory.CreateScope();
                    var storageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
                    await storageService.RemoveValue("auth_token");

                    WeakReferenceMessenger.Default.Send("Logout");
                });

                _hubConnection.On<int, string>(nameof(IChatHubClientProxy.OnCallAction), ProcessCallAction);

                _hubConnection.On<int, string>(nameof(IChatHubClientProxy.OnMessageDeliveryUpdate), UpdateMessage);

                _hubConnection.Reconnecting += error =>
                {
                    _logger?.LogWarning("SignalR reconnecting: {Error}", error?.Message);
                    return Task.CompletedTask;
                };

                _hubConnection.Reconnected += connectionId =>
                {
                    _logger?.LogInformation("SignalR reconnected. ConnectionId: {ConnectionId}", connectionId);
                    return Task.CompletedTask;
                };

                _hubConnection.Closed += async error =>
                {
                    _logger?.LogError("SignalR closed: {Error}", error?.Message);
                    await Task.Delay(TimeSpan.FromSeconds(5), token);
                    await _monitoringChannel.Writer.WriteAsync(string.Empty, token);
                };

                await _hubConnection.StartAsync(token);
                await Task.Delay(500);
                _logger.LogInformation("SignalR connection started...{0}", _hubConnection.State);

            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex.ToString());
            await _monitoringChannel.Writer.WriteAsync(string.Empty);
        }
    }

    // Process incoming SignalR messages.
    private async Task ProcessIncomingMessage(int userId, string messageJson)
    {
        try
        {
            _logger.LogInformation("SignalR Received broadcast: {0}", messageJson);
            var formBusinessObject = JsonSerializer.Deserialize<ChatMessagesSyncFormBusinessObject>(messageJson);
            if (userId > 0 && formBusinessObject != null)
            {
                // Create a new scope for the DbContext to keep it short-lived.
                using var scope = _scopeFactory!.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                var localStorageService = scope.ServiceProvider.GetRequiredService<ILocalStorageService>();
                var conversation = await dbContext.Conversations
                    .FirstOrDefaultAsync(x => x.Id == formBusinessObject.ConversationId);
                if (conversation == null)
                {
                    conversation = new Conversation
                    {
                        Id = formBusinessObject.ConversationId,
                        CreatedAt = formBusinessObject.CreatedAt,
                        IsDeleted = formBusinessObject.IsDeleted,
                        Title = formBusinessObject.SenderUserName ?? string.Empty
                    };
                    dbContext.Conversations.Add(conversation);


                    var participants = new List<ConversationParticipant>
                        {
                            new() {
                                Id = Guid.NewGuid().ToString(),
                                UserId = userId!,
                                ConversationId = conversation.Id,
                                IsAdmin = true,
                                JoinedAt = DateTime.UtcNow
                            },
                            new() {
                                Id = Guid.NewGuid().ToString(),
                                UserId = formBusinessObject.SenderId,
                                UserName = formBusinessObject.SenderUserName,
                                DisplayPictureUrl = formBusinessObject.SenderDisplayPictureUrl,
                                ConversationId = conversation.Id,
                                IsAdmin = false,
                                JoinedAt = DateTime.UtcNow
                            }
                        };
                    dbContext.ConversationParticipants.AddRange(participants);

                    await dbContext.SaveChangesAsync();
                }

                var message = await dbContext.Messages.FirstOrDefaultAsync(x => x.Id == formBusinessObject.Id);
                if (message == null)
                {
                    message = new Message();
                    message.Id = formBusinessObject.Id;
                    message.ConversationId = formBusinessObject.ConversationId;
                    message.CreatedAt = formBusinessObject.CreatedAt;
                    message.SenderId = formBusinessObject.SenderId;
                    dbContext.Messages.Add(message);
                }

                message.DeletedAt = formBusinessObject.DeletedAt;
                message.DisappearAfter = formBusinessObject.DisappearAfter;
                message.DisappearAt = formBusinessObject.DisappearAt;
                message.IsDeleted = formBusinessObject.IsDeleted;
                message.IsEdited = formBusinessObject.IsEdited;
                message.IsEphemeral = formBusinessObject.IsEphemeral;
                message.EditedAt = formBusinessObject.EditedAt;
                message.PlainContent = formBusinessObject.PlainContent;
                var effectedRows = await dbContext.SaveChangesAsync();

                if (_hubConnection != null && _hubConnection.State == HubConnectionState.Connected)
                    await _hubConnection.SendAsync(nameof(IChatHubServerProxy.AcknowledgeMessage), message.Id, DeliveryStatus.DeliveredToEndUser, DateTime.UtcNow);

                _logger.LogDebug("Added message: {0}, {1}", message.Id, message.PlainContent);
                WeakReferenceMessenger.Default.Send(new ChatSyncItem() { SyncType = 1 });

            }
        }
        catch (Exception ex)
        {
            _logger?.LogError("Error processing broadcast: {Error}", ex.Message);
        }
    }

    public async Task AcknowledgeMessageRead(string messageId, DateTime timeStamp)
    {
        if (_hubConnection != null && _hubConnection.State == HubConnectionState.Connected)
        {
            _logger.LogDebug("Acknowledging message update: {MessageId}", messageId);
            await _hubConnection.SendAsync("AcknowledgeMessage", messageId, DeliveryStatus.DeliveredToEndUser, timeStamp);

            using var scope = _scopeFactory.CreateScope();
            var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            dbContext.Messages.Where(x => x.Id == messageId)
                .ExecuteUpdate(m => m.SetProperty(x => x.DeliveryStatus, DeliveryStatus.DeliveredToEndUser)
                                     .SetProperty(x => x.DeliveryStatusTime, timeStamp));
        }
    }

    public async Task SendMessageAsync(string messageJson)
    {
        if (_hubConnection != null && _hubConnection.State == HubConnectionState.Connected)
        {
            await _hubConnection.SendAsync(nameof(IChatHubServerProxy.NewClientMessage), messageJson);
        }
    }

    public event Action<string>? OnMessageReceived;
    public event Action<int, string>? CallActionReceived;

    public   Task ProcessCallAction(int userId, string messageJson)
    {
        try
        {
            _logger.LogInformation("Call action received from user {UserId}: {Message}", userId, messageJson);
            CallActionReceived?.Invoke(userId, messageJson);
        }
        catch (Exception ex)
        {
            _logger?.LogError("Error processing call action: {Error}", ex.Message);
        }
        return Task.CompletedTask;
    }

    public async Task UpdateMessage(int userId, string messageJson)
    {
        try
        {
            var formBusinessObject = JsonSerializer.Deserialize<ChatMessageUpdate>(messageJson);
            if (userId > 0 && formBusinessObject != null)
            {
                using var scope = _scopeFactory.CreateScope();
                var dbContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
                var message = await dbContext.Messages.FirstOrDefaultAsync(x => x.Id == formBusinessObject.Id);
                if (message == null)
                {
                    throw new InvalidOperationException();
                }
                message.DeliveryStatus = formBusinessObject.DeliveryStatus;
                message.DeliveryStatusTime = formBusinessObject.DeliveryStatusTime;
                await dbContext.SaveChangesAsync();

                WeakReferenceMessenger.Default.Send(new ChatMessageStatus()
                {
                    Id = message.Id,
                    DeliveryStatus = message.DeliveryStatus,
                    Timestamp = message.DeliveryStatusTime
                });
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError("Error updating message: {Error}", ex.Message);
        }
    }

    public async ValueTask DisposeAsync()
    {
        _cts.Cancel();
        if (_hubConnection != null)
        {
            await _hubConnection.StopAsync();
            await _hubConnection.DisposeAsync();
            _hubConnection = null;
        }
    }
}




