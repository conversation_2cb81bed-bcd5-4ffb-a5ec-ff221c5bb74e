@page "/chatbot-settings"
@using PetVet.Shared.Components.Apis
@using PetVet.Shared.Models.AzureOpenAI
@inject IChatApi Cha<PERSON><PERSON>
@inject IJSRuntime JSRuntime

<PageTitle>Chatbot Settings</PageTitle>

<!-- Chatbot Settings Page -->
<div class="min-h-screen bg-gradient-to-br from-primary-50 to-secondary-50 p-4">
    <div class="max-w-md mx-auto">
        
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="w-16 h-16 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-full flex items-center justify-content-center mx-auto mb-4">
                <svg width="32" height="32" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="text-white">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                </svg>
            </div>
            <h1 class="text-2xl font-bold text-gray-800 mb-2">Chatbot Settings</h1>
            <p class="text-gray-600">Configure Azure OpenAI integration</p>
        </div>

        <!-- Settings Cards -->
        <div class="space-y-6">
            
            <!-- Service Status Card -->
            <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="text-green-500">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Service Status
                </h3>
                
                @if (isLoading)
                {
                    <div class="flex items-center gap-3">
                        <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-primary-500"></div>
                        <span class="text-gray-600">Checking status...</span>
                    </div>
                }
                else if (healthStatus != null)
                {
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">Status</span>
                            <span class="@(healthStatus.Healthy ? "text-green-600" : "text-red-600") font-medium">
                                @(healthStatus.Healthy ? "✓ Connected" : "✗ Disconnected")
                            </span>
                        </div>
                        
                        @if (healthStatus.Stats != null)
                        {
                            <div class="grid grid-cols-2 gap-4 pt-3 border-t border-gray-100">
                                <div class="text-center">
                                    <div class="text-xl font-bold text-primary-500">@healthStatus.Stats.TotalConversations</div>
                                    <div class="text-xs text-gray-600">Conversations</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-xl font-bold text-secondary-500">@healthStatus.Stats.TotalMessages</div>
                                    <div class="text-xs text-gray-600">Messages</div>
                                </div>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <div class="text-red-600">Failed to check status</div>
                }
                
                <button @onclick="CheckHealth" 
                        class="w-full mt-4 p-2 bg-primary-50 text-primary-700 rounded-lg hover:bg-primary-100 transition-colors"
                        disabled="@isLoading">
                    @(isLoading ? "Checking..." : "Refresh Status")
                </button>
            </div>

            <!-- Configuration Info Card -->
            <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="text-blue-500">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    Configuration
                </h3>
                
                <div class="space-y-3 text-sm">
                    <div class="bg-blue-50 rounded-lg p-4">
                        <h4 class="font-medium text-blue-800 mb-2">Azure OpenAI Setup</h4>
                        <p class="text-blue-700 leading-relaxed">
                            To enable Azure OpenAI integration, update the configuration in 
                            <code class="bg-blue-100 px-1 rounded">appsettings.json</code>:
                        </p>
                        <div class="mt-3 bg-blue-100 rounded p-3 font-mono text-xs overflow-x-auto">
{<br/>
&nbsp;&nbsp;"AzureOpenAI": {<br/>
&nbsp;&nbsp;&nbsp;&nbsp;"Endpoint": "https://your-resource.openai.azure.com/",<br/>
&nbsp;&nbsp;&nbsp;&nbsp;"ApiKey": "your-api-key",<br/>
&nbsp;&nbsp;&nbsp;&nbsp;"DeploymentName": "gpt-35-turbo"<br/>
&nbsp;&nbsp;}<br/>
}
                        </div>
                    </div>
                    
                    <div class="bg-yellow-50 rounded-lg p-4">
                        <h4 class="font-medium text-yellow-800 mb-2">Current Mode</h4>
                        <p class="text-yellow-700">
                            The chatbot is currently running in <strong>dummy mode</strong> with simulated responses. 
                            Configure Azure OpenAI credentials to enable AI-powered responses.
                        </p>
                    </div>
                </div>
            </div>

            <!-- Test Chat Card -->
            <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                    <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" class="text-purple-500">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.959 8.959 0 01-4.906-1.456L3 21l2.456-5.094A8.959 8.959 0 013 12c0-4.418 3.582-8 8-8s8 3.582 8 8z"/>
                    </svg>
                    Test Chat
                </h3>
                
                <div class="space-y-3">
                    <textarea @bind="testMessage" 
                             placeholder="Enter a test message..."
                             class="w-full p-3 border border-gray-200 rounded-lg resize-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                             rows="3"></textarea>
                    
                    <button @onclick="SendTestMessage" 
                            class="w-full p-3 bg-gradient-to-r from-primary-500 to-secondary-500 text-white rounded-lg font-medium hover:shadow-lg transition-all"
                            disabled="@(string.IsNullOrWhiteSpace(testMessage) || isSendingTest)">
                        @(isSendingTest ? "Sending..." : "Send Test Message")
                    </button>
                    
                    @if (!string.IsNullOrEmpty(testResponse))
                    {
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h4 class="font-medium text-gray-800 mb-2">Response:</h4>
                            <p class="text-gray-700 text-sm leading-relaxed">@testResponse</p>
                            @if (!string.IsNullOrEmpty(testModel))
                            {
                                <div class="mt-2 text-xs text-gray-500">
                                    Model: @testModel
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>

            <!-- Actions Card -->
            <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">Actions</h3>
                
                <div class="grid grid-cols-2 gap-3">
                    <button @onclick="OpenChatbot" 
                            class="p-3 bg-green-50 rounded-lg text-center hover:bg-green-100 transition-colors">
                        <div class="text-green-500 text-xl mb-1">💬</div>
                        <span class="text-sm font-medium text-green-700">Open Chat</span>
                    </button>
                    
                    <button @onclick="ClearConversations" 
                            class="p-3 bg-red-50 rounded-lg text-center hover:bg-red-100 transition-colors"
                            disabled="@isClearingConversations">
                        <div class="text-red-500 text-xl mb-1">🗑️</div>
                        <span class="text-sm font-medium text-red-700">
                            @(isClearingConversations ? "Clearing..." : "Clear History")
                        </span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private ChatHealthResponse? healthStatus;
    private bool isLoading = false;
    private string testMessage = "";
    private string testResponse = "";
    private string testModel = "";
    private bool isSendingTest = false;
    private bool isClearingConversations = false;

    protected override async Task OnInitializedAsync()
    {
        await CheckHealth();
    }

    private async Task CheckHealth()
    {
        isLoading = true;
        try
        {
            healthStatus = await ChatApi.GetHealthAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Health check failed: {ex.Message}");
            healthStatus = null;
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task SendTestMessage()
    {
        if (string.IsNullOrWhiteSpace(testMessage))
            return;

        isSendingTest = true;
        testResponse = "";
        testModel = "";

        try
        {
            var request = new SendMessageRequest
            {
                Message = testMessage,
                UserId = "test-user"
            };

            var response = await ChatApi.SendMessageAsync(request);
            
            if (response.Success)
            {
                testResponse = response.Response ?? "No response received";
                testModel = response.Model ?? "Unknown";
            }
            else
            {
                testResponse = $"Error: {response.Message}";
            }
        }
        catch (Exception ex)
        {
            testResponse = $"Exception: {ex.Message}";
        }
        finally
        {
            isSendingTest = false;
            StateHasChanged();
        }
    }

    private async Task OpenChatbot()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("eval", "window.parent.blazorCulture.invokeMethodAsync('OpenChatbot')");
        }
        catch
        {
            Console.WriteLine("Could not open chatbot");
        }
    }

    private async Task ClearConversations()
    {
        isClearingConversations = true;
        try
        {
            await ChatApi.ClearUserConversationsAsync("test-user");
            await CheckHealth(); // Refresh stats
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to clear conversations: {ex.Message}");
        }
        finally
        {
            isClearingConversations = false;
            StateHasChanged();
        }
    }
}
