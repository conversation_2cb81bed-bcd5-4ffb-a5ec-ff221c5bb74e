﻿@page "/petOwner/chat"
@page "/vet/chat"
@inject IUserApi _UserApi
@inject IJSRuntime _JS
@inject IAppState _AppState
@inject PetVetAuthStateProvider _PetVetAuthStateProvider
@inject NavigationManager _NavigationManager
@inject IPlatform Platform
@implements IAsyncDisposable

<PageTitle>Chat</PageTitle>

<!-- Modern Chat Interface -->
<div class="px-4 py-6 space-y-6 min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="text-center mb-6">
        <div class="flex items-center justify-center gap-3 mb-2">
            <div class="w-12 h-12 bg-gradient-to-br from-teal-500 to-blue-600 rounded-2xl flex items-center justify-center">
                <i class="fas fa-comments text-white text-xl"></i>
            </div>
            <h1 class="text-2xl font-bold text-gray-800">PetVet Chat</h1>
        </div>
        <p class="text-gray-600">Connect with veterinarians and pet owners</p>
    </div>

    <!-- Chat Container -->
    <div class="bg-white rounded-3xl shadow-lg border border-gray-100 overflow-hidden h-[calc(100vh-200px)]">
        @if (Platform.IsWeb)
        {
            <!-- Desktop Layout -->
            <div class="flex h-full">
                <!-- Chat List Sidebar -->
                <div class="w-1/3 border-r border-gray-200 bg-gray-50">
                    <ChatList Chats="Chats" OnUserSelected="OnUserSelected" Loading="_loadingChats" />
                </div>

                <!-- Chat Details Main Area -->
                <div class="flex-1 flex flex-col">
                    <ChatDetails SelectedUser="_selectedUser"
                                 OnCancel="HandleChatDetailsCancel"
                                 NewIncomingMessage="@_newIncomingMessage"
                                 OnIncomingMessageRecieved="() => _newIncomingMessage = null"
                                 OnMessageSent="HandleMessageSent" />
                </div>
            </div>
        }
        else
        {
            <!-- Mobile Layout -->
            <div class="h-full">
                @if (_selectedUser == null)
                {
                    <!-- Show Chat List -->
                    <div class="h-full">
                        <ChatList Chats="Chats" OnUserSelected="OnUserSelected" Loading="_loadingChats" />
                    </div>
                }
                else
                {
                    <!-- Show Chat Details -->
                    <div class="h-full flex flex-col">
                        <ChatDetails SelectedUser="_selectedUser"
                                     OnCancel="HandleChatDetailsCancel"
                                     NewIncomingMessage="@_newIncomingMessage"
                                     OnIncomingMessageRecieved="() => _newIncomingMessage = null"
                                     OnMessageSent="HandleMessageSent" />
                    </div>
                }
            </div>
        }
    </div>
</div>

@code {
    private HubConnection? _hubConnection;
    private CancellationTokenSource? _connectionCts;
    public ICollection<UserDto> Users { get; set; } = new HashSet<UserDto>();
    public IList<UserDto> Chats { get; set; } = new List<UserDto>();
    private UserDto? _selectedUser = null;
    private MessageDto? _newIncomingMessage = null;
    private bool _loadingChats = false;
    private int _initialUserId = 0; // To store the userId from the query parameter

    protected override async Task OnInitializedAsync()
    {
        // Load chat users from database
        await LoadChatUsersAsync();

        // Check if there's a userId in the query string
        var uri = _NavigationManager.ToAbsoluteUri(_NavigationManager.Uri);
        var queryString = uri.Query;
        if (!string.IsNullOrEmpty(queryString))
        {
            // Remove the '?' from the start of the query string
            queryString = queryString.TrimStart('?');

            // Split the query string into key-value pairs
            var queryParams = queryString.Split('&');
            foreach (var param in queryParams)
            {
                var parts = param.Split('=');
                if (parts.Length == 2 && parts[0] == "userId")
                {
                    if (int.TryParse(parts[1], out var userId))
                    {
                        _initialUserId = userId;
                        break;
                    }
                }
            }
        }

        _connectionCts = new CancellationTokenSource();
        try
        {
            _hubConnection = ConfigureHubConnection();
            await _hubConnection.StartAsync(_connectionCts.Token);
            await _hubConnection.SendAsync(nameof(IPetVetChatHubServer.ConnectUser), _PetVetAuthStateProvider.User, _connectionCts.Token);

            // Select the initial user if specified in the query parameter
            if (_initialUserId > 0)
            {
                var user = Chats.FirstOrDefault(u => u.Id == _initialUserId);
                if (user != null)
                {
                    await OnUserSelected(user);
                }
                else
                {
                    // Fetch the user details if not already in the chat list
                    try
                    {
                        var fetchedUser = await _UserApi.GetUserAsync(_initialUserId);
                        if (fetchedUser != null)
                        {
                            await OnUserSelected(fetchedUser);
                        }
                    }
                    catch (Exception)
                    {
                        // Handle the case where the user can't be fetched
                    }
                }
            }
        }
        catch (Exception)
        {
            // Handle connection failures gracefully
            // Could implement a reconnection logic here if needed
        }
    }

    private async Task LoadChatUsersAsync()
    {
        _AppState.ShowLoader("Loading Chats");
        _loadingChats = true;
        int currentUserId = _PetVetAuthStateProvider.User.Id;
        var chatUsers = await _UserApi.GetChatUsersAsync(currentUserId);
        Chats = chatUsers.ToList();
        _loadingChats = false;
        _AppState.HideLoader();

        StateHasChanged();
    }

    private async Task OnUserSelected(UserDto user)
    {
        _selectedUser = user;

        var selectedChatUser = Chats.FirstOrDefault(c => c.IsSelected);

        if (selectedChatUser is not null)
        {
            selectedChatUser.IsSelected = false;
        }

        var chatUser = Chats.FirstOrDefault(c => c.Id == user.Id);

        if (chatUser is null)
        {
            user.IsSelected = true;
            Chats.Add(user);
        }
        else
        {
            chatUser.IsSelected = true;
        }
    }

    private async Task HandleChatDetailsCancel(bool shouldRemoveFromChatList)
    {
        if (shouldRemoveFromChatList && _selectedUser is not null)
        {
            Chats.Remove(_selectedUser);
        }
        else if (_selectedUser is not null)
        {
            var chatUser = Chats.FirstOrDefault(c => c.Id == _selectedUser.Id);
            if (chatUser is not null)
            {
                chatUser.IsSelected = false;
            }
        }

        _selectedUser = null;
    }

    private HubConnection ConfigureHubConnection()
    {
        var token = _PetVetAuthStateProvider.User.Token;

        // Use the same API URL configuration as in MauiProgram.cs
        string baseUrl = "https://localhost:7129";

        // Check for physical device or iOS
        bool isPhysicalOrIOS = false;
        bool isAndroid = false;

        // We can check for the browser host to make some determinations
        var host = _NavigationManager.Uri.ToLower();
        if (host.Contains("localhost"))
        {
            // Local development - use localhost
            baseUrl = "https://localhost:7129";
        }
        else
        {
            // Deployed or physical device - use the devtunnel URL
            isPhysicalOrIOS = true;
            baseUrl = "https://m6hpgtsx-7129.inc1.devtunnels.ms";
        }

        var hubUrl = $"{baseUrl}/hubs/petvet-chat?access-token={token}";

        var hubConnection = new HubConnectionBuilder()
            .WithUrl(hubUrl)
            .WithAutomaticReconnect()
            .Build();

        hubConnection.On<UserDto>(nameof(IPetVetChatHubClient.UserConnected), (user) =>
        {
            Users.Add(user);
            StateHasChanged();
        });

        hubConnection.On<ICollection<UserDto>>(nameof(IPetVetChatHubClient.ConnectedUserList), (users) =>
        {
            Users = users;
            foreach (var user in Users)
            {
                if (users.Any(u => u.Id == user.Id))
                {
                    user.IsOnline = true;
                }
            }

            StateHasChanged();
        });

        hubConnection.On<int>(nameof(IPetVetChatHubClient.UserIsOnline), (userId) =>
        {
            var user = Users.FirstOrDefault(u => u.Id == userId);

            if (user is not null)
            {
                user.IsOnline = true;
                StateHasChanged();
            }
        });

        hubConnection.On<MessageDto>(nameof(IPetVetChatHubClient.MessageReceived), (messageDto) =>
        {
            var fromUser = Users.FirstOrDefault(u => u.Id == messageDto.FromUserId);

            if (!Chats.Any(c => c.Id == messageDto.FromUserId))
            {
                Chats.Insert(0, fromUser!);
            }
            else
            {
                // Move chat with new message to the top
                var chatUser = Chats.FirstOrDefault(c => c.Id == messageDto.FromUserId);
                if (chatUser != null && Chats.IndexOf(chatUser) > 0)
                {
                    Chats.Remove(chatUser);
                    Chats.Insert(0, chatUser);
                }

                if (_selectedUser?.Id == messageDto.FromUserId)
                {
                    _newIncomingMessage = messageDto;
                }
            }

            StateHasChanged();
        });

        return hubConnection;
    }

    private void HandleMessageSent(UserDto user)
    {
        var chatUser = Chats.FirstOrDefault(c => c.Id == user.Id);
        if (chatUser != null && Chats.IndexOf(chatUser) > 0)
        {
            Chats.Remove(chatUser);
            Chats.Insert(0, chatUser);
        }
    }

    public async ValueTask DisposeAsync()
    {
        // Cancel any ongoing connection operations first
        if (_connectionCts != null && !_connectionCts.IsCancellationRequested)
        {
            _connectionCts.Cancel();
            _connectionCts.Dispose();
            _connectionCts = null;
        }

        // Then dispose the hub connection
        if (_hubConnection != null)
        {
            try
            {
                await _hubConnection.DisposeAsync();
            }
            catch (Exception)
            {
                // Suppress any exceptions during disposal
            }
            finally
            {
                _hubConnection = null;
            }
        }
    }
}
