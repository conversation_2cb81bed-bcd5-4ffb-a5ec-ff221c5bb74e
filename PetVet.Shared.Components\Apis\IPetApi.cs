﻿using PetVet.Shared.DTOs;
using Refit;

namespace PetVet.Shared.Components.Apis
{
    [Headers("Authorization: Bearer ")]
    public interface IPetApi
    {
        [Post("/api/pets")]
        Task<PetVetApiResponse> SavePetAsync(PetDto petDto);

        [Get("/api/pets")]
        Task<PetDto[]> GetPetsAsync();

        [Get("/api/pets/user/{userId}")]
        Task<PetDto[]> GetPetsByUserAsync(int userId);

        [Get("/api/pets/{id}")]
        Task<PetDto> GetPetAsync(int id);

        [Delete("/api/pets/{id}")]
        Task<PetVetApiResponse> DeletePetAsync(int id);
    }
}
