﻿@page "/"
@inject NavigationManager _NavigationManager
@inject PetVetAuthStateProvider _PetVetAuthStateProvider
@layout MobileEmptyLayout

@attribute [Authorize]

<PageTitle>Home</PageTitle>

<AuthorizeView>
    <Authorized>
        <AuthorizeView Roles="@nameof(UserRole.PetOwner)" Context="petOwnerContext">
            <RedirectToHome Url="petOwner/home" />
        </AuthorizeView>

        <AuthorizeView Roles="@nameof(UserRole.Vet)" Context="vetContext">
            <RedirectToHome Url="vet/home" />
        </AuthorizeView>

        <AuthorizeView Roles="@nameof(UserRole.Admin)" Context="adminContext">
            <AdminRestrict />
        </AuthorizeView>
    </Authorized>
</AuthorizeView>

@code {
    protected override async Task OnInitializedAsync()
    {
        var isSuccess = await _PetVetAuthStateProvider.InitializeAsync(redirectToLogin: false);

        if(!isSuccess)
        {
            // Need to redirect to login
            _NavigationManager.NavigateTo("auth/login", replace: true);
            return;
        }
    }
}