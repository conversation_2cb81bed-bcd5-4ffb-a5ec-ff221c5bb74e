﻿@page "/admin/manage/pets/profile/create"
@page "/admin/manage/pets/profile/update/{Id:int}"
@inject IPetApi _PetApi
@inject ICategoryApi _CategoryApi
@inject IJSRuntime _JS
@inject NavigationManager _NavigationManager
@inject IAppState _AppState
@inject PetVetAuthStateProvider _PetVetAuthStateProvider

<PageTitle>@(Id == 0 ? "Create Pet Profile" : "Update Pet Profile")</PageTitle>

<BsModal OnModalConfirmation="ConfirmDeleteImage"
         ButtonText="Delete"
         Title="Are you sure you want to delete this image?">
</BsModal>

<div class="bg-gray-50 p-8 min-h-screen w-full max-w-4xl mx-auto">
    <div class="bg-white rounded-3xl shadow-xl border border-gray-200 overflow-hidden">
        <!-- Header -->
        <div class="bg-primary-500 rounded-t-3xl p-8 text-center text-white">
            <h2 class="text-3xl font-bold py-2 font-acme">
                @(Id == 0 ? "Create New Pet Profile" : "Update Pet Profile") 
                <i class="fas fa-paw ml-2"></i>
            </h2>
        </div>
        
        <!-- Body -->
        <div class="p-8">
            <EditForm Model="_pet" OnValidSubmit="SavePetAsync">
                <DataAnnotationsValidator />

                <!-- Form Fields -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <!-- Pet Name -->
                    <div class="floating-label-group">
                        <InputText @bind-Value="_pet.Name" class="floating-label-input" id="Name" placeholder=" " />
                        <label for="Name" class="floating-label">Pet Name</label>
                        <ValidationMessage For="() => _pet.Name" />
                    </div>

                    <!-- Pet Category -->
                    <div class="floating-label-group">
                        <InputSelect @bind-Value="_pet.CategoryId" class="floating-label-input" id="Category">
                            <option value="0" disabled selected>Select Category</option>
                            @foreach (var category in _categories)
                            {
                                <option value="@category.Id" selected="@(category.Id == _pet.CategoryId)">
                                    @category.Name
                                </option>
                            }
                        </InputSelect>
                        <label for="Category" class="floating-label">Category</label>
                        <ValidationMessage For="() => _pet.CategoryId" />
                    </div>

                    <!-- Pet Age -->
                    <div class="floating-label-group">
                        <InputNumber @bind-Value="_pet.Age" class="floating-label-input" id="Age" placeholder=" " />
                        <label for="Age" class="floating-label">Age (years)</label>
                        <ValidationMessage For="() => _pet.Age" />
                    </div>

                    <!-- Pet Weight -->
                    <div class="floating-label-group">
                        <InputNumber @bind-Value="_pet.Weight" class="floating-label-input" id="Weight" placeholder=" " />
                        <label for="Weight" class="floating-label">Weight (kg)</label>
                        <ValidationMessage For="() => _pet.Weight" />
                    </div>

                    <!-- Pet Gender -->
                    <div class="floating-label-group">
                        <InputSelect @bind-Value="_pet.Gender" class="floating-label-input" id="Gender">
                            <option value="">Select Gender</option>
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                        </InputSelect>
                        <label for="Gender" class="floating-label">Gender</label>
                        <ValidationMessage For="() => _pet.Gender" />
                    </div>

                    <!-- Pet Breed -->
                    <div class="floating-label-group">
                        <InputText @bind-Value="_pet.Breed" class="floating-label-input" id="Breed" placeholder=" " />
                        <label for="Breed" class="floating-label">Breed</label>
                        <ValidationMessage For="() => _pet.Breed" />
                    </div>

                    <!-- Pet Date of Birth -->
                    <div class="floating-label-group">
                        <InputDate @bind-Value="_pet.DateOfBirth" class="floating-label-input" id="DateOfBirth" placeholder=" " />
                        <label for="DateOfBirth" class="floating-label">Date of Birth</label>
                        <ValidationMessage For="() => _pet.DateOfBirth" />
                    </div>

                    <!-- Pet Vaccination Status -->
                    <div class="floating-label-group">
                        <InputText @bind-Value="_pet.VaccinationStatus" class="floating-label-input" id="VaccinationStatus" placeholder=" " />
                        <label for="VaccinationStatus" class="floating-label">Vaccination Status</label>
                        <ValidationMessage For="() => _pet.VaccinationStatus" />
                    </div>
                </div>

                <!-- Image Upload Section -->
                <div class="mb-8">
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">Pet Image</h3>
                    @if (!string.IsNullOrEmpty(_pet.ImageUrl))
                    {
                        <div class="flex flex-col sm:flex-row items-start gap-4">
                            <div class="flex-shrink-0">
                                <img src="@_pet.ImageUrl" alt="Pet Image" class="w-32 h-32 rounded-xl object-cover shadow-lg border border-gray-200" />
                            </div>
                            <div class="flex-grow">
                                <button type="button" 
                                        class="inline-flex items-center space-x-2 bg-red-500 hover:bg-red-600 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200 hover:transform hover:scale-105 shadow-md"
                                        @onclick="() => HandleImageDeleteConfirmation()">
                                    <i class="fas fa-trash"></i>
                                    <span>Remove Image</span>
                                </button>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="border-2 border-dashed border-gray-300 rounded-xl p-6 text-center hover:border-primary-400 transition-colors duration-200">
                            <div class="space-y-2">
                                <i class="fas fa-cloud-upload-alt text-3xl text-gray-400"></i>
                                <div class="text-gray-600">
                                    <label for="CustomFile" class="cursor-pointer text-primary-600 hover:text-primary-500 font-medium">
                                        Click to upload
                                    </label>
                                    <span> or drag and drop</span>
                                </div>
                                <p class="text-sm text-gray-500">PNG, JPG, JPEG up to 10MB</p>
                            </div>
                            <InputFile OnChange="LoadFiles" class="hidden" id="CustomFile" accept="image/x-png,image/jpeg,image/jpg" />
                        </div>
                    }
                </div>

                <!-- Error Alert -->
                @if (!string.IsNullOrEmpty(_error))
                {
                    <div class="bg-red-50 border border-red-200 text-red-800 p-4 rounded-xl mb-6 flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle mr-3"></i>
                            <span>@_error</span>
                        </div>
                        <button type="button" class="text-red-600 hover:text-red-800 text-xl" @onclick="() => _error = null">
                            &times;
                        </button>
                    </div>
                }

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-between mt-8">
                    <div class="flex gap-4">
                        @if (!_isBusy)
                        {
                            <button type="submit" 
                                    class="inline-flex items-center space-x-2 bg-primary-500 hover:bg-primary-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 hover:transform hover:scale-105 shadow-lg"
                                    disabled="@_isBusy">
                                <i class="fas @(Id > 0 ? "fa-save" : "fa-plus")"></i>
                                <span>@(Id > 0 ? "Update Pet" : "Create Pet")</span>
                            </button>
                        }
                        else
                        {
                            <button type="button" 
                                    disabled 
                                    class="inline-flex items-center space-x-2 bg-gray-400 text-white font-semibold py-3 px-6 rounded-xl shadow-lg cursor-not-allowed">
                                <i class="fas fa-spinner fa-spin"></i>
                                <span>@(Id > 0 ? "Updating..." : "Creating...")</span>
                            </button>
                        }
                    </div>

                    <div class="flex gap-4">
                        <a href="/admin/manage/pets/profile" 
                           class="inline-flex items-center space-x-2 bg-gray-500 hover:bg-gray-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 hover:transform hover:scale-105 shadow-lg">
                            <i class="fas fa-arrow-left"></i>
                            <span>Back To List</span>
                        </a>
                    </div>
                </div>
            </EditForm>
        </div>
    </div>
</div>

@code {
    [Parameter] public int Id { get; set; }
    private CategoryDto[] _categories = [];
    private PetDto _pet = new();
    private bool _isBusy;
    private string? _error;
    private string? _directoryPath { get; set; }
    private bool isProcessing { get; set; } = true;
    private int _currentUserId => _PetVetAuthStateProvider?.User.Id ?? 0;


    protected override async Task OnInitializedAsync() => await LoadPetAsync();


    private async Task LoadPetAsync()
    {
        if (Id > 0)
        {
            isProcessing = true;
            _AppState.ShowLoader("Fetching Pet Data");
            _pet = await _PetApi.GetPetAsync(Id);
        }

        _categories = await _CategoryApi.GetCategoriesAsync();
        _AppState.HideLoader();
        isProcessing = false;

        // Ensure the selected category is set correctly
        if (_pet.CategoryId == 0 && _categories.Length > 0)
        {
            _pet.CategoryId = _categories.FirstOrDefault()?.Id ?? 0;
        }
    }


    private void OnEditPet(PetDto pet)
    {
        _pet = pet;
        _NavigationManager.NavigateTo("/admin/manage/pets/profile");
    }


    private async Task SavePetAsync()
    {
        _error = null;
        _AppState.ShowLoader("Saving Pet Data");
        _isBusy = true;
        isProcessing = true;

        try
        {
            // Only set admin's user ID for new pets, not when updating existing ones
            if (Id == 0) // New pet
            {
                // For new pets created by admin, assign to admin user
                _pet.UserId = 1; // Admin user ID
            }
            // For existing pets, keep the original owner (UserId already set from the database)

            var response = await _PetApi.SavePetAsync(_pet);

            if (Id > 0)
            {
                OnEditPet(_pet);
                await _JS.ToastrSuccess("Pet profile updated successfully.");
            }
            else
            {
                if (!response.IsSuccess)
                {
                    // Error message on UI
                    _error = response.ErrorMessage ?? "An unknown error has occurred.";
                    await _JS.ToastrError(_error);
                    return;
                }

                // Reset the form
                _pet = new();
                await _JS.ToastrSuccess("Pet profile created successfully.");
                _NavigationManager.NavigateTo("/admin/manage/pets/profile");
            }
        }
        catch (Exception ex)
        {
            await _JS.ToastrError("An unknown error has occurred.");
            _error = ex.Message;
        }
        finally
        {
            _AppState.HideLoader();
            _isBusy = false;
            isProcessing = false;
        }
    }


    private async Task LoadFiles(InputFileChangeEventArgs e)
    {
        _AppState.ShowLoader("Uploading Image");

        var file = e.File;

        // Check file type
        if (file.ContentType != "image/jpeg" && file.ContentType != "image/png" && file.ContentType != "image/jpg")
        {
            _error = "The file format is invalid. Please upload an image in JPEG, PNG, or JPG format.";
            await _JS.ToastrError(_error);
            _AppState.HideLoader();
            return;
        }

        var fileContent = new byte[file.Size];
        await file.OpenReadStream().ReadAsync(fileContent);

        var base64Image = Convert.ToBase64String(fileContent);
        _pet.ImageUrl = $"data:image/jpeg;base64,{base64Image}";

        _AppState.HideLoader();
    }


    private void HandleImageDeleteConfirmation()
    {
        _JS.InvokeVoidAsync("ShowConfirmationModal");
    }


    public async Task ConfirmDeleteImage(bool isConfirmed)
    {
        if (isConfirmed)
        {
            _pet.ImageUrl = null;
            await _JS.InvokeVoidAsync("HideConfirmationModal");
            await _JS.ToastrSuccess("Image deleted successfully.");
        }

        _AppState.HideLoader();
        StateHasChanged();
    }
}
