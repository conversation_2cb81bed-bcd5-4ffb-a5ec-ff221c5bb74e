using Microsoft.Extensions.Logging;
using PetVet.Shared.Models.AzureCommunication;
using PetVet.Shared.Services;
using System.Timers;

namespace PetVet.Mobile.Components.Native;

public partial class CallControlsComponent : ContentView
{
    private IAzureCommunicationService? _communicationService;
    private ILogger<CallControlsComponent>? _logger;
    private System.Timers.Timer? _callTimer;
    private DateTime _callStartTime;
    private string? _currentCallId;
    private string? _currentUserId;
    private bool _isAudioMuted = false;
    private bool _isVideoEnabled = true;
    private bool _isSpeakerOn = false;
    private bool _isOnHold = false;
    private bool _isScreenSharing = false;

    // Events
    public event EventHandler<string>? CallEnded;
    public event EventHandler<CallControlEventArgs>? CallControlChanged;

    public CallControlsComponent()
    {
        InitializeComponent();
        InitializeCallTimer();
        UpdateButtonStates();
    }

    public void InitializeServices(IAzureCommunicationService communicationService, ILogger<CallControlsComponent> logger)
    {
        _communicationService = communicationService;
        _logger = logger;
    }

    public void InitializeCall(string callId, string userId, DateTime startTime)
    {
        _currentCallId = callId;
        _currentUserId = userId;
        _callStartTime = startTime;
        
        StartCallTimer();
        UpdateCallStatus(CallStatus.Connected);
        
        _logger?.LogInformation("Call controls initialized for call {CallId}", callId);
    }

    public void UpdateCallStatus(CallStatus status)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            CallStatusLabel.Text = status.ToString();
            
            // Update status indicator color
            StatusIndicator.Fill = status switch
            {
                CallStatus.Connected => Colors.Green,
                CallStatus.Connecting => Colors.Orange,
                CallStatus.OnHold => Colors.Yellow,
                CallStatus.Ringing => Colors.Blue,
                CallStatus.Ended => Colors.Red,
                CallStatus.Failed => Colors.Red,
                _ => Colors.Gray
            };

            // Show/hide controls based on status
            var isCallActive = status == CallStatus.Connected || status == CallStatus.OnHold;
            MuteButton.IsEnabled = isCallActive;
            VideoButton.IsEnabled = isCallActive;
            SpeakerButton.IsEnabled = isCallActive;
            HoldButton.IsEnabled = isCallActive;
            ScreenShareButton.IsEnabled = isCallActive;
            AddParticipantButton.IsEnabled = isCallActive;
        });
    }

    public void UpdateCallQuality(string quality, bool showIndicator = true)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            CallQualityLabel.Text = quality;
            CallQualityFrame.IsVisible = showIndicator;
        });
    }

    public void ShowRecordingIndicator(bool isRecording)
    {
        MainThread.BeginInvokeOnMainThread(() =>
        {
            RecordingFrame.IsVisible = isRecording;
        });
    }

    private void InitializeCallTimer()
    {
        _callTimer = new System.Timers.Timer(1000); // Update every second
        _callTimer.Elapsed += OnCallTimerElapsed;
    }

    private void StartCallTimer()
    {
        _callTimer?.Start();
    }

    private void StopCallTimer()
    {
        _callTimer?.Stop();
    }

    private void OnCallTimerElapsed(object? sender, ElapsedEventArgs e)
    {
        var duration = DateTime.UtcNow - _callStartTime;
        var formattedDuration = $"{duration.Minutes:D2}:{duration.Seconds:D2}";
        
        MainThread.BeginInvokeOnMainThread(() =>
        {
            CallDurationLabel.Text = formattedDuration;
        });
    }

    private async void OnMuteClicked(object sender, EventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(_currentCallId) || string.IsNullOrEmpty(_currentUserId) || _communicationService == null)
                return;

            _isAudioMuted = !_isAudioMuted;

            var success = await _communicationService.ToggleAudioAsync(_currentCallId, _currentUserId, !_isAudioMuted);
            
            if (success)
            {
                UpdateMuteButton();
                OnCallControlChanged("Audio", !_isAudioMuted);
                
                // Haptic feedback
                try
                {
#if ANDROID
                    // Simple haptic feedback for Android
                    // In a real implementation, you would use proper haptic feedback APIs
#endif
                }
                catch { }
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error toggling audio");
            await DisplayAlert("Error", "Failed to toggle audio", "OK");
        }
    }

    private async void OnVideoClicked(object sender, EventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(_currentCallId) || string.IsNullOrEmpty(_currentUserId) || _communicationService == null)
                return;

            _isVideoEnabled = !_isVideoEnabled;

            var success = await _communicationService.ToggleVideoAsync(_currentCallId, _currentUserId, _isVideoEnabled);
            
            if (success)
            {
                UpdateVideoButton();
                OnCallControlChanged("Video", _isVideoEnabled);
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error toggling video");
            await DisplayAlert("Error", "Failed to toggle video", "OK");
        }
    }

    private async void OnEndCallClicked(object sender, EventArgs e)
    {
        try
        {
            var result = await DisplayAlert("End Call", "Are you sure you want to end this call?", "Yes", "No");
            
            if (result && !string.IsNullOrEmpty(_currentCallId) && !string.IsNullOrEmpty(_currentUserId))
            {
                var endRequest = new EndCallRequest
                {
                    CallId = _currentCallId,
                    UserId = _currentUserId,
                    Reason = "User ended call"
                };

                var success = await _communicationService.EndCallAsync(endRequest);
                
                if (success)
                {
                    StopCallTimer();
                    UpdateCallStatus(CallStatus.Ended);
                    CallEnded?.Invoke(this, _currentCallId);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ending call");
            await DisplayAlert("Error", "Failed to end call", "OK");
        }
    }

    private void OnSpeakerClicked(object sender, EventArgs e)
    {
        try
        {
            _isSpeakerOn = !_isSpeakerOn;
            UpdateSpeakerButton();
            OnCallControlChanged("Speaker", _isSpeakerOn);
            
            // In a real implementation, you would toggle the device speaker here
            _logger.LogInformation("Speaker {Action}", _isSpeakerOn ? "enabled" : "disabled");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling speaker");
        }
    }

    private async void OnHoldClicked(object sender, EventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(_currentCallId) || string.IsNullOrEmpty(_currentUserId))
                return;

            bool success;
            
            if (_isOnHold)
            {
                success = await _communicationService.ResumeCallAsync(_currentCallId, _currentUserId);
                if (success)
                {
                    _isOnHold = false;
                    UpdateCallStatus(CallStatus.Connected);
                }
            }
            else
            {
                success = await _communicationService.HoldCallAsync(_currentCallId, _currentUserId);
                if (success)
                {
                    _isOnHold = true;
                    UpdateCallStatus(CallStatus.OnHold);
                }
            }

            if (success)
            {
                UpdateHoldButton();
                OnCallControlChanged("Hold", _isOnHold);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling hold");
            await DisplayAlert("Error", "Failed to toggle hold", "OK");
        }
    }

    private async void OnScreenShareClicked(object sender, EventArgs e)
    {
        try
        {
            if (string.IsNullOrEmpty(_currentCallId) || string.IsNullOrEmpty(_currentUserId))
                return;

            bool success;
            
            if (_isScreenSharing)
            {
                success = await _communicationService.StopScreenShareAsync(_currentCallId, _currentUserId);
                if (success)
                {
                    _isScreenSharing = false;
                }
            }
            else
            {
                success = await _communicationService.StartScreenShareAsync(_currentCallId, _currentUserId);
                if (success)
                {
                    _isScreenSharing = true;
                }
            }

            if (success)
            {
                UpdateScreenShareButton();
                OnCallControlChanged("ScreenShare", _isScreenSharing);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error toggling screen share");
            await DisplayAlert("Error", "Failed to toggle screen share", "OK");
        }
    }

    private async void OnAddParticipantClicked(object sender, EventArgs e)
    {
        try
        {
            // In a real implementation, this would open a participant selection dialog
            var result = await DisplayPromptAsync("Add Participant", "Enter participant ID:", "Add", "Cancel");
            
            if (!string.IsNullOrEmpty(result) && !string.IsNullOrEmpty(_currentCallId))
            {
                var success = await _communicationService.AddParticipantAsync(_currentCallId, result, "New Participant");
                
                if (success)
                {
                    OnCallControlChanged("ParticipantAdded", result);
                    await DisplayAlert("Success", "Participant added successfully", "OK");
                }
                else
                {
                    await DisplayAlert("Error", "Failed to add participant", "OK");
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding participant");
            await DisplayAlert("Error", "Failed to add participant", "OK");
        }
    }

    private async void OnSettingsClicked(object sender, EventArgs e)
    {
        try
        {
            // In a real implementation, this would open call settings
            var actions = new[] { "Call Quality", "Audio Settings", "Video Settings", "Cancel" };
            var result = await DisplayActionSheet("Call Settings", "Cancel", null, actions);
            
            _logger.LogInformation("Settings action selected: {Action}", result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error opening settings");
        }
    }

    private void UpdateButtonStates()
    {
        UpdateMuteButton();
        UpdateVideoButton();
        UpdateSpeakerButton();
        UpdateHoldButton();
        UpdateScreenShareButton();
    }

    private void UpdateMuteButton()
    {
        MuteButton.Text = _isAudioMuted ? "🔇" : "🎤";
        MuteButton.BackgroundColor = _isAudioMuted ? Colors.Red : Color.FromArgb("#8FBFA8");
    }

    private void UpdateVideoButton()
    {
        VideoButton.Text = _isVideoEnabled ? "📹" : "📷";
        VideoButton.BackgroundColor = _isVideoEnabled ? Color.FromArgb("#8FBFA8") : Colors.Gray;
    }

    private void UpdateSpeakerButton()
    {
        SpeakerButton.Text = _isSpeakerOn ? "🔊" : "🔉";
        SpeakerButton.BackgroundColor = _isSpeakerOn ? Color.FromArgb("#FEA195") : Color.FromArgb("#8FBFA8");
    }

    private void UpdateHoldButton()
    {
        HoldButton.Text = _isOnHold ? "▶️" : "⏸️";
        HoldButton.BackgroundColor = _isOnHold ? Color.FromArgb("#FEA195") : Color.FromArgb("#8FBFA8");
    }

    private void UpdateScreenShareButton()
    {
        ScreenShareButton.Text = _isScreenSharing ? "📱" : "📲";
        ScreenShareButton.BackgroundColor = _isScreenSharing ? Color.FromArgb("#FEA195") : Color.FromArgb("#8FBFA8");
    }

    private void OnCallControlChanged(string controlType, object value)
    {
        CallControlChanged?.Invoke(this, new CallControlEventArgs
        {
            ControlType = controlType,
            Value = value,
            CallId = _currentCallId,
            UserId = _currentUserId
        });
    }

    private async Task<bool> DisplayAlert(string title, string message, string accept, string? cancel = null)
    {
        try
        {
            var mainPage = Application.Current?.Windows?.FirstOrDefault()?.Page;
            if (mainPage == null) return false;

            if (cancel != null)
            {
                return await mainPage.DisplayAlert(title, message, accept, cancel);
            }
            else
            {
                await mainPage.DisplayAlert(title, message, accept);
                return true;
            }
        }
        catch
        {
            return false;
        }
    }

    private async Task<string?> DisplayPromptAsync(string title, string message, string accept, string cancel)
    {
        try
        {
            var mainPage = Application.Current?.Windows?.FirstOrDefault()?.Page;
            return await (mainPage?.DisplayPromptAsync(title, message, accept, cancel) ?? Task.FromResult<string?>(null));
        }
        catch
        {
            return null;
        }
    }

    private async Task<string?> DisplayActionSheet(string title, string cancel, string? destruction, params string[] buttons)
    {
        try
        {
            var mainPage = Application.Current?.Windows?.FirstOrDefault()?.Page;
            return await (mainPage?.DisplayActionSheet(title, cancel, destruction, buttons) ?? Task.FromResult<string?>(null));
        }
        catch
        {
            return null;
        }
    }

    protected override void OnHandlerChanged()
    {
        base.OnHandlerChanged();
        
        if (Handler == null)
        {
            // Cleanup when component is disposed
            StopCallTimer();
            _callTimer?.Dispose();
        }
    }
}

// Event args for call control changes
public class CallControlEventArgs : EventArgs
{
    public string ControlType { get; set; } = "";
    public object? Value { get; set; }
    public string? CallId { get; set; }
    public string? UserId { get; set; }
}


