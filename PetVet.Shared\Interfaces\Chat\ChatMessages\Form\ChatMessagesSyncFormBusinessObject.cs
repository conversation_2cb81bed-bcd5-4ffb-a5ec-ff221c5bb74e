﻿using PetVet.ServiceContracts.Enums;
using System.ComponentModel.DataAnnotations;
namespace PetVet.ServiceContracts.Features.Conversation;
public class ChatMessagesSyncFormBusinessObject
{
    [StringLength(450)]
    public string Id { get; set; } = null!;

    [StringLength(450)]
    public string ConversationId { get; set; } = null!;

    public int SenderId { get; set; } = 0;

    public string? SenderUserName { get; set; }

    public string? SenderDisplayPictureUrl { get; set; }

    public string? PlainContent { get; set; }


    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    //public byte SyncStatus { get; set; }

    // Soft-deletion
    public bool IsDeleted { get; set; } = false;
    public DateTime? DeletedAt { get; set; }

    // If you want to track edits:
    public bool IsEdited { get; set; } = false;
    public DateTime? EditedAt { get; set; }

    // Disappearing (ephemeral) message fields
    public bool IsEphemeral { get; set; } = false;
    public TimeSpan? DisappearAfter { get; set; }
    public DateTime? DisappearAt { get; set; }

    public bool EnableFallBackChannel { get; set; }

    public string? MessageRecepientId { get; set; } // extra field for queing logic
    public string? MessageRecepientUserName { get; set; } // extra field for queing logic
    
}

 

public class ChatMessageUpdate
{
    public string Id { get; set; } = null!;
 
    public DeliveryStatus DeliveryStatus { get; set; }

    public DateTime DeliveryStatusTime { get; set; }

    public int SenderId { get; set; }
}