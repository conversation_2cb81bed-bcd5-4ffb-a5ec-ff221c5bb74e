﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using PetVet.Api.Data;
using PetVet.Server.DataServices.Helpers;
using PetVet.ServiceContracts.Enums;
using PetVet.ServiceContracts.Features.Conversation;
using PetVet.Shared.Enums;
using PetVet.Shared.IHub;
using System.Collections.Concurrent;
using System.Security.Claims;
using System.Text.Json;

namespace PetVet.Api.Hubs;

[Authorize]
public class ChatHub : Hub<IChatHubClientProxy>, IChatHubServerProxy
{
    private static ConcurrentDictionary<int, string> connectionIdCache = new ConcurrentDictionary<int, string>();
    private readonly IServiceScopeFactory scopeFactory;

    public ILogger<ChatHub> Logger { get; }

    public ChatHub(ILogger<ChatHub> logger, IServiceScopeFactory scopeFactory)
    {
        Logger = logger;
        this.scopeFactory = scopeFactory;
    }

    public override async Task OnConnectedAsync()
    {
        var userId = Convert.ToInt32(Context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
        if (userId == 0)
        {
            await Clients.Client(Context.ConnectionId).Logout();
            return;
        }

        Logger.LogInformation("Connected {0}, {1} - {2}", Context.ConnectionId, Context.User.Identity.Name, userId);

        connectionIdCache.AddOrUpdate(userId, Context.ConnectionId, (key, oldValue) => Context.ConnectionId);


        await base.OnConnectedAsync();
        await SendPendingMessages(userId);
    }

    private async Task SendPendingMessages(int userId)
    {
        var scope = scopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<PetVetContext>();
        var pendingMessages = (from m in context.ConversationMessages
                               from recipients in context.MessageRecipients.Where(x => x.MessageId == m.Id)
                               from user in context.Users.Where(x => x.Id == recipients.RecipientId)
                               where recipients.DeliveryStatus <= DeliveryStatus.SentToEndUserViaSignalR
                               && recipients.RecipientId == userId
                               orderby m.Id
                               select new ChatMessagesSyncFormBusinessObject
                               {
                                   Id = m.Id,
                                   MessageRecepientId = recipients.Id,
                                   MessageRecepientUserName = user.Name,
                                   ConversationId = m.ConversationId,
                                   SenderId = m.SenderId,
                                   CreatedAt = m.CreatedAt,
                                   DeletedAt = m.DeletedAt,
                                   EditedAt = m.EditedAt,
                                   IsDeleted = m.IsDeleted,
                                   IsEdited = m.IsEdited,
                                   PlainContent = m.PlainContent,
                               }).ToList();
        pendingMessages = pendingMessages.DistinctBy(x => x.Id).ToList();
        foreach (var message in pendingMessages)
        {
            await SendMessageAsync(userId, message.MessageRecepientUserName, message.Id, message.PlainContent,
                 JsonSerializer.Serialize(message), SignalRMethod.OnNewMessage);
            await AcknowledgeMessage(message.Id, DeliveryStatus.SentToEndUserViaSignalR, DateTime.UtcNow);
        }

        var utcNow = DateTime.UtcNow;

        var messageIds = pendingMessages.Select(x => x.Id).ToList();
        await context.ConversationMessages.Where(x => messageIds.Contains(x.Id) && x.DeliveryStatus < DeliveryStatus.SentToEndUserViaSignalR)
                      .ExecuteUpdateAsync(x => x
                      .SetProperty(p => p.DeliveryStatus, DeliveryStatus.SentToEndUserViaSignalR)
                      .SetProperty(p => p.DeliveryStatusTime, utcNow));

        var messageRecipients = pendingMessages.Select(x => x.MessageRecepientId).ToList();
        await context.MessageRecipients.Where(x => messageRecipients.Contains(x.Id))
            .ExecuteUpdateAsync(x => x
                        .SetProperty(p => p.DeliveryStatus, DeliveryStatus.SentToEndUserViaSignalR)
                        .SetProperty(p => p.DeliveryStatusTime, utcNow));
    }

    public async Task AcknowledgeMessage(string messageId, DeliveryStatus deliveryStatus, DateTime timeStamp)
    {
        var userId = Convert.ToInt32(Context.User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
        if (userId <= 0)
        {
            Logger.LogWarning("Received Ack without valid user context.");
            return;
        }
        if (deliveryStatus != DeliveryStatus.SentToEndUserViaSignalR)
        {
            Logger.LogWarning("Received Ack  for {0} from {1} - {2}.", messageId, userId, deliveryStatus);
        }
        var scope = scopeFactory.CreateScope();
        var context = scope.ServiceProvider.GetRequiredService<PetVetContext>();
        var messageDispatcher = scope.ServiceProvider.GetRequiredService<MessageDispatcher>();
        await context.MessageRecipients.Where(x => x.MessageId == messageId && x.RecipientId == userId)
              .ExecuteUpdateAsync(x => x.SetProperty(y => y.DeliveryStatus, deliveryStatus)
              .SetProperty(y => y.DeliveryStatusTime, timeStamp));

        //messageDispatcher.DispatchMessageUpdate(new ChatMessageUpdate
        //{
        //    Id = messageId,
        //    DeliveryStatusTime = timeStamp,
        //    SenderId = userId,
        //    DeliveryStatus = deliveryStatus
        //});
    }

    public async Task<bool> SendMessageAsync(int userId, string? userName, string messageId, string? content, string messageJson, SignalRMethod signalRMethod)
    {
        var connectionId = connectionIdCache.ContainsKey(userId) ? connectionIdCache[userId] : string.Empty;

        if (string.IsNullOrEmpty(connectionId))
        {
            Logger.LogDebug("User offline {0} - for message {1}", userName, content);
            await Task.Delay(100); //todo: Create a separate logic for offline devices
            return false;
        }

        if (Clients == null)
        {
            Logger.LogWarning("Clients array is null");
            return false;
        }

        var client = Clients.Client(connectionId);
        if (client == null)
        {
            Logger.LogWarning("Client id not not found for user {0}, {1}", userName, connectionId);
            return false;
        }
        if (signalRMethod == SignalRMethod.OnNewMessage)
        {
            await client.OnNewMessage(userId, messageJson);
        }
        else if (signalRMethod == SignalRMethod.OnFeedUpdate)
        {
            await client.OnMessageDeliveryUpdate(userId, messageJson);
        }
        else if (signalRMethod == SignalRMethod.OnCallAction)
        {
            await client.OnCallAction(userId, messageJson);
        }
        else
        {
            Logger.LogWarning("Unknown SignalR method {0} for user {1}", signalRMethod, userName);
            return false;
        }
        Logger.LogDebug("Sent message {0} to {1} with connection-id {2} via {3}", content, userName, connectionId, signalRMethod);
        return true;
    }

    public override Task OnDisconnectedAsync(Exception? e)
    {
        var userId = Convert.ToInt32(Context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value);
        if (userId > 0)
        {
            var connectionId = connectionIdCache.ContainsKey(userId) ? connectionIdCache[userId] : string.Empty;
            connectionIdCache.TryRemove(userId, out _);
            Logger.LogWarning("Disconnected {0}, {1} - {2}", Context.ConnectionId, Context.User?.Identity?.Name, e?.Message);
        }
        return base.OnDisconnectedAsync(e);
    }

    public async Task NewClientMessage(string messageJson)
    {
        var messageData = JsonSerializer.Deserialize<Dictionary<string, object>>(messageJson);

        if (messageData?.ContainsKey("ToUserId") == true)
        {
            var userIdString = messageData["ToUserId"]?.ToString();
            var userId = Convert.ToInt32(userIdString);
            if (userId > 0)
            {
               await SendMessageAsync(userId, userId.ToString(), string.Empty, JsonSerializer.Serialize(messageData), messageJson, SignalRMethod.OnCallAction);
            }

        }
    }
}