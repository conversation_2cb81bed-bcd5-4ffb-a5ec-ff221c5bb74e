﻿using PetVet.Shared.Components.Framework;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;

namespace PetVet.Platform.Razor
{
    public interface BaseHttpClient
    {
        Task<T> GetFromJsonAsync<T>(string requestUri);
        Task<T> PostAsJsonAsync<T>(string requestUri, object formBusinessObject);
    }

    public class HttpTokenClient : BaseHttpClient
    {
        private readonly HttpClient _httpClient;
        private readonly ILocalStorageService localStorageService;
        public HttpTokenClient(HttpClient client, ILocalStorageService localStorageService)
        {
            _httpClient = client;
            this.localStorageService = localStorageService;
        }

        public async Task<T> GetFromJsonAsync<T>(string requestUri)
        {

            _ = _httpClient ?? throw new ArgumentNullException($"{nameof(_httpClient)} is null in {GetType().Name}");
            var authToken = await localStorageService.GetValue("auth_token");
            if(!string.IsNullOrEmpty(authToken))
            {
                _httpClient.DefaultRequestHeaders.Remove("Authorization");
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {authToken}");
            }
            var response = await _httpClient.GetAsync(requestUri);
            var responseString = await response.Content.ReadAsStringAsync();
            if (response.IsSuccessStatusCode)
            {
                var responseObject = JsonSerializer.Deserialize<T>(responseString, JsonSerializerOptions.Default);
                return responseObject ?? throw new Exception("response is null");
            }
            else if (response.StatusCode == HttpStatusCode.Unauthorized)
            {
                throw new UnauthorizedAccessException(responseString);
            }

            throw new Exception(responseString);
        }

        public async Task<T> PostAsJsonAsync<T>(string requestUri, object formBusinessObject)
        {
            var authToken = await localStorageService.GetValue("auth_token");
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {authToken}");

            _ = _httpClient ?? throw new ArgumentNullException($"{nameof(_httpClient)} is null in {GetType().Name}"); 
            HttpResponseMessage response = new HttpResponseMessage();
            response = await _httpClient.PostAsJsonAsync(requestUri, formBusinessObject);
            var responseString = await response.Content.ReadAsStringAsync();
            if (!response.IsSuccessStatusCode)
            {
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    throw new UnauthorizedAccessException(responseString);
                }
                else
                {
                    throw new Exception("Error: " + responseString);
                }
            }
                
            if (typeof(T) == typeof(string))
            {
                return (dynamic)responseString;
            }

            if (typeof(T) == typeof(int))
            {
                return (dynamic)Convert.ToInt32(responseString);
            }
            if (typeof(T) == typeof(bool))
            {
                return (dynamic)Convert.ToBoolean(responseString);
            }
            if (typeof(T) == typeof(Guid))
            {
                return (dynamic)JsonSerializer.Deserialize<Guid>(responseString);
            }
            throw new InvalidCastException($"Conversion for {typeof(T)} is not defined in BaseHttpClient");
        }
    }
     

}
