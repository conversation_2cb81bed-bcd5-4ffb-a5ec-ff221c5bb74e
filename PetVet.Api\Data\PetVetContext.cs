﻿using PetVet.Server.DataServices.Data;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using PetVet.Api.Data.Entities;
using PetVet.Shared;

namespace PetVet.Api.Data
{
    public class PetVetContext : DbContext
    {
        private readonly IPasswordHasher<User> _passwordHasher;

        public PetVetContext(DbContextOptions<PetVetContext> options, IPasswordHasher<User> passwordHasher) : base(options)
        {
            _passwordHasher = passwordHasher;
        }

        public DbSet<Category> Categories { get; set; }
        public DbSet<Pet> Pets { get; set; }
        public DbSet<User> Users { get; set; }
        public DbSet<Education> Educations { get; set; }
        public DbSet<WorkingHour> WorkingHours { get; set; }
        public DbSet<Message> Messages { get; set; }
        public DbSet<VetTimeSlot> VetTimeSlots { get; set; }
        public DbSet<Appointment> Appointments { get; set; }

        public DbSet<Conversation> Conversations => Set<Conversation>();
        public DbSet<ConversationParticipant> ConversationParticipants => Set<ConversationParticipant>();
        public DbSet<ConversationMessage> ConversationMessages => Set<ConversationMessage>();
        public DbSet<MessageRecipient> MessageRecipients => Set<MessageRecipient>();
        public DbSet<MessageAttachment> MessageAttachments => Set<MessageAttachment>();

        public DbSet<UserDevice> UserDevices => Set<UserDevice>();

        // WhatsApp notification entities
        //public DbSet<WhatsAppNotificationLog> WhatsAppNotificationLogs => Set<WhatsAppNotificationLog>();
        //public DbSet<WhatsAppNotificationConfig> WhatsAppNotificationConfigs => Set<WhatsAppNotificationConfig>();
        //public DbSet<WhatsAppUserPreferences> WhatsAppUserPreferences => Set<WhatsAppUserPreferences>();
        //public DbSet<WhatsAppDeliveryReport> WhatsAppDeliveryReports => Set<WhatsAppDeliveryReport>();

        // SMS notification entities
        public DbSet<SmsNotificationLog> SmsNotificationLogs => Set<SmsNotificationLog>();
        public DbSet<SmsNotificationConfig> SmsNotificationConfigs => Set<SmsNotificationConfig>();

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            base.OnConfiguring(optionsBuilder);
        }


        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            //var adminUser = new User
            //{
            //    Id = 1,
            //    Name = "Bilal",
            //    Email = "<EMAIL>",
            //    Phone = "03457265250",
            //    Address = "Abbottabad",
            //    Role = nameof(UserRole.Admin),
            //    IsApproved = true,
            //};
            //adminUser.PasswordHash = _passwordHasher.HashPassword(adminUser, "BilalMalik11!");

            //modelBuilder.Entity<User>().HasData(adminUser);

            modelBuilder.Entity<Message>(e =>
            {
                e.HasOne(m => m.ToUser).WithMany().OnDelete(DeleteBehavior.NoAction);
                e.HasOne(m => m.FromUser).WithMany().OnDelete(DeleteBehavior.NoAction);
            });

            // Configure Appointments foreign keys to prevent cascade delete cycles
            modelBuilder.Entity<Appointment>(e =>
            {
                e.HasOne(a => a.PetOwner).WithMany().OnDelete(DeleteBehavior.NoAction);
                e.HasOne(a => a.Vet).WithMany().OnDelete(DeleteBehavior.NoAction);
            });

            // Configure WhatsApp notification entities
            //modelBuilder.Entity<WhatsAppNotificationLog>(e =>
            //{
            //    e.HasOne(w => w.User).WithMany().OnDelete(DeleteBehavior.NoAction);
            //    e.HasOne(w => w.Appointment).WithMany().OnDelete(DeleteBehavior.NoAction);
            //    e.HasIndex(w => w.UserId);
            //    e.HasIndex(w => w.AppointmentId);
            //    e.HasIndex(w => w.Status);
            //    e.HasIndex(w => w.CreatedAt);
            //    e.HasIndex(w => w.WhatsAppMessageId);
            //});

            //modelBuilder.Entity<WhatsAppUserPreferences>(e =>
            //{
            //    e.HasOne(w => w.User).WithMany().OnDelete(DeleteBehavior.Cascade);
            //    e.HasIndex(w => w.UserId).IsUnique();
            //});

            //modelBuilder.Entity<WhatsAppDeliveryReport>(e =>
            //{
            //    e.HasOne(w => w.NotificationLog).WithMany().OnDelete(DeleteBehavior.Cascade);
            //    e.HasIndex(w => w.WhatsAppMessageId);
            //    e.HasIndex(w => w.NotificationLogId);
            //    e.HasIndex(w => w.Status);
            //});

            //modelBuilder.Entity<WhatsAppNotificationConfig>(e =>
            //{
            //    e.HasIndex(w => w.NotificationType).IsUnique();
            //});

            // Configure SMS notification entities
            modelBuilder.Entity<SmsNotificationLog>(e =>
            {
                e.HasOne(s => s.User).WithMany().OnDelete(DeleteBehavior.NoAction);
                e.HasOne(s => s.Appointment).WithMany().OnDelete(DeleteBehavior.NoAction);
                e.HasIndex(s => s.UserId);
                e.HasIndex(s => s.AppointmentId);
                e.HasIndex(s => s.Status);
                e.HasIndex(s => s.CreatedAt);
                e.HasIndex(s => s.TwilioMessageSid);
                e.HasIndex(s => s.PhoneNumber);
            });

            modelBuilder.Entity<SmsNotificationConfig>(e =>
            {
                e.HasIndex(s => s.NotificationType).IsUnique();
            });

            // Seed default notification configurations
            SeedNotificationConfigurations(modelBuilder);
        }

        private void SeedNotificationConfigurations(ModelBuilder modelBuilder)
        {
            // Seed WhatsApp notification configurations
            //modelBuilder.Entity<WhatsAppNotificationConfig>().HasData(
            //    new WhatsAppNotificationConfig
            //    {
            //        Id = 1,
            //        NotificationType = "AppointmentBooked",
            //        TemplateName = "appointment_booked",
            //        IsEnabled = true,
            //        DelayMinutes = 0,
            //        MaxRetries = 3,
            //        RetryIntervalMinutes = 5,
            //        CreatedAt = DateTime.UtcNow
            //    },
            //    new WhatsAppNotificationConfig
            //    {
            //        Id = 2,
            //        NotificationType = "AppointmentConfirmed",
            //        TemplateName = "appointment_confirmed",
            //        IsEnabled = true,
            //        DelayMinutes = 0,
            //        MaxRetries = 3,
            //        RetryIntervalMinutes = 5,
            //        CreatedAt = DateTime.UtcNow
            //    },
            //    new WhatsAppNotificationConfig
            //    {
            //        Id = 3,
            //        NotificationType = "AppointmentCancelled",
            //        TemplateName = "appointment_cancelled",
            //        IsEnabled = true,
            //        DelayMinutes = 0,
            //        MaxRetries = 3,
            //        RetryIntervalMinutes = 5,
            //        CreatedAt = DateTime.UtcNow
            //    }
            //);

            // Seed SMS notification configurations
            modelBuilder.Entity<SmsNotificationConfig>().HasData(
                new SmsNotificationConfig
                {
                    Id = 1,
                    NotificationType = "VetNewAppointment",
                    IsEnabled = true,
                    DelayMinutes = 0,
                    MaxRetries = 3,
                    RetryIntervalMinutes = 5,
                    MessageTemplate = "New appointment: {PetOwnerName} on {Date} at {Time}",
                    EnableForVets = true,
                    EnableForPetOwners = false,
                    CreatedAt = new DateTime(2024,6,17, 8, 00, 1, 1, DateTimeKind.Utc) // Example date
                },
                new SmsNotificationConfig
                {
                    Id = 2,
                    NotificationType = "AppointmentConfirmed",
                    IsEnabled = true,
                    DelayMinutes = 0,
                    MaxRetries = 3,
                    RetryIntervalMinutes = 5,
                    MessageTemplate = "Appointment confirmed with Dr. {VetName} on {Date} at {Time}",
                    EnableForVets = false,
                    EnableForPetOwners = true,
                    CreatedAt = new DateTime(2024, 6, 17, 8, 00, 1, 1, DateTimeKind.Utc)
                },
                new SmsNotificationConfig
                {
                    Id = 3,
                    NotificationType = "AppointmentCancelled",
                    IsEnabled = true,
                    DelayMinutes = 0,
                    MaxRetries = 2,
                    RetryIntervalMinutes = 10,
                    MessageTemplate = "Appointment cancelled: {Date} at {Time}",
                    EnableForVets = true,
                    EnableForPetOwners = true,
                    CreatedAt = new DateTime(2024, 6, 17, 8, 00, 1, 1, DateTimeKind.Utc)
                },
                new SmsNotificationConfig
                {
                    Id = 4,
                    NotificationType = "AppointmentRescheduled",
                    IsEnabled = true,
                    DelayMinutes = 0,
                    MaxRetries = 3,
                    RetryIntervalMinutes = 5,
                    MessageTemplate = "Appointment rescheduled to {Date} at {Time}",
                    EnableForVets = true,
                    EnableForPetOwners = true,
                    CreatedAt = new DateTime(2024, 6, 17, 8, 00, 1, 1, DateTimeKind.Utc)
                }
            );
        }

    }
}
