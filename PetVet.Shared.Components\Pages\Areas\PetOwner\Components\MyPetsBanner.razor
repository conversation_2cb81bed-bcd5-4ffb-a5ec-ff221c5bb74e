﻿
<!-- Modern Pet Banner with Theme Styling -->
<div class="relative overflow-hidden rounded-3xl @GetBannerGradient() p-6 mb-6 transition-all duration-1000" id="banner">
    <!-- Decorative Elements -->
    <div class="absolute top-4 right-4 w-20 h-20 bg-white/10 rounded-full"></div>
    <div class="absolute bottom-4 left-4 w-16 h-16 bg-white/10 rounded-full"></div>
    <div class="absolute top-1/2 right-1/4 w-8 h-8 bg-white/5 rounded-full"></div>

    <div class="relative z-10 flex items-center justify-between">
        <!-- Text Content -->
        <div class="flex-1 pr-6">
            <h2 class="text-2xl font-bold text-white mb-2 leading-tight">
                Give your pets the spotlight.
            </h2>
            <p class="text-white/90 mb-6 text-lg">
                Build their profiles today!
            </p>
            <a href="petOwner/myPets/profile/create"
               class="inline-flex items-center gap-2 bg-white/20 hover:bg-white/30 text-white font-bold py-3 px-6 rounded-2xl transition-all transform hover:scale-105 backdrop-blur-sm border border-white/20">
                <i class="fas fa-plus text-sm"></i>
                <span>Create Profile</span>
            </a>
        </div>

        <!-- Pet Image -->
        <div class="flex-shrink-0">
            <img src="@bannerImage"
                 alt="Pet Image"
                 class="w-32 h-32 object-contain transition-all duration-1000 transform hover:scale-110"
                 id="bannerImage" />
        </div>
    </div>
</div>

@code {
    private int currentIndex = 0;
    private string bannerImage = "images/pets/animatedpet1.png";
    private string bannerBgColor = "#4A90E2";
    private Timer? _bannerTimer;



    private List<string> BannerImages = new List<string>
    {
        "images/pets/animatedpet1.png",
        "images/pets/animatedpet3.png",
        "images/pets/amimatedpet2.png"
    };

    private List<string> BannerGradients = new List<string>
    {
        "bg-gradient-to-br from-primary-400 to-primary-500",
        "bg-gradient-to-br from-secondary-400 to-primary-500",
        "bg-gradient-to-br from-primary-400 to-secondary-500"
    };

    protected override void OnInitialized()
    {
        _bannerTimer = new Timer(UpdateBanner, null, 0, 4000);
    }

    private void UpdateBanner(object? state)
    {
        currentIndex = (currentIndex + 1) % BannerImages.Count;
        bannerImage = BannerImages[currentIndex];
        InvokeAsync(StateHasChanged);
    }

    private string GetBannerGradient()
    {
        return BannerGradients[currentIndex];
    }

    public void Dispose()
    {
        _bannerTimer?.Dispose();
    }
}
