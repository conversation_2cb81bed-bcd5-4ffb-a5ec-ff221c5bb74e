﻿using PetVet.ServiceContracts.Features.Conversation;
using PetVet.Client.Common.Data;
using System.Security.Claims;
using PetVet.Cient.Common.Data;
using PetVet.Shared.Components.Framework;
using PetVet.Shared.Interfaces;
using PetVet.MauiApp.Helpers;
using PetVet.Platform.Razor;
namespace Platform.Client.Common.Features.Conversation;
public class ChatThreadsOfflineListingDataService : ClientSideListingDataService<ChatThreadsListingBusinessObject, ChatThreadsFilterBusinessObject>, IChatThreadsListingDataService
{

    private readonly AppDbContext _context;
    private readonly ILocalStorageService localStorageService;

    public ChatThreadsOfflineListingDataService(AppDbContext context, ILocalStorageService localStorageService)
    {
        _context = context;
        this.localStorageService = localStorageService;
    }

    public override IQueryable<ChatThreadsListingBusinessObject> GetQuery(ChatThreadsFilterBusinessObject filterBusinessObject)
    {
        var userId = Convert.ToInt32(Task.Run(() => (localStorageService.GetValue(ClaimTypes.NameIdentifier))).Result);
        var conversations = (from c in _context.Conversations
                             from p in _context.ConversationParticipants.Where(x => x.ConversationId == c.Id)
                             where p.UserId == userId
                             select c.Id).ToList();

        var query = (from c in _context.Conversations
                     from p in _context.ConversationParticipants.Where(x => x.ConversationId == c.Id)
                     where conversations.Contains(c.Id) && p.UserId != userId
                     select new ChatThreadsListingBusinessObject
                     {
                         Id = c.Id,
                         Avatar = p.DisplayPictureUrl,
                         Name = p.UserName,
                         FriendId = p.UserId,
                         LastMessage = _context.Messages.Where(x => x.ConversationId == c.Id)
                             .OrderByDescending(x => x.CreatedAt)
                             .Select(x => x.PlainContent)
                             .FirstOrDefault(),

                         LastMessageTime = _context.Messages.Where(x => x.ConversationId == c.Id)
                             .OrderByDescending(x => x.CreatedAt)
                             .Select(x => x.CreatedAt)
                             .FirstOrDefault()
                     });
        return query;
    }
}

public class ChatThreadsClientSideListingDataService : IChatThreadsListingDataService
{

    private readonly BaseHttpClient _httpClient;

    public ChatThreadsClientSideListingDataService(BaseHttpClient context)
    {
        _httpClient = context;
    }
    public async Task<PagedDataList<ChatThreadsListingBusinessObject>> GetPaginatedItems(ChatThreadsFilterBusinessObject filterBusinessObject)
    {
        return await _httpClient.GetFromJsonAsync<PagedDataList<ChatThreadsListingBusinessObject>>($"api/ChatThreadsListing/GetPaginatedItems" + filterBusinessObject.ToQueryString());
    }
}

