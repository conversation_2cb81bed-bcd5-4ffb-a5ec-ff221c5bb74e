﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

    <PropertyGroup>
		<EnableNativeDebugging>true</EnableNativeDebugging>

		<TargetFrameworks>net9.0-android</TargetFrameworks>
        
        <OutputType>Exe</OutputType>
        <RootNamespace>PetVet.Mobile</RootNamespace>
        <UseMaui>true</UseMaui>
        <SingleProject>true</SingleProject>
        <ImplicitUsings>enable</ImplicitUsings>
        <EnableDefaultCssItems>false</EnableDefaultCssItems>
        <Nullable>enable</Nullable>

        <!-- Display name -->
        <ApplicationTitle>PetVet</ApplicationTitle>
    
        <!-- App Identifier -->
        <ApplicationId>com.petvet.mobile</ApplicationId>

        <!-- Versions -->
        <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
        <ApplicationVersion>1</ApplicationVersion>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
	</PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net9.0-android|AnyCPU'">
      <AndroidPackageFormat>apk</AndroidPackageFormat>
    </PropertyGroup>

    <ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.png" Color="#512BD4" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splashicon.svg" Color="#3E97DA" BaseSize="500,500" />

        <!-- Images -->
        <MauiImage Include="Resources\Images\*" />
        <MauiImage Update="Resources\Images\dotnet_bot.svg" BaseSize="168,208" />

        <!-- Custom Fonts -->
        <MauiFont Include="Resources\Fonts\*" />

        <!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
        <MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />

		
    </ItemGroup>
 

    <ItemGroup>
        <PackageReference Include="Microsoft.Maui.Controls" Version="9.0.70" />
        <PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="9.0.70" />
        <PackageReference Include="Microsoft.AspNetCore.Components.WebView.Maui" Version="9.0.70" />
        <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.5" />
        <PackageReference Include="Plugin.FirebasePushNotification" Version="3.4.35" />
        <PackageReference Include="Plugin.Maui.Audio" Version="3.0.1" />

		<PackageReference Include="Azure.AI.OpenAI" Version="2.1.0" />
		<PackageReference Include="Azure.AI.OpenAI.Assistants" Version="1.0.0-beta.4" />

		<!-- Azure Communication Services - Removed for dummy implementation -->
		<!-- <PackageReference Include="Azure.Communication.Common" Version="1.3.0" /> -->
		<!-- <PackageReference Include="Azure.Communication.Identity" Version="1.3.1" /> -->
		<!-- <PackageReference Include="Azure.Communication.Chat" Version="1.3.0" /> -->
	</ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\MauiShared\Platform.Framework.Maui.csproj" />
      <ProjectReference Include="..\PetVet.Mobile.Data\PetVet.Mobile.Data.csproj" />
      <ProjectReference Include="..\PetVet.Shared.Components\PetVet.Shared.Components.csproj" />
      <ProjectReference Include="..\PetVet.Shared\PetVet.Shared.csproj" />
    </ItemGroup>
	<ItemGroup Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">
		<GoogleServicesJson Include="google-services.json" />
	</ItemGroup>



	<Target Name="Tailwind" BeforeTargets="Build">
		<exec command="npm run ui:build" />
	</Target>
</Project>
