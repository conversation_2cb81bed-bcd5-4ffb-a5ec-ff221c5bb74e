﻿@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
    --rz-input-height: 2.625rem;
    
    /* New Theme Colors - Custom Properties */
    --theme-primary: #3E97DA;
    --theme-primary-light: #61bcfd;
    --theme-primary-dark: #2875c7;
    --theme-secondary: #45C1A5;
    --theme-secondary-light: #4ade80;
    --theme-secondary-dark: #15993e;
    --theme-accent: #14b8a6;
}
/*** ARM Start ***/
@layer components {
    
    /* Updated Button Classes with New Theme */
    .btn {
        @apply inline-flex items-center justify-center gap-2 rounded-lg border px-3 py-2 text-center text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-4 disabled:opacity-50 disabled:cursor-not-allowed;
    }

    .btn-primary {
        @apply btn border-primary-600 bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-300 shadow-lg shadow-primary-500/25;
    }

    .btn-secondary {
        @apply btn border-secondary-600 bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-300 shadow-lg shadow-secondary-500/25;
    }

    .btn-outline-primary {
        @apply btn border-primary-600 bg-transparent text-primary-600 hover:bg-primary-600 hover:text-white focus:ring-primary-300;
    }

    .btn-outline-secondary {
        @apply btn border-secondary-600 bg-transparent text-secondary-600 hover:bg-secondary-600 hover:text-white focus:ring-secondary-300;
    }

    .btn-gradient-primary {
        @apply btn border-0 bg-gradient-to-r from-primary-500 to-primary-600 text-white hover:from-primary-600 hover:to-primary-700 focus:ring-primary-300 shadow-lg shadow-primary-500/30;
    }

    .btn-gradient-secondary {
        @apply btn border-0 bg-gradient-to-r from-secondary-500 to-secondary-600 text-white hover:from-secondary-600 hover:to-secondary-700 focus:ring-secondary-300 shadow-lg shadow-secondary-500/30;
    }

    .btn-gradient-accent {
        @apply btn border-0 bg-gradient-to-r from-primary-400 to-secondary-400 text-white hover:from-primary-500 hover:to-secondary-500 focus:ring-primary-300 shadow-lg;
    }

    .btn-danger {
        @apply btn border-red-600 bg-red-600 text-white hover:bg-red-700 focus:ring-red-300;
    }

    .btn-warning {
        @apply btn border-yellow-500 bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-300;
    }

    .btn-info {
        @apply btn border-blue-500 bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-300;
    }

    .btn-light {
        @apply btn border-gray-200 bg-white text-gray-700 hover:bg-gray-50 focus:ring-gray-300;
    }

    .btn-dark {
        @apply btn border-gray-800 bg-gray-800 text-white hover:bg-gray-900 focus:ring-gray-600;
    }

    .btn-close {
        @apply w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600 focus:outline-none;
    }

    .btn-close-white {
        @apply w-6 h-6 flex items-center justify-center text-white hover:text-gray-200 focus:outline-none;
    }

    .btn-xs {
        @apply text-xs px-2 py-1;
    }

    .btn-sm {
        @apply py-2;
    }

    .btn-base {
        @apply px-5 py-2.5 text-sm;
    }

    .btn-lg {
        @apply px-5 py-3 text-base;
    }

    .btn-xl {
        @apply px-6 py-3.5 text-base;
    }

    /* Professional Card Components */
    .card-theme-primary {
        @apply bg-gradient-to-br from-primary-50 to-white border border-primary-100 rounded-2xl shadow-lg shadow-primary-500/10 hover:shadow-xl hover:shadow-primary-500/15 transition-all duration-300;
    }

    .card-theme-secondary {
        @apply bg-gradient-to-br from-secondary-50 to-white border border-secondary-100 rounded-2xl shadow-lg shadow-secondary-500/10 hover:shadow-xl hover:shadow-secondary-500/15 transition-all duration-300;
    }

    .card-professional {
        @apply bg-white border border-gray-100 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden;
    }

    .card-header-primary {
        @apply bg-gradient-to-r from-primary-500 to-primary-600 text-white p-6 rounded-t-2xl;
    }

    .card-header-secondary {
        @apply bg-gradient-to-r from-secondary-500 to-secondary-600 text-white p-6 rounded-t-2xl;
    }

    /* Badge Components */
    .badge {
        @apply flex items-center justify-center rounded-md px-2.5 py-1 align-middle text-xs font-medium;
    }

    .badge-pink {
        @apply bg-pink-100 text-pink-800;
    }

    .badge-primary {
        @apply bg-primary-100 text-primary-800;
    }

    .badge-secondary {
        @apply bg-secondary-100 text-secondary-800;
    }

    .badge-green {
        @apply bg-green-100 text-green-800;
    }

    .badge-red {
        @apply bg-red-100 text-red-800;
    }

    .badge-yellow {
        @apply bg-yellow-100 text-yellow-800;
    }

    .badge-gray {
        @apply bg-gray-100 text-gray-800;
    }

    .notification-badge-primary {
        @apply bg-primary-600 shrink-0 flex h-5 w-5 items-center justify-center rounded-full text-xs text-white;
    }

    .notification-badge-secondary {
        @apply bg-secondary-600 shrink-0 flex h-5 w-5 items-center justify-center rounded-full text-xs text-white;
    }

    .notification-badge-red {
        @apply shrink-0 flex h-5 w-5 items-center justify-center rounded-full bg-red-600 text-xs text-white;
    }

    .pulse-primary {
        animation: pulse-primary 2s infinite;
    }

    .pulse-secondary {
        animation: pulse-secondary 2s infinite;
    }

    .pulse-red {
        animation: pulse 2s infinite;
        -webkit-animation-name: pulse-red;
        animation-name: pulse-red;
    }

    /* Professional Theme Navigation */
    .nav-theme {
        @apply bg-gradient-to-r from-primary-600 to-secondary-600 text-white shadow-lg;
    }

    .nav-theme .nav-link {
        @apply text-white/90 hover:text-white hover:bg-white/10 transition-all duration-200 rounded-lg px-3 py-2;
    }

    .nav-theme .nav-link.active {
        @apply text-white bg-white/20 font-medium;
    }

    /* Professional Form Styling */
    .label-dark {
        @apply text-sm font-medium text-gray-900;
    }

    .label-light {
        @apply text-sm font-medium text-gray-500;
    }

    .form-control {
        @apply block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-primary-600 focus:ring-primary-600 disabled:bg-gray-200 disabled:text-gray-400 transition-all duration-200;
    }

    .form-control:focus {
        @apply shadow-lg shadow-primary-500/10;
    }

    .form-select, .rz-dropdown {
        @apply block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 hover:!border-gray-300 focus:!outline focus:!outline-1 focus:!outline-primary-600 focus:!shadow-none focus:!border-primary-600 focus:!ring-primary-600;
    }

    .form-check {
        @apply text-primary-600 h-4 w-4 rounded border-gray-300 bg-gray-100 focus:ring-2 focus:ring-primary-600;
    }

    input[type="radio"].form-check {
        @apply rounded-full;
    }

    .form-switch {
        @apply relative flex cursor-pointer items-center gap-2;
    }

    .switch-style {
        @apply h-5 w-9 rounded-full bg-gray-200 after:absolute after:left-[2px] after:top-[2px] after:h-4 after:w-4 after:rounded-full after:border after:border-gray-300 after:bg-white after:transition-all after:content-[''] peer-checked:bg-primary-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 peer-checked:after:translate-x-full peer-checked:after:border-white;
    }

    .form-group {
        @apply flex w-full flex-col gap-2;
    }

    .breadcrumb {
        @apply flex items-center text-sm font-medium;
        .breadcrumb-item

    {
        @apply flex items-center;
        a

    {
        @apply flex items-center text-gray-700 hover:text-primary-600;
    }

    i {
        @apply text-sm rtl:rotate-180;
    }

    }

    li:not(:first-child)::before {
        @apply content-["\f054"] font-fa mx-2 font-semibold text-gray-400 rtl:rotate-180;
    }

    .active span {
        @apply text-gray-500;
    }

    }

    div.breadcrumb-item {
        a, span

    {
        @apply ms-1 md:ms-2;
    }

    }

    .pagination-container {
        @apply grid-cols-8 mx-auto grid w-fit items-center justify-center gap-2 px-2 py-2 md:flex md:px-4 md:gap-3 lg:flex-row;
        select

    {
        @apply col-span-2 order-3 block w-auto rounded-lg border border-gray-300 bg-gray-50 px-3 py-1.5 pr-7 text-sm text-gray-900 focus:ring-primary-600 focus:border-primary-600 md:order-3;
    }

    .results {
        @apply order-2 col-span-6 text-sm font-normal text-gray-500 md:order-1;
    }

    }

    .pagination {
        @apply order-1 col-span-8 mx-auto flex items-center -space-x-px md:order-2;
        .previous

    {
        @apply border-e-0 ms-0 flex h-[34px] items-center justify-center rounded-s-lg border border-gray-300 bg-white px-3 leading-tight text-gray-500 hover:bg-gray-100 hover:text-gray-700;
    }

    .next {
        @apply flex h-[34px] items-center justify-center rounded-e-lg border border-gray-300 bg-white px-3 leading-tight text-gray-500 hover:bg-gray-100 hover:text-gray-700;
    }

    .page-link {
        @apply flex h-[34px] items-center justify-center border border-gray-300 bg-white px-3 leading-tight text-gray-500 hover:bg-gray-100 hover:text-gray-700;
        &.active

    {
        @apply z-10 !text-primary-600 !border-primary-300 !bg-primary-50 hover:!bg-primary-100 hover:!text-primary-600;
    }

    }
}

.invalid-feedback {
    @apply text-sm text-red-600;
}

.chat-card {
    @apply flex w-full items-center gap-3 px-5 py-2 text-left hover:bg-primary-50 focus:outline-none focus-visible:bg-indigo-50;
    &.unread, &.active

{
    @apply bg-primary-100/50;
}

.chat-head {
    @apply flex items-center justify-between;
    h3

{
    @apply line-clamp-1 text-sm font-semibold text-gray-900;
}

.location {
    @apply shrink-0 flex items-center gap-2 text-xs;
    img

{
    @apply h-4 w-4;
}

}

time {
    @apply text-2xs;
}

}

.chat-content {
    @apply flex items-center justify-between;
    .inquiry

{
    @apply line-clamp-1 text-sm;
}

}

.msg {
    @apply flex items-center gap-1 text-xs;
    i

{
    @apply text-gray-400;
}

}
}


.alert {
    @apply mb-4 flex items-center gap-2 rounded-lg border p-4 text-sm;
}

.alert-danger {
    @apply border-red-300 bg-red-50 text-red-800;
}

.alert-success {
    @apply border-green-300 bg-green-50 text-green-800;
}

.alert-info {
    @apply border-blue-300 bg-blue-50 text-blue-800;
}

.alert-warning {
    @apply border-yellow-300 bg-yellow-50 text-yellow-800;
}

/* Modal Components */
.modal {
    @apply fixed inset-0 z-50 overflow-y-auto;
}

.modal.fade {
    @apply opacity-0 transition-opacity duration-300;
}

.modal.show {
    @apply opacity-100;
}

.modal-dialog {
    @apply relative w-auto mx-auto my-6 max-w-lg;
}

.modal-dialog-centered {
    @apply flex items-center min-h-screen;
}

.modal-dialog-scrollable {
    @apply max-h-screen overflow-hidden;
}

.modal-content {
    @apply relative flex flex-col w-full bg-white border border-gray-200 rounded-lg shadow-lg;
}

.modal-header {
    @apply flex items-center justify-between p-4 border-b border-gray-200 rounded-t-lg;
}

.modal-title {
    @apply text-lg font-semibold text-gray-900;
}

.modal-body {
    @apply relative p-4 flex-auto;
}

.modal-footer {
    @apply flex items-center justify-end p-4 space-x-2 border-t border-gray-200 rounded-b-lg;
}

.modal-backdrop {
    @apply fixed inset-0 bg-black bg-opacity-50 z-40;
}

.modal-backdrop.fade {
    @apply opacity-0 transition-opacity duration-300;
}

.modal-backdrop.show {
    @apply opacity-100;
}

/* Modal sizes */
.modal-sm .modal-dialog {
    @apply max-w-sm;
}

.modal-lg .modal-dialog {
    @apply max-w-4xl;
}

.modal-xl .modal-dialog {
    @apply max-w-6xl;
}

/* Bootstrap Grid System */
.container {
    @apply w-full mx-auto px-4;
}

.container-fluid {
    @apply w-full px-4;
}

.row {
    @apply flex flex-wrap -mx-2;
}

.col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6, .col-7, .col-8, .col-9, .col-10, .col-11, .col-12 {
    @apply px-2;
}

.col-1 { @apply w-1/12; }
.col-2 { @apply w-2/12; }
.col-3 { @apply w-3/12; }
.col-4 { @apply w-4/12; }
.col-5 { @apply w-5/12; }
.col-6 { @apply w-6/12; }
.col-7 { @apply w-7/12; }
.col-8 { @apply w-8/12; }
.col-9 { @apply w-9/12; }
.col-10 { @apply w-10/12; }
.col-11 { @apply w-11/12; }
.col-12 { @apply w-full; }

/* Bootstrap Utility Classes */
.d-block { @apply block; }
.d-flex { @apply flex; }
.d-none { @apply hidden; }
.d-inline { @apply inline; }
.d-inline-block { @apply inline-block; }

.justify-content-center { @apply justify-center; }
.justify-content-between { @apply justify-between; }
.justify-content-end { @apply justify-end; }
.justify-content-start { @apply justify-start; }

.align-items-center { @apply items-center; }
.align-items-start { @apply items-start; }
.align-items-end { @apply items-end; }
 
.text-dark { @apply text-gray-900; }
.text-muted { @apply text-gray-500; }

.bg-light { @apply bg-gray-50; }
.bg-dark { @apply bg-gray-900; }
.bg-primary { @apply bg-primary-600; }
.bg-secondary { @apply bg-gray-500; }
.bg-success { @apply bg-green-600; }
.bg-danger { @apply bg-red-600; }
.bg-warning { @apply bg-yellow-500; }
.bg-info { @apply bg-blue-500; }
 
.border-top { @apply border-t; }
.border-bottom { @apply border-b; }
.border-left { @apply border-l; }
.border-right { @apply border-r; }

.border-bottom-0 { @apply border-b-0; }
 
.rounded-top { @apply rounded-t; }
.rounded-bottom { @apply rounded-b; }
   
.w-100 { @apply w-full; }
.h-100 { @apply h-full; }

/* Font sizes */
.fs-1 { @apply text-5xl; }
.fs-2 { @apply text-4xl; }
.fs-3 { @apply text-3xl; }
.fs-4 { @apply text-2xl; }
.fs-5 { @apply text-xl; }
.fs-6 { @apply text-lg; }

/* Font weights */
.fw-bold { @apply font-bold; }
.fw-normal { @apply font-normal; }
.fw-light { @apply font-light; }

 
.text-decoration-none { @apply no-underline; }
 

/* Additional button sizes */
.btn-sm { @apply text-sm px-2 py-1; }
.btn-lg { @apply text-lg px-4 py-3; }

/* Form Controls */
.form-control {
    @apply block w-full px-3 py-2 text-base font-normal text-gray-700 bg-white bg-clip-padding border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-primary-600 focus:outline-none;
}

.form-control:focus {
    @apply border-primary-600 shadow-sm;
}

.form-floating {
    @apply relative;
}

.form-floating > .form-control {
    @apply h-14 leading-6;
}

.form-floating > label {
    @apply absolute text-gray-500 duration-300 transform -translate-y-4 scale-75 top-2 z-10 origin-[0] bg-white px-2 peer-focus:px-2 peer-focus:text-primary-600 peer-placeholder-shown:scale-100 peer-placeholder-shown:-translate-y-1/2 peer-placeholder-shown:top-1/2 peer-focus:top-2 peer-focus:scale-75 peer-focus:-translate-y-4 left-1;
}

.form-check {
    @apply w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 focus:ring-2;
}

.form-check-input {
    @apply w-4 h-4 text-primary-600 bg-gray-100 border-gray-300 rounded focus:ring-primary-500 focus:ring-2;
}

.form-check-label {
    @apply ml-2 text-sm font-medium text-gray-900;
}

.form-select {
    @apply block w-full px-3 py-2 text-base font-normal text-gray-700 bg-white bg-clip-padding border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-primary-600 focus:outline-none;
}

/* Navbar */
.navbar { @apply flex items-center justify-between; }
.navbar-dark { @apply text-white; }

.aside-nav {
    @apply -translate-x-full left-0 top-0 z-50 overflow-y-auto bg-white transition-transform lg:h-full lg:w-[5.6rem];
    &.open

{
    @apply drop-shadow-right fixed h-full w-full px-3 md:px-0 lg:drop-shadow lg:relative lg:w-[5.6rem];
}

&.close {
    @apply drop-shadow;
}

.nav {
    @apply flex flex-col gap-1 overflow-y-auto lg:gap-2 lg:px-2;
    .nav-link

{
    @apply flex items-center gap-1.5 rounded-lg p-3 text-left text-base font-medium text-gray-700 hover:bg-gray-100 hover:text-primary-700 lg:flex-col lg:text-center lg:pt-0.5 lg:pb-1 lg:px-1 lg:justify-center lg:gap-0 lg:text-2xs;
    &.active

{
    @apply text-primary-700 bg-primary-100;
}

i {
    @apply min-h-[21px] w-8 text-center text-xl md:w-auto;
}

}
}
}
}

.alert-banner {
    @apply z-[32] relative left-0 right-0 m-auto flex w-full items-center justify-center gap-2 px-2.5 py-2 text-center font-medium text-white lg:absolute lg:w-fit lg:rounded-lg lg:top-0.5 lg:h-11;
    p

{
    @apply flex items-center gap-2 text-center text-xs;
}

}
/*** ARM End ***/
/* @layer base{
  @font-face{
    font-family:"jameel-noori-nastaleeq";
    src:url("../fonts/jameel-noori-nastaleeq.ttf") format("woff"),
    url("../fonts/jameel-noori-nastaleeq.ttf") format("opentype"),
    url("../fonts/jameel-noori-nastaleeq.ttf") format("truetype");
  }
} */
[x-cloak] {
    display: none !important;
}

/*[x-disclosure\:panel]:has(li.select2-selection__choice) {
    @apply !block;
}*/

@media (min-width: 1024px) {
    /* Hide scrollbar for Chrome, Safari and Opera */
    ::-webkit-scrollbar-track {
        background-color: transparent;
    }

        ::-webkit-scrollbar-track:hover {
            @apply bg-gray-300;
        }

    ::-webkit-scrollbar-thumb {
        @apply bg-primary-400 rounded-full;
    }

        ::-webkit-scrollbar-thumb:hover {
            @apply bg-primary-600;
        }

    ::-webkit-scrollbar {
        width: 7px;
        height: 4px;
    }

    .scroll {
        @apply overflow-y-auto;
    }

        .scroll:active::-webkit-scrollbar-thumb,
        .scroll:focus::-webkit-scrollbar-thumb,
        .scroll:hover::-webkit-scrollbar-thumb {
            @apply visible;
        }

        .scroll::-webkit-scrollbar-thumb {
            @apply bg-primary-400 invisible rounded-full;
        }

            .scroll::-webkit-scrollbar-thumb:hover {
                @apply bg-primary-600;
            }
}

.city-name::before {
    content: "";
    height: 50%;
    background: linear-gradient(0deg, #000000 0%, rgba(0, 0, 0, 0) 50.17%);
    z-index: 0;
    @apply absolute bottom-0 w-full rounded-lg md:rounded-xl;
}

.swiper-button-disabled {
    opacity: .35;
    cursor: auto;
    pointer-events: none;
}

.swiper-pagination-bullet {
    width: var(--swiper-pagination-bullet-width,var(--swiper-pagination-bullet-size,16px)) !important;
    height: var(--swiper-pagination-bullet-height,var(--swiper-pagination-bullet-size,16px)) !important;
}

.swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet, .swiper-pagination-horizontal.swiper-pagination-bullets .swiper-pagination-bullet {
    margin: 0 var(--swiper-pagination-bullet-horizontal-gap,6px) !important;
}

/* <link rel="stylesheet" href="https://unpkg.com/tippy.js@6/dist/tippy.css" /> */
.tippy-box[data-animation=fade][data-state=hidden] {
    opacity: 0
}

[data-tippy-root] {
    max-width: calc(100vw - 10px)
}

.tippy-box {
    position: relative;
    background-color: #333;
    color: #fff;
    border-radius: 4px;
    font-size: 14px;
    line-height: 1.4;
    white-space: normal;
    outline: 0;
    transition-property: transform,visibility,opacity
}

    .tippy-box[data-placement^=top] > .tippy-arrow {
        bottom: 0
    }

        .tippy-box[data-placement^=top] > .tippy-arrow:before {
            bottom: -7px;
            left: 0;
            border-width: 8px 8px 0;
            border-top-color: initial;
            transform-origin: center top
        }

    .tippy-box[data-placement^=bottom] > .tippy-arrow {
        top: 0
    }

        .tippy-box[data-placement^=bottom] > .tippy-arrow:before {
            top: -7px;
            left: 0;
            border-width: 0 8px 8px;
            border-bottom-color: initial;
            transform-origin: center bottom
        }

    .tippy-box[data-placement^=left] > .tippy-arrow {
        right: 0
    }

        .tippy-box[data-placement^=left] > .tippy-arrow:before {
            border-width: 8px 0 8px 8px;
            border-left-color: initial;
            right: -7px;
            transform-origin: center left
        }

    .tippy-box[data-placement^=right] > .tippy-arrow {
        left: 0
    }

        .tippy-box[data-placement^=right] > .tippy-arrow:before {
            left: -7px;
            border-width: 8px 8px 8px 0;
            border-right-color: initial;
            transform-origin: center right
        }

    .tippy-box[data-inertia][data-state=visible] {
        transition-timing-function: cubic-bezier(.54,1.5,.38,1.11)
    }

.tippy-arrow {
    width: 16px;
    height: 16px;
    color: #333
}

    .tippy-arrow:before {
        content: "";
        position: absolute;
        border-color: transparent;
        border-style: solid
    }

.tippy-content {
    position: relative;
    padding: 5px 9px;
    z-index: 1
}

/* ::-webkit-scrollbar:hover{
  width: 4px;
} */
/* .scrollbar-hide {
	scrollbar-width: auto;
} */
/*.scrollbar-hide::-webkit-scrollbar {
   display: none; 
} */
/* Hide scrollbar for IE, Edge and Firefox */
/*.scrollbar-hide {*/
/* -ms-overflow-style: none;   */
/* IE and Edge */
/* scrollbar-width: none;   */
/* Firefox */
/*}*/



/*** Select 2 ***/

.select2-container--default .select2-selection--single .select2-selection__rendered {
    line-height: 24px;
}

.select2-selection.select2-selection--single {
    height: 46px;
}

.select2-results__options[aria-multiselectable="true"] .select2-results__option, .select2-results__options .select2-results__option {
    padding-right: 20px;
    vertical-align: middle;
}

    .select2-results__options[aria-multiselectable="true"] .select2-results__option:before, .select2-results__options .select2-results__option:before {
        content: "";
        display: inline-block;
        position: relative;
        height: 20px;
        width: 20px;
        border: 2px solid #e9e9e9;
        border-radius: 4px;
        background-color: #fff;
        margin-right: 20px;
        vertical-align: middle;
    }

    .select2-results__options[aria-multiselectable="true"] .select2-results__option.select2-results__option--selected:before, .select2-results__options .select2-results__option.select2-results__option--selected:before {
        content: "\2713";
        color: #fff;
        background-color: #673AB7;
        border: 0;
        display: inline-block;
        padding-left: 5px;
        font-size: 15px;
    }

.select2-results__options[aria-multiselectable="true"] .select2-results__message.select2-results__option:before, .select2-results__options .select2-results__message.select2-results__option:before {
    content: "";
    margin-right: 0;
    height: 0;
    width: 0;
}


.select2-selection.select2-selection--multiple, .select2-selection.select2-selection--single {
    @apply block w-full !rounded-lg !border !border-solid !border-gray-300 !bg-gray-50 px-4 !py-3 text-xs !font-medium font-normal text-gray-500;
}

.select2-container--default.select2-container--focus .select2-selection--multiple, .select2-container--default.select2-container--focus .select2-selection--single {
    @apply !border-primary-600 outline-2 !border outline-red-500;
}

.select2-container--default .select2-search--inline .select2-search__field {
    @apply !mt-0 h-5;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice, .select2-container--default .select2-selection--single .select2-selection__choice {
    @apply bg-primary-100 text-primary-700 border-0 py-0.5 pl-1 pr-4;
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove, .select2-container--default .select2-selection--single .select2-selection__choice__remove {
    @apply text-primary-600 border-r-0 left-[auto] right-0 -mt-1 pl-1 pr-1 text-lg;
}

.select2-results__options[aria-multiselectable=true] .select2-results__option, .select2-results__options .select2-results__option {
    @apply text-xs;
}

.select2-container--default .select2-results__option--highlighted.select2-results__option--selectable {
    @apply bg-primary-600 text-xs;
}

.select2-results__options[aria-multiselectable=true] .select2-results__option.select2-results__option--selected:before, .select2-results__options .select2-results__option.select2-results__option--selected:before {
    @apply pt-[3px];
}


[popover]:popover-open {
    position: absolute;
    inset: unset;
    left: 24px;
    margin: 0;
}

/*[popover]:popover-open button{
    @apply md:hidden; 
}*/

[popover].info::before {
    content: '💡 ';
}

[popover].success::before {
    content: '✅ ';
}

[popover].warning::before {
    content: '⚠️ ';
}

[popover].danger::before {
    content: '🚨 ';
}

.judgement-detail a {
    @apply text-primary-600 font-medium;
}



/*** Congratulations animation css ***/

@keyframes confetti-slow {
    0% {
        transform: translate3d(0, 0, 0) rotateX(0) rotateY(0);
    }

    100% {
        transform: translate3d(25px, 105vh, 0) rotateX(360deg) rotateY(180deg);
    }
}

@keyframes confetti-medium {
    0% {
        transform: translate3d(0, 0, 0) rotateX(0) rotateY(0);
    }

    100% {
        transform: translate3d(100px, 105vh, 0) rotateX(100deg) rotateY(360deg);
    }
}

@keyframes confetti-fast {
    0% {
        transform: translate3d(0, 0, 0) rotateX(0) rotateY(0);
    }

    100% {
        transform: translate3d(-50px, 105vh, 0) rotateX(10deg) rotateY(250deg);
    }
}

.confetti-container {
    perspective: 700px;
    position: absolute;
    overflow: hidden;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.confetti {
    position: absolute;
    z-index: 1;
    top: -10px;
    border-radius: 0%;
}

.confetti--animation-slow {
    animation: confetti-slow 2.25s linear 1 forwards;
}

.confetti--animation-medium {
    animation: confetti-medium 1.75s linear 1 forwards;
}

.confetti--animation-fast {
    animation: confetti-fast 1.25s linear 1 forwards;
}

/* Checkmark */
.checkmark-circle {
    width: 150px;
    height: 150px;
    position: relative;
    display: inline-block;
    vertical-align: top;
    margin-left: auto;
    margin-right: auto;
}

    .checkmark-circle .background {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        background: #00C09D;
        position: absolute;
    }

    .checkmark-circle .checkmark {
        border-radius: 5px;
    }

        .checkmark-circle .checkmark.draw:after {
            -webkit-animation-delay: 100ms;
            -moz-animation-delay: 100ms;
            animation-delay: 100ms;
            -webkit-animation-duration: 3s;
            -moz-animation-duration: 3s;
            animation-duration: 3s;
            -webkit-animation-timing-function: ease;
            -moz-animation-timing-function: ease;
            animation-timing-function: ease;
            -webkit-animation-name: checkmark;
            -moz-animation-name: checkmark;
            animation-name: checkmark;
            -webkit-transform: scaleX(-1) rotate(135deg);
            -moz-transform: scaleX(-1) rotate(135deg);
            -ms-transform: scaleX(-1) rotate(135deg);
            -o-transform: scaleX(-1) rotate(135deg);
            transform: scaleX(-1) rotate(135deg);
            -webkit-animation-fill-mode: forwards;
            -moz-animation-fill-mode: forwards;
            animation-fill-mode: forwards;
        }

        .checkmark-circle .checkmark:after {
            opacity: 1;
            height: 75px;
            width: 37.5px;
            -webkit-transform-origin: left top;
            -moz-transform-origin: left top;
            -ms-transform-origin: left top;
            -o-transform-origin: left top;
            transform-origin: left top;
            border-right: 15px solid white;
            border-top: 15px solid white;
            border-radius: 2.5px !important;
            content: "";
            left: 25px;
            top: 75px;
            position: absolute;
        }

@-webkit-keyframes checkmark {
    0% {
        height: 0;
        width: 0;
        opacity: 1;
    }

    20% {
        height: 0;
        width: 37.5px;
        opacity: 1;
    }

    40% {
        height: 75px;
        width: 37.5px;
        opacity: 1;
    }

    100% {
        height: 75px;
        width: 37.5px;
        opacity: 1;
    }
}

@-moz-keyframes checkmark {
    0% {
        height: 0;
        width: 0;
        opacity: 1;
    }

    20% {
        height: 0;
        width: 37.5px;
        opacity: 1;
    }

    40% {
        height: 75px;
        width: 37.5px;
        opacity: 1;
    }

    100% {
        height: 75px;
        width: 37.5px;
        opacity: 1;
    }
}

@keyframes checkmark {
    0% {
        height: 0;
        width: 0;
        opacity: 1;
    }

    20% {
        height: 0;
        width: 37.5px;
        opacity: 1;
    }

    40% {
        height: 75px;
        width: 37.5px;
        opacity: 1;
    }

    100% {
        height: 75px;
        width: 37.5px;
        opacity: 1;
    }
}

/*** Dialog Height ***/
dialog::backdrop {
    @apply bg-blue-950/60;
}

@media (min-height: 500px) and (min-width: 500px) {
    .wizard-dialog {
        @apply h-[calc(100dvh-8rem)];
    }
}

@media (min-height: 900px) {
    .wizard-dialog {
        @apply h-[calc(100dvh-21rem)];
    }
}

/*** subscription alert ***/
body:has(.nosubscription) .ktcontainer {
    @apply md:!h-[calc(100vh-5.5rem)];
}

@media (max-width: 768px) {
    body:has(.nosubscription + header.hidden) .ktcontainer {
        @apply h-[calc(100dvh-5rem)];
    }
}


.loading-progress {
    position: relative;
    display: block;
    width: 8rem;
    height: 8rem;
    margin: 20vh auto 1rem auto;
}

    .loading-progress circle {
        fill: none;
        stroke: #e0e0e0;
        stroke-width: 0.6rem;
        transform-origin: 50% 50%;
        transform: rotate(-90deg);
    }

        .loading-progress circle:last-child {
            stroke: #5152b1;
            stroke-dasharray: calc(3.141 * var(--blazor-load-percentage, 0%) * 0.8), 500%;
            transition: stroke-dasharray 0.05s ease-in-out;
        }

.loading-progress-text {
    position: absolute;
    text-align: center;
    font-weight: bold;
    inset: calc(20vh + 3.25rem) 0 auto 0.2rem;
    z-index: -1;
}

    .loading-progress-text:after {
        content: var(--blazor-load-percentage-text, "Loading");
    }

.kt-radio {
    @apply text-primary-600 mr-2 h-4 w-4 border-gray-300 bg-gray-100 focus:ring-primary-600 focus:ring-1;
}

.kt-label {
    @apply text-sm font-medium text-gray-900;
}


/*** ARM Start ***/
.account-container {
    @apply relative flex grow flex-col overflow-hidden bg-white;
    h1

{
    @apply flex text-2xl font-bold text-white focus:outline-none md:text-4xl;
}

.logo {
    @apply h-12 md:h-16;
}

}

.account-grid {
    @apply grid h-full overflow-visible overflow-x-hidden md:grid-cols-2;
}

.account-left {
    @apply from-primary-600 via-primary-600 relative hidden items-center justify-center bg-gradient-to-r to-pink-500 md:flex;
    section

{
    @apply flex max-w-md flex-col gap-4 p-5 md:p-8 md:max-w-2xl md:gap-8;
}

}

.account-right {
    @apply relative top-0 grid h-full flex-col overflow-visible overflow-y-auto p-5 md:items-center lg:p-7;
    section

{
    @apply mx-auto w-full max-w-sm;
}

h2 {
    @apply text-2xl font-semibold text-gray-900 md:text-3xl;
}

p {
    @apply text-xs font-medium text-gray-900 md:text-base;
}

.text-link {
    @apply text-primary-600 hover:text-primary-700 hover:underline;
}

}

.intro-list {
    @apply flex flex-col space-y-4;
    li

{
    @apply flex items-center gap-2 text-xs font-medium text-white md:text-lg;
}

}

.rz-dropdown-trigger .rzi-chevron-down:before {
    content: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3E%3C/svg%3E");
}

.rz-dropdown-items li:hover, .rz-dropdown-item:hover {
    @apply bg-primary-50 text-primary-600;
}

.rz-dropdown-filter-container .rz-inputtext {
    @apply rounded-lg
}
/*** ARM End ***/
.rz-dropdown {
    @apply form-control;
}

/*** Asad Start ***/
.package-tab {
    @apply flex items-center justify-between gap-1 bg-white px-2 py-4 md:justify-center md:border-b-2 md:px-4;
}

/*** Asad End ***/

.chat-message-received {
    @apply w-fit max-w-[80%] rounded-r-2xl rounded-bl-2xl bg-slate-100 px-2 pb-2 pt-1.5 text-xs font-medium text-gray-900 drop-shadow-sm;
}

.chat-message-sent {
    @apply ml-auto w-fit max-w-[80%] rounded-l-2xl rounded-br-2xl bg-emerald-100 px-2 pb-2 pt-1.5 text-xs font-medium text-gray-900 drop-shadow-sm;
}

.read-more-link {
    @apply text-primary-600 cursor-pointer hover:text-primary-700;
}

.messages time {
    @apply z-10 text-2xs relative float-right -mb-[5px] ml-1 mt-[0.500rem];
}

.chat-bubble {
    @apply bg-primary-600 relative bottom-2 right-2 ml-auto flex h-12 w-12 items-center justify-center rounded-full text-xl text-white hover:bg-primary-700 focus:ring-4 focus:outline-none focus:ring-primary-300;
    .counter

{
    @apply text-3xs absolute -top-1 left-2 h-3 w-3 rounded-full bg-red-600;
}

}

@-webkit-keyframes pulse-primary {
    0% {
        -webkit-box-shadow: 0 0 0 rgba(62, 151, 218, 0.6);
    }

    70% {
        -webkit-box-shadow: 0 0 0 10px rgba(62, 151, 218, 0);
    }

    100% {
        -webkit-box-shadow: 0 0 0 0 rgba(62, 151, 218, 0);
    }
}

@-webkit-keyframes pulse-secondary {
    0% {
        -webkit-box-shadow: 0 0 0 rgba(69, 193, 165, 0.6);
    }

    70% {
        -webkit-box-shadow: 0 0 0 10px rgba(69, 193, 165, 0);
    }

    100% {
        -webkit-box-shadow: 0 0 0 0 rgba(69, 193, 165, 0);
    }
}

@-webkit-keyframes pulse-red {
    0% {
        -webkit-box-shadow: 0 0 0 rgba(235, 87, 87, 0.6);
    }

    70% {
        -webkit-box-shadow: 0 0 0 10px rgba(235, 87, 87, 0);
    }

    100% {
        -webkit-box-shadow: 0 0 0 0 rgba(235, 87, 87, 0);
    }
}

@keyframes pulse-primary {
    0% {
        -moz-box-shadow: 0 0 0 rgba(62, 151, 218, 0.6);
        box-shadow: 0 0 0 rgba(62, 151, 218, 0.6);
    }

    70% {
        -moz-box-shadow: 0 0 0 10px rgba(62, 151, 218, 0);
        box-shadow: 0 0 0 10px rgba(62, 151, 218, 0);
    }

    100% {
        -moz-box-shadow: 0 0 0 0 rgba(62, 151, 218, 0);
        box-shadow: 0 0 0 0 rgba(62, 151, 218, 0);
    }
}

@keyframes pulse-secondary {
    0% {
        -moz-box-shadow: 0 0 0 rgba(69, 193, 165, 0.6);
        box-shadow: 0 0 0 rgba(69, 193, 165, 0.6);
    }

    70% {
        -moz-box-shadow: 0 0 0 10px rgba(69, 193, 165, 0);
        box-shadow: 0 0 0 10px rgba(69, 193, 165, 0);
    }

    100% {
        -moz-box-shadow: 0 0 0 0 rgba(69, 193, 165, 0);
        box-shadow: 0 0 0 0 rgba(69, 193, 165, 0);
    }
}

@keyframes pulse-red {
    0% {
        -moz-box-shadow: 0 0 0 rgba(235, 87, 87, 0.6);
        box-shadow: 0 0 0 rgba(235, 87, 87, 0.6);
    }

    70% {
        -moz-box-shadow: 0 0 0 10px rgba(235, 87, 87, 0);
        box-shadow: 0 0 0 10px rgba(235, 87, 87, 0);
    }

    100% {
        -moz-box-shadow: 0 0 0 0 rgba(235, 87, 87, 0);
        box-shadow: 0 0 0 0 rgba(235, 87, 87, 0);
    }
}


@layer components {
    .results-container {
      @apply mx-auto max-w-6xl bg-gray-50 p-4;
    }
  
    .results-layout {
      @apply flex flex-wrap gap-3;
    }
  
    .results-section {
      @apply rounded-xl bg-white shadow-[0px_1px_2px_0px_rgba(0,0,0,0.08)] px-8 py-4;
      
      &.--main { @apply flex-1; }
      &.--sidebar { @apply w-64; }
    }
  
    .section-heading {
      @apply mb-6 border-b border-gray-200 pb-3 text-center text-xl font-semibold;
    }
  
    .race-container {
      @apply mb-8 flex;
    }
  
    .race-title-block {
      @apply mr-6 flex w-48 items-center justify-center rounded-lg bg-gray-200 p-4 font-medium;
    }
  
    .candidates-grid {
      @apply flex flex-wrap gap-4;
    }
  
    .candidate-card {
      @apply justify-items-center rounded-lg bg-primary-50 p-2 text-center;
      
      &.--winner { @apply mx-auto w-fit; }
    }
  
    .card-image {
      @apply mb-2 h-24 w-24 rounded-lg object-cover;
    }
  
    .card-name {
      @apply text-xs font-bold text-gray-700;
    }
  
    .card-votes {
      @apply text-xs text-primary-500;
    }
  }


.featured-profile-card {
    @apply bg-white rounded-2xl p-3 lg:p-6 shadow-[0px_1px_2px_0px_rgba(0,0,0,0.20)] hover:shadow-xl transition-all duration-200;
}

.card-header {
    @apply flex flex-col items-start gap-3;
}

.icon-wrapper {
    @apply w-12 h-12 flex items-center justify-center shrink-0;
}

.card-content {
    @apply flex-grow;
}

.card-title {
    @apply text-base font-semibold text-gray-900 mb-1;
}

.card-description {
    @apply text-xs text-gray-500;
}

.stats-row {
    @apply grid grid-cols-2 gap-1 mt-3;
}

.stat-badge {
    @apply px-1.5 lg:px-6 py-1 lg:py-2 rounded lg:rounded-lg text-white flex justify-between items-center gap-2;
}

.stat-number {
    @apply text-xs lg:text-xl font-bold;
}

.stat-label {
    @apply text-xs lg:text-base font-medium;
}

.small-cards-grid {
    @apply grid grid-cols-2 md:grid-cols-3 gap-2 lg:gap-6;
}

.small-card {
    @apply featured-profile-card flex flex-col h-full;
}