module.exports = {
    content: {
        files: [
            "../PetVet.Shared.Components/**/*.{razor,cshtml,cs,html}",
            "**/*.{razor,cshtml,cs,html}",
        ],
        transform: {
            // `razor` here is the file extension of your razor view files
            razor: (content) => content.replace(/@@/g, "@"),
        }
    },
    darkMode: 'class',
    theme: {
        container: {
            center: true,
            padding: '1rem',
            screens: {
                sm: '640px',
                md: '768px',
                lg: '1024px',
                xl: '1152px',
            }
        }, 
        extend: {
            fontSize: {
                '2xs': ['0.625rem', '0.75rem'],
                '3xs': ['0.5rem', '0.625rem'],
            },
            spacing: {
                '18': '4.5rem'
            },
            boxShadow: {
                'xs': '0px 0px 1px rgba(0, 0, 0, 0.25)',
            },
            colors: {
                primary: {
                    '50': '#eff8ff',
                    '100': '#dbefff',
                    '200': '#c0e4ff',
                    '300': '#94d5ff',
                    '400': '#61bcfd',
                    '500': '#3E97DA',
                    '600': '#3E97DA',
                    '700': '#2875c7',
                    '800': '#2562a1',
                    '900': '#25537f',
                    '950': '#1a3450',
                },
                secondary: {
                    '50': '#f0fdf6',
                    '100': '#dcfce8',
                    '200': '#bbf7d2',
                    '300': '#86efad',
                    '400': '#4ade80',
                    '500': '#45C1A5',
                    '600': '#45C1A5',
                    '700': '#15993e',
                    '800': '#16783a',
                    '900': '#146233',
                    '950': '#073617',
                },
                accent: {
                    '50': '#f0fdfa',
                    '100': '#ccfbf1',
                    '200': '#99f6e4',
                    '300': '#5eead4',
                    '400': '#2dd4bf',
                    '500': '#14b8a6',
                    '600': '#0d9488',
                    '700': '#0f766e',
                    '800': '#115e59',
                    '900': '#134e4a',
                    '950': '#042f2e',
                },
                success: {
                    '50': '#f0fdf4',
                    '100': '#dcfce7',
                    '200': '#bbf7d0',
                    '300': '#86efac',
                    '400': '#4ade80',
                    '500': '#22c55e',
                    '600': '#16a34a',
                    '700': '#15803d',
                    '800': '#166534',
                    '900': '#14532d',
                    '950': '#052e16',
                },
                warning: {
                    '50': '#fffbeb',
                    '100': '#fef3c7',
                    '200': '#fde68a',
                    '300': '#fcd34d',
                    '400': '#fbbf24',
                    '500': '#f59e0b',
                    '600': '#d97706',
                    '700': '#b45309',
                    '800': '#92400e',
                    '900': '#78350f',
                    '950': '#451a03',
                },
                error: {
                    '50': '#fef2f2',
                    '100': '#fee2e2',
                    '200': '#fecaca',
                    '300': '#fca5a5',
                    '400': '#f87171',
                    '500': '#ef4444',
                    '600': '#dc2626',
                    '700': '#b91c1c',
                    '800': '#991b1b',
                    '900': '#7f1d1d',
                    '950': '#450a0a',
                },
            },
            dropShadow: {
                'top': [
                    '0 -5px 8px rgb(0 0 0 / 10%)',
                    '0 -8px 5px rgb(0 0 0 / 6%)'
                ],
                'right': [
                    '0 0px 40px rgba(0,0,0,0.4)',
                    '0 0px 36px rgb(0 0 0/38%)'
                ]
            },

            keyframes: {
                fadeIn: {
                    '0%': { opacity: '0' },
                    '100%': { opacity: '1' },
                },

                fadeIn2: {
                    '50%': { opacity: '0.5' },
                    '100%': { opacity: '1' },
                },
            },
            animation: {
                fadeIn: 'fadeIn .15s ease-in-out',
                fadeIn2: 'fadeIn2 0.25s ease-in-out',
                fadeIn1Sec: 'fadeIn 1s ease-in-out',
            },
            height: {
                '100vh-32rem': 'calc(100vh - 32rem)',
                '100vh-16rem': 'calc(100vh - 16rem)',
            },

        },
    },
    plugins: [
        require('@tailwindcss/container-queries'),
        require('@tailwindcss/forms'),
        require('tailwindcss'),
        require('autoprefixer')
    
    ],
}
