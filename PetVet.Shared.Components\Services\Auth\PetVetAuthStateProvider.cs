﻿using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using PetVet.Shared.IStorage;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;

namespace PetVet.Shared.Components.Services.Auth
{
    public class PetVetAuthStateProvider : AuthenticationStateProvider
    {
        private const string AuthType = "PetVet-Auth";
        private const string UserDataKey = "udata";

        private Task<AuthenticationState> _authStateTask;
        private readonly IStorageService _storageService;
        private readonly NavigationManager _NavigationManager;

        public PetVetAuthStateProvider(IStorageService storageService, NavigationManager NavigationManager)
        {
            _storageService = storageService;
            _NavigationManager = NavigationManager;
            SetAuthStateTask();
        }

        public override Task<AuthenticationState> GetAuthenticationStateAsync() => _authStateTask;

        public LoggedInUser User { get; private set; }
        public bool IsLoggedIn => User?.Id > 0;

        public async Task SetLoginAsync(LoggedInUser user)
        {
            User = user;
            SetAuthStateTask();
            NotifyAuthenticationStateChanged(_authStateTask);
            await _storageService.SetItem(UserDataKey, user.ToJson());
            await _storageService.SetItem(ClaimTypes.NameIdentifier, user.Id.ToString()); // Store token separately if needed
            await _storageService.SetItem(ClaimTypes.Name, user.Name);
        }


        public async Task SetLogoutAsync()
        {
            User = null;
            SetAuthStateTask();
            NotifyAuthenticationStateChanged(_authStateTask);
            await _storageService.RemoveItem(UserDataKey);
        }



        public bool IsInitializing { get; private set; } = true;

        public async Task InitializeAsync()
        {
            await InitializeAsync(redirectToLogin: true);
        }
        public async Task<bool> InitializeAsync(bool redirectToLogin = true)
        {
            try
            {
                var udata = await _storageService.GetItem(UserDataKey);

                if (string.IsNullOrWhiteSpace(udata))
                {
                    // User data/state is not there in the browser's storage
                    if (redirectToLogin)
                    {
                        RedirectToLogin();
                    }

                    return false;
                }

                var user = LoggedInUser.LoadFrom(udata);

                if (user == null || user.Id == 0)
                {
                    // User data/state is not valid
                    if (redirectToLogin)
                    {
                        RedirectToLogin();
                    }

                    return false;
                }

                // Check if JWT Token is still Valid (not expired)
                if (!IsTokenValid(user.Token))
                {
                    // Token is expired
                    if (redirectToLogin)
                    {
                        RedirectToLogin();
                    }

                    return false;
                }

                await SetLoginAsync(user);
                return true;
            }
            catch (Exception ex)
            {
                // TODO: Fix this error
                // SetLoginAsync from this InitializeAsync method throws
                // Collection was modified - Enueration has changed on the NotifyAuthenticationStateChanged  
                // in the SetLoginAsync method  
            }
            finally
            {
                IsInitializing = false;
            }
            return false;
        }


        private void RedirectToLogin()
        {
            _NavigationManager.NavigateTo("auth/pre/login");
        }


        private static bool IsTokenValid(string token)
        {
            if (string.IsNullOrWhiteSpace(token))
            {
                return false;
            }

            var jwtHandler = new JwtSecurityTokenHandler();

            if (!jwtHandler.CanReadToken(token))
            {
                return false;
            }

            var jwt = jwtHandler.ReadJwtToken(token);
            var expClaim = jwt.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Exp);

            if (expClaim == null)
            {
                return false;
            }

            var expTime = long.Parse(expClaim.Value);

            var expUtcDateTime = DateTimeOffset.FromUnixTimeSeconds(expTime).UtcDateTime;

            return expUtcDateTime > DateTime.UtcNow;
        }


        private void SetAuthStateTask()
        {
            if (IsLoggedIn)
            {
                var identity = new ClaimsIdentity(User.ToClaims(), AuthType);
                var principal = new ClaimsPrincipal(identity);
                var authState = new AuthenticationState(principal);

                _authStateTask = Task.FromResult(authState);
            }
            else
            {
                var identity = new ClaimsIdentity();
                var principal = new ClaimsPrincipal(identity);
                var authState = new AuthenticationState(principal);

                _authStateTask = Task.FromResult(authState);
            }
        }
    }
}
