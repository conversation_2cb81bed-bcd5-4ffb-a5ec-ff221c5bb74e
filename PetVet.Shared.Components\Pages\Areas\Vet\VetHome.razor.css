﻿.Vet-Home-CONTAINER {
    background: #f2f6f9;
    padding: 2rem;
    min-height: 100vh;
    width: 100%;
    max-width: 72rem;
    margin: 0 auto;
    font-family: 'Inter', sans-serif;
}

.banner-section {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #157BAB;
    border-radius: 1.25rem;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.banner-content {
    flex: 1;
    padding-right: 2rem;
}

.welcome-heading {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    font-family: 'Acme', sans-serif;
}

.welcome-text {
    font-size: 1.125rem;
    line-height: 1.6;
    opacity: 0.9;
    
}

.banner-image-container {
    flex: 1;
    max-width: 500px;
}

.banner-image {
    width: 100%;
    height: 200px;
    border-radius: 1.25rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2);
    object-fit: cover;
}

.action-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.action-card {
    background: white;
    border-radius: 1.25rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

    .action-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.15);
    }

.card-icon {
    color: #157BAB;
    margin-bottom: 1rem;
}

.home-icon {
    width: 2.5rem;
    height: 2.5rem;
    margin-bottom: 0.75rem;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.card-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 0.75rem;
    font-family: 'Acme', sans-serif;
}

.card-description {
    color: #6B7280;
    font-size: 0.875rem;
    line-height: 1.5;
    
}

@media (max-width: 768px) {
    .Vet-Home-CONTAINER {
        padding: 1rem;
    }

    .banner-section {
        flex-direction: column;
        text-align: center;
        padding: 1.5rem;
    }

    .banner-content {
        padding-right: 0;
        margin-bottom: 1.5rem;
    }

    .welcome-heading {
        font-size: 2rem;
    }

    .banner-image-container {
        max-width: 100%;
    }

    .action-cards {
        grid-template-columns: 1fr;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.Vet-Home-CONTAINER {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
