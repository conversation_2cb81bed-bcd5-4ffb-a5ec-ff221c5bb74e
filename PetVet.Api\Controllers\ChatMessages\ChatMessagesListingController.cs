﻿using PetVet.ServiceContracts.Features.Conversation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PetVet.Shared.Interfaces;
namespace PetVet.Server.WebApis.Controller.Conversation;
[ApiController, Authorize, Route("api/[controller]/[action]")]
public class ChatMessagesListingController : ControllerBase, IChatMessagesListingDataService
{

	private readonly IChatMessagesListingDataService dataService;

	public ChatMessagesListingController(IChatMessagesListingDataService dataService)
	{
		this.dataService = dataService;
	}
	
	[HttpGet]
	public async Task<PagedDataList<ChatMessagesListingBusinessObject>> GetPaginatedItems([FromQuery] ChatMessagesFilterBusinessObject businessObject)
	{
		return await dataService.GetPaginatedItems(businessObject);
	}
}
