﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.SignalR;
using PetVet.Shared.DTOs;
using PetVet.Shared.IHub;

namespace PetVet.Api.Hubs
{
    [Authorize]
    public class PetVetChatHub : Hub<IPetVetChatHubClient>, IPetVetChatHubServer
    {

        private static readonly IDictionary<int, UserDto> _connectedUsers = new Dictionary<int, UserDto>();

        public PetVetChatHub()
        {

        }


        public override Task OnConnectedAsync()
        {
            return base.OnConnectedAsync();
        }


        public async Task ConnectUser(UserDto user)
        {
            // Send the list of connected users, excluding the current user
            var otherUsers = _connectedUsers.Values.Where(u => u.Id != user.Id).ToList();
            await Clients.Caller.ConnectedUserList(otherUsers);

            if (!_connectedUsers.ContainsKey(user.Id))
            {
                _connectedUsers.Add(user.Id, user);
                await Clients.Others.UserConnected(user);
                await Clients.Others.UserIsOnline(user.Id);
            }
        }

    }
}
