﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace PetVet.Api.Data.Entities
{
    public class User
    {
        [Key]
        public int Id { get; set; }

        [MaxLength(20)]
        public string Name { get; set; } = string.Empty;

        [MaxLength(150)]
        [EmailAddress(ErrorMessage = "Invalid Email Address")]
        public string Email { get; set; } = string.Empty;

        [StringLength(15, MinimumLength = 10, ErrorMessage = "Phone number should be between 10 and 15 characters")]
        public string Phone { get; set; } = string.Empty;

        [MaxLength(250)]
        public string PasswordHash { get; set; } = string.Empty;

        [MaxLength(15)]
        public string Role { get; set; } = string.Empty;

        public bool IsApproved { get; set; }

        [MaxLength(250)]
        public string Address { get; set; } = string.Empty;

        // Vet-specific fields (Nullable for PetOwner)
        [MaxLength(250)]
        public string? ClinicName { get; set; }

        [MaxLength(50)]
        public string? Specialization { get; set; }

        [Range(1, 100, ErrorMessage = "Years of experience must be between 1 and 100")]
        public int YearsOfExperience { get; set; }

       
        public string? LicenceDocumentUrl { get; set; }

        public bool? IsOnline { get; set; }

        
        public string? ImageUrl { get; set; }

        [Range(0, 5, ErrorMessage = "Rating must be between 0 and 5")]
        public double? Rating { get; set; }

        public string? CertificationsUrl { get; set; }

        public List<WorkingHour>? WorkingHours { get; set; }

        public int? EducationId { get; set; }

        [ForeignKey(nameof(EducationId))]
        public virtual Education? Education { get; set; }
    }
}
