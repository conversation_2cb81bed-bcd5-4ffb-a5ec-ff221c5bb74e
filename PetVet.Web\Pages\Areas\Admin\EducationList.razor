﻿@page "/admin/manage/education"
@inject IEducationApi _EducationApi
@inject IJSRuntime _JS
@inject IAppState _AppState

<PageTitle>Manage Education</PageTitle>

<BsModal OnModalConfirmation="ConfirmDelete_Click"
ButtonText="Delete"
Title="Are you sure you want to delete this education category?">
</BsModal>

@if(!isProcessing)
{
    <div class="bg-gray-50 p-8 min-h-screen w-full max-w-6xl mx-auto">
        <div class="bg-white rounded-3xl shadow-xl border border-gray-200 overflow-hidden">
            <!-- Header -->
            <div class="bg-primary-500 rounded-t-3xl p-8 text-center text-white">
                <h2 class="text-3xl font-bold py-2 font-acme">Education List <i class="fas fa-graduation-cap ml-2"></i></h2>
            </div>
            
            <!-- Body -->
            <div class="p-8">
                <!-- Add New Education Button -->
                <div class="flex justify-end mb-6">
                    <a href="admin/manage/education/create" 
                       class="inline-flex items-center space-x-2 bg-primary-500 hover:bg-primary-600 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 hover:transform hover:scale-105 shadow-lg">
                        <i class="fas fa-plus-circle"></i>
                        <span>Add New Education</span>
                    </a>
                </div>

                @if (!_educations.Any())
                {
                    <div class="text-center bg-yellow-50 border border-yellow-200 text-yellow-800 p-6 rounded-xl">
                        <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                        <p class="font-medium">No Education Categories Available.</p>
                    </div>
                }
                else
                {
                    <!-- Table Container -->
                    <div class="bg-white border border-gray-200 rounded-xl overflow-hidden shadow-sm">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900">#</th>
                                        <th class="px-6 py-4 text-left text-sm font-semibold text-gray-900">Name</th>
                                        <th class="px-6 py-4 text-center text-sm font-semibold text-gray-900">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-200">
                                    @{
                                        int number = 1;
                                    }
                                    @foreach (var education in _educations)
                                    {
                                        <tr class="hover:bg-gray-50 transition-colors duration-150">
                                            <td class="px-6 py-4 text-sm text-gray-700">@(number++)</td>
                                            <td class="px-6 py-4 text-sm font-medium text-gray-900">@education.Name</td>
                                            <td class="px-6 py-4 text-center">
                                                <div class="flex justify-center space-x-2">
                                                    <a href="@($"/admin/manage/education/update/{education.Id}")" 
                                                       class="inline-flex items-center space-x-1 bg-yellow-500 hover:bg-yellow-600 text-white text-xs font-medium py-2 px-3 rounded-lg transition-colors duration-200">
                                                        <i class="fas fa-edit"></i>
                                                        <span>Edit</span>
                                                    </a>
                                                    
                                                    <button @onclick="() => HandleDelete(education.Id)"
                                                            class="inline-flex items-center space-x-1 bg-red-500 hover:bg-red-600 text-white text-xs font-medium py-2 px-3 rounded-lg transition-colors duration-200">
                                                        <i class="fas fa-trash"></i>
                                                        <span>Delete</span>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
}

@code {
    private EducationDto[] _educations = [];
    private int DeleteEducationID { get; set; } = 0;
    private bool isProcessing { get; set; } = true;


    protected override async Task OnInitializedAsync() => await LoadEducationListAsync();


    private async Task LoadEducationListAsync()
    {
        isProcessing = true;
        _AppState.ShowLoader("Fetching Education Data");
        _educations = await _EducationApi.GetEducationListAsync();
        _AppState.HideLoader();
        isProcessing = false;
    }


    private async Task HandleDelete(int id)
    {
        DeleteEducationID = id;
        await _JS.InvokeVoidAsync("ShowConfirmationModal");
    }

    [JSInvokable]
    public async Task ConfirmDelete_Click(bool isConfirmed)
    {
        await _JS.InvokeVoidAsync("HideConfirmationModal");

        _AppState.ShowLoader("Deleting Education Data");

        if (isConfirmed && DeleteEducationID != 0)
        {
            var result = await _EducationApi.DeleteEducationAsync(DeleteEducationID);
            if (result.IsSuccess)
            {
                await _JS.InvokeVoidAsync("toastr.success", "Education category deleted successfully.");
            }
            else
            {
                await _JS.InvokeVoidAsync("toastr.error", "Failed to delete education category.");
            }
            await LoadEducationListAsync();
        }
        DeleteEducationID = 0;

        _AppState.HideLoader();

        StateHasChanged();
    }
}