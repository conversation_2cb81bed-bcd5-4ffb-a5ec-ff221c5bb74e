﻿using Microsoft.JSInterop;
using PetVet.Shared.IStorage;
using PetVet.Shared.Components.Framework;
using System.Runtime.CompilerServices;

namespace PetVet.Web.Services.Storage
{
    public class StorageService : IStorageService
    {
        private readonly IJSRuntime _jSRuntime;

        public StorageService(IJSRuntime jSRuntime)
        {
            _jSRuntime = jSRuntime;
        }

        public async ValueTask<string?> GetItem(string key) =>
            await _jSRuntime.InvokeAsync<string>("localStorage.getItem", key);

        public async ValueTask RemoveItem(string key) =>
            await _jSRuntime.InvokeVoidAsync("localStorage.removeItem", key);

        public async ValueTask SetItem(string key, string value) =>
             await _jSRuntime.InvokeVoidAsync("localStorage.setItem", key, value);
    }

    public class LocalStorageService : ILocalStorageService
    {
        private readonly IJSRuntime _jSRuntime;

        public LocalStorageService(IJSRuntime jSRuntime)
        {
            _jSRuntime = jSRuntime;
        }

        public async Task<string?> GetValue([CallerMemberName] string memberName = "")
        {
            try
            {
                return await _jSRuntime.InvokeAsync<string>("localStorage.getItem", $"__{memberName}__");
            }
            catch
            {
                return null;
            }
        }

        public async Task SetValue(string? value, [CallerMemberName] string memberName = "")
        {
            try
            {
                if (string.IsNullOrEmpty(value))
                {
                    await _jSRuntime.InvokeVoidAsync("localStorage.removeItem", $"__{memberName}__");
                }
                else
                {
                    await _jSRuntime.InvokeVoidAsync("localStorage.setItem", $"__{memberName}__", value);
                }
            }
            catch
            {
                // Ignore localStorage errors
            }
        }

        public async Task RemoveValue([CallerMemberName] string memberName = "")
        {
            try
            {
                await _jSRuntime.InvokeVoidAsync("localStorage.removeItem", $"__{memberName}__");
            }
            catch
            {
                // Ignore localStorage errors
            }
        }
    }
}
