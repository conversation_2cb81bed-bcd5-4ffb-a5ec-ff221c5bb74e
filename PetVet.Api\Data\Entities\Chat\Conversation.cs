﻿using PetVet.ServiceContracts.Enums;
using System.ComponentModel.DataAnnotations;

namespace PetVet.Server.DataServices.Data
{
    public class Conversation
    {
        [Key, StringLength(450)]
        public string Id { get; set; } = null!;

        public string Title { get; set; } = null!; 

        public bool IsDeleted { get; set; } = false;

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public ICollection<ConversationParticipant> Participants { get; set; } = new List<ConversationParticipant>();
    }
     
    public class ConversationParticipant
    {
        [Key, StringLength(450)]
        public string Id { get; set; } = null!;

        [StringLength(450)]
        public string ConversationId { get; set; } = null!;

        public Conversation Conversation { get; set; } = null!;

        public int UserId { get; set; } 

        public bool IsAdmin { get; set; } = false;
        public DateTime JoinedAt { get; set; } = DateTime.UtcNow;
    }

    /// <summary>
    /// A single 'logical' message in a conversation. 
    /// The actual encrypted copies are in the 'MessageRecipient' table (one per recipient).
    /// </summary>
    public class ConversationMessage
    {
        [Key, StringLength(450)] 
        public string Id { get; set; } = null!;

        [StringLength(450)]
        public string ConversationId { get; set; } = null!;
         
        public int SenderId { get; set; } 

        /// <summary>
        /// (Optional) Plaintext content if you store it server-side. 
        /// If you truly want E2E encryption, you might store only ephemeral or no plaintext.
        /// For demonstration, we keep it. 
        /// </summary>
        public string? PlainContent { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        // Soft-deletion
        public bool IsDeleted { get; set; } = false;
        public DateTime? DeletedAt { get; set; }

        // If you want to track edits:
        public bool IsEdited { get; set; } = false;
        public DateTime? EditedAt { get; set; }
          
        public DeliveryStatus DeliveryStatus { get; set; }
        public DateTime? DeliveryStatusTime { get; set; }
 
    }

    /// <summary>
    /// One row per message per user. 
    /// Holds the encryption payload, read status, and other user-specific states for the message.
    /// </summary>
    public class MessageRecipient
    {
        [Key, StringLength(450)]
        public string Id { get; set; } = null!;

        [StringLength(450)]
        public string MessageId { get; set; } = null!;
          
        public int RecipientId { get; set; } 

        public int UserDeviceId { get; set; } 
           
        public DeliveryStatus DeliveryStatus { get; set; }

        public DateTime? DeliveryStatusTime { get; set; }
 
        public bool IsRead { get; set; } = false;

        public DateTime? ReadAt { get; set; }

        public bool DeliveryStatusNotified { get; set; }

    }

    /// <summary>
    /// Supports pictures, documents, audio, or other file attachments. 
    /// You can store the actual file data in a BLOB column or just keep a URL/path reference.
    /// </summary>
    public class MessageAttachment
    {
        [Key, StringLength(450)]
        public string Id { get; set; } = null!;

        [StringLength(450)] 
        public string MessageId { get; set; } = null!;
         
        // E.g. "Image", "Document", "Audio"...
        public string AttachmentType { get; set; } = string.Empty;

        // The filename or caption:
        public string FileName { get; set; } = null!;

        // For referencing the file location (could be local path, cloud storage, etc.).
        public string FileUrl { get; set; } = null!;

        // Optionally store the file size, mime type, metadata, etc.
        public long? FileSizeBytes { get; set; }
        public string? MimeType { get; set; }

        // Soft-deletion or ephemeral logic if needed
        public bool IsDeleted { get; set; } = false;
        public DateTime? DeletedAt { get; set; }
    }
    public class UserDevice
    {
        [StringLength(450), Key]
        public string Id { get; set; } = string.Empty;
         
        public int UserId { get; set; }  

        [StringLength(450)]
        public string Name { get; set; } = string.Empty;

        [StringLength(450)]
        public string? DeviceToken { get; set; }

        [StringLength(450)]
        public string? Platform { get; set; }

        public DateTime LastLogin { get; set; }

        public DateTime LastSync { get; set; }

        public int Status { get; set; }

        public DateTime? StatusAppliedAt { get; set; }

        public string? StatusLog { get; set; }
    }
}
