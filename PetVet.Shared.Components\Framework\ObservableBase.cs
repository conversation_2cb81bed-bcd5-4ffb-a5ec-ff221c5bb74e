﻿using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace PetVet.Framework.Core
{
    public class ObservableBase : INotifyPropertyChanged
    {

        public bool IsPropertyChangeEventEnabled { get; set; } = true;

        protected bool SetField<T>(ref T field, T value, [CallerMemberName] string propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value)) return false;
            field = value;
            NotifyPropertyChanged(propertyName);
            return true;
        }

        public event PropertyChangedEventHandler PropertyChanged;

        public void NotifyPropertyChanged([CallerMemberName] string propertyName = "")
        {
            if (IsPropertyChangeEventEnabled)
            {
                PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
            }
        }
    }
}
