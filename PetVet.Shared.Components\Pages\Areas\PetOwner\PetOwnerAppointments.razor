@page "/petOwner/appointments"
@page "/petOwner/vetList/appointments"
@using CommunityToolkit.Mvvm.Messaging
@inject IAppointmentApi _AppointmentApi
@inject IAppState _AppState
@inject IJSRuntime _JS
@inject NavigationManager _NavigationManager
@inject PetVetAuthStateProvider _PetVetAuthStateProvider

<PageTitle>My Appointments</PageTitle>

<!-- Modern Pet Owner Appointments Page -->
<div class="px-4 py-6 space-y-6">

    <!-- Header Section -->
    <div class="text-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800 mb-2">My Appointments</h1>
        <p class="text-gray-600">Manage and track your veterinary appointments</p>
    </div>

    <!-- Quick Stats Cards -->
    <div class="grid grid-cols-3 gap-4 mb-6">
        <div class="bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl p-4 text-white text-center">
            <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2">
                <i class="fas fa-clock text-sm"></i>
            </div>
            <div class="text-xl font-bold">@filteredAppointments.Count(a => a.Status == nameof(AppointmentStatus.Confirmed))</div>
            <div class="text-xs text-primary-100">Upcoming</div>
        </div>
        <div class="bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl p-4 text-white text-center">
            <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2">
                <i class="fas fa-check text-sm"></i>
            </div>
            <div class="text-xl font-bold">@filteredAppointments.Count(a => a.Status == nameof(AppointmentStatus.Completed))</div>
            <div class="text-xs text-secondary-100">Completed</div>
        </div>
        <div class="bg-gradient-to-br from-primary-400 to-secondary-400 rounded-2xl p-4 text-white text-center">
            <div class="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-2">
                <i class="fas fa-calendar text-sm"></i>
            </div>
            <div class="text-xl font-bold">@filteredAppointments.Count()</div>
            <div class="text-xs text-primary-100">Total</div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 mb-6">
        <!-- Status Filters -->
        <div class="flex gap-2 mb-4 overflow-x-auto">
            <button class="px-4 py-2 rounded-xl text-sm font-medium transition-all whitespace-nowrap @(selectedStatus == "All" ? "bg-primary-500 text-white" : "bg-gray-100 text-gray-600 hover:bg-gray-200")"
                    @onclick="@(() => FilterByStatus("All"))">
                All
            </button>
            <button class="px-4 py-2 rounded-xl text-sm font-medium transition-all whitespace-nowrap @(selectedStatus == "Confirmed" ? "bg-primary-500 text-white" : "bg-gray-100 text-gray-600 hover:bg-gray-200")"
                    @onclick="@(() => FilterByStatus("Confirmed"))">
                Upcoming
            </button>
            <button class="px-4 py-2 rounded-xl text-sm font-medium transition-all whitespace-nowrap @(selectedStatus == "Completed" ? "bg-secondary-500 text-white" : "bg-gray-100 text-gray-600 hover:bg-gray-200")"
                    @onclick="@(() => FilterByStatus("Completed"))">
                Completed
            </button>
            <button class="px-4 py-2 rounded-xl text-sm font-medium transition-all whitespace-nowrap @(selectedStatus == "Cancelled" ? "bg-red-500 text-white" : "bg-gray-100 text-gray-600 hover:bg-gray-200")"
                    @onclick="@(() => FilterByStatus("Cancelled"))">
                Cancelled
            </button>
        </div>

        <!-- Date Filter -->
        <div class="space-y-3">
            <div class="grid grid-cols-2 gap-3">
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">From</label>
                    <input type="date"
                           class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           @bind="startDate" />
                </div>
                <div>
                    <label class="block text-xs font-medium text-gray-700 mb-1">To</label>
                    <input type="date"
                           class="w-full px-3 py-2 bg-gray-50 border border-gray-200 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                           @bind="endDate" />
                </div>
            </div>
            <div class="flex gap-2">
                <button class="flex-1 bg-primary-500 text-white py-2 px-4 rounded-xl text-sm font-medium hover:bg-primary-600 transition-all"
                        @onclick="async () => await ApplyDateFilter()">
                    <i class="fas fa-filter mr-1"></i>Apply
                </button>
                <button class="flex-1 bg-gray-100 text-gray-600 py-2 px-4 rounded-xl text-sm font-medium hover:bg-gray-200 transition-all"
                        @onclick="async () => await ResetDateFilter()">
                    <i class="fas fa-undo mr-1"></i>Reset
                </button>
            </div>
        </div>
    </div>

    <!-- Appointments List -->
    <div class="space-y-4">
        @if (!filteredAppointments.Any())
        {
            <div class="bg-white rounded-2xl p-8 text-center shadow-sm border border-gray-100">
                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-calendar-times text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-lg font-bold text-gray-800 mb-2">No Appointments Found</h3>
                <p class="text-gray-500">No appointments match your current filters.</p>
            </div>
        }
        else
        {
            foreach (var appointment in filteredAppointments.OrderByDescending(a => a.CreatedAt))
            {
                <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 @GetStatusBorderClass(appointment.Status)">
                    <div class="flex items-start gap-4">
                        <!-- Date Badge -->
                        <div class="flex-shrink-0">
                            <div class="w-16 h-16 @GetStatusBgClass(appointment.Status) rounded-2xl flex flex-col items-center justify-center text-white">
                                <span class="text-xs font-medium">@GetMonthFromDate(appointment.AppointmentDate)</span>
                                <span class="text-lg font-bold">@GetDayFromDate(appointment.AppointmentDate)</span>
                            </div>
                        </div>

                        <!-- Appointment Info -->
                        <div class="flex-1 min-w-0">
                            <div class="flex items-start justify-between mb-2">
                                <div>
                                    <h3 class="font-bold text-gray-800">Dr. @appointment.VetName</h3>
                                    <div class="flex items-center gap-2 text-sm text-gray-600">
                                        <i class="fas fa-clock"></i>
                                        <span>@appointment.Time</span>
                                    </div>
                                </div>
                                <span class="px-3 py-1 rounded-full text-xs font-medium @GetStatusClass(appointment.Status)">
                                    @appointment.Status
                                </span>
                            </div>

                            @if (!string.IsNullOrEmpty(appointment.Notes))
                            {
                                <div class="bg-gray-50 rounded-xl p-3 mb-3">
                                    <div class="flex items-start gap-2 text-sm text-gray-600">
                                        <i class="fas fa-clipboard-list mt-0.5"></i>
                                        <span>@appointment.Notes</span>
                                    </div>
                                </div>
                            }

                            <!-- Action Buttons -->
                            <div class="flex gap-2 flex-wrap">
                                @if (appointment.Status == nameof(AppointmentStatus.Confirmed))
                                {
                                    <button class="px-3 py-2 bg-primary-100 text-primary-700 rounded-xl text-xs font-medium hover:bg-primary-200 transition-all"
                                            @onclick="() => RescheduleAppointment(appointment)">
                                        <i class="fas fa-calendar-alt mr-1"></i>Reschedule
                                    </button>
                                    <button class="px-3 py-2 bg-red-100 text-red-700 rounded-xl text-xs font-medium hover:bg-red-200 transition-all"
                                            @onclick="() => CancelAppointment(appointment.Id)">
                                        <i class="fas fa-times-circle mr-1"></i>Cancel
                                    </button>
                                }
                                <button class="px-3 py-2 bg-secondary-100 text-secondary-700 rounded-xl text-xs font-medium hover:bg-secondary-200 transition-all"
                                        @onclick="() => Contact(appointment.VetId, appointment.VetName, appointment.VetDisplayPicture)">
                                    <i class="fas fa-comments mr-1"></i>Message
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
    </div>

    <!-- Calendar Link -->
    <div class="mt-6">
        <a href="/petOwner/calendar"
           class="w-full bg-gradient-to-r from-primary-500 to-secondary-500 text-white py-4 px-6 rounded-2xl font-medium hover:from-primary-600 hover:to-secondary-600 transition-all transform hover:scale-105 shadow-lg flex items-center justify-center gap-2">
            <i class="fas fa-calendar"></i>
            <span>View Calendar</span>
        </a>
    </div>

</div>

<!-- Modal for Appointment Cancellation -->
<BsModal OnModalConfirmation="ConfirmCancel_Click"
         ButtonText="Cancel Appointment"
         Title="Are you sure you want to cancel this appointment?">
</BsModal>

@if (isRescheduleModalOpen && selectedAppointment != null)
{
    <Modal Title="Reschedule Appointment"
           ActionButtonText="Confirm"
           OnActionButtonClick="ConfirmReschedule"
           OnCancelButtonClick="CloseRescheduleModal"
           IsVisible="isRescheduleModalOpen"
           Size="ModalSize.Large">

        <div class="date-picker">
            <label for="reschedule-date">Select New Date</label>
            <input type="date"
                   id="reschedule-date"
                   @bind-value="newAppointmentDate"
                   @bind-value:event="oninput"
                   min="@DateTime.Today.ToString("yyyy-MM-dd")" />
        </div>

        @if (newAppointmentDate != default)
        {
            <div class="time-slots">
                <label>Available Time Slots for @newAppointmentDate.ToString("dddd")</label>
                @if (!availableTimeSlots.Any(ts => ts.Day == newAppointmentDate.ToString("dddd") && !ts.IsBooked))
                {
                    <div class="alert alert-warning">
                        No time slots available for this day. Please select another date.
                    </div>
                }
                else
                {
                    <div class="slot-grid">
                        @foreach (var slot in availableTimeSlots.Where(ts => ts.Day == newAppointmentDate.ToString("dddd")))
                        {
                            <button class="time-slot @(slot.IsBooked ? "booked" : "") @(slot.IsExpired ? "expired" : "") @(slot.Id == newTimeSlotId ? "selected" : "")"
                                    disabled="@(slot.IsBooked || slot.IsExpired)"
                                    @onclick="() => SelectTimeSlot(slot.Id)">
                                @slot.Time
                                @if (slot.IsBooked)
                                {
                                    <span class="booked-label">Booked</span>
                                }
                                @if (slot.IsExpired)
                                {
                                    <span class="expired-label">Expired</span>
                                }
                            </button>
                        }
                    </div>
                }
            </div>
        }
        </Modal>
}

@code {
    private int _currentUserId;
    private List<AppointmentDto> appointments = new();
    private List<AppointmentDto> filteredAppointments = new();
    private string selectedStatus = "All";
    private DateTime startDate = DateTime.Today;
    private DateTime endDate = DateTime.Today.AddDays(3);
    private bool isDateFilterActive = true;
    private int CancelAppointmentID { get; set; } = 0;

    // Reschedule modal state
    private bool isRescheduleModalOpen = false;
    private AppointmentDto selectedAppointment;
    private DateTime newAppointmentDate = DateTime.Today;
    private int newTimeSlotId;
    private List<VetTimeSlotDto> availableTimeSlots = new();

    protected override async Task OnInitializedAsync()
    {
        _currentUserId = _PetVetAuthStateProvider.User.Id;

        await LoadAppointments();
    }

    private async Task LoadAppointments()
    {
        try
        {
            _AppState.ShowLoader("Loading Appointments");
            appointments = await _AppointmentApi.GetPetOwnerAppointmentsAsync(_currentUserId);
            ApplyFilters();
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error loading appointments: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private void FilterByStatus(string status)
    {
        selectedStatus = status;
        ApplyFilters();
    }

    private async Task ApplyDateFilter()
    {
        if (endDate < startDate)
        {
            await _JS.ToastrError("End date cannot be earlier than start date");
            return;
        }

        isDateFilterActive = true;
        ApplyFilters();
        await _JS.ToastrSuccess("Date filter applied");
    }

    private async Task ResetDateFilter()
    {
        startDate = DateTime.Today;
        endDate = DateTime.Today.AddDays(3);
        isDateFilterActive = true;
        ApplyFilters();
        await _JS.ToastrSuccess("Date filter reset to default range");
    }

    private void ApplyFilters()
    {
        filteredAppointments = appointments.Where(a =>
        {
            bool matchesStatus = selectedStatus == "All" || a.Status == selectedStatus;

            if (!DateTime.TryParse(a.AppointmentDate, out DateTime appointmentDate))
            {
                return matchesStatus;
            }

            bool matchesDateRange = !isDateFilterActive || (appointmentDate.Date >= startDate.Date && appointmentDate.Date <= endDate.Date);
            return matchesStatus && matchesDateRange;

        }).ToList();

        // Sort by date and time
        filteredAppointments = filteredAppointments
            .OrderByDescending(a => DateTime.TryParse(a.AppointmentDate, out DateTime dt) ? dt : DateTime.MinValue)
            .ThenBy(a => a.Time)
            .ToList();
    }

    private string GetStatusClass(string status)
    {
        return status switch
        {
            nameof(AppointmentStatus.Confirmed) => "bg-primary-100 text-primary-700",
            nameof(AppointmentStatus.Completed) => "bg-secondary-100 text-secondary-700",
            nameof(AppointmentStatus.Cancelled) => "bg-red-100 text-red-700",
            nameof(AppointmentStatus.Expired) => "bg-gray-100 text-gray-700",
            _ => "bg-gray-100 text-gray-700"
        };
    }

    private string GetStatusBgClass(string status)
    {
        return status switch
        {
            nameof(AppointmentStatus.Confirmed) => "bg-gradient-to-br from-primary-500 to-primary-600",
            nameof(AppointmentStatus.Completed) => "bg-gradient-to-br from-secondary-500 to-secondary-600",
            nameof(AppointmentStatus.Cancelled) => "bg-gradient-to-br from-red-500 to-red-600",
            nameof(AppointmentStatus.Expired) => "bg-gradient-to-br from-gray-500 to-gray-600",
            _ => "bg-gradient-to-br from-gray-500 to-gray-600"
        };
    }

    private string GetStatusBorderClass(string status)
    {
        return status switch
        {
            nameof(AppointmentStatus.Confirmed) => "border-l-4 border-l-primary-500",
            nameof(AppointmentStatus.Completed) => "border-l-4 border-l-secondary-500",
            nameof(AppointmentStatus.Cancelled) => "border-l-4 border-l-red-500",
            nameof(AppointmentStatus.Expired) => "border-l-4 border-l-gray-500",
            _ => ""
        };
    }

    private string GetMonthFromDate(string dateString)
    {
        if (DateTime.TryParse(dateString, out DateTime date))
        {
            return date.ToString("MMM");
        }
        return "";
    }

    private string GetDayFromDate(string dateString)
    {
        if (DateTime.TryParse(dateString, out DateTime date))
        {
            return date.Day.ToString();
        }
        return "";
    }

    private async Task CancelAppointment(int appointmentId)
    {
        CancelAppointmentID = appointmentId;
        await _JS.InvokeVoidAsync("ShowConfirmationModal");
    }

    [JSInvokable]
    public async Task ConfirmCancel_Click(bool isConfirmed)
    {
        await _JS.InvokeVoidAsync("HideConfirmationModal");

        _AppState.ShowLoader("Cancelling Appointment");

        if (isConfirmed && CancelAppointmentID != 0)
        {
            var response = await _AppointmentApi.UpdateAppointmentStatusAsync(CancelAppointmentID, nameof(AppointmentStatus.Cancelled));

            if (response.IsSuccess)
            {
                await _JS.ToastrSuccess("Appointment cancelled successfully.");
                await LoadAppointments();
            }
            else
            {
                await _JS.ToastrError($"Error cancelling appointment: {response.ErrorMessage}");
            }
        }
        CancelAppointmentID = 0;

        _AppState.HideLoader();

        StateHasChanged();
    }

    private async Task RescheduleAppointment(AppointmentDto appointment)
    {
        selectedAppointment = appointment;
        newAppointmentDate = DateTime.Today;
        newTimeSlotId = 0;
        isRescheduleModalOpen = true;

        // Load time slots for the selected vet
        await LoadAvailableTimeSlots(appointment.VetId);
    }

    private async Task LoadAvailableTimeSlots(int vetId)
    {
        try
        {
            _AppState.ShowLoader("Loading Time Slots");
            availableTimeSlots = await _AppointmentApi.GetVetTimeSlotsAsync(vetId);
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error loading time slots: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private void SelectTimeSlot(int timeSlotId)
    {
        newTimeSlotId = timeSlotId;
    }

    private void CloseRescheduleModal()
    {
        isRescheduleModalOpen = false;
        selectedAppointment = null;
        availableTimeSlots.Clear();
    }

    private async Task ConfirmReschedule()
    {
        if (selectedAppointment == null || newTimeSlotId == 0)
        {
            await _JS.ToastrError("Please select a new date and time slot.");
            return;
        }

        try
        {
            _AppState.ShowLoader("Rescheduling Appointment");

            var response = await _AppointmentApi.RescheduleAppointmentAsync(
                selectedAppointment.Id,
                newTimeSlotId,
                newAppointmentDate.ToString("yyyy-MM-dd"));

            if (response.IsSuccess)
            {
                await _JS.ToastrSuccess("Appointment rescheduled successfully.");
                CloseRescheduleModal();
                await LoadAppointments();
            }
            else
            {
                await _JS.ToastrError($"Error rescheduling appointment: {response.ErrorMessage}");
            }
        }
        catch (Exception ex)
        {
            await _JS.ToastrError($"Error rescheduling appointment: {ex.Message}");
        }
        finally
        {
            _AppState.HideLoader();
        }
    }

    private void Contact(int vetId, string vetName, string vetDisplayPicture)
    {
        // Navigate to chat with the vet
        WeakReferenceMessenger.Default.Send<Tuple<string, int, string, string>>(new Tuple<string, int, string, string>("StartChat", vetId, vetName, vetDisplayPicture));
    } 
} 