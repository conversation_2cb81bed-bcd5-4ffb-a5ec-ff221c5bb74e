using PetVet.Api.Services;

namespace PetVet.Api.Endpoints
{
    public static class RecentActivityEndpoints
    {
        public static IEndpointRouteBuilder MapRecentActivityEndpoints(this IEndpointRouteBuilder app)
        {
            var activityGroup = app.MapGroup("/api/activities")
                .RequireAuthorization();

            // Get recent activities for a pet owner
            activityGroup.MapGet("/petowner/{petOwnerId:int}", async (int petOwnerId, RecentActivityService activityService) =>
                Results.Ok(await activityService.GetPetOwnerRecentActivitiesAsync(petOwnerId)));

            return app;
        }
    }
} 