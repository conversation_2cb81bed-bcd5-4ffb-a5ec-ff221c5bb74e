﻿using Microsoft.Extensions.Configuration;
using System;
using System.Threading.Tasks;
using Vonage.Messaging;
using Vonage.Request;
using PetVet.Shared;
using PetVet.Shared.DTOs;

namespace PetVet.Api.Services
{
    public class SmsService
    {
        private readonly IConfiguration _configuration;

        public SmsService(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public PetVetApiResponse SendSms(string to, string from, string text)
        {
            try
            {
                var apiKey = _configuration["Vonage:ApiKey"];
                var apiSecret = _configuration["Vonage:ApiSecret"];

                if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(apiSecret))
                {
                    return PetVetApiResponse.Fail("SMS API credentials missing. Check Vonage:ApiKey and Vonage:ApiSecret in configuration.");
                }

                // Use default sender if none provided
                if (string.IsNullOrEmpty(from))
                {
                    from = _configuration["Vonage:DefaultSender"] ?? "PetVet";
                }

                // Format Pakistani numbers correctly
                if (to.StartsWith("+92"))
                {
                    // Pakistani SMS regulation compliance
                    if (text.Length > 160)
                    {
                        text = text.Substring(0, 157) + "...";
                    }
                }

                var creds = Credentials.FromApiKeyAndSecret(apiKey, apiSecret);
                var client = new SmsClient(creds);

                var request = new SendSmsRequest
                {
                    To = to,
                    From = from,
                    Text = text
                };

                // Send the SMS
                var taskResult = client.SendAnSmsAsync(request);
                taskResult.Wait(); // Wait for the task to complete
                var response = taskResult.Result;

                return PetVetApiResponse.Success();
            }
            catch (Exception ex)
            {
                return PetVetApiResponse.Fail($"Failed to send SMS: {ex.Message}");
            }
        }
    }
}
