﻿.MyPetsFilters-CONTAINER {
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
   
}

.MyPetsFilters-HEADING {
    font-size: 1.25rem;
    font-family: 'Acme', sans-serif;
    font-weight: bold;
    margin-bottom: 1rem;
    cursor: default;
}

.MyPetsFilters-SUBHEADING {
    font-size: 1.125rem;
    font-family: 'Acme', sans-serif;
    font-weight: semi-bold;
    margin-bottom: 0.5rem;
    cursor: default;
}

.MyPetsFilters-BODY {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.MyPetsFilters-ITEM {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
}

input[type="checkbox"] {
    width: 1.25rem;
    height: 1.25rem;
    cursor: pointer;
    accent-color: #FFC107;
    transition: background-color 0.3s ease;
}

    input[type="checkbox"]:hover {
        background-color: #e0a406;
    }

label {
    cursor: pointer;
    font-size: 1rem;
    color: #343a40;
    
    transition: color 0.3s ease;
}

    label:hover {
        color: #157BAB;
    }

input[type="checkbox"]:checked + label {
    color: #29AB87;
    font-weight: bold;
}
