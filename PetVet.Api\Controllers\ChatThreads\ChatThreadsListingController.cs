﻿using PetVet.ServiceContracts.Features.Conversation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using PetVet.Shared.Interfaces;
namespace PetVet.Server.WebApis.Controller.Conversation;
[ApiController, Authorize, Route("api/[controller]/[action]")]
public class ChatThreadsListingController : ControllerBase, IChatThreadsListingDataService
{

	private readonly IChatThreadsListingDataService dataService;

	public ChatThreadsListingController(IChatThreadsListingDataService dataService)
	{
		this.dataService = dataService;
	}
	
	[HttpGet]
	public async Task<PagedDataList<ChatThreadsListingBusinessObject>> GetPaginatedItems([FromQuery] ChatThreadsFilterBusinessObject businessObject)
	{
		return await dataService.GetPaginatedItems(businessObject);
	}
}
