﻿@page "/"
@inject PetVetAuthStateProvider _PetVetAuthStateProvider

@attribute [Authorize]

<PageTitle>Home</PageTitle>

<AuthorizeView>
    <Authorized>
      <AuthorizeView Roles="@nameof(UserRole.PetOwner)" Context="studentContext">
            <RedirectToHome Url="petOwner/home" />
      </AuthorizeView>

       <AuthorizeView Roles="@nameof(UserRole.Vet)" Context="vetContext">
            <RedirectToHome Url="vet/home" />
        </AuthorizeView>

        <AuthorizeView Roles="@nameof(UserRole.Admin)" Context="adminContext">
            <RedirectToHome Url="admin/home" />
        </AuthorizeView>
    </Authorized>
</AuthorizeView>