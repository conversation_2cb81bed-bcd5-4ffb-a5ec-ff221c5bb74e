﻿using PetVet.Api.Services;
using PetVet.Shared.DTOs;

namespace PetVet.Api.Endpoints
{
    public static class AuthEndpoints
    {
        public static IEndpointRouteBuilder MapAuthEndpoints(this IEndpointRouteBuilder app)
        {
            app.MapPost("/api/auth/login", async (LoginDto dto, AuthService authService) => 
            Results.Ok(await authService.LoginAsync(dto)));

            app.MapPost("/api/auth/register/petOwner", async (PetOwnerDto dto, AuthService authService) =>
            Results.Ok(await authService.RegisterPetOwnerAsync(dto)));
            
            app.MapPost("/api/auth/register/vet", async (VetDto dto, AuthService authService) =>
            Results.Ok(await authService.RegisterVetAsync(dto)));

            app.MapPost("/api/auth/changePassword", async (ChangePasswordDto dto, AuthService authService) =>
            Results.Ok(await authService.ChangePasswordAsync(dto)))
            .RequireAuthorization();

            return app;
        }
    }
}
