﻿using Microsoft.EntityFrameworkCore;
using PetVet.Api.Data;
using PetVet.Api.Data.Entities;
using PetVet.Shared.DTOs;

namespace PetVet.Api.Services
{
    public class CategoryService
    {
        private readonly PetVetContext _context;

        public CategoryService(PetVetContext context)
        {
            _context = context;
        }


        public async Task<PetVetApiResponse> SaveCategoryAsync(CategoryDto dto)
        {
            if (await _context.Categories
                .AsNoTracking()
                .AnyAsync(c => c.Name == dto.Name && c.Id != dto.Id))
            {
                // Category with same name already exists
                return PetVetApiResponse.Fail("Category with same name already exists.");
            }

            if (dto.Id == 0)
            {
                // Create new Category
                var category = new Category
                {
                    Name = dto.Name,
                    IsGroupCategory = dto.IsGroupCategory
                };
                _context.Categories.Add(category);
            }
            else
            {
                // Update existing Category
                var dbCategory = await _context.Categories.FirstOrDefaultAsync(c => c.Id == dto.Id);

                if (dbCategory == null)
                {
                    // Category does not exist
                    return PetVetApiResponse.Fail("Category does not exist.");
                }
                dbCategory.Name = dto.Name;
                dbCategory.IsGroupCategory = dto.IsGroupCategory;
                _context.Categories.Update(dbCategory);
            }
            await _context.SaveChangesAsync();
            return PetVetApiResponse.Success();
        }


        public async Task<CategoryDto[]> GetCategoriesAsync() => await _context.Categories
            .AsNoTracking()
            .Select(c => new CategoryDto
            {
                Name = c.Name,
                Id = c.Id,
                IsGroupCategory = c.IsGroupCategory
            })
            .ToArrayAsync();


        public async Task<CategoryDto> GetCategoryAsync(int id)
        {
            var category = await _context.Categories
                .AsNoTracking()
                .FirstOrDefaultAsync(c => c.Id == id);

            if (category == null)
            {
                return null;
            }

            return new CategoryDto
            {
                Id = category.Id,
                Name = category.Name,
                IsGroupCategory = category.IsGroupCategory
            };
        }


        public async Task<PetVetApiResponse> DeleteCategoryAsync(int id)
        {
            var category = await _context.Categories.FirstOrDefaultAsync(c => c.Id == id);

            if (category == null)
            {
                return PetVetApiResponse.Fail("Category not found.");
            }

            _context.Categories.Remove(category);
            await _context.SaveChangesAsync();

            return PetVetApiResponse.Success();
        }
    }
}
