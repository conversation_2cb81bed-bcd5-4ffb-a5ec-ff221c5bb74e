using PetVet.Shared.Models.AzureOpenAI;
using PetVet.Shared.Services;
using Refit;

namespace PetVet.Shared.Components.Apis;

public interface IChatApi
{
    /// <summary>
    /// Send a simple chat message
    /// </summary>
    [Post("/api/chat/message")]
    Task<ChatApiResponse> SendMessageAsync([Body] SendMessageRequest request);

    /// <summary>
    /// Send a full chat request with conversation context
    /// </summary>
    [Post("/api/chat/chat")]
    Task<ChatApiResponse> SendChatRequestAsync([Body] ChatRequestWrapper request);

    /// <summary>
    /// Get conversation history
    /// </summary>
    [Get("/api/chat/conversation/{conversationId}")]
    Task<ChatConversation> GetConversationAsync(string conversationId);

    /// <summary>
    /// Get all conversations for a user
    /// </summary>
    [Get("/api/chat/conversations/{userId}")]
    Task<List<ChatConversation>> GetUserConversationsAsync(string userId);

    /// <summary>
    /// Delete a conversation
    /// </summary>
    [Delete("/api/chat/conversation/{conversationId}")]
    Task DeleteConversationAsync(string conversationId);

    /// <summary>
    /// Clear all conversations for a user
    /// </summary>
    [Delete("/api/chat/conversations/{userId}")]
    Task ClearUserConversationsAsync(string userId);

    /// <summary>
    /// Get service health status
    /// </summary>
    [Get("/api/chat/health")]
    Task<ChatHealthResponse> GetHealthAsync();

    /// <summary>
    /// Get service statistics
    /// </summary>
    [Get("/api/chat/stats")]
    Task<ChatServiceStats> GetStatsAsync();
}

// Request/Response models for the API
public class SendMessageRequest
{
    public string Message { get; set; } = "";
    public string? ConversationId { get; set; }
    public string? UserId { get; set; }
}

public class ChatRequestWrapper
{
    public ChatRequest ChatRequest { get; set; } = new();
    public string? ConversationId { get; set; }
    public string? UserId { get; set; }
}

public class ChatHealthResponse
{
    public bool Healthy { get; set; }
    public string Status { get; set; } = "";
    public ChatServiceStats? Stats { get; set; }
    public DateTime Timestamp { get; set; }
}
