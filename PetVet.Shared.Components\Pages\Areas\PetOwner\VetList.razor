﻿@page "/petOwner/vetList"
@inject IUserApi _UserApi
@inject IAppointmentApi _AppointmentApi
@inject IAppState _AppState
@inject IJSRuntime _JS
@inject PetVetAuthStateProvider _PetVetAuthStateProvider
@inject NavigationManager _NavigationManager

<PageTitle>Vets List</PageTitle>

@if (!isProcessing)
{
    <!-- Modern Vet List Page -->
    <div class="px-4 py-6 space-y-6">

        <!-- Header -->
        <div>
            <h1 class="text-2xl font-bold text-gray-800 mb-2">Find Veterinarians</h1>
            <p class="text-gray-500 text-sm">Discover trusted vets in your area</p>
        </div>

        <!-- Modern Search Bar -->
        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400"></i>
            </div>
            <input type="text"
                   class="w-full pl-10 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all"
                   placeholder="Search vets, specializations, locations..."
                   @bind="searchQuery"
                   @oninput="FilterVets" />
        </div>

        <!-- Featured Veterinarians -->
        <div>
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-bold text-gray-800">Featured Veterinarians</h2>
                <button @onclick="NavigateToAllVets" class="text-teal-600 text-sm font-medium flex items-center gap-1">
                    See all <i class="fas fa-arrow-right text-xs"></i>
                </button>
            </div>

            <div class="flex gap-4 overflow-x-auto pb-2">
                @foreach (var vet in _vetUsers.Take(3))
                {
                    <div class="flex-shrink-0 bg-white rounded-2xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-all cursor-pointer w-40"
                         @onclick="() => NavigateToVetProfile(vet.Id)" role="button" tabindex="0">
                        <div class="text-center">
                            <img src="@GetVetImageSrc(vet.ImageUrl)"
                                 alt="@vet.Name"
                                 class="w-16 h-16 rounded-full mx-auto mb-3 object-cover border-2 border-gray-100" />
                            <h3 class="font-semibold text-gray-800 text-sm mb-1">Dr. @vet.Name</h3>
                            <p class="text-gray-500 text-xs mb-2">@vet.Specialization</p>
                            <div class="flex items-center justify-center gap-1 text-yellow-500 text-xs">
                                @for (int i = 0; i < 5; i++)
                                {
                                    if (i < Math.Floor(vet.Rating ?? 0))
                                    {
                                        <i class="fas fa-star"></i>
                                    }
                                    else
                                    {
                                        <i class="far fa-star"></i>
                                    }
                                }
                                <span class="text-gray-500 ml-1">@(vet.Rating?.ToString("0.0") ?? "0.0")</span>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>

        <!-- Quick Access Services -->
        <div>
            <h2 class="text-lg font-bold text-gray-800 mb-4">Quick Services</h2>
            <div class="grid grid-cols-3 gap-4">
                <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-all cursor-pointer text-center"
                     @onclick="NavigateToAllVets" role="button" tabindex="0">
                    <div class="w-12 h-12 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-3">
                        <img src="images/icons/veterinarian.png" alt="Vets" class="w-6 h-6" />
                    </div>
                    <p class="text-sm font-medium text-gray-800">All Vets</p>
                </div>

                <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-all cursor-pointer text-center"
                     @onclick="NavigateToAppointments" role="button" tabindex="0">
                    <div class="w-12 h-12 bg-green-100 rounded-2xl flex items-center justify-center mx-auto mb-3">
                        <img src="images/icons/clinic.png" alt="Appointments" class="w-6 h-6" />
                    </div>
                    <p class="text-sm font-medium text-gray-800">Appointments</p>
                </div>

                <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-all cursor-pointer text-center"
                     @onclick="NavigateToVetTips" role="button" tabindex="0">
                    <div class="w-12 h-12 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-3">
                        <img src="images/icons/document.png" alt="Vet Tips" class="w-6 h-6" />
                    </div>
                    <p class="text-sm font-medium text-gray-800">Vet Tips</p>
                </div>
            </div>
        </div>

        <!-- Available Vets in Your Area -->
        <div>
            <h2 class="text-lg font-bold text-gray-800 mb-4">Vets Available In Your Area</h2>
            <div class="space-y-4">
                @foreach (var vet in _vetUsers)
                {
                    <div class="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-all cursor-pointer"
                         @onclick="() => NavigateToVetProfile(vet.Id)" role="button" tabindex="0">

                        <div class="flex items-center gap-4">
                            <!-- Vet Image -->
                            <img src="@GetVetImageSrc(vet.ImageUrl)"
                                 alt="@vet.Name"
                                 class="w-16 h-16 rounded-2xl object-cover border-2 border-gray-100" />

                            <!-- Vet Info -->
                            <div class="flex-1">
                                <div class="flex items-start justify-between mb-2">
                                    <div>
                                        <h3 class="font-semibold text-gray-800 text-lg">Dr. @vet.Name</h3>
                                        <p class="text-gray-500 text-sm">@vet.Specialization Specialist</p>
                                    </div>
                                    <div class="flex items-center gap-1 text-yellow-500 text-sm">
                                        @for (int i = 0; i < 5; i++)
                                        {
                                            if (i < Math.Floor(vet.Rating ?? 0))
                                            {
                                                <i class="fas fa-star"></i>
                                            }
                                            else if (i < Math.Ceiling(vet.Rating ?? 0))
                                            {
                                                <i class="fas fa-star-half-alt"></i>
                                            }
                                            else
                                            {
                                                <i class="far fa-star"></i>
                                            }
                                        }
                                        <span class="text-gray-500 ml-1">@(vet.Rating?.ToString("0.0") ?? "0.0")</span>
                                    </div>
                                </div>

                                <!-- Clinic Info -->
                                @if (!string.IsNullOrEmpty(vet.ClinicName))
                                {
                                    <div class="flex items-center gap-2 text-sm text-gray-500 mb-2">
                                        <i class="fas fa-clinic-medical text-xs"></i>
                                        <span>@vet.ClinicName</span>
                                    </div>
                                }

                                <div class="flex items-center gap-2 text-sm text-gray-500 mb-3">
                                    <i class="fas fa-map-marker-alt text-xs"></i>
                                    <span>@vet.Address</span>
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex gap-2">
                                    <button class="flex-1 bg-teal-500 text-white py-2 px-4 rounded-xl text-sm font-medium hover:bg-teal-600 transition-colors">
                                        Book Appointment
                                    </button>
                                    <button class="bg-gray-100 text-gray-600 py-2 px-4 rounded-xl text-sm font-medium hover:bg-gray-200 transition-colors">
                                        View Profile
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
}

@code {
    private UserDto[] _vetUsers = [];
    private UserDto[] _allVetUsers = [];
    private string searchQuery = "";
    private UserDto? selectedVet;
    private bool isModalOpen = false;
    private bool isProcessing { get; set; } = true;
    private string? selectedTimeSlot;
    private int selectedTimeSlotId;
    private List<VetTimeSlotDto> vetTimeSlots = new();
    private int _currentUserId;
    private string appointmentNotes = "";

    protected override async Task OnInitializedAsync()
    {
        _currentUserId = _PetVetAuthStateProvider.User.Id;

        await LoadVetUsersAsync();
    }

    private async Task LoadVetUsersAsync()
    {
        isProcessing = true;
        _AppState.ShowLoader("Fetching Vets Data");
        _allVetUsers = await _UserApi.GetVetUsersAsync();
        _vetUsers = _allVetUsers;
        _AppState.HideLoader();
        isProcessing = false;
    }

    private void FilterVets(ChangeEventArgs e)
    {
        searchQuery = e.Value?.ToString()?.ToLower() ?? "";
        _vetUsers = _allVetUsers.Where(vet =>
            vet.Name.ToLower().Contains(searchQuery) ||
            vet.Specialization.ToLower().Contains(searchQuery) ||
            vet.Address.ToLower().Contains(searchQuery) ||
            (!string.IsNullOrEmpty(vet.ClinicName) && vet.ClinicName.ToLower().Contains(searchQuery))
        ).ToArray();
    }

    private void NavigateToVetProfile(int vetId)
    {
        _NavigationManager.NavigateTo($"/petOwner/vetList/vet/{vetId}");
    }

    private void NavigateToAllVets()
    {
        _NavigationManager.NavigateTo("/petOwner/vetList/allVets");
    }

    private void NavigateToAppointments()
    {
        _NavigationManager.NavigateTo("/petOwner/vetList/appointments");
    }

    private void NavigateToVetTips()
    {
        _NavigationManager.NavigateTo("/petOwner/vetList/vetTips");
    }

    private string GetVetImageSrc(string? imageUrl)
    {
        if (string.IsNullOrEmpty(imageUrl))
            return "images/dummy-profile.png";

        // If it's already a data URL, return as is
        if (imageUrl.StartsWith("data:"))
            return imageUrl;

        // If it's a regular URL (http/https), return as is
        if (imageUrl.StartsWith("http"))
            return imageUrl;

        // Otherwise, treat it as base64 and create data URL
        return $"data:image/jpeg;base64,{imageUrl}";
    }
}
