﻿using Microsoft.EntityFrameworkCore;
using PetVet.Api.Data;
using PetVet.Api.Data.Entities;
using PetVet.Shared.DTOs;

namespace PetVet.Api.Services
{
    public class PetService
    {
        private readonly PetVetContext _context;

        public PetService(PetVetContext context)
        {
            _context = context;
        }

        // Save or update a pet profile
        public async Task<PetVetApiResponse> SavePetAsync(PetDto dto)
        {
            // Validate UserId is provided
            if (dto.UserId <= 0)
            {
                return PetVetApiResponse.Fail("User ID is required for pet creation or update.");
            }

            // Validate user exists
            var userExists = await _context.Users.AnyAsync(u => u.Id == dto.UserId);
            if (!userExists)
            {
                return PetVetApiResponse.Fail("User does not exist.");
            }

            // Check if selected category is a group category
            var category = await _context.Categories.FindAsync(dto.CategoryId);
            if (category == null)
            {
                return PetVetApiResponse.Fail("Selected category does not exist.");
            }

            // Respect client-side group category flag for flexibility
            bool isGroupCategory = category.IsGroupCategory || dto.IsGroupCategory;

            if (dto.Id == 0)
            {
                // Create new pet profile
                var pet = new Pet
                {
                    Name = dto.Name,
                    ImageUrl = dto.ImageUrl,
                    Age = dto.Age,
                    Weight = dto.Weight,
                    Gender = dto.Gender,
                    Breed = dto.Breed,
                    DateOfBirth = dto.DateOfBirth,
                    VaccinationStatus = dto.VaccinationStatus,
                    CategoryId = dto.CategoryId,
                    UserId = dto.UserId,
                    Count = isGroupCategory ? dto.Count : 1 // Set count only for group categories
                };

                await _context.Pets.AddAsync(pet);
            }
            else
            {
                // Update existing pet profile
                var dbPet = await _context.Pets.FirstOrDefaultAsync(p => p.Id == dto.Id);

                if (dbPet == null)
                {
                    return PetVetApiResponse.Fail("Pet profile does not exist.");
                }

                // Get original owner ID to prevent ownership changes
                var originalUserId = dbPet.UserId;

                // Update properties of existing pet
                dbPet.Name = dto.Name;
                dbPet.ImageUrl = dto.ImageUrl;
                dbPet.Age = dto.Age;
                dbPet.Weight = dto.Weight; // Weight represents average weight for group pets
                dbPet.Gender = dto.Gender;
                dbPet.Breed = dto.Breed;
                dbPet.DateOfBirth = dto.DateOfBirth;
                dbPet.VaccinationStatus = dto.VaccinationStatus;
                dbPet.CategoryId = dto.CategoryId;

                // Set count only for group categories
                if (isGroupCategory)
                {
                    dbPet.Count = dto.Count;
                }
                else
                {
                    dbPet.Count = 1;
                }

                // Always preserve the original owner
                dbPet.UserId = originalUserId;

                _context.Pets.Update(dbPet);
            }

            await _context.SaveChangesAsync();
            return PetVetApiResponse.Success();
        }

        // Retrieve a list of all pets
        public async Task<PetDto[]> GetPetsAsync() =>
            await _context.Pets
                .AsNoTracking()
                .Select(p => new PetDto
                {
                    Id = p.Id,
                    Name = p.Name,
                    ImageUrl = p.ImageUrl,
                    Age = p.Age,
                    Weight = p.Weight,
                    Gender = p.Gender,
                    Breed = p.Breed,
                    DateOfBirth = p.DateOfBirth,
                    VaccinationStatus = p.VaccinationStatus,
                    CategoryId = p.CategoryId,
                    CategoryName = p.Category.Name,
                    UserId = p.UserId,
                    Count = p.Count,
                    IsGroupCategory = p.Category.IsGroupCategory
                })
                .ToArrayAsync();

        // Get pets by user ID
        public async Task<PetDto[]> GetPetsAsync(int userId) =>
            await _context.Pets
                .AsNoTracking()
                .Where(p => p.UserId == userId)
                .Select(p => new PetDto
                {
                    Id = p.Id,
                    Name = p.Name,
                    ImageUrl = p.ImageUrl,
                    Age = p.Age,
                    Weight = p.Weight,
                    Gender = p.Gender,
                    Breed = p.Breed,
                    DateOfBirth = p.DateOfBirth,
                    VaccinationStatus = p.VaccinationStatus,
                    CategoryId = p.CategoryId,
                    CategoryName = p.Category.Name,
                    UserId = p.UserId,
                    Count = p.Count,
                    IsGroupCategory = p.Category.IsGroupCategory
                })
                .ToArrayAsync();

        // Retrieve a specific pet by ID
        public async Task<PetDto> GetPetAsync(int id)
        {
            var pet = await _context.Pets
                .AsNoTracking()
                .Include(p => p.Category)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (pet == null)
            {
                return null;
            }

            return new PetDto
            {
                Id = pet.Id,
                Name = pet.Name,
                ImageUrl = pet.ImageUrl,
                Age = pet.Age,
                Weight = pet.Weight,
                Gender = pet.Gender,
                Breed = pet.Breed,
                DateOfBirth = pet.DateOfBirth,
                VaccinationStatus = pet.VaccinationStatus,
                CategoryId = pet.CategoryId,
                CategoryName = pet.Category?.Name,
                UserId = pet.UserId,
                Count = pet.Count,
                IsGroupCategory = pet.Category?.IsGroupCategory ?? false
            };
        }

        // Delete a pet profile by ID
        public async Task<PetVetApiResponse> DeletePetAsync(int id)
        {
            var pet = await _context.Pets.FirstOrDefaultAsync(p => p.Id == id);

            if (pet == null)
            {
                return PetVetApiResponse.Fail("Pet profile not found.");
            }

            _context.Pets.Remove(pet);
            await _context.SaveChangesAsync();

            return PetVetApiResponse.Success();
        }
    }
}
