@using Microsoft.AspNetCore.Components.Web
@using PetVet.Shared.Components.Apis
@using PetVet.Shared.Models.AzureOpenAI
@inject IJSRuntime JSRuntime
@inject IChatApi ChatApi

<!-- Chatbot Floating Button -->
<ChatbotFloatingButton IsOpen="@isChatbotOpen" 
IsOpenChanged="@OnChatbotToggle"
HasUnreadMessages="@hasUnreadMessages"
UnreadCount="@unreadCount" />

<!-- Chatbot Interface -->
<ChatbotInterface @ref="chatbotInterface"
IsOpen="@isChatbotOpen" 
IsOpenChanged="@OnChatbotToggle"
OnMessageSent="@HandleMessageSent" />

@code {

    [Parameter] public EventCallback<string> OnUserMessage { get; set; }
    [Parameter] public EventCallback<string> OnAIResponse { get; set; }

    private ChatbotInterface? chatbotInterface;
    private bool isChatbotOpen = false;
    private bool hasUnreadMessages = false;
    private int unreadCount = 0;
    private bool isProcessingMessage = false;
    private string? currentConversationId = null;
    private string? currentUserId = null;

    private async Task OnChatbotToggle(bool isOpen)
    {
        isChatbotOpen = isOpen;

        if (isOpen)
        {
            // Clear unread messages when opening
            hasUnreadMessages = false;
            unreadCount = 0;
        }

        StateHasChanged();
    }

    private async Task HandleMessageSent(string message)
    {
        if (isProcessingMessage)
            return;

        isProcessingMessage = true;

        try
        {
            // Notify parent component about user message
            await OnUserMessage.InvokeAsync(message);

            // Send message to Azure OpenAI API
            await SendToAzureOpenAI(message);
        }
        catch (Exception ex)
        {
            // Handle error with fallback
            await AddAIResponse("I'm sorry, I encountered an error. Please try again later.");
            Console.WriteLine($"Chatbot error: {ex.Message}");
        }
        finally
        {
            isProcessingMessage = false;

            await InvokeAsync(() => StateHasChanged());
        }
    }

    private async Task SendToAzureOpenAI(string userMessage)
    {
        try
        {
            // Initialize conversation ID if not set
            if (string.IsNullOrEmpty(currentConversationId))
            {
                currentConversationId = Guid.NewGuid().ToString();
            }

            // Initialize user ID if not set (you can get this from authentication context)
            if (string.IsNullOrEmpty(currentUserId))
            {
                currentUserId = "anonymous-user"; // Replace with actual user ID from auth
            }

            // Create request
            var request = new SendMessageRequest
            {
                Message = userMessage,
                ConversationId = currentConversationId,
                UserId = currentUserId
            };

            // Send to API
            var response = await ChatApi.SendMessageAsync(request);

            if (response.Success && !string.IsNullOrEmpty(response.Response))
            {
                await AddAIResponse(response.Response);

                // Update conversation ID if it was created by the server
                if (!string.IsNullOrEmpty(response.ConversationId))
                {
                    currentConversationId = response.ConversationId;
                }
            }
            else
            {
                // Fallback to local response if API fails
                await AddAIResponse(response.Message ?? "I'm sorry, I couldn't process your request right now. Please try again.");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Azure OpenAI API error: {ex.Message}");

            // Fallback to local response
            await AddAIResponse("I'm experiencing some technical difficulties. Let me try to help you with a general response.\n\n" +
                              "I'm here to help with pet care questions including nutrition, health, grooming, and training. " +
                              "What specific aspect of pet care would you like to know about?");
        }
    }

    // Fallback method for when API is unavailable
    private async Task UseFallbackResponse(string userMessage)
    {
        await Task.Delay(1000); // Simulate processing time

        var lowerMessage = userMessage.ToLower();
        string response;

        if (lowerMessage.Contains("nutrition") || lowerMessage.Contains("food"))
        {
            response = "Great question about pet nutrition! A balanced diet is crucial for your pet's health. Choose high-quality food appropriate for your pet's age and size, avoid toxic foods like chocolate and grapes, and maintain regular feeding schedules.";
        }
        else if (lowerMessage.Contains("health") || lowerMessage.Contains("sick"))
        {
            response = "I understand your concern about pet health. Watch for changes in appetite, lethargy, vomiting, or unusual behavior. For any health concerns, please consult with a veterinarian for proper diagnosis and treatment.";
        }
        else if (lowerMessage.Contains("hello") || lowerMessage.Contains("hi"))
        {
            response = "Hello! I'm your PetVet AI Assistant. I can help with pet nutrition, health guidance, grooming tips, and training advice. What would you like to know about caring for your pet?";
        }
        else
        {
            response = "I'm here to help with pet care questions! I can provide guidance on nutrition, health, grooming, and training. What specific aspect of pet care would you like to know about?";
        }

        await AddAIResponse(response);
    }

    public async Task AddAIResponse(string response)
    {
        if (chatbotInterface != null)
        {
            await chatbotInterface.AddAIResponse(response);
            
            // Notify parent component about AI response
            await OnAIResponse.InvokeAsync(response);

            // If chatbot is closed, show unread indicator
            if (!isChatbotOpen)
            {
                hasUnreadMessages = true;
                unreadCount++;
                StateHasChanged();
            }
        }
    }

    // Public method to send a message programmatically
    public async Task SendMessage(string message)
    {
        if (chatbotInterface != null && !string.IsNullOrWhiteSpace(message))
        {
            await HandleMessageSent(message);
        }
    }

    // Public method to open chatbot
    public async Task OpenChatbot()
    {
        await OnChatbotToggle(true);
    }

    // Public method to close chatbot
    public async Task CloseChatbot()
    {
        await OnChatbotToggle(false);
    }

    // Public method to check if chatbot is open
    public bool IsOpen => isChatbotOpen;

    // Public method to get message count
    public int MessageCount => chatbotInterface?.Messages?.Count ?? 0;
}
