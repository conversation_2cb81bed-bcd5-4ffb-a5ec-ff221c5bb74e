﻿using PetVet.Server.DataServices.Data;
using PetVet.ServiceContracts.Features.Conversation;
using PetVet.Api.Data;
using System.Security.Claims;
namespace PetVet.Api.Services.ChatMessages.Form;
public class ChatMessageServerSideFormDataService : IChatMessageFormDataService
{

    private readonly PetVetContext _context;
    private readonly IHttpContextAccessor contextAccessor;

    public ChatMessageServerSideFormDataService(PetVetContext context, IHttpContextAccessor contextAccessor)
    {
        _context = context;
        this.contextAccessor = contextAccessor;
    }

    public async Task<string> SaveAsync(ChatMessageFormBusinessObject formBusinessObject)
    {
        var userId = Convert.ToInt32(contextAccessor.HttpContext?.User.FindFirst(ClaimTypes.NameIdentifier)?.Value);
        ArgumentException.ThrowIfNullOrEmpty(formBusinessObject.ConversationId);

        var message = new ConversationMessage()
        {
            Id = Guid.NewGuid().ToString(),
            ConversationId = formBusinessObject.ConversationId,
            CreatedAt = DateTime.UtcNow,
            PlainContent = formBusinessObject.Content,
            SenderId = userId
        };

        _context.ConversationMessages.Add(message);
        await _context.SaveChangesAsync();
        return message.Id;
    }

    public Task<ChatMessageFormBusinessObject?> GetItemByIdAsync(string id)
    {
        throw new NotImplementedException();
    }
}
