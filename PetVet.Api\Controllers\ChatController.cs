using Microsoft.AspNetCore.Mvc;
using PetVet.Shared.Models.AzureOpenAI;
using PetVet.Shared.Services;

namespace PetVet.Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ChatController : ControllerBase
{
    private readonly IAzureOpenAIService _azureOpenAIService;
    private readonly ILogger<ChatController> _logger;

    public ChatController(IAzureOpenAIService azureOpenAIService, ILogger<ChatController> logger)
    {
        _azureOpenAIService = azureOpenAIService;
        _logger = logger;
    }

    /// <summary>
    /// Send a chat message to the AI assistant
    /// </summary>
    [HttpPost("message")]
    public async Task<ActionResult<ChatApiResponse>> SendMessage([FromBody] SendMessageRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.Message))
            {
                return BadRequest(new ChatApiResponse
                {
                    Success = false,
                    Message = "Message cannot be empty",
                    ErrorCode = "INVALID_MESSAGE"
                });
            }

            var response = await _azureOpenAIService.SendChatMessageAsync(
                request.Message, 
                request.ConversationId, 
                request.UserId
            );

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing chat message");
            return StatusCode(500, new ChatApiResponse
            {
                Success = false,
                Message = "An internal error occurred",
                ErrorCode = "INTERNAL_ERROR"
            });
        }
    }

    /// <summary>
    /// Send a full chat request with conversation context
    /// </summary>
    [HttpPost("chat")]
    public async Task<ActionResult<ChatApiResponse>> SendChatRequest([FromBody] ChatRequestWrapper request)
    {
        try
        {
            if (request.ChatRequest?.Messages == null || !request.ChatRequest.Messages.Any())
            {
                return BadRequest(new ChatApiResponse
                {
                    Success = false,
                    Message = "Chat request must contain at least one message",
                    ErrorCode = "INVALID_REQUEST"
                });
            }

            var response = await _azureOpenAIService.SendChatMessageAsync(
                request.ChatRequest, 
                request.ConversationId, 
                request.UserId
            );

            return Ok(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing chat request");
            return StatusCode(500, new ChatApiResponse
            {
                Success = false,
                Message = "An internal error occurred",
                ErrorCode = "INTERNAL_ERROR"
            });
        }
    }

    /// <summary>
    /// Get conversation history
    /// </summary>
    [HttpGet("conversation/{conversationId}")]
    public async Task<ActionResult<ChatConversation>> GetConversation(string conversationId)
    {
        try
        {
            var conversation = await _azureOpenAIService.GetConversationAsync(conversationId);
            
            if (conversation == null)
            {
                return NotFound(new { Message = "Conversation not found" });
            }

            return Ok(conversation);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving conversation {ConversationId}", conversationId);
            return StatusCode(500, new { Message = "An internal error occurred" });
        }
    }

    /// <summary>
    /// Get all conversations for a user
    /// </summary>
    [HttpGet("conversations/{userId}")]
    public async Task<ActionResult<List<ChatConversation>>> GetUserConversations(string userId)
    {
        try
        {
            var conversations = await _azureOpenAIService.GetUserConversationsAsync(userId);
            return Ok(conversations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving conversations for user {UserId}", userId);
            return StatusCode(500, new { Message = "An internal error occurred" });
        }
    }

    /// <summary>
    /// Delete a conversation
    /// </summary>
    [HttpDelete("conversation/{conversationId}")]
    public async Task<ActionResult> DeleteConversation(string conversationId)
    {
        try
        {
            var success = await _azureOpenAIService.DeleteConversationAsync(conversationId);
            
            if (!success)
            {
                return NotFound(new { Message = "Conversation not found" });
            }

            return Ok(new { Message = "Conversation deleted successfully" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting conversation {ConversationId}", conversationId);
            return StatusCode(500, new { Message = "An internal error occurred" });
        }
    }

    /// <summary>
    /// Clear all conversations for a user
    /// </summary>
    [HttpDelete("conversations/{userId}")]
    public async Task<ActionResult> ClearUserConversations(string userId)
    {
        try
        {
            var success = await _azureOpenAIService.ClearUserConversationsAsync(userId);
            return Ok(new { Message = $"Cleared conversations for user {userId}", Success = success });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing conversations for user {UserId}", userId);
            return StatusCode(500, new { Message = "An internal error occurred" });
        }
    }

    /// <summary>
    /// Get service health status
    /// </summary>
    [HttpGet("health")]
    public async Task<ActionResult> GetHealth()
    {
        try
        {
            var isHealthy = await _azureOpenAIService.IsHealthyAsync();
            var stats = await _azureOpenAIService.GetStatsAsync();

            return Ok(new
            {
                Healthy = isHealthy,
                Status = isHealthy ? "OK" : "Unhealthy",
                Stats = stats,
                Timestamp = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking service health");
            return StatusCode(500, new { Message = "Health check failed" });
        }
    }

    /// <summary>
    /// Get service statistics
    /// </summary>
    [HttpGet("stats")]
    public async Task<ActionResult<ChatServiceStats>> GetStats()
    {
        try
        {
            var stats = await _azureOpenAIService.GetStatsAsync();
            return Ok(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving service stats");
            return StatusCode(500, new { Message = "An internal error occurred" });
        }
    }
}

// Request models
public class SendMessageRequest
{
    public string Message { get; set; } = "";
    public string? ConversationId { get; set; }
    public string? UserId { get; set; }
}

public class ChatRequestWrapper
{
    public ChatRequest ChatRequest { get; set; } = new();
    public string? ConversationId { get; set; }
    public string? UserId { get; set; }
}
