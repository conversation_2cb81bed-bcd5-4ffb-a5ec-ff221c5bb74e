﻿@page "/petOwner/myPets"
@using PetVet.Shared.Components.Pages.Areas.PetOwner.Components
@inject NavigationManager _NavigationManager

<PageTitle>My Pets</PageTitle>

<!-- Modern My Pets Page -->
<div class="px-4 py-6 space-y-6">

    <!-- Header with Search -->
    <div class="space-y-4">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">My Pets</h1>
                <p class="text-gray-500 text-sm">Manage your furry friends</p>
            </div>
            <button class="w-10 h-10 bg-teal-500 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all transform hover:scale-105"
                    @onclick="NavigateToAddPet">
                <i class="fas fa-plus text-white"></i>
            </button>
        </div>

        <!-- Modern Search Bar -->
        <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400"></i>
            </div>
            <input type="text"
                   class="w-full pl-10 pr-4 py-3 bg-gray-50 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent transition-all"
                   placeholder="Search your pets..."
                   @bind="searchTerm"
                   @bind:event="oninput"
                   @onkeyup="HandleSearch" />
        </div>
    </div>

    <!-- Pet Categories Filter -->
    <div class="flex gap-2 overflow-x-auto pb-2">
        <button class="px-4 py-2 bg-teal-500 text-white rounded-full text-sm font-medium whitespace-nowrap">
            All Pets
        </button>
        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm font-medium whitespace-nowrap hover:bg-gray-200 transition-colors">
            Dogs
        </button>
        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm font-medium whitespace-nowrap hover:bg-gray-200 transition-colors">
            Cats
        </button>
        <button class="px-4 py-2 bg-gray-100 text-gray-600 rounded-full text-sm font-medium whitespace-nowrap hover:bg-gray-200 transition-colors">
            Others
        </button>
    </div>

    <!-- Adoption Banner -->
    <div class="bg-gradient-to-r from-green-400 to-blue-500 rounded-2xl p-4 text-white relative overflow-hidden">
        <div class="absolute top-2 right-2 w-16 h-16 bg-white/20 rounded-full"></div>
        <div class="absolute bottom-2 left-2 w-8 h-8 bg-white/20 rounded-full"></div>

        <div class="relative z-10">
            <h3 class="font-bold text-lg mb-1">Adopting for a reason that can only be</h3>
            <p class="text-white/90 text-sm mb-3">described as divine, have the ability to forgive, let go of the past, and live each day joyously.</p>
            <button class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-full text-sm font-medium transition-colors">
                Catchup
            </button>
        </div>
    </div>

    <!-- Pets Grid -->
    <div>
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-bold text-gray-800">All Pets</h2>
            <a href="/petOwner/myPets/all" class="text-teal-600 text-sm font-medium flex items-center gap-1">
                See all <i class="fas fa-arrow-right text-xs"></i>
            </a>
        </div>

        <!-- Pet Cards Component -->
        <MyPetsList @ref="petsList" />
    </div>

</div>

@code {
    private MyPetsList petsList;
    private string searchTerm = "";

    protected override void OnInitialized()
    {
        base.OnInitialized();
    }

    private async Task HandleSearch(KeyboardEventArgs e)
    {
        if (petsList != null)
        {
            await petsList.HandleSearch(searchTerm);
        }
    }

    private void NavigateToAddPet()
    {
        _NavigationManager.NavigateTo("/petOwner/addPet");
    }
}


