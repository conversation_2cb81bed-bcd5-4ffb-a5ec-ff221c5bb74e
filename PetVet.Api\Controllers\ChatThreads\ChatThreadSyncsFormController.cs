﻿using Microsoft.AspNetCore.Mvc;
using PetVet.ServiceContracts.Features.Conversation;
namespace PetVet.Server.WebApis.Controller.Conversation;
[ApiController, Route("api/[controller]/[action]")]
public class ChatThreadSyncsFormController : ControllerBase, IChatThreadSyncFormDataService
{

	private readonly IChatThreadSyncFormDataService dataService;

	public ChatThreadSyncsFormController(IChatThreadSyncFormDataService dataService)
	{
		this.dataService = dataService;
	}
	
	[HttpPost]
	public async Task<string> SaveAsync([FromBody] ChatThreadSyncFormBusinessObject formBusinessObject)
	{
		return await dataService.SaveAsync(formBusinessObject);
	}

	
	[HttpGet]
	public async Task<ChatThreadSyncFormBusinessObject> GetItemByIdAsync(string id)
	{
		return await dataService.GetItemByIdAsync(id);
	}

    
    [HttpGet]
    public async Task<ChatThreadSyncFormBusinessObject[]> GetItemsAsync()
    {
       return await dataService.GetItemsAsync();
    }
}
