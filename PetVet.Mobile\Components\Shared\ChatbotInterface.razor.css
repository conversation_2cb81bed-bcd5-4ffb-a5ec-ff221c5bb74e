/* Chatbot Modal Styles */
.chatbot-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: 1001;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.chatbot-modal--open {
    opacity: 1;
    visibility: visible;
}

.chatbot-container {
    background: white;
    border-radius: 1.5rem 1.5rem 0 0;
    width: 100%;
    max-width: 100%;
    height: 80vh;
    max-height: 600px;
    display: flex;
    flex-direction: column;
    transform: translateY(100%);
    transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 -10px 25px rgba(0, 0, 0, 0.1);
}

.chatbot-modal--open .chatbot-container {
    transform: translateY(0);
}

/* Header */
.chatbot-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid #f3f4f6;
    background: linear-gradient(135deg, #FEA195 0%, #f49d87 100%);
    color: white;
    border-radius: 1.5rem 1.5rem 0 0;
}

.chatbot-header__info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.chatbot-header__avatar {
    width: 2.5rem;
    height: 2.5rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.chatbot-header__text h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
}

.chatbot-header__text p {
    margin: 0;
    font-size: 0.875rem;
    opacity: 0.9;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chatbot-status--typing {
    animation: pulse 2s infinite;
}

.chatbot-header__close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
}

.chatbot-header__close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Messages Area */
.chatbot-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    scroll-behavior: smooth;
}

.chatbot-messages::-webkit-scrollbar {
    width: 4px;
}

.chatbot-messages::-webkit-scrollbar-track {
    background: transparent;
}

.chatbot-messages::-webkit-scrollbar-thumb {
    background: rgba(156, 163, 175, 0.3);
    border-radius: 2px;
}

/* Welcome Message */
.chatbot-welcome {
    text-align: center;
    padding: 2rem 1rem;
    animation: fadeInUp 0.6s ease-out;
}

.chatbot-welcome__icon {
    width: 4rem;
    height: 4rem;
    background: linear-gradient(135deg, #FEA195 0%, #f49d87 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: white;
}

.chatbot-welcome h4 {
    margin: 0 0 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.chatbot-welcome p {
    margin: 0 0 1.5rem;
    color: #6b7280;
    line-height: 1.5;
}

.chatbot-quick-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-top: 1.5rem;
}

.quick-action-btn {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 0.75rem;
    font-size: 0.875rem;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: left;
}

.quick-action-btn:hover {
    background: #f3f4f6;
    border-color: #FEA195;
    transform: translateY(-1px);
}

/* Chat Messages */
.chatbot-message {
    display: flex;
    gap: 0.75rem;
    animation: fadeInUp 0.3s ease-out;
}

.chatbot-message--user {
    flex-direction: row-reverse;
}

.chatbot-message--typing {
    opacity: 0.7;
}

.chatbot-message__avatar {
    width: 2rem;
    height: 2rem;
    background: linear-gradient(135deg, #8FBFA8 0%, #6fa088 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.chatbot-message__content {
    flex: 1;
    max-width: 80%;
}

.chatbot-message--user .chatbot-message__content {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
}

.chatbot-message__bubble {
    background: #f3f4f6;
    border-radius: 1.125rem;
    padding: 0.75rem 1rem;
    color: #1f2937;
    line-height: 1.5;
    word-wrap: break-word;
}

.chatbot-message--user .chatbot-message__bubble {
    background: linear-gradient(135deg, #FEA195 0%, #f49d87 100%);
    color: white;
}

.chatbot-message__time {
    font-size: 0.75rem;
    color: #9ca3af;
    margin-top: 0.25rem;
    padding: 0 0.5rem;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    gap: 0.25rem;
    align-items: center;
}

.typing-indicator span {
    width: 0.5rem;
    height: 0.5rem;
    background: currentColor;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
    animation-delay: -0.16s;
}

/* Input Area */
.chatbot-input {
    border-top: 1px solid #f3f4f6;
    padding: 1rem 1.5rem;
    background: white;
}

.chatbot-input__container {
    display: flex;
    gap: 0.75rem;
    align-items: flex-end;
}

.chatbot-input__field {
    flex: 1;
    border: 1px solid #e5e7eb;
    border-radius: 1.25rem;
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    resize: none;
    outline: none;
    transition: all 0.2s ease;
    max-height: 6rem;
    min-height: 2.75rem;
}

.chatbot-input__field:focus {
    border-color: #FEA195;
    box-shadow: 0 0 0 3px rgba(254, 161, 149, 0.1);
}

.chatbot-input__field:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.chatbot-input__send {
    width: 2.75rem;
    height: 2.75rem;
    background: linear-gradient(135deg, #FEA195 0%, #f49d87 100%);
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.chatbot-input__send:hover:not(.chatbot-input__send--disabled) {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(254, 161, 149, 0.4);
}

.chatbot-input__send--disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #d1d5db;
}

.chatbot-input__footer {
    text-align: center;
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: #9ca3af;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes typing {
    0%, 60%, 100% {
        transform: translateY(0);
        opacity: 0.4;
    }
    30% {
        transform: translateY(-10px);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Tablet and larger screens */
@media (min-width: 768px) {
    .chatbot-modal {
        align-items: center;
        justify-content: flex-end;
        padding: 2rem;
    }

    .chatbot-container {
        width: 400px;
        height: 600px;
        border-radius: 1.5rem;
        margin-right: 1rem;
        margin-bottom: 5rem;
    }

    .chatbot-header {
        border-radius: 1.5rem 1.5rem 0 0;
    }

    .chatbot-quick-actions {
        grid-template-columns: 1fr;
    }
}
