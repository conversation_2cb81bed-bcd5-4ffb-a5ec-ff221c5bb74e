﻿.MyAllPetsList-CONTAINER {
    margin-top: 3rem 0;
}

.MyAllPetsList-HEADING {
    font-size: 1.125rem;
    font-family: 'Acme', sans-serif;
    font-weight: bold;
    cursor: default;
}

.MyAllPetsList-PET-LIST {
    display: flex;
    justify-content: center;
    gap: 0.625rem;
    margin: 0.625rem 0;
}

.MyAllPetsList-CARD {
    border-radius: 1.25rem;
}

.MyAllPetsList-CARD-BODY {
    border-radius: 1.25rem;
}

.MyAllPetsList-IMAGE {
    height: 4.6875rem;
    width: 4.6875rem;
    object-fit: cover;
    border-radius: 50%;
}

.MyAllPetsList-Name {
    font-size: 1.125rem;
    font-family: 'Acme', sans-serif;
    font-weight: bold;
    cursor: default;
    color: #ffffff;
}

    .MyAllPetsList-Name .badge {
        font-size: 0.75rem;
        margin-left: 0.375rem;
        vertical-align: middle;
    }

.MyAllPetsList-VIEWPROFILE-BTN {
    display: inline-block;
    background-color: #FFC107;
    border-radius: 0.625rem;
    padding: 0.3125rem 0.625rem;
    color: #333;
    
    font-weight: bold;
    cursor: pointer;
    margin-top: 0.3125rem;
    text-decoration: none;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

    .MyAllPetsList-VIEWPROFILE-BTN:hover {
        color: #333;
        background-color: #e0a406;
        transform: scale(1.05);
    }

.MyAllPetsList-CARD-HEART-ICONS {
    background-color: white;
    border-radius: 50%;
    padding: 0.5rem;
    font-size: 1.5rem;
    color: red;
    display: inline-block;
    line-height: 1;
    box-shadow: 0 0.25rem 0.375rem rgba(0, 0, 0, 0.1);
}
