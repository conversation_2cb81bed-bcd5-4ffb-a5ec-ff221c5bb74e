using System.Collections.Concurrent;

namespace PetVet.Mobile.Models;

/// <summary>
/// Enhanced audio frame with metadata for real-time streaming
/// </summary>
public class AudioFrame
{
    public string CallId { get; set; } = "";
    public int FromUserId { get; set; }  
    public byte[] AudioData { get; set; } = Array.Empty<byte>();
    public int SampleRate { get; set; } = 44100;
    public int Channels { get; set; } = 1;
    public int BitsPerSample { get; set; } = 16;
    public long SequenceNumber { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public TimeSpan Duration { get; set; }
    public AudioQuality Quality { get; set; } = AudioQuality.Standard; 
}

/// <summary>
/// Audio quality settings for adaptive streaming
/// </summary>
public enum AudioQuality
{
    Low = 0,      // 8kHz, 8-bit
    Standard = 1, // 16kHz, 16-bit
    High = 2,     // 44.1kHz, 16-bit
    Premium = 3   // 48kHz, 24-bit
}

/// <summary>
/// Audio compression types
/// </summary>
public enum CompressionType
{
    None = 0,
    PCM = 1,
    ADPCM = 2,
    Opus = 3
}

/// <summary>
/// Audio buffer for managing real-time playback
/// </summary>
public class AudioBuffer
{
    private readonly ConcurrentQueue<AudioFrame> _frameQueue = new();
    private readonly object _lockObject = new();
    private readonly int _maxBufferSize;
    private readonly TimeSpan _maxBufferDuration;
    
    public int Count => _frameQueue.Count;
    public bool IsEmpty => _frameQueue.IsEmpty;
    public TimeSpan BufferedDuration { get; private set; }
    public bool IsBuffering { get; private set; } = true;
    public int MinBufferFrames { get; set; } = 3;
    public int MaxBufferFrames { get; set; } = 10;

    public AudioBuffer(int maxBufferSize = 50, TimeSpan? maxBufferDuration = null)
    {
        _maxBufferSize = maxBufferSize;
        _maxBufferDuration = maxBufferDuration ?? TimeSpan.FromSeconds(2);
    }

    public void AddFrame(AudioFrame frame)
    {
        lock (_lockObject)
        {
            // Remove old frames if buffer is full
            while (_frameQueue.Count >= _maxBufferSize)
            {
                if (_frameQueue.TryDequeue(out var oldFrame))
                {
                    BufferedDuration -= oldFrame.Duration;
                }
            }

            _frameQueue.Enqueue(frame);
            BufferedDuration += frame.Duration;

            // Check if we have enough frames to start playback
            if (IsBuffering && _frameQueue.Count >= MinBufferFrames)
            {
                IsBuffering = false;
            }
        }
    }

    public AudioFrame? GetNextFrame()
    {
        lock (_lockObject)
        {
            if (_frameQueue.TryDequeue(out var frame))
            {
                BufferedDuration -= frame.Duration;
                
                // Check if we need to start buffering again
                if (_frameQueue.Count < MinBufferFrames)
                {
                    IsBuffering = true;
                }
                
                return frame;
            }
            return null;
        }
    }

    public void Clear()
    {
        lock (_lockObject)
        {
            _frameQueue.Clear();
            BufferedDuration = TimeSpan.Zero;
            IsBuffering = true;
        }
    }

    public AudioFrame[] GetAllFrames()
    {
        lock (_lockObject)
        {
            var frames = new List<AudioFrame>();
            while (_frameQueue.TryDequeue(out var frame))
            {
                frames.Add(frame);
            }
            BufferedDuration = TimeSpan.Zero;
            IsBuffering = true;
            return frames.ToArray();
        }
    }
}

/// <summary>
/// Audio quality metrics for monitoring call performance
/// </summary>
public class AudioQualityMetrics
{
    public string CallId { get; set; } = "";
    public double Latency { get; set; } // in milliseconds
    public double Jitter { get; set; } // in milliseconds
    public int PacketsLost { get; set; }
    public int PacketsReceived { get; set; }
    public double PacketLossRate => PacketsReceived > 0 ? (double)PacketsLost / (PacketsLost + PacketsReceived) : 0;
    public double AudioLevel { get; set; } // 0.0 to 1.0
    public bool IsMuted { get; set; }
    public AudioQuality CurrentQuality { get; set; } = AudioQuality.Standard;
    public DateTime LastUpdate { get; set; } = DateTime.UtcNow;
    public TimeSpan BufferHealth { get; set; }
    public int BufferUnderruns { get; set; }
    public double NetworkQuality { get; set; } = 1.0; // 0.0 to 1.0
}

/// <summary>
/// Audio streaming configuration
/// </summary>
public class AudioStreamingConfig
{
    public int SampleRate { get; set; } = 16000; // 16kHz for voice calls
    public int Channels { get; set; } = 1; // Mono
    public int BitsPerSample { get; set; } = 16;
    public int FrameSizeMs { get; set; } = 20; // 20ms frames
    public AudioQuality Quality { get; set; } = AudioQuality.Standard;
    public bool EnableEchoCancellation { get; set; } = true;
    public bool EnableNoiseSuppression { get; set; } = true;
    public bool EnableAutomaticGainControl { get; set; } = true;
    public double VolumeLevel { get; set; } = 1.0; // 0.0 to 2.0
    public bool EnableAdaptiveQuality { get; set; } = true;
    public int MaxRetransmissions { get; set; } = 3;
    public TimeSpan MaxLatency { get; set; } = TimeSpan.FromMilliseconds(150);

    public int GetFrameSizeInBytes()
    {
        return (SampleRate * Channels * BitsPerSample / 8) * FrameSizeMs / 1000;
    }

    public TimeSpan GetFrameDuration()
    {
        return TimeSpan.FromMilliseconds(FrameSizeMs);
    }

    public static AudioStreamingConfig GetQualityConfig(AudioQuality quality)
    {
        return quality switch
        {
            AudioQuality.Low => new AudioStreamingConfig
            {
                SampleRate = 8000,
                BitsPerSample = 8,
                Quality = AudioQuality.Low
            },
            AudioQuality.Standard => new AudioStreamingConfig
            {
                SampleRate = 16000,
                BitsPerSample = 16,
                Quality = AudioQuality.Standard
            },
            AudioQuality.High => new AudioStreamingConfig
            {
                SampleRate = 44100,
                BitsPerSample = 16,
                Quality = AudioQuality.High
            },
            AudioQuality.Premium => new AudioStreamingConfig
            {
                SampleRate = 48000,
                BitsPerSample = 24,
                Quality = AudioQuality.Premium
            },
            _ => new AudioStreamingConfig()
        };
    }
}

/// <summary>
/// Audio mixer for handling multiple audio streams
/// </summary>
public class AudioMixer
{
    private readonly Dictionary<int, AudioBuffer> _participantBuffers = new();
    private readonly object _lockObject = new();
    private readonly AudioStreamingConfig _config;

    public AudioMixer(AudioStreamingConfig config)
    {
        _config = config;
    }

    public void AddParticipant(int userId)
    {
        lock (_lockObject)
        {
            if (!_participantBuffers.ContainsKey(userId))
            {
                _participantBuffers[userId] = new AudioBuffer();
            }
        }
    }

    public void RemoveParticipant(int userId)
    {
        lock (_lockObject)
        {
            if (_participantBuffers.TryGetValue(userId, out var buffer))
            {
                buffer.Clear();
                _participantBuffers.Remove(userId);
            }
        }
    }

    public void AddAudioFrame(int userId, AudioFrame frame)
    {
        lock (_lockObject)
        {
            if (_participantBuffers.TryGetValue(userId, out var buffer))
            {
                buffer.AddFrame(frame);
            }
        }
    }

    public byte[]? GetMixedAudio()
    {
        lock (_lockObject)
        {
            var frames = new List<AudioFrame>();
            
            // Get next frame from each participant
            foreach (var buffer in _participantBuffers.Values)
            {
                var frame = buffer.GetNextFrame();
                if (frame != null)
                {
                    frames.Add(frame);
                }
            }

            if (frames.Count == 0)
                return null;

            if (frames.Count == 1)
                return frames[0].AudioData;

            // Mix multiple audio streams
            return MixAudioFrames(frames);
        }
    }

    private byte[] MixAudioFrames(List<AudioFrame> frames)
    {
        if (frames.Count == 0)
            return Array.Empty<byte>();

        var maxLength = frames.Max(f => f.AudioData.Length);
        var mixedData = new byte[maxLength];
        var tempBuffer = new int[maxLength / 2]; // Assuming 16-bit samples

        // Mix all frames
        foreach (var frame in frames)
        {
            for (int i = 0; i < frame.AudioData.Length - 1; i += 2)
            {
                // Convert bytes to 16-bit sample
                var sample = BitConverter.ToInt16(frame.AudioData, i);
                tempBuffer[i / 2] += sample;
            }
        }

        // Convert back to bytes with clipping
        for (int i = 0; i < tempBuffer.Length; i++)
        {
            var mixed = Math.Max(-32768, Math.Min(32767, tempBuffer[i]));
            var bytes = BitConverter.GetBytes((short)mixed);
            mixedData[i * 2] = bytes[0];
            mixedData[i * 2 + 1] = bytes[1];
        }

        return mixedData;
    }

    public void ClearAll()
    {
        lock (_lockObject)
        {
            foreach (var buffer in _participantBuffers.Values)
            {
                buffer.Clear();
            }
        }
    }
}
