/* Base container */
.PetOwner-Account-CONTAINER {
    background: #f2f6f9;
    padding: 2rem;
    min-height: 100vh;
    width: 100%;
    max-width: 72rem;
    margin: 0 auto;
    font-family: 'Inter', sans-serif;
    overflow-x: hidden;
}

/* Profile Header Section */
.profile-header {
    background: #157BAB;
    border-radius: 1.25rem;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    text-align: center;
}

.profile-image-section {
    margin-bottom: 1.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.profile-image {
    width: 150px;
    height: 150px;
    border-radius: 1.25rem;
    object-fit: cover;
    border: 3px solid rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
}

    .profile-image:hover {
        transform: scale(1.05);
    }

.profile-image-placeholder {
    width: 150px;
    height: 150px;
    border-radius: 1.25rem;
    background: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    margin-bottom: 1rem;
    border: 3px solid rgba(255, 255, 255, 0.5);
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.3);
}

.profile-info {
    color: white;
}

.profile-name {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    font-family: 'Acme', sans-serif;
}

.profile-email {
    color: #BFDBFE;
    
    font-size: 1.125rem;
}

.stat-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

/* Stats Section */
.stats-section {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    background-color: white;
    border-radius: 1.25rem;
    padding: 1.5rem;
    margin: 2rem 0;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.stats-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #3B82F6;
    font-size: 0.875rem;
    font-weight: 600;
    text-align: center;
    transition: transform 0.3s ease;
    font-family: 'Acme', sans-serif;
}

    .stats-item:hover {
        transform: scale(1.05);
    }

.stats-icon {
    width: 2.5rem;
    height: 2.5rem;
    margin-bottom: 0.75rem;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    color: #1F2937;
    font-family: 'Acme', sans-serif;
}

.stat-label {
    font-size: 1rem;
    color: #6B7280;
    
}

/* Content Section */
.content-section {
    background: white;
    border-radius: 1.25rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.section-tabs {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    background: #157BAB;
}

.tab-button {
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: 1.25rem;
    color: black;
    
    font-weight: 600;
    transition: all 0.3s ease;
    cursor: pointer;
}

    .tab-button:hover {
        transform: translateY(-2px);
    }

    .tab-button.active {
        background: #FFC107;
        color: #1F2937;
    }

.tab-content {
    padding: 2rem;
}

/* Form Styles */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

    .form-group label {
        font-weight: 600;
        color: #374151;
        
    }

.form-control {
    padding: 0.875rem;
    border: 1px solid #E5E7EB;
    border-radius: 1.25rem;
    
    transition: all 0.3s ease;
    font-size: 1rem;
}

    .form-control:focus {
        outline: none;
        border-color: #3B82F6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
}

/* Buttons */
.action-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: 1.25rem;
    
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
}

.image-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.upload-button {
    background: #FFC107;
    color: #1F2937;
}

    .upload-button:hover {
        background: #F59E0B;
        transform: translateY(-2px);
    }

.remove-button {
    background: #EF4444;
    color: white;
}

    .remove-button:hover {
        background: #DC2626;
        transform: translateY(-2px);
    }

.submit-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    background: #157BAB;
    color: white;
    border: none;
    border-radius: 1.25rem;
    
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 180px;
}

    .submit-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 6px -1px rgba(59, 130, 246, 0.5);
    }

    .submit-button:disabled {
        opacity: 0.7;
        cursor: not-allowed;
    }

/* Error Alert */
.error-alert {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: #FEE2E2;
    color: #991B1B;
    border-radius: 1.25rem;
    margin-bottom: 1.5rem;
    
}

.hidden {
    display: none;
}

/* Loading Spinner */
.spinner {
    display: inline-block;
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .PetOwner-Account-CONTAINER {
        padding: 1rem;
    }

    .profile-header {
        padding: 1.5rem;
    }

    .profile-name {
        font-size: 1.5rem;
    }

    .profile-email {
        font-size: 1rem;
    }

    .stats-section {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        padding: 1.25rem;
        margin: 1.5rem 0;
    }

    .stats-icon {
        width: 2rem;
        height: 2rem;
        margin-bottom: 0.5rem;
    }

    .form-grid {
        grid-template-columns: 1fr;
    }

    .section-tabs {
        flex-direction: column;
        gap: 0.75rem;
    }

    .tab-button {
        width: 100%;
    }

    .profile-image,
    .profile-image-placeholder {
        width: 120px;
        height: 120px;
    }

    .image-actions {
        flex-direction: column;
        align-items: stretch;
    }

    .action-button {
        width: 100%;
    }

    .tab-content {
        padding: 1.5rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .submit-button {
        width: 100%;
    }
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.PetOwner-Account-CONTAINER {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}
